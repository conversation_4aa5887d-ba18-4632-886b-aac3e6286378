<% include ../header.html %>
<link rel="stylesheet" href="<%- locals.static %>/css/element/index.css">
<link rel="stylesheet" href="<%- locals.static %>/css/landingPage.css">
<div class="oiq-landing-page">
  <div class="oiq-banner">
    <div class="wrap">
      <div class="banner-content">
        <div class="banner-title">快速报价</div>
        <div class="banner-detail">— 专业工程师在线服务，2小时获取测试方案 —</div>
        <a class="banner-btn" onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1', '?')">
          开始询价
          <i class="banner-btn-icon"></i>
        </a>
      </div>
    </div>
  </div>
  <div class="big-data">
    <div class="data-title">
      <i class="data-icon"></i>
      <span>SGS在线服务大数据</span>
      <ul class="data-scroll-list" id="data-scroll-list">
        <% for(let [i, item] of oiqList.entries()){ %>
        <li>来自 <span style="font-weight: bold"><%- item.city %></span> 的用户 <%- item.userName %> <%- item.quotationTimeStr %>获得 <span
            style="font-weight: bold"><%- item.lastCategory %></span> 测试方案与报价
        </li>
        <% } %>
      </ul>
      <div class="data-list" id="bigData">
        <div class="data-item">
          <div class="data-detail"><em>23800</em>+</div>
          <div class="data-label">在线订单数量</div>
        </div>
        <div class="data-item">
          <div class="data-detail"><<em>1</em>h</div>
          <div class="data-label">平均报价时间</div>
        </div>
        <div class="data-item">
          <div class="data-detail"><em>800</em>+</div>
          <div class="data-label">服务覆盖类目</div>
        </div>
        <div class="data-item">
          <div class="data-detail"><em>8000</em>+</div>
          <div class="data-label">测试项目及标准</div>
        </div>
      </div>
    </div>
  </div>
  <div class="big-btn-floor">
    <a class="big-btn btn1" onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1', '?')">
      <span class="title">简单</span>
      <span class="detail">3个步骤<br/>5分钟完成提交</span>
    </a>
    <a class="big-btn btn2" onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1', '?')">
      <span class="title">快速</span>
      <span class="detail">2小时获取<br/>完整方案及报价</span>
    </a>
    <a class="big-btn btn3" onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1', '?')">
      <span class="title">限时折扣 </span>
      <!-- <i class="btn-icon"><i class="big-btn-icon"></i></i> -->
    </a>
  </div>
  <div class="video-floor">
    <div class="wrap">
      <div class="video-left-content">
        <div class="video-title">
          <img src="<%- locals.static %>/images/oiq/landingPage/video-title-icon.png" alt="">
          SGS快速报价系统简介
        </div>
        <div class="video-content">
          <img src="<%- locals.static %>/images/oiq/landingPage/video-page.jpg" alt="" id="video-img">
          <video src="<%- locals.static %>/video/oiq.mp4" controls="controls" width="100%" height="100%">
            your browser does not support the video tag
          </video>
        </div>
      </div>
      <div class="video-right-content">
        <div class="ques-title">什么是SGS快速报价？</div>
        <ul class="video-text-list">
          <li>20年技术沉淀，智能推荐检测项目</li>
          <li>2小时快速获取准确报价</li>
          <li>一键轻松定制全面检测方案</li>
          <li>零距离在线对接专业工程师</li>
        </ul>
        <div class="ques-title">适合谁来用？</div>
        <ul class="video-text-list">
          <li>业务开发、采购人员、研发专员、品控QA</li>
          <li>不论您是行业专家还是职场新人都能轻松掌握的工具</li>
        </ul>
        <a class="video-btn"
           onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1', '?')">立即体验</a>
        <img src="<%- locals.static %>/images/oiq/landingPage/video-text-icon.png" alt="" class="text-icon">
      </div>
    </div>
  </div>
  <div class="category-floor">
    <div class="category-title">如何获取方案</div>
    <div class="plan">
      <div class="plan-step">
        <ul>
          <li>
            选择产品类目
          </li>
          <li>
            选择测试项目
          </li>
          <li>
            获取完整方案
          </li>
        </ul>
      </div>
      <div class="plan-content" id='plan-content'>
        <ul>
          <li>
            上线800+品类，涵盖金属及高分子材料产品、<br>餐厨用具、包装，更多品类持续上新...
          </li>
          <li>
            根据产品智能推荐检测项目<br>自助选择或补充需求
          </li>
          <li>
            2小时在线获取方案 <br>专业工程师在线沟通
          </li>
        </ul>
      </div>
      <div class="plan-button">
        <div id='plan-button'></div>
      </div>
    </div>
  </div>
  <div class="products-floor">
    <div class="products-title">询价产品及热门项目</div>
    <div class="product-swiper">
      <div class="products-list swiper-wrapper">
        <% for(let [i, item] of products.entries()){ %>
        <a class="products-item swiper-slide"
           onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=<%- item.urlPath %>', '&')">
          <div class="tit"><%- item.title %></div>
          <img class="title_img" src="<%- locals.static %>/images/oiq/landingPage/products/<%- i+1 %>.png" alt="">
          <div class="title_children">
            <% for(let [i, s] of item.list.entries()){ %>
            <div class="title_children_list">
              <div class="title_children_icon">
                <img src="<%- locals.static %>/images/oiq/landingPage/products/<%- i+1 %>-<%- i+1 %>.png"
                     alt="">
              </div>
              <div class="title_children_content"><%- s.title %></div>
            </div>
            <% } %>
          </div>
          <div class="products-btn">
            <div class="products-icon">
              <div class="products-text">更多</div>
              <div><i class="products-btn-icon"></i></div>
            </div>
          </div>
        </a>
        <% } %>
      </div>
      <div class="swiper-bottom1">
        <div class="swiper-pagination1"></div>
      </div>
    </div>
  </div>
  <!--    <div class="category-floor">-->
  <!--        <div class="category-title">热门询价类目</div>-->
  <!--        <div class="category-list">-->
  <!--            <% for(let [i, item] of category.entries()){ %>-->
  <!--            <a class="category-item"-->
  <!--               onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=<%- item.urlPath %>', '&')">-->
  <!--                <img src="<%- locals.static %>/images/oiq/landingPage/category/<%- i %>.png" alt="">-->
  <!--                <div><%- item.label %></div>-->
  <!--                <span class="category-btn">-->
  <!--          <i class="category-btn-icon"></i>-->
  <!--        </span>-->
  <!--            </a>-->
  <!--            <% } %>-->
  <!--        </div>-->
  <!--    </div>-->
  <!--    <div class="project-floor">-->
  <!--        <div class="project-title">热门询价项目</div>-->
  <!--        <div class="project-content">-->
  <!--            <% for(let [i, item] of project.entries()){ %>-->
  <!--            <ul class="project-list">-->
  <!--                <% for(let [j, projectItem] of item.entries()){ %>-->
  <!--                <li class="project-item">-->
  <!--                    <a onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1', '?')">-->
  <!--                        <img class="normal-icon"-->
  <!--                             src="<%- locals.static %>/images/oiq/landingPage/project/<%- i + 1 %>-<%- j + 1 %>.png"-->
  <!--                             alt="">-->
  <!--                        <img class="active-icon"-->
  <!--                             src="<%- locals.static %>/images/oiq/landingPage/project/<%- i + 1 %>-<%- j + 1 %>-active.png"-->
  <!--                             alt="">-->
  <!--                        <span><%- projectItem %></span>-->
  <!--                    </a>-->
  <!--                </li>-->
  <!--                <% } %>-->
  <!--                <% if (i === 5) { %>-->
  <!--                <li>-->
  <!--                    <a onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1', '?')" class="project-btn">开始询价</a>-->
  <!--                </li>-->
  <!--                <% } %>-->
  <!--            </ul>-->
  <!--            <% } %>-->
  <!--        </div>-->
  <!--    </div>-->
  <div class="new-order-floor">
    <div class="order-title">
      实时最新询价
      <div class="reload-list-btn" onclick="getOiqList()">
        <i class="reload-list-btn-icon"></i>
        最新测试方案
      </div>
    </div>
    <div class="order-list" id="order-list">
      <% for(let [i, item] of oiqList.entries()){ %>
      <div class="order-item">
        <div class="order-top-content">
          <span class="order-time"><%- item.stateDate %></span>
          <span class="top-title">来自 <span style="font-weight: bold"><%- item.city %></span> 的用户 <%- item.userName %> 确认测试方案：</span>
        </div>
        <div class="category-content">
          <span class="item-label">产品类目</span>
          <span class="item-detail"><%- item.otherLabel %><span style="font-weight: bold"><%- item.lastCategory %></span></span>
        </div>
        <div class="category-content">
          <span class="item-label">检测项目</span>
          <span class="item-detail"><%- item.itemNameList %></span>
        </div>
        <a class="order-btn"
           onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1', '?')">我也要询价</a>
      </div>
      <% } %>
    </div>
    <div class="el-loading-mask" style="display: none">
      <div class="el-loading-spinner">
        <svg viewBox="25 25 50 50" class="circular">
          <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
        </svg>
      </div>
    </div>
    <!--<div class="order-loading"><div class="loading-ring"></div></div>-->
  </div>
  <div class="comment-floor">
    <div class="comment-title">客户评论</div>
    <div class="swiper-container">
      <div class="swiper-wrapper">
        <div class="swiper-slide">
          <div class="comment-content">
            <img class="step-img" src="<%- locals.static %>/images/oiq/landingPage/comment-step1.png"
                 alt="">
            <img class="avatar-img" src="<%- locals.static %>/images/oiq/landingPage/comment-icon1.png"
                 alt="">
            <div class="user-content">
              <span class="user-name">朱女士</span>
              <img class="location-icon"
                   src="<%- locals.static %>/images/oiq/landingPage/location-icon.png">
              <span class="location-name">上海市 </span>
              <div class="company-name">日立（中国）研究开发有限公司 制造工艺部 副主任研究员</div>
            </div>
            <div class="comment-line"></div>
            <div class="comment-detail">
              <img class="comment-icon" src="<%- locals.static %>/images/oiq/landingPage/comment-icon.png"
                   alt="">
              SGS开通了在线询价，网上自助下单。测试标准，样品要求等信息均清晰明了的标注在旁，界面设计友好，还跟着提示，非常容易上手，也省去邮件电话来回确认的烦恼，大大提高了我的工作效率，很棒！
            </div>
          </div>
        </div>
        <div class="swiper-slide">
          <div class="comment-content">
            <img class="step-img" src="<%- locals.static %>/images/oiq/landingPage/comment-step2.png"
                 alt="">
            <img class="avatar-img" src="<%- locals.static %>/images/oiq/landingPage/comment-icon2.png"
                 alt="">
            <div class="user-content">
              <span class="user-name">王先生</span>
              <img class="location-icon"
                   src="<%- locals.static %>/images/oiq/landingPage/location-icon.png">
              <span class="location-name">上海市 </span>
              <div class="company-name">上海齐夫电子有限公司 <br>市场部经理</div>
            </div>
            <div class="comment-line"></div>
            <div class="comment-detail">
              <img class="comment-icon" src="<%- locals.static %>/images/oiq/landingPage/comment-icon.png"
                   alt="">
              最近听客服说做检测可以全程在线搞定，就试了一下，觉得的确很方便。从报价到申请检测，流程简单清晰，遇到问题可随时点页面的在线咨询，帮我即时解决，体验非常好，很方便省心，推荐大家使用。
            </div>
          </div>
        </div>
        <div class="swiper-slide">
          <div class="comment-content">
            <img class="step-img" src="<%- locals.static %>/images/oiq/landingPage/comment-step3.png"
                 alt="">
            <img class="avatar-img" src="<%- locals.static %>/images/oiq/landingPage/comment-icon3.png"
                 alt="">
            <div class="user-content">
              <span class="user-name">张先生</span>
              <img class="location-icon"
                   src="<%- locals.static %>/images/oiq/landingPage/location-icon.png">
              <span class="location-name">上海市 </span>
              <div class="company-name">伊顿上飞航空管路制造有限公司 <br>实验室技术工程师</div>
            </div>
            <div class="comment-line"></div>
            <div class="comment-detail">
              <img class="comment-icon" src="<%- locals.static %>/images/oiq/landingPage/comment-icon.png"
                   alt="">
              现在到SGS在线询价，只需要1小时左右就能收到报价了，非常快。
              只需要选产品，注明测试标准，我的需求，工程师就可以根据标准制作详细的报价单，一目了然，很方便，以后询价我都会用这个在线工具。
            </div>
          </div>
        </div>
      </div>
      <div class="swiper-bottom">
        <div class="swiper-pagination"></div>
      </div>
    </div>
    <div class="swiper-button-prev"></div>
    <div class="swiper-button-next"></div>
  </div>
  <div class="quick-btn-floor" id="quick-btn">
    <a class="quick-btn" onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1', '?')">开始询价<i
        class="quick-btn-icon"></i></a>
  </div>
  <!--  <a onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1', '?')" class="oiq-loading-ads">-->
  <!--    <img src="<%- locals.static %>/images/oiq/landingPage/ads.png" alt="现时8折">-->
  <!--  </a>-->
</div>
<script src="<%- locals.static %>/js/swiper.min.js"></script>
<script>
  var speed = 30;
  var delay = 1000;
  var height = 22;
  var time;
  // 大数据数字动画
  var advantageFirst = true

  function advantageAnimate() {
    advantageFirst = false
    var box1 = $("#bigData .data-detail em").eq(0);
    console.log(box1);
    var min1 = 0;
    var max1 = box1.data('max');
    var box2 = $("#bigData .data-detail em").eq(1);
    var min2 = 0;
    var max2 = box2.data('max');
    var box3 = $("#bigData .data-detail em").eq(2);
    var min3 = 0;
    var max3 = box3.data('max');
    var box4 = $("#bigData .data-detail em").eq(3);
    var min4 = 0;
    var max4 = box4.data('max');
    setInterval(function () {
      if (min1 < max1) {
        min1 = min1 + 400
        box1.html(min1)
      }
    }, 10)

    setInterval(function () {
      if (min2 < max2) {
        min2 = min2 + 0.5
        box2.html(min2)
      }
    }, 10)
    setInterval(function () {
      if (min3 < max3) {
        min3 = min3 + 10
        box3.html(min3)
      }
    }, 10)
    setInterval(function () {
      if (min4 < max4) {
        min4 = min4 + 160
        box4.html(min4)
      }
    }, 10)
  }

  function getOiqList() {
    $('.new-order-floor .el-loading-mask').css('display', 'block')
    $.ajax({
      type: 'POST',
      url: '/getOiqList',
      data: {},
      headers: {
        frontUrl: window.location.href
      },
      success: function (res) {
        $('.new-order-floor .el-loading-mask').css('display', 'none')
        if (res && res.length) {
          var scrollHtml = '';
          var listHtml = '';
          res.forEach(function (v) {
            scrollHtml += '<li>来自 <span style="font-weight: bold">' + v.city + '</span> 的用户 ' + v.userName + ' 获得 <span style="font-weight: bold">' + v.lastCategory + '</span> 测试方案与报价</li>'
            listHtml += '<div class="order-item"><div class="order-top-content"><span class="order-time">' + v.stateDate + '</span><span class="top-title">来自 <span style="font-weight: bold">' + v.city + '</span> 的用户 ' + v.userName + ' 确认测试方案：</span></div><div class="category-content"><span class="item-label">产品类目</span><span class="item-detail">' + v.otherLabel + '<span style="font-weight: bold">' + v.lastCategory + '</span></span></div><div class="category-content"><span class="item-label">检测项目</span><span class="item-detail">' + v.itemNameList + '</span></div><a class="order-btn" onclick="jumpOIQAddtionSource("<%- memberUrl %>/questionnaire/step1", "?")">我也要询价</a></div>'
          })
          $('#data-scroll-list').html(scrollHtml)
          $('#order-list').html(listHtml)
        }
      },
      fail: function (data) {
        $('.new-order-floor .el-loading-mask').css('display', 'none')
      },
      complete: function (complete) {
      }
    })
  }

  $(function () {
    $('#video-img').click(function (e) {
      $(e.target).css('display', 'none')
      $('.video-content video').css('display', 'block')
      $('.video-content video')[0].play()
    })
    var area = document.getElementById("data-scroll-list");
    area.innerHTML += area.innerHTML;

    function scroll() {
      if (area.scrollTop % height == 0) {
        clearInterval(time);
        setTimeout(start, delay);
      } else {
        area.scrollTop++;
        if (area.scrollTop >= area.scrollHeight / 2) {
          area.scrollTop = 0;
        }
      }
    }

    function start() {
      time = setInterval(scroll, speed);
      area.scrollTop++;
    }

    setTimeout(start, delay);
    advantageAnimate()
    var swiper = new Swiper('.swiper-container', {
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
      },
      pagination: {
        el: '.swiper-pagination',
        clickable: true,
      },
      speed: 1000,
      loop: true,
      autoplay: {
        delay: 5000,
        disableOnInteraction: false,
      },
      // effect: 'fade',
      fadeEffect: {
        crossFade: true,
      }
    });

    var scontainer = $('.swiper-container')[0];
    scontainer.onmouseenter = function () {
      swiper.autoplay.stop();
    };
    scontainer.onmouseleave = function () {
      swiper.autoplay.start();
    };

    var productSwiper = new Swiper(".product-swiper", {
      slidesPerView: 5,
      spaceBetween: 10.2,
      pagination: {
        el: ".swiper-pagination1",
        clickable: true,
      },
      speed: 1000,
      // loop: true,
      autoplay: {
        delay: 5000,
        disableOnInteraction: false,
      },
      // effect: 'fade',
      fadeEffect: {
        crossFade: true,
      }
    });
    var scontainer1 = $('.product-swiper')[0];
    scontainer1.onmouseenter = function () {
      productSwiper.autoplay.stop();
    };
    scontainer1.onmouseleave = function () {
      productSwiper.autoplay.start();
    };
    window.addEventListener('scroll', function () {
      var scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
      if ($('#helpInfo').offset().top - $(window).height() < scrollTop) {
        $('#quick-btn').addClass('not-fixed')
      } else if ($('#quick-btn').hasClass('not-fixed')) {
        $('#quick-btn').removeClass('not-fixed')
      }
    })
    var scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
    if ($('#helpInfo').offset().top - $(window).height() < scrollTop) {
      $('#quick-btn').addClass('not-fixed')
    } else if ($('#quick-btn').hasClass('not-fixed')) {
      $('#quick-btn').removeClass('not-fixed')
    }

    // 方案楼层按钮事件
    $("#plan-button").on('click', function () {
      $(this).toggleClass('active');
      $('#plan-content').toggle();
    });
  })
</script>
<% include ../footer.html %>

