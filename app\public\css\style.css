/* html,
body {
    margin: 0;
    padding: 0;
}

body {
    min-width: 1226px;
    background: #fff;
}

a {
    text-decoration: none;
}

* {
    font-family: <PERSON><PERSON>, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
} */

/* .clearfix:before,
.clearfix:after {
    content: " ";
    display: table;
}

.clearfix:after {
    clear: both;
} */

.buy-icon {
  position: absolute;
  display: block;
  width: 82px;
  height: 34px;
  overflow: hidden;
  background: url(../images/buyicon.png) no-repeat 0 0;
  z-index: 2;
}

/* 新版 nav */
.navList > ul {
  float: left;
  width: auto;
  height: 100%;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0;
  margin-left: 55px;
}

#tradeNav,
#serviceNav {
  display: none;
}

.allNav * {
  padding: 0;
  margin: 0;
}

#tradeHome .allNav {
  left: -361px;
}

#serviceHome .allNav {
  left: -241px;
}

.allNav {
  height: 100px;
  width: 1226px;
  position: absolute;
  top: 100px;
  left: 0px;
  z-index: 9;
  border-top: 4px solid #ca4300;
  min-height: 468px;
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.1);
}

.allNav .side li.active a.cursorDefault {
  cursor: default;
}

.allNav li {
  list-style: none;
}

.allNav .side {
  float: left;
  width: 230px;
  background: rgba(75, 75, 75, 1);
  height: 100%;
}

.allNav .side li {
  height: 36px;
  line-height: 36px;
  text-align: left;
}

.allNav .side li:hover,
.allNav .side li.active {
  background: #fff;
}

.myLocation_box {
  position: relative;
}

.icon_box {
  position: absolute;
  top: 77px;
  left: calc(50% - 680px);
}

.icon_box .icon {
  width: 46px;
  height: 46px;
  background: url("./../../public/images/zan_a.png");
  border-radius: 50%;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.icon_box .icon:hover {
  background: url("./../../public/images/zan_b.png");
}

.icon_box .iconTrue {
  width: 46px;
  height: 46px;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: url("./../../public/images/zan_b.png");
}

.icon_box .icon_rate {
  width: 46px;
  height: 46px;
  background: url("./../../public/images/cang_a.png");
  border-radius: 50%;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.icon_box .icon_rate:hover {
  background: url("./../../public/images/cang_b.png");
}

.allNav .side li:hover a,
.allNav .side li.active a {
  color: #ca4300;
  cursor: pointer;
  font-weight: bold;
}

.allNav .side a {
  color: #fff;
  font-size: 15px;
  padding-left: 30px;
  display: inline-block;
  height: 24px;
  line-height: 24px;
}

.allNav .main {
  background: #fff;
  height: 468px;
  float: left;
  width: 966px;
  padding: 0 30px 0 0;
  overflow: hidden;
  position: relative;
}

.allNav .main .list {
  display: none;
  width: 996px;
  height: 468px;
  overflow-y: auto;
}

.main-list {
  min-height: 277px;
}

.main-list2 {
  min-height: 275px;
}

.allNav .main .list.active {
  display: block;
}

.allNav .main .list dl {
  border-bottom: 1px dashed #999;
  padding-bottom: 15px;
  margin: 0 30px;
}

.allNav .main .list dl:last-child {
  border-bottom: 0;
  padding-bottom: 0;
}

.allNav .main .list dt {
  text-align: left;
  padding: 15px 0 0 0;
  height: 20px;
  line-height: 20px;
  clear: both;
}

.allNav .main .list dt span {
  background: #4b4b4b;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  display: block;
  margin-top: 8px;
  float: left;
  color: #fff;
}

.allNav .main .list dt a,
.allNav .main .list dt em {
  color: #4b4b4b;
  font-size: 14px;
  display: block;
  float: left;
  padding-left: 5px;
  font-style: normal;
}

.allNav .main .list dt em.cursorDefault {
  cursor: default;
}

.allNav .main .list dd {
  float: left;
  height: 20px;
  line-height: 20px;
  padding-top: 10px;
  text-align: left;
  margin: 0;
  margin-right: 20px;
  padding-left: 10px;
}

.allNav .main .list dd a {
  color: #999;
  font-size: 14px;
}

.allNav .main .list a:hover {
  color: #ca4300;
}

.allNav .main .list dd i {
  width: 20px;
  height: 14px;
  display: inline-block;
  background: url(../images/nav-hot.png) no-repeat 0 0;
}

.allNav .main .second {
  float: left;
  width: 233px;
  background: #f5f5f5;
  height: 468px;
  position: absolute;
  top: 0;
  left: 0;
}

.allNav .main .second li {
  height: 36px;
  line-height: 36px;
  text-align: left;
}

.allNav .main .second li.active,
.allNav .main .second li:hover {
  background: #fff;
}

.allNav .main .second li.active a,
.allNav .main .second li:hover a {
  border-left: 4px solid #ca4300;
}

.allNav .main .second li a {
  color: #4b4b4b;
  font-size: 15px;
  display: inline-block;
  height: 16px;
  line-height: 16px;
  border-left: 4px solid #f5f5f5;
  padding-left: 30px;
}

.allNav .main .secondList {
  width: 690px;
  float: right;
  height: 468px;
  padding: 0 30px;
}

.allNav .main .secondList li {
  display: none;
  width: 100%;
  clear: both;
  padding-top: 10px;
}

.allNav .main .secondList li.active {
  display: block;
}

.allNav .main .secondList p {
  text-align: left;
  color: #4b4b4b;
  font-size: 15px;
  clear: both;
  height: 24px;
  line-height: 24px;
  padding-top: 10px;
}

.allNav .main .secondList .main-list2 p {
  cursor: default;
}

.allNav .main .secondList p:first-child {
  padding-top: 0;
}

.allNav .main .secondList span {
  height: 25px;
  line-height: 25px;
  padding-top: 5px;
  text-align: left;
  margin: 0;
  padding-right: 30px;
  float: left;
}

.allNav .main .secondList i {
  width: 20px;
  height: 14px;
  display: inline-block;
  background: url(../images/nav-hot.png) no-repeat 0 0;
}

.allNav .main .secondList li a {
  color: #999;
  font-size: 14px;
}

.allNav .ads {
  margin: 0 30px;
}

.allNav .secondList .ads {
  margin: 0;
}

.allNav .secondList .ads.ads-mt {
  margin-top: -81px;
}

.allNav .main .secondList .ads li {
  display: block;
  clear: none;
  width: 321px;
}

.allNav .main .secondList .ads li img {
  width: 146px;
  height: 72px;
  margin-right: 10px;
}

.allNav .main .secondList .ads li .info {
  width: 165px;
  height: 72px;
}

.allNav .main .secondList .ads li h3 {
  width: 165px;
}

.allNav .main .secondList .ads li p {
  color: #999;
  width: 165px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 13px;
  height: 14px;
  line-height: 14px;
  text-align: left;
  margin-top: 3px;
  padding: 0;
}

.allNav .main .secondList .ads li em,
.allNav .main .secondList .ads li button {
  color: #fff;
  width: 72px;
  height: 24px;
  line-height: 24px;
  font-size: 13px;
  margin-top: 12px;
}

.allNav .moreService {
  text-align: right;
  border-bottom: 1px dashed #999;
  padding: 10px 0;
  font-size: 14px;
  height: 22px;
  line-height: 22px;
  margin: 9px 0;
}

.allNav .moreService i {
  display: inline-block;
  width: 15px;
  height: 10px;
  background: url(../images/nav-more.png) no-repeat 0 0;
}

.allNav .secondList .moreService i {
  display: inline-block;
  width: 15px;
  height: 10px;
  background: url(../images/nav-more.png) no-repeat 0 0;
}

.allNav .moreService a {
  color: #ca4300;
}

.allNav .main .secondList li a.moreServiceA {
  color: #ca4300;
}

.allNav .ads ul {
  padding-bottom: 20px;
}

.allNav .ads li {
  float: left;
  margin-top: 20px;
}

.allNav .ads li:nth-child(even) {
  float: right;
}

.allNav .ads img {
  width: 180px;
  height: 90px;
  float: left;
  margin-right: 10px;
}

.allNav .ads .mini img {
  width: 146px;
  height: 72px;
  margin-right: 10px;
}

.allNav .ads .info {
  float: right;
  width: 240px;
  height: 90px;
}

.allNav .ads .mini .info {
  height: 72px;
}

.allNav .ads h3 {
  width: 240px;
  font-size: 16px;
  color: #252525;
  font-weight: normal;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 18px;
  line-height: 18px;
  text-align: left;
}

.allNav .ads h3:hover {
  color: #ca4300;
}

.allNav .ads p {
  color: #999;
  width: 240px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 13px;
  height: 14px;
  line-height: 14px;
  text-align: left;
  margin-top: 3px;
}

.allNav .ads li button,
.allNav .ads li em {
  font-size: 15px;
  color: #fff;
  border: 0;
  outline: 0;
  background: #ca4300;
  cursor: pointer;
  display: block;
  width: 90px;
  height: 34px;
  line-height: 34px;
  margin-top: 21px;
  float: left;
  font-style: normal;
}

.allNav .ads .mini li button,
.allNav .ads .mini li em {
  width: 72px;
  height: 24px;
  line-height: 24px;
  font-size: 13px;
  margin-top: 13px;
  display: block;
  font-style: normal;
}

.allNav .ads li em:hover,
.allNav .ads .mini li em:hover {
  color: #fff;
  background: #fe7427;
}

/* .navSearchBox {
height: 46px;
float: right;
margin-top: 27px;
position: relative;
width: 400px;
}

.navSearchBox .hotKeyword {
position: absolute;
right: 50px;
top: 13px;
}

.navSearchBox .hotKeyword span {
background: #ddd;
color: #fff;
padding: 0 5px;
font-size: 12px;
cursor: pointer;
display: inline-block;
margin-right: 3px;
height: 20px;
line-height: 20px;
}

.navSearchBox .hotKeyword span:hover {
background: #ca4300;
}

.navSearchBox .linkageWord {
position: absolute;
top: 45px;
left: 0;
border: 1px solid #ca4300;
width: 398px;
z-index: 2;
background: #fff;
display: none;
}

.navSearchBox .linkageWord ul {
padding: 5px 0;
margin: 0;
}

.navSearchBox .linkageWord li {
height: 35px;
line-height: 35px;
overflow: hidden;
list-style: none;
padding-left: 15px;
font-size: 14px;
color: #666;
cursor: pointer;
text-overflow: ellipsis;
white-space: nowrap;
width: 370px;
}

.navSearchBox .linkageWord li:hover {
background: #f5f5f5;
}

.navSearchBox form {
width: 100%;
height: 100%;
}

.navSearch {
width: 342px;
height: 40px;
border: none;
outline: none;
border: 1px solid #ca4300;
float: right;
padding: 2px 0 2px 10px;
font-size: 16px;
line-height: 40px;
}

.navSearchBox button {
width: 46px;
height: 46px;
background: #ca4300;
float: right;
position: relative;
cursor: pointer;
overflow: hidden;
border: 0;
} */

.navSearchIconIcon {
  width: 20px;
  height: 20px;
  background: url(../images/search.png) no-repeat 50% 50%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

/*banner css*/
.banner {
  width: 100%;
  height: 374px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  margin-bottom: 22px;
}

.bannerUl {
  width: 100%;
  height: 100%;
  position: relative;
}

.bannerLi1 {
  background: url(../images/banner.jpg) no-repeat 50% 50%;
  /* background-size: cover; */
  height: 100%;
}

.bannerLi2 {
  background: url(../images/banner3.jpg) no-repeat 50% 50%;
  /* background-size: cover; */
  height: 100%;
}

.point {
  width: 46px;
  height: 10px;
  left: 50%;
  margin-left: -23px;
  bottom: 28px;
  position: absolute;
}

.point li {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: white;
  float: left;
  margin-right: 13px;
  cursor: pointer;
  -ms-behavior: url(../PIE/ie-css3.htc);
}

.point li.active {
  background: #ca4300;
}

.bannerTitle {
  width: 100%;
  text-align: center;
  position: absolute;
  top: 99px;
  font-size: 35px;
  color: rgb(255, 254, 254);
}

.bannerDetail {
  width: 100%;
  text-align: center;
  font-size: 18px;
  color: rgb(255, 254, 254);
  position: absolute;
  top: 170px;
}

.bannerMore {
  width: 160px;
  height: 46px;
  position: absolute;
  bottom: 120px;
  left: 50%;
  margin-left: -80px;
  line-height: 46px;
  text-align: center;
  color: rgb(255, 255, 255);
  background: rgba(0, 0, 0, 0.5);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#7f000000, endColorstr=#7f000000);
}

.bannerMore:hover .bannermoreBox {
  width: 160px;
  transition: 0.4s;
}

.bannermoreword {
  width: 160px;
  height: 100%;
  line-height: 46px;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  cursor: pointer;
}

.bannerLeft {
  width: 36px;
  height: 54px;
  top: 160px;
  left: 80px;
  position: absolute;
  background: url(../images/leftarrow.png) no-repeat 100% 100%;

  display: none;
}

.bannerRight {
  width: 36px;
  height: 54px;
  top: 160px;
  right: 80px;
  position: absolute;
  background: url(../images/rightarrow.png) no-repeat 100% 100%;
  display: none;
}

.banner:hover .bannerLeft {
  display: block;
}

.banner:hover .bannerRight {
  display: block;
}

.bannermoreword:hover .bannermoreBox {
  width: 180px;
  transition: 0.4s;
}

/*location css*/
.location {
  width: 1226px;
  height: 20px;
  margin: 0 auto;
}

.location ul {
  margin: 0;
  padding: 0;
}

.locationLi {
  float: left;
  margin-right: 20px;
  line-height: 17.78px;
  font-size: 14px;
  list-style: none;
  color: #ababab;
}

.locationLi a {
  color: #ababab;
}

.locationLi a:hover {
  color: #e1e1e1;
}

.locationLi.icon {
  width: 13px;
  height: 16px;
  background: url(../images/arrow.png) no-repeat;
}

.titleBox {
  width: 1226px;
  margin: auto;
  border-bottom: 2px solid #e9e9e9;
  font-family: "Microsoft YaHei";
}

.titleBox .mainTitle {
  color: #333;
  position: relative;
}

.msg-icon {
  display: inline-block;
  width: 36px;
  height: 36px;
  background: url(../images/lt.png) no-repeat 100% 100%;
  background-size: contain;
  position: absolute;
  top: 9px;
  right: 56px;
  cursor: pointer;
}

.msg-icon:hover {
  background: url(../images/lt-active.png) no-repeat 100% 100%;
}

.sc-icon {
  display: inline-block;
  width: 36px;
  height: 36px;
  background: url(../images/sc.png) no-repeat 100% 100%;
  background-size: contain;
  position: absolute;
  top: 9px;
  right: 0px;
  cursor: pointer;
}

.sc-icon:hover {
  background: url(../images/sc-active.png) no-repeat 100% 100%;
}

.titleBox .subTitle {
  font-size: 16px;
  color: #878787;
  margin: 25px 0;
  line-height: 1.5;
}

.linkBox {
  width: 100%;
  /* margin: 10px 0; */
  padding: 0;
  /* height: 20px; */
}

.link-item {
  float: left;
  margin-right: 20px;
  font-size: 15px;
  color: #ca4300;
  position: relative;
}

.link-item::after {
  position: absolute;
  content: "";
  top: -12px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #ca4300;
  transform: scaleX(0);
  transition: 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.link-item:hover::after {
  transform: scaleX(1);
}

.link-item a {
  color: #ca4300;
}

.dragLi-icon {
  width: 13px;
  height: 16px;
  background: url(../images/arrow.png) no-repeat;
  float: left;
  margin-top: 12px;
  margin-right: 4px;
}

.dragLi-word {
  width: 100%;
  float: left;
  text-align: center;
}

/*.dragLi-word:nth-child(n){*/
/*border-right: 2px solid #eeeeee;*/
/*behavior: url(../PIE/PIE.htc);*/
/*}*/
.locationLi:nth-child(4) {
  width: 10px;
  height: 15px;
  background: url(../images/arrow.png) no-repeat;
  /*background-size: contain;*/
}

/*introduce css*/
.introduce {
  width: 1226px;
  min-height: 400px;
  margin: 42px auto 30px;
}

.introduceTitle {
  font-size: 32px;
  color: rgb(51, 51, 51);
  margin-bottom: 35px;
}

.introduceLeft {
  width: 670px;
  /*float: left;*/
  display: inline-block;
}

.introduceLeft-word1 {
  display: block;
  font-size: 16px;
  line-height: 1.69;
  color: rgb(102, 102, 102);
  margin-bottom: 20px;
}

.introduceLeft-word2 {
  display: block;
  font-size: 16px;
  line-height: 1.69;
  color: rgb(102, 102, 102);
  margin-bottom: 33px;
}

.introduceLeft-question {
  width: 222px;
  height: 46px;
  text-align: center;
  line-height: 45px;
  color: #ca4300;
  cursor: pointer;
  border: 1px solid #ca4300;
  margin-top: 20px;
}

.introduceRight {
  float: right;
  width: 457px;
  height: 277px;

  background: url(../images/9_03.png) no-repeat 100% 100%;
  background-size: 100%;
  overflow: hidden;
}

/*server css*/
.server {
  width: 100%;
  height: auto;
  margin: 0px auto 0;
  background: rgb(250, 250, 250);
  overflow: hidden;
}

.serverBox {
  width: 1226px;
  margin: 0 auto;
  position: relative;
}

.serverHead {
  width: 100%;
  height: 74px;
  border-bottom: 3px solid rgb(242, 242, 242);
  position: relative;
  margin-bottom: 20px;
}

.serverHead ul {
  margin: 0;
  padding: 0;
}

/* .serverLi {
  width: auto;
  height: 74px;
  padding: 0 18px;
  text-align: center;
  line-height: 80px;
  font-size: 16px;
  color: rgb(51, 51, 51);
  float: left;
  cursor: pointer;
  list-style: none;
  min-width: 100px;
  text-align: center;
  border-bottom: 3px solid rgb(255, 255, 255, 0);
  transition: border-bottom-color 0.4s;
}

.serverLi span {
  position: absolute;
  right: 0;
  display: none;
}


.serverLi a {
  color: rgb(51, 51, 51);
}

.serverLiActive {
  border-bottom: 3px solid #ca4300;
  font-style: normal;
  font-stretch: normal;
  letter-spacing: normal;
  color: #ca4300;
  font-weight: bold;
}

.serverLiActive a {
  color: #ca4300;
} */

.bor {
  width: 120px;
  height: 3px;
  background: #ca4300;
  position: absolute;
  bottom: 0;
  left: 0;
}

.serverIcon {
  font-size: 0;
  line-height: 0;
  border-width: 5px;
  border-color: #ca4300;
  border-right-width: 0;
  border-style: dashed;
  border-left-style: solid;
  border-top-color: transparent;
  border-bottom-color: transparent;
  position: absolute;
  right: 0;
  top: 36px;
}

.allServer {
  position: absolute;
  right: 12px;

  top: 32px;

  line-height: 16px;
  cursor: pointer;
}

.allServer a {
  color: #ca4300;
}

.allServer:hover {
  font-weight: 900;
}

.serverConLi:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  -webkit-transform: translate3d(0, -3px, 0);
  transform: translate3d(0, -3px, 0);
  transition: 0.4s;
}

.serverConLi:hover .serverConLiWord1 {
  color: #ca4300;
}

.serverConLi:hover .serverConLiWord2 {
  color: #000000;
}

.serverCon {
  width: 100%;
  height: 519px;
}

.serverCon ul {
  width: 100%;
  height: 100%;
  display: none;
  margin: 0;
  padding: 0;
}

.serverCon ul.show {
  display: block;
}

.serverCon ul:first-child {
  width: 100%;
  height: auto;
  z-index: 10;
}

.serverConLi {
  float: left;
  width: 284px;
  height: 234px;
  margin-right: 30px;
  margin-bottom: 25px;
  float: left;
  position: relative;
  cursor: pointer;
  list-style: none;
  /*background: #cccccc;*/
}

.serverConLi:nth-child(4n) {
  margin-right: 0;
}

.serverConLi img {
  width: 284px;
  height: 142px;
  display: block;
  /*background: #ccc;*/
}

.serverConLiWorBac {
  width: 100%;
  height: 92px;
  background: white;
  display: block;
  float: left;
}

.serverConLiWord1 {
  width: 90%;
  height: auto;
  text-align: center;
  display: block;
  margin: 20px auto 0;
  font-size: 17px;
  line-height: 1.47;
  color: #000000;
  letter-spacing: normal;
}

.serverConLiWord2 {
  height: auto;
  text-align: center;
  display: block;
  margin-top: 4px;
  font-size: 13px;
  color: #999999;
  letter-spacing: normal;
  padding: 0 15px;
}

.pages {
  width: 100%;
  height: 18px;
  position: absolute;
  bottom: -40px;
}

.pages ul {
  width: 92px;
  height: 100%;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  margin: 0;
  padding: 0;
}

.pagesLi {
  width: 23px;
  height: 18px;
  border-radius: 9px;
  text-align: center;
  float: left;
  /*background-color: #ca4300;*/
  margin-right: 7px;
  color: #cccccc;
  font-size: 12px;
  line-height: 18px;
  cursor: pointer;
  list-style: none;
}

.pagesLiActive {
  background-color: #ca4300;
  color: white;
}

/*solution css*/
/* .serverLi.solu {
  border-bottom: 3px solid #ca4300;
  font-style: normal;
  font-stretch: normal;
  letter-spacing: normal;
  color: #333333;
  font-weight: bold;
}

.serverLi.demo {
  border-bottom: 3px solid #ca4300;
  font-style: normal;
  font-stretch: normal;
  letter-spacing: normal;
  color: #333333;
  font-weight: bold;
}

.serverLi.reletive {
  border-bottom: 3px solid #ca4300;
  font-style: normal;
  font-stretch: normal;
  letter-spacing: normal;
  color: #333333;
  font-weight: bold;
} */

.solutionConLi-word1:hover {
  color: #ca4300;
}

.cooperationDemo1 li:hover a {
  color: #ca4300;
}

.cooperationDemo2 li:hover a {
  color: #ca4300;
}

.resourceConLi-word:hover a {
  color: #ca4300;
}

.dragLi:hover {
  color: #ca4300;
}

.dragLi:hover .dragLi-icon {
  background: url(../images/arrow.png) no-repeat -15px 100%;
}

.bannermoreBox {
  width: 0;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: #ca4300;
  overflow: hidden;
}

.solutionCon {
  height: auto;
  overflow: hidden;
  padding-bottom: 0;
}

.solutionCon ul {
  margin: 0;
  padding: 0;
}

.solutionConLi {
  width: 593px;
  height: 162px;
  float: left;
  position: relative;
  margin-bottom: 51px;
  margin-right: 38px;
  list-style: none;
}

/*.solutionConLi:nth-child(2n){*/
/*margin-right: 0;*/
/*}*/
.solutionConLi img {
  float: left;
  width: 237px;
  height: 162px;
  display: block;
  background: #ccc;
  cursor: pointer;
}

.solutionConLi .img {
  float: left;
  width: 237px;
  height: 162px;
  display: block;
  background: #ccc;
  cursor: pointer;
  background-size: cover;
}

.solutionConLi .cc {
  padding-left: 265px;
  text-align: left;
  padding-right: 20px;
}

.solutionConLi-word1 {
  /*font-family: MicrosoftYaHei;*/
  display: block;
  font-size: 18px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: 1.17;
  letter-spacing: normal;
  color: #333333;
  cursor: pointer;
  padding: 5px 0;
  margin-top: 10px;
}

.solutionConLi-word2 {
  /*font-family: MicrosoftYaHei;*/
  display: block;
  font-size: 14px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: 1.64;
  letter-spacing: normal;
  color: #999999;
  padding: 5px 0;
}

.solutionConLi .cc p {
  font-size: 14px;
  line-height: 22px;
  height: 65px;
  overflow: hidden;
  color: #999;
}

.solutionConLi:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  -webkit-transform: translate3d(0, -2px, 0);
  transform: translate3d(0, -2px, 0);
  transition: 0.4s;
}

.solution .serverBox .pages {
  bottom: 23px;
}

/*cooperation css*/
/* .cooperation {
  width: 100%;
  height: auto;
  background: #f8f8f8;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  padding-bottom: 20px;
}

.cooperationBox {
  width: 1226px;
  margin: 0 auto;
}

.cooperationDemo {
  margin-top: 25px;
}

.cooperationDemo ul {
  float: left;
  width: 50%;
  margin: 0;
  padding: 0;
}

.cooperationDemo ul.limitHeight {
  height: 126px;
  margin-bottom: 14px;
  overflow: hidden;
}

.cooperationDemo li {
  list-style: disc inside;
  font-size: 16px;
  margin-bottom: 14px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-left: 20px;

  cursor: pointer;
}

.cooperationDemo li a {
  color: #333333;
}

.cooperation .pages {
  bottom: 25px;
} */

/*resource css*/
/* .resources {
  width: 1226px;
  height: auto;
  margin: 0 auto;
  padding-bottom: 10px;
}

.resourceBox {
  width: 1226px;
  height: 100%;
  margin: 0 auto;
}

.resourceBox .serverHead {
  margin-bottom: 25px;
}

.resourceCon {
  width: 1226px;
  height: auto;
  overflow: hidden;
}

.resourceCon.limitHeight {
  height: 170px;
}

.resourceCon ul {
  padding: 0;
  margin: 0;
}

.resourceConLi {
  height: 20px;
  padding: 10px 0;
  float: left;
  list-style: none;
  width: 33.33%;
  text-align: left;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.resourceConLi img {
  width: 18px;
  height: 20px;
  float: left;
  margin-right: 12px;
  border: none;
}

.resourceConLi-word {
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  cursor: pointer;
  display: inline-block;
  width: 22em;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.resourceConLi-word a {
  color: #333333;
} */

/*footer css*/
/*
.footerBox{
  clear: both;
  overflow: hidden;
  width: 100%;height: 459.5px;
  background: #111111;
}
.footer{
  width: 1226px;
  height: 459.5px;
  background-color: #111111;
  margin: 0 auto;
}
.footer-top{
  width: 1200px;height: 376.5px;
  margin: 0 auto;
  position: relative;
}
.footer-top-title{
  position: absolute;
  top: 61px;
  left: -13.5px;
}
.footer-top-title ul{
  padding: 0;margin: 0;
}
.footer-top-title-li{
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  color: #ffffff;
  float: left;
  margin-right: 116px;
  list-style: none;
}
.footer-top-title-li:nth-child(4){
  margin-right: 288.5px;
}
*/
.help {
  position: absolute;
  top: 65px;
  left: 0;
}

.helpLi {
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  cursor: pointer;
  color: #aaaaaa;
  list-style: none;
  line-height: 1;
}

.helpLi a {
  color: #aaaaaa;
}

.ourServer {
  position: absolute;
  left: 183.5px;
  top: 65px;
}

.aboutUs {
  position: absolute;
  top: 65px;
  left: 383.5px;
}

.aboutUs .helpLi {
  /*font-family: FZLTHJW-GB1-0;*/
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  color: #aaaaaa;
}

.connect {
  position: absolute;
  left: 566.5px;
  top: 65px;
}

.footer-top-title-li:last-child {
  position: relative;
}

.weibo-img {
  width: 51px;
  height: 51px;
  border-radius: 50%;
  top: 44px;
  position: absolute;
  background: url(../images/weibo.png) 100% 100%;
  background-size: contain;
}

.weibo-word {
  width: 68px;
  position: absolute;
  top: 60px;
  left: 61px;
  /*font-family: FZLTHJW-GB1-0;*/
  font-size: 12px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  color: #999999;
}

.weixin-img {
  width: 51px;
  height: 51px;
  border-radius: 50%;
  top: 44px;
  position: absolute;
  left: 148.5px;
  background: url(../images/weixin.png) 100% 100%;
  background-size: contain;
}

.weixin-word {
  width: 68px;
  position: absolute;
  top: 60px;
  left: 208px;
  /*font-family: FZLTHJW-GB1-0;*/
  font-size: 12px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  color: #999999;
}

.footer-bottom {
  width: 1226px;
  height: 85px;
  text-align: center;
  line-height: 85px;
  /*font-family: DIN;*/
  font-size: 15px;
  font-weight: 300;
  font-style: normal;
  font-stretch: normal;

  color: #eaeaea;
}

.message {
  /*position: fixed;*/
  width: 40px;
  height: 40px;
  background-color: #ca4300;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.messageIcon {
  width: 20px;
  height: 18px;
  background: url(../images/1-1.jpg);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.note {
  /*position: fixed;*/
  width: 30px;
  height: auto;
  text-align: center;
  padding: 10px 5px;
  background-color: #ca4300;
  color: white;
}

/*.noteIcon{*/
/*width: 20px;height: 18px;*/
/*!*background: url(../images/1-2.jpg);*!*/
/*position: absolute;*/
/*top: 0;left: 0;right: 0;bottom: 0;margin: auto;*/
/*}*/
.box {
  width: auto;
  height: auto;
  right: 0;
  top: 67%;
  position: fixed;
  z-index: 10;
  cursor: pointer;
}

.erweima {
  width: 296.5px;
  height: 185px;
  border-radius: 2px;
  background-color: #2f2f2f;
  position: absolute;
  top: 106.5px;
  left: 2.5px;
}

.weiboer {
  width: 114px;
  height: 114px;
  position: absolute;
  top: 22px;
  left: 22px;
  background: url(../images/weiboerweima.png) 100% 100%;
  background-size: 100%;
}

.weixiner {
  width: 114px;
  height: 114px;
  position: absolute;
  right: 22.5px;
  top: 22px;
  background: url(../images/weixinerweima.png) 100% 100%;
  background-size: 100%;
}

.weiboerword {
  width: 95px;
  height: 23.5px;
  font-family: FZLTHJW-GB1-0;
  font-size: 10px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: 1.4;
  letter-spacing: 0.2px;
  text-align: center;
  color: #999999;
  position: absolute;
  right: 31.5px;
  bottom: 17.5px;
}

.weixinerword {
  width: 95px;
  height: 23.5px;
  font-family: FZLTHJW-GB1-0;
  font-size: 12px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: 1.4;
  letter-spacing: 0.2px;
  text-align: center;
  color: #999999;
  position: absolute;
  left: 37.5px;
  bottom: 17.5px;
}

.weixinerword1 {
  width: 122px;
  height: 23.5px;
  font-family: FZLTHJW-GB1-0;
  font-size: 12px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: 1.4;
  letter-spacing: 0.2px;
  text-align: center;
  color: #999999;
  position: absolute;
  left: 18px;
  bottom: -1.5px;
}

.weiboerword1 {
  width: 136px;
  height: 23.5px;
  font-family: FZLTHJW-GB1-0;
  font-size: 10px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: 1.4;
  letter-spacing: 0.2px;
  text-align: center;
  color: #999999;
  position: absolute;
  right: 12px;
  bottom: -1.5px;
}

.line {
  width: 100%;
  height: 1px;
  background: #ffffff;
  opacity: 0.1;
  position: absolute;
  bottom: 0;
}

.serverRight0 {
  margin-right: 0;
}

.footer-top-title-li-last {
  position: relative;
}

.footer-top-title-li-four {
  margin-right: 288.5px;
}

.introduceLeft-question {
  position: relative;
}

.introduceLeft-questionbox1 {
  width: 0;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.introduceLeft-question1 {
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  left: 0;
  top: 0;

  /*overflow: hidden;*/
}

.introduceLeft-question:hover .introduceLeft-questionbox1 {
  width: 223px;
  height: 100%;
  background: #ca4300;
  transition: 0.4s;
  display: block;
}

.introduceLeft-question:hover .introduceLeft-question1 {
  color: white;
}

.baa {
  position: absolute;
  z-index: 999;
  width: 100%;
  height: 374px;
  top: 0;
  left: 0;
}

.swiper-container {
  height: 374px;
}

.swiper-wrapper {
  width: 100%;
  height: 374px;
}

.introduceLeft-word {
  min-height: 207px;
  line-height: 1.7;
  color: #666;
}

.rtfc {
  line-height: 1.7;
  padding: 20px 0;
  font-size: 16px;
  padding-left: 33px;
  width: 825px;
}

.question-word2.rtfc {
  padding: 0;
}

.rtfc a {
  color: #f60;
  text-decoration: none;
}

.rtfc p {
  padding: 0;
  margin: 0;
  line-height: 1.7;
}

/*aboutus*/
.bgBanner {
  width: 100%;
  height: 300px;
  /*background: url(../images/bgbanner.png) no-repeat 100% 100%;*/
  position: relative;
  margin-bottom: 22px;
}

.bgBanner img {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.bgBannerW1 {
  width: auto;
  height: auto;
  font-size: 48px;
  color: white;
  margin-top: 78px;
}

.bgBannerW2 {
  width: auto;
  height: auto;
  font-size: 16px;
  color: white;
  margin-top: 10px;
}

.bgBannerW {
  width: 1226px;
  height: 100%;
  margin: 0 auto;
  overflow: hidden;
  position: absolute;
  left: 50%;
  margin-left: -613px;
}

.aboutBox {
  width: 1226px;
  height: auto;
  margin: 0 auto;
}

.aboutBox-word1 {
  width: 812px;
  margin-bottom: 48px;
  font-size: 18px;
  font-weight: bold;
  color: #000000;
  line-height: 1.6;
}

.aboutBox-word2 {
  margin-bottom: 45px;
  font-size: 14px;
  color: #000000;
  line-height: 1.6;
  width: 750px;
}

.aboutBox-word3 {
  margin-bottom: 45px;
  font-size: 14px;
  color: #000000;
  line-height: 1.6;
  width: 750px;
}

.location {
  margin-bottom: 45px;
}

.honners {
  width: 812px;
  height: auto;
}

.honnerT {
  width: 812px;
  margin-bottom: 30px;
  font-size: 18px;
  font-weight: bold;
  color: #000000;
  line-height: 1.6;
}

.honner1 {
  margin-bottom: 30px;
  position: relative;
}

.honner1-bg {
  width: 9px;
  height: 3px;
  background: #ca4300;
  display: inline-block;
  position: absolute;
  top: 9px;
  left: 10px;
}

.honner1-word {
  margin-left: 25px;
  font-size: 14px;
}

.photo {
  width: 345px;
  height: 345px;
  background: url(../images/guangtou.png);
  float: right;
}

/*classlist*/
.class_info {
  width: 100%;
  height: 170px;
  background: #f8f8f8;
  position: relative;
}

.categoryUl {
  width: 1226px;
  height: 42px;
  margin: 0 auto;
  padding: 0;
}

.categoryLi {
  line-height: 42px;
  list-style: none;
}

.keySearch {
  top: 6px;
}

.navSearchIconIcon1 {
  width: 14px;
  height: 14px;
  background: url(../images/search14.png);
}

.detailNav {
  border-bottom: 1px solid #eeeeee;
  height: 100px;
  overflow: visible;
}

.detailBox {
  width: 1226px;
  height: auto;
  margin: 0 auto 52px;
  position: relative;
}

/* .detailbanner {
  width: 825px;
  height: 310px;
  background: #ccc;
  float: left;
  overflow: hidden;
}

.detailbanner img {
  display: block;
  width: 825px;
  height: 310px;
} */

/* .bannerInfo {
  width: 342px;
  height: auto;
  float: right;
} */

/* .detailbannerBox {
  width: 100%;
  height: 340px;
  margin-bottom: 10px;
} */

.word1 {
  font-size: 21px;
  color: #000000;
  margin-top: 17px;
}

.word2 {
  font-size: 14px;
  color: #999999;
  margin-top: 17px;
}

.word3 {
  font-size: 14px;
  color: #666666;
  /*margin-top: 21px;*/
  line-height: 1.7;
  margin-bottom: 31px;
  min-height: 89px;
}

.word3-icon {
  height: 18px;
  width: 3px;
  background: #bfbfbf;
  position: absolute;
  top: 32px;
  left: 0;
}

.word5-1 {
  width: 0;
  height: 100%;
  position: absolute;
  background: #ca4300;
  left: 0;
  top: 0;
}

.word5-2 {
  width: 100%;
  height: 100%;
  position: absolute;
  color: #fefefe;
  font-size: 18px;
  z-index: 2;
}

.word5:hover .word5-1 {
  width: 100%;
  transition: 0.4s;
}

.word5:hover .word5-2 {
  color: white;
}

.word4 {
  font-size: 14px;
  color: #666666;
  /*margin-top: 31px;*/
}

.word5 {
  /* width: 222px; */
  height: 46px;
  /*background-color: #ca4300;*/
  /*border: 1px solid #ca4300;*/
  text-align: center;
  line-height: 46px;
  margin-top: 16px;
  cursor: pointer;
  color: #fefefe;
  position: relative;
  background: #ca4300;
}

.word5::after {
  content: "";
  width: 0;
  height: 100%;
  background: #cc5500;
  position: absolute;
  top: 0;
  left: 0;
}

.word5:hover:after {
  width: 100%;
  transition: 0.4s;
}

.serverConBox {
  width: 858px;
  margin-bottom: 44px;
}

.serverTitleBox {
  width: 100%;
  height: 31px;
  /*border-bottom: 3px solid #f2f2f2;*/
  margin-bottom: 8px;
}

.serverCon {
  width: 100%;
  height: auto;
  font-size: 16px;
  font-weight: normal;
  color: #333333;
  /*overflow: hidden;*/
  overflow: visible;
}

.detailBox .serverCon {
  border-left: 7px solid #ca4300;
  padding-left: 26px;
  font-size: 24px;
  height: 22px;
  line-height: 24px;
  font-weight: bold;
}

.server-word1 {
  font-size: 14px;
  margin-top: 20px;
  line-height: 2;
}

.lei {
  margin-top: 12px;
  margin-bottom: 12px;
}

.leiUl {
}

.leiLi {
  margin-bottom: 8px;
  font-size: 14px;
  list-style: initial;
  margin-left: 16px;
}

.server-word2 {
  font-size: 14px;
  color: #000000;
  margin-bottom: 12px;
  line-height: 2;
}

.server-word3 {
  font-size: 14px;
  color: #000000;
  margin-bottom: 12px;
  line-height: 2;
}

.questionBox {
  width: 825x;
  border-radius: 4px;
  background-color: #f8f8f8;
  margin-top: 15px;
  position: relative;
  margin-left: 33px;
}

.question-icon {
  width: 20px;
  height: 18px;
  position: absolute;
  top: 64px;
  left: 31px;
  background: url(../images/da.png) no-repeat 100% 100%;
  background-size: contain;
}

.question-icon-wen {
  width: 20px;
  height: 15px;
  position: absolute;
  top: 16px;
  left: 31px;
  background: url(../images/wen.png) no-repeat 100% 100%;
  background-size: contain;
}

.question-word1 {
  width: 733px;
  margin-left: 31px;
  margin-right: 32px;
  line-height: 36px;
  border-bottom: 1px solid #e0e0e0;
  font-weight: bold;
  font-size: 16px;
  padding: 6px 0;
  padding-left: 29px;
}

.question-word2 {
  margin-left: 60px;
  margin-right: 32px;
  line-height: 78px;
  color: #333333;
  font-size: 14px;
  width: 733px;
}

.question-word2 p {
  margin: 12px 0;
}

.liucheng {
  width: auto;
  margin-top: 24px;
  height: 65px;
}

.liuchengUl {
  width: auto;
  height: auto;
}

.liuchengLi {
  width: 120px;
  height: 65px;
  border-radius: 4px;
  background-color: #f2f3f5;
  text-align: center;
  line-height: 65px;
  float: left;
  margin-right: 53px;
  color: #333333;
  list-style: none;
  /*cursor: pointer;*/
}

.liuchengLi:last-child {
  margin-right: 0;
}

.recoment {
  width: 1226px;
  height: 334px;
  border-radius: 4px;
  background-color: #f8f8f8;
  /*margin-left: 88px;*/
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 21;
}

.square {
  width: 4px;
  height: 21px;
  position: absolute;
  mix-blend-mode: multiply;
  background-color: #f2f2f2;
  left: 0;
  top: 18px;
}

.recoment-word1 {
  width: auto;
  font-size: 16px;
  color: #333333;
  font-weight: bold;
  position: absolute;
  top: 17px;
  /*left: 15px;*/
  color: #333333;
}

.recoment-word2 {
  font-size: 14px;
  color: #333333;
  position: absolute;
  right: 59px;
  top: 22px;
  cursor: pointer;
}

.recomentUl {
  width: auto;
  height: 200px;
  position: absolute;
  top: 63px;
  /*left: 20px;*/
  margin: 0;
  padding: 0;
}

.recomentLi {
  width: 284px;
  height: 234px;
  background-color: #ffffff;
  margin-right: 20px;
  float: left;
  list-style: none;
  position: relative;
}

.recomentLi:last-child {
  margin-right: 0 !important;
}

.recomentLi-img {
  width: 100%;
  height: 142px;
  margin-bottom: 20px;
  background: #ccc;
  cursor: pointer;
}

.recomentLi-img img {
  width: 100%;
  height: 100%;
}

.recomentLi-word {
  /*width: 100%;*/
  /*text-align: center;*/
  /*color: #000000;*/
  /*font-size: 16px;*/
  width: 90%;
  height: auto;
  text-align: center;
  display: block;
  margin: 20px auto 0;
  font-size: 17px;
  line-height: 1.47;
  color: #000000;
  letter-spacing: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recomentLi-word2 {
  height: auto;
  text-align: center;
  display: block;
  margin-top: 4px;
  font-size: 13px;
  color: #999999;
  letter-spacing: normal;
  padding: 0 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recomentLi:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  -webkit-transform: translate3d(0, -2px, 0);
  transform: translate3d(0, -2px, 0);
  transition: 0.4s;
}

.recoment-icon {
  width: 16px;
  height: 16px;
  position: absolute;
  right: 30px;
  top: 24px;
  background: url(../images/huanyipi.jpg) no-repeat 100% 100%;
  background-size: contain;
}

.detailLoca {
  width: 100%;
  margin: 0 auto;
  padding: 20px 0 0;
  /*border-top: 1px solid #e9e9e9;*/
}

.detailLoca ul {
  width: 1226px;
  height: 100%;
  margin: 0 auto;
}

.ngsFix {
  position: fixed;
  top: 100px;
  z-index: 20;
}

.ngsAbsolute {
  position: absolute;
  bottom: 0;
}

.word-day {
  width: 100%;
  height: auto;
  margin-top: 10px;
  overflow: hidden;
}

.word-day-word {
  line-height: 16px;
  float: left;
  color: #999999;
  font-size: 14px;
  width: 100%;
  overflow: hidden;
  padding: 2px 0;
}

.word-day-icon {
  width: 12px;
  height: 15px;
  margin-right: 4px;
  float: left;
  background: url(../images/day.png) no-repeat;
  background-size: contain;
}

.liuchengLilast {
  margin-right: 0;
}

.serverRight0 {
  margin-right: 0;
}

/*detail1*/
.word-time {
  width: 100%;
  height: 20px;
  margin-top: 27px;
}

.word-time-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  float: left;
  background: url(../images/time-loc.png);
}

.word-time-word {
  line-height: 16px;
  float: left;
  color: #999999;
}

.word-loca {
  width: 100%;
  height: 20px;
  margin-top: 10px;
}

.word-loca-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  float: left;
  background: url(../images/time-loc.png) 16px;
}

.word-loca-word {
  line-height: 16px;
  float: left;
  color: #999999;
}

.zixun-dinggou {
  width: 100%;
  height: 46px;

  margin-top: 20px;
}

.word3 {
  line-height: 1.7;
}

.zixun {
  width: 108px;
  height: 44px;
  border: 1px solid #ca4300;
  line-height: 43px;
  text-align: center;
  float: left;
  margin-right: 33px;
  color: #ca4300;
  cursor: pointer;
}

.zixun a {
  color: #ca4300;
}

.zixun:hover {
  color: white;
  background: #ca4300;
  transition: 0.4s;
}

.zixun:hover a {
  color: white;
}

.dinggou {
  width: 110px;
  height: 100%;
  line-height: 43px;
  text-align: center;
  float: left;
  background: #ca4300;
  color: white;
  cursor: pointer;
  position: relative;
}

.dinggou a {
  color: white;
  display: block;
  line-height: 46px;
}

.deepen {
  width: 0;
  height: 100%;
  background: #cc5500;
  position: absolute;
}

.dinggou:hover .deepen {
  width: 100%;
  transition: 0.4s;
  left: 0;
  top: 0;
}

.dinggouword {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

/*newList*/
.nlBox {
  width: 100%;
  height: auto;
  margin-bottom: 42px;
}

.nlSearch {
  width: 100%;
  height: 62px;
  background: #f8f8f8;
  margin-bottom: 15px;
}

.location {
  margin-bottom: 20px;
}

/*
.nlSearchBox {
  width: 1226px;
  height: 26px;
  margin: 0 auto;
  padding-top: 18px;
} */

.tSearch {
  width: auto;
  height: 100%;
  display: inline-block;
  margin-right: 27px;
}

.dSearch {
  width: auto;
  height: 100%;
  display: inline-block;
  position: relative;
}

/* .nlSearchBox input,
.nlSearchBox select {
  border: 1px solid #ca4300;
  outline: none;
  padding-left: 5px;
  height: 24px;
  line-height: 22px;
  padding-top: 0;
  padding-bottom: 0;
}

.nlSearchBox span {
  color: #999999;
  font-size: 14px;
} */

.tSearch input {
  width: 142px;
}

.dSearch input {
  width: 255px;
  text-align: center;
  margin-right: 16px;
}

.calendar {
  width: 27px;
  height: 19px;
  position: absolute;
  background: url(../images/calendar.png) no-repeat 100% 100%;

  left: 80px;
  top: 5px;
  cursor: pointer;
}

.calendar.is-case {
  left: 52px;
}

.dSearchIb {
  width: 38px;
  height: 26px;
  background: url(../images/searchIcon.png);
  position: absolute;
  right: -45px;
  top: 0;
  background-size: contain;
  cursor: pointer;
}

.dSearchIb button {
  width: 38px;
  height: 26px;
  background: url(../images/searchIcon.png);
  border: 0;
  display: block;
  cursor: pointer;
}

/* .nlLists {
  width: 1226px;
  height: auto;
  margin: 0 auto;
}

.nlListsUl {
  width: 100%;
  height: auto;
  margin-bottom: 28px;
  margin-top: 0;
  padding: 0;
  overflow: hidden;
}

.nlListsLi {
  width: 100%;
  height: 45px;
  border-bottom: 1px solid #e5e5e5;
  list-style: none;
}

.nlListsLi img {
  width: 25px;
}

.nlListsI {
  width: 13px;
  height: 17px;
  display: inline-block;
  background: url(../images/textI.png);
  background-position: 0 50%;
}

.nlListsA {
  width: auto;
  height: 100%;
  line-height: 45px;
  color: #000000;
}

.nlListsA:hover {
  color: #ca4300;
  text-decoration: underline;
}

.nlListsT {
  width: auto;
  height: 100%;
  line-height: 45px;
  display: inline-block;
  float: right;
  color: #999999;
} */

/*news*/
.myLocation {
  width: 100%;
  height: 16px;
  margin-bottom: 34px;
  padding-top: 20px;
}

.myLocationBox {
  width: 1226px;
  height: 100%;
  margin: 0 auto;
  margin-bottom: 33px;
  /*padding-top: 6px;*/
  /*padding-left: 88px;*/
}

.myLocation-word1 {
  width: auto;
  height: 100%;
  font-size: 12px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #ABABAB;
  line-height: 16px;
  float: left;
  margin-right: 15px;
}

.myLocation-word1 a {
  color: #ABABAB;
}

.myLocation-word2 {
  width: auto;
  height: 100%;
  font-size: 12px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #ABABAB;
  line-height: 16px;
  float: left;
}

.myLocation-word2 a {
  color: #000000;
}

.myLocation-icon1 {
  width: 16px;
  height: 16px;
  background: url(../images/arrow.png);
  float: left;
  margin-right: 27px;
}

.newsBox {
  width: 1226px;
  margin: 0 auto;
  height: auto;
  /* overflow: hidden; */
  padding-bottom: 40px;
}

.newsBox_left {
  width: 858px;
  float: left;
}

.newsBox_right {
  width: 470px;
  float: right;
}

.newsBox_left {

}

.leftBox {
  width: 849px;
  margin-bottom: 20px;
  /* float: left; */
}

.news_title {
  font-size: 32px;
  font-weight: bold;
  line-height: 1.6;
  letter-spacing: normal;
  text-align: left;
  color: #000000;
  margin: 0 0 30px 0;
}

.subhead {
  width: 100%;
  height: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #EEEEEE;
  margin-bottom: 20px;
}

.subhead * {
  height: 15px;
  line-height: 15px;
}

.subhead .original {

  width: 31px;
  height: 18px;
  background: #EEEEEE;
  border-radius: 3px;
  font-size: 11px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #959595;
  line-height: 18px;
  margin-right: 20px;
  display: inline-block;
  float: left;
  text-align: center;
  margin-top: -2px;
}

.subhead1,
.subhead2 {
  font-size: 15px;
  color: #646464;
  float: left;
  padding-right: 20px;
  border-right: 1px solid #e7e7e7;
  margin-right: 20px;
}

/*.subhead2 {*/
/*    font-size: 15px;*/
/*    color: #999999;*/
/*    float: left;*/
/*}*/
.subhead3 {
  font-size: 15px;
  color: #646464;
  float: left;
}

.con1 {
  font-size: 14px;
  line-height: 1.7;
  color: #000000;
  margin-bottom: 30px;
}
.con1 * {
  font-family: Roboto, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif !important;
}

.likes {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.likes .Collection {
  width: 96px;
  height: 35px;
  background: #FFFFFF;
  border: 1px solid #878787;
  border-radius: 17px;
  margin: 0 13px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.likes .CollectionTrue {
  width: 96px;
  height: 35px;
  background: #FFFFFF;
  border-radius: 17px;
  margin: 0 13px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border: 1px solid #CA4300;
}

.likes .CollectionTrue span {
  color: #CA4300;
}

.CollectionTrue .Collection_zan {
  width: 15px;
  height: 15px;
  background: url("./../../public/images/dianzan_hover.png");
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
}

.CollectionTrue span {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #878787;
  margin-left: 9px;
}

.likes .Collection:hover {
  border: 1px solid #CA4300;
}

.likes .Collection:hover span {
  color: #CA4300;
}

.Collection span {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #878787;
  margin-left: 9px;
}

.Collection:hover span {
  color: #CA4300;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  margin-left: 9px;
}

.Collection_zan {
  width: 15px;
  height: 15px;
  background: url("./../../public/images/dianzan.png");
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
}

.Collection:hover .Collection_zan {
  width: 15px;
  height: 15px;
  background: url("./../../public/images/dianzan_hover.png");
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
}

.Collection_cang {
  width: 18px;
  height: 16px;
  background: url("./../../public/images/duobianxing.png");
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
}

.Collection:hover .Collection_cang {
  width: 18px;
  height: 16px;
  background: url("./../../public/images/duobianxing_hover.png");
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
}

.original_box {
  background: #fff0e6;
  color: #CA4300;
  height: 40px;
  line-height: 40px;
  text-align: center;
  margin: 20px 0;
  font-size: 14px;
  opacity: 0.7;
}

.img1 {
  width: 750px;
  height: 330px;
  margin-bottom: 30px;
  background: url(../images/img1.png);
}

.con2 {
  font-size: 14px;
  line-height: 2;
  color: #000000;
  margin-bottom: 30px;
}

.img2 {
  width: 750px;
  height: 330px;
  margin-bottom: 30px;
  background: url(../images/img2.png);
}

.con3 {
  font-size: 14px;
  line-height: 2;
  color: #000000;
  margin-bottom: 30px;
}

.img3 {
  width: 750px;
  height: 330px;
  margin-bottom: 30px;
  background: url(../images/img1.png);
}

.rightBox {
  /* width: 785px; */
  height: auto;
  background: #f8f8f8;
  padding: 29px 38px;
}

.rightBox1 {
  width: 100%;
  height: auto;
}

.rela {
  font-size: 18px;
  margin-bottom: 31px;
  color: #000000;
}

.rightConBox {
  width: auto;
  padding-bottom: 22px;
  border-bottom: 1px solid #cccccc;
}

.rightCon {
  width: auto;
  padding: 8px 12px;
  height: 22px;
  background-color: #f7f7f7;
  border: 1px solid #e0e0e0;
  margin-right: 20px;
  color: #000000;
}

/*
.rightCon:hover{
  background: rgb(254,102,2);
  color: white;
}
*/
.rightContitle:hover {
  color: #ca4300;
}

.rightContitleBox {
  width: 100%;
  height: auto;
}

.rightContitleBox li {
  padding-left: 0 !important;
}

.rightContitleBox li::marker {
  font-size: 10px;
  line-height: 30px;
  color: #b6b6b6;
}

.rightContitle {
  font-size: 14px;
  color: #000000;
  cursor: pointer;
}

.rightConSubtitle {
  font-size: 14px;
  line-height: 1.64;
  color: #999999;
  padding-bottom: 19px;
}

.rightContitleBox:last-child {
  border-bottom: none;
}


.rightContitleBoxRecommends {
  display: flex;
  /* justify-content: space-between; */
  margin-bottom: 50px;
}

.rightContitleBoxRecommends .item {
  width: 248px;
  background: #fff;
  height: 204px;
  margin-right: 30px;
}
.rightContitleBoxRecommends .item:last-child {
  margin-right: 0;
}
.rightContitleBoxRecommends .item  img {
  width: 248px;
  height: 124px;  
}
.rightContitleBoxRecommends .item .content {
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.rightContitleBoxRecommends .item .content p {
  width: 248px;
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.rightContitleBoxRecommends .item .content p.main {
  color: #000;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
}
.rightContitleBoxRecommends .item .content p.sub {
  color: #878787;
  font-size: 12px;
  height: 20px;
  line-height: 20px;
}

.chapters {
  width: 100%;
}

.lastchapter {
  width: auto;
  float: left;
  max-width: 350px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 50%;
}

.lastchapter a {
  color: #000;
}

.lastchapter a:hover {
  color: #ca4300;
}

.nextchapter {
  width: auto;
  float: right;
  max-width: 350px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 50%;
}

.nextchapter a {
  color: #000;
}

.nextchapter a:hover {
  color: #ca4300;
}

/*search1*/
.searchConB1 {
  width: 100%;
  height: 21px;
  margin-bottom: 10px;
  margin-top: 20px;
}

.searchCon1T {
  width: auto;
  font-size: 16px;
  color: #000000;
  /*margin-top: 34px;*/
  margin-bottom: 20px;
  cursor: pointer;
}

.searchCon1T:hover {
  color: #ca4300;
}

.searchCon1C {
  width: auto;
  font-size: 12px;
  color: #eeeeee;
  display: inline-block;
  height: 14px;
  line-height: 14px;
  margin-left: 5px;
  background: #888;
  padding: 3px;
  vertical-align: middle;
}

.searchCon1T1 {
  color: #999999;
  float: right;
}

.searchCon2 {
  color: #999999;
  font-size: 14px;
}

.file_but1 {
  width: auto;
  height: 20px;
  font-size: 14px;
  color: #333333;
  margin-right: 15px;
  vertical-align: center;
  background: url(../images/down.png) no-repeat;
  background-position: 0 0;
  padding-left: 24px;
  line-height: 20px;
  cursor: pointer;
  margin-top: 20px;
}

.file_but1 a {
  width: auto;
  margin-right: 10px;
  color: #000;
}

.file_but1 a:hover {
  color: #ca4300;
}

.keyWord {
  color: #ca4300;
  display: none;
}

/*server*/
.serverBox {
  width: 100%;
  background: rgb(250, 250, 250);
  height: auto;
  margin: 0 auto;
  overflow: hidden;
}

.serverBox1 {
  width: 1226px;
  height: 100%;
  margin: 0 auto;
  position: relative;
  padding-top: 30px;
}

.serverTab {
  width: 100%;
  height: 62px;
  position: relative;
}

.serverTabUl {
  width: 1221px;
  height: 100%;
  border-bottom: 3px solid rgb(242, 242, 242);
  padding: 0 0px 0px 5px;
}

.serverTabLi {
  width: auto;
  padding-right: 24px;
  border-right: 3px solid rgb(242, 242, 242);
  float: left;
  font-size: 16px;
  color: rgb(51, 51, 51);
  margin-right: 24px;
  margin-bottom: 10px;
  cursor: pointer;
  list-style: none;
}

.serverTabLi:nth-child(8) {
  /*border-right: 0;*/
}

.serverTabLi:last-child {
  border-right: 0;
}

/*.serverTabLi:hover{*/
/*color: rgb(254,102,3);*/
/*}*/
.serverAlls {
  width: 104px;
  height: 21px;
  /*position: absolute;*/
  /*right: 0;*/
  /*top: 24px;*/
  cursor: pointer;
}

.serverAllWord {
  width: auto;
  height: 100%;
  line-height: 21px;
  color: #ca4300;
  font-size: 16px;
  /*margin-left: 32px;*/
  float: left;
}

.serverAllIcon {
  /*position: relative;*/
  height: 0px;
  width: 0px;
  border-top: 4px solid transparent;
  border-left: 4px solid #ca4300;
  border-bottom: 4px solid transparent;
  float: left;
  margin-left: 6px;
  margin-top: 8px;
}

.serverLists {
  width: 100%;
  height: auto;
  margin-top: 20px;
}

.serverListsUl {
  width: 100%;
  height: 100%;
}

.serverListLi {
  width: 284px;
  float: left;
  height: 250px;
  margin-right: 30px;
}

.serverListLi:nth-child(4n) {
  margin-right: 0;
}

.serverCon.server1 {
  height: auto;
  position: relative;
  margin-top: 50px;
  width: 100%;
}

/* .serverpages {
  width: auto;
  height: auto;
  margin: 0;
  padding: 20px 0;
  overflow: hidden;
}

.serverpage {
  width: 40px;
  height: 30px;
  background: rgb(242, 242, 242);
  margin-right: 5px;
  float: left;
  text-align: center;
  line-height: 30px;
  font-size: 12px;
  border: 1px solid rgb(242, 242, 242);
  cursor: pointer;
  list-style: none;
}

.serverpage a {
  color: #666;
  display: block;
}

.pageActive {
  background: #ca4300;
  color: white;
  border: 1px solid #ca4300;
}

.pageActive a {
  color: #fff;
}

.serverpage.pageActive:hover {
  background: #ca4300;
  border: 1px solid #ca4300;
  color: white;
}

.serverpage:hover {
  background: white;
  border: 1px solid #ca4300;
  color: #000000;
} */

.liActive {
  color: #ca4300;
}

/*serverlist*/
.location {
  margin-bottom: 20px;
}

.locationLi {
  float: left;
}

.listBigBox {
  width: 1226px;
  margin: 20px auto 0;
}

.categorybox {
  width: 100%;
  height: auto;
  background: #f8f8f8;
  /*padding-left: 37px;*/
  position: relative;
  /*position: absolute;*/
  min-height: 63px;
  overflow: hidden;
}

.categorybox-wrap {
  width: 1226px;
  margin: 0 auto;
}

.categoryUl {
  float: left;
  width: 1000px;
  height: 100%;
  margin: 0 auto;
  position: relative;
  padding: 15px;
}

.categoryLi {
  width: auto;
  height: 100%;
  line-height: 33px;
  margin-right: 36px;
  float: left;
  font-size: 14px;
  color: #000000;
  list-style: none;
}

.clickActive {
  color: #ca4300;
  font-weight: bold;
}

.categoryLi:hover {
  color: #ca4300;
  cursor: pointer;
}

.location > ul > .categoryLi:first-child {
  color: #666666;
  cursor: initial;
}

.location > ul > .categoryLi:first-child:hover {
  color: #666666;
}

.keySearch {
  float: right;
  width: 184px;
  height: 30px;
  /*border-bottom: 1px solid #eeeeee;*/
  /*margin-bottom: 20px;*/
  margin-top: 16px;
  overflow: hidden;
}

.keySearch input {
  float: left;
  font-size: 12px;
  line-height: 14px;
  overflow: hidden;
  width: 127px;
}

.listSearch {
  width: 38px;
  height: 26px;
  float: left;
  background: #ca4300;
  color: #fff;
  font-size: 12px;
  line-height: 26px;
  text-align: center;
}

.keyInput {
  height: 20px;
  outline: none;
  float: left;
  padding: 2px 0px 2px 10px;
  border: 1px solid #ca4300;
}

/* .listBox {
  width: 100%;
  position: relative;
  margin-bottom: 80px;
  margin-top: 20px;
}

.listUl {
  width: 100%;
  padding: 0;
  margin: 0;
  padding: 0;
}

.listLi {
  width: 100%;
  height: 142px;
  margin-bottom: 20px;
  list-style: none;
  position: relative;
}

.list-img {
  width: 284px;
  height: 142px;
  float: left;
  background: url(../images/listing1.png) no-repeat;
} */

.listwordbox {
  width: 906px;
  height: 100%;
  padding-left: 20px;
  float: left;
}

.navSearchIconIcon1 {
  width: 14px;
  height: 14px;
  background: url(../images/search14.png);
}

.listwordT {
  width: auto;
  height: auto;
  margin-top: 0px;
  font-size: 14px;
  cursor: pointer;
  color: #333;
}

.listwordT:hover {
  text-decoration: underline;
  color: #ca4300;
}

.listwordS {
  margin-top: 10px;
  font-size: 12px;
  padding-bottom: 10px;
  color: #999999;
}

.listwordI {
  padding-top: 18px;
  font-size: 12px;
  border-top: 1px dashed #eeeeee;
  margin-bottom: 15px;
  color: #999999;
}

.listwordC1 {
  width: 16px;
  height: 16px;
  background: #cccccc;
  display: inline-block;
  background: url(../images/icon1.png);
  position: absolute;
  margin-right: 10px;
  top: 5px;
  /*transform: translateY(-50%);*/
}

.listwordC2 {
  width: auto;
  height: 21px;
  line-height: 21px;
  padding: 0 5px;
  background: #eeeeee;
  font-size: 12px;
  display: inline-block;
  margin-left: 16px;
  color: #999999;
}

.listwordC {
  height: 21px;
  position: relative;
}

.listwordTS {
  height: 55px;
  margin-bottom: 10px;
}

.listwordC2:nth-child(2) {
  margin-left: 28px;
}

/*shouye*/
.videoBox {
  width: 100%;
  height: 425px;
  background: url(../images/banner3.jpg);
  position: relative;
}

.videoWord1 {
  color: #ffffff;
  font-size: 26px;
  font-weight: bold;
  left: 0;
  width: 100%;
  margin-left: 0;
  top: 100px;
  position: absolute;
  text-align: center;
}

.videoWord2 {
  font-size: 41px;
  color: white;
  width: 100%;
  position: absolute;
  left: 0;
  top: 160px;
  text-align: center;
}

.videoWord3 {
  font-size: 17px;
  color: #ffffff;
  width: 100%;
  position: absolute;
  left: 0;
  top: 230px;
  text-align: center;
}

.deepen {
  width: 0;
  height: 100%;
  background: #cc5500;
  position: absolute;
}

.videoBox .learnMore {
  width: 170px;
  opacity: 1;
  left: 50%;
  top: 316px;
  margin-left: -88px;
  position: absolute;
}

.learnMore:hover .deepen {
  width: 100%;
  transition: 0.4s;
}

.homeConBox {
  width: 100%;
  height: auto;
  overflow: hidden;
}

.homeCon1 {
  width: 1226px;
  height: 640px;
  margin: 0 auto;
}

.homeContitle {
  width: 100%;
  height: 22px;
  font-size: 16px;
  padding: 24px 0;
  border-bottom: 3px solid #f2f2f2;
  position: relative;
  margin-bottom: 30px;
}

.homeContitle span {
  font-size: 16px;
  display: inline-block;
  width: auto;
  height: 22px;
  text-align: center;
  padding: 0 25px;
  cursor: pointer;
}

.homeContitle .title:hover {
  color: #ca4300;
}

.homeContitle .spanL {
  padding: 0;
  float: right;
}

.spanL .spanL-word {
  float: left;
  font-size: 14px;
  line-height: 22px;
  padding: 0;
  margin-right: 5px;

  cursor: pointer;
}

.spanL .spanL-word a {
  color: #ca4300;
}

.spanL-icon {
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-left: 6px solid #ca4300;
  border-bottom: 6px solid transparent;
  border-right: 6px solid transparent;
  float: right;
  margin-top: 6px;
}

.active {
  color: #ca4300;
  /* font-weight: bold; */
}

.titleBor {
  width: 114px;
  height: 3px;
  background: #ca4300;
  position: absolute;
  top: 70px;
  left: 0;
}

.homeConCon1 {
  width: 100%;
  height: 528px;
}

.homeConConL {
  width: 284px;
  height: 498px;
  float: left;
  cursor: pointer;
  position: relative;
}

.homeConConL em {
  display: inline-block;
  width: 100px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  position: absolute;
  bottom: 85px;
  left: 95px;
  border: 1px solid #fff;
  color: #fff;
  font-size: 14px;
  font-style: normal;
}

.homeConConL em:hover {
  border: 1px solid #ca4300;
  background: #ca4300;
  font-size: 14px;
}

.homeConConL:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transition: 0.4s;
  transform: translate3d(0, -3px, 0);
}

.homeConConL img {
  width: 100%;
  height: 100%;
  background-size: contain;
}

.homeConConR {
  width: 912px;
  height: 498px;
  float: right;
}

.homeConConR ul {
  width: 942px;
  height: 100%;
  margin: 0;
  padding: 0;
}

.homeConConRLi {
  width: 284px;
  height: 234px;
  float: left;
  margin-right: 30px;
  background: #fafafa;
  margin-bottom: 30px;
  cursor: pointer;
  list-style: none;
  position: relative;
}

.homeConConRLi-img {
  width: 284px;
  height: 142px;
  margin-bottom: 20px;
}

.homeConConRLi-word1 {
  text-align: center;
  font-size: 17px;
  color: #252525;
  margin-bottom: 5px;
}

.homeConConRLi-word2 {
  text-align: center;
  font-size: 13px;
  color: #999999;
  padding: 0 30px;
  line-height: 1.5;
}

.homeCon2 {
  width: 100%;
  height: 514px;
  background: #fafafa;
}

.homeCon2Box {
  width: 1226px;
  height: 100%;
  margin: 0 auto;
}

.homeConCon2 {
  width: 100%;
  height: 370px;
}

.homeConCon2 ul {
  width: 1254px;
  height: 100%;
  margin: 0;
  padding: 0;
}

.homeConCon2Li {
  width: 390px;
  height: 100%;
  float: left;
  background: white;
  margin-right: 28px;
  list-style: none;
  position: relative;
}

.homeConCon2Li-img {
  width: 390px;
  height: 192px;
  margin-bottom: 36px;
}

.homeConCon2Li-word1 {
  font-size: 17px;
  color: #000000;
  text-align: center;
  margin-bottom: 20px;
  margin-top: 20px;
}

.homeConCon2Li-borderBox {
  width: 100%;
  height: 3px;
  position: relative;
  margin-bottom: 20px;
}

.homeConCon2Li-border {
  width: 36px;
  height: 3px;
  background-color: #f1f1f1;
  position: absolute;
  left: 50%;
  top: 0;
  margin-left: -18px;
}

.homeConCon2Li-word2 {
  font-size: 13px;
  width: 285px;
  color: #999999;
  margin-left: 52px;
  text-align: center;
  line-height: 1.7;
}

.homeCon3 {
  background: white;
  height: 514px;
}

.homeCon4 {
  background: #fafafa;
  height: 550px;
}

.over:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transition: 0.4s;
  transform: translate3d(0, -3px, 0);
}

.homeConCon4Li {
  position: relative;
  height: 400px;
}

.homeConCon4Li-word1 {
  font-weight: bold;
  font-size: 23px;
  color: white;
  position: absolute;
  left: 25px;
  top: 41px;
}

.homeConCon4Li-word2 {
  width: 340px;
  font-size: 15px;
  color: #ffffff;
  position: absolute;
  left: 25px;
  top: 86px;
  line-height: 1.6;
}

.homeConCon2Li-img2 {
  position: absolute;
  left: 0;
  top: 0;
  width: 390px;
  height: 400px;
}

.homeConCon2Li-img1 {
  position: absolute;
}

.over:hover .homeConConRLi-word1 {
  color: #ca4300;
}

.over:hover .homeConConRLi-word2 {
  color: #000000;
}

.over:hover .homeConCon2Li-word1 {
  color: #ca4300;
}

.over:hover .homeConCon2Li-word2 {
  color: #000000;
}

.learnMore {
  width: 157px;
  height: 46px;
  background: #ca4300;
  position: absolute;
  left: 25px;
  bottom: 40px;
  opacity: 0;
  cursor: pointer;
}

.learnMore-word {
  font-size: 16px;
  color: white;
  line-height: 44px;
  position: absolute;
  left: 28px;
}

.learnMore-icon {
  position: absolute;
  width: 24px;
  height: 24px;
  background: url(../images/learnmore.png);
  top: 10px;
  right: 28px;
}

.homeConCon5Li {
  width: 284px;
  height: 123px;
  margin-right: 30px;
  float: left;
  /*background: #ccc;*/
  position: relative;
  padding-top: 177px;
  list-style: none;
}

.homeConCon5Li:hover .homeConCon2Li-word1 {
  color: #ca4300;
  transition: 0.4s;
}

.homeConCon5Li:hover .homeConCon2Li-word2 {
  color: #000000;
  transition: 0.4s;
}

.homeCon5 ul {
  width: 1256px;
}

.homeConCon5Li-img {
  width: 122px;
  height: 122px;
  /*background: url(../images/minsuxuan.png);*/
  margin-left: 81px;
  position: absolute;
  top: 10px;
}

.homeConCon5Li-word1 {
  font-size: 18px;
  color: #000000;
  text-align: center;
  margin-bottom: 21px;
}

.homeConCon5Li-word2 {
  width: 232px;
  margin-left: 27px;
  font-size: 14px;
  color: #999999;
  text-align: center;
  line-height: 1.6;
}

.wrap {
  width: 1226px;
  margin: 0 auto;
}

.wrap * {
  padding: 0;
  margin: 0;
}

.wrap ul,
.wrap li {
  list-style: none;
}

/* .homeCon-head {
  height: 86px;
  line-height: 86px;
  border-top: 2px solid #dcdcdc;
}

.homeCon-head span {
  float: left;
  font-size: 18px;
  color: #898989;
}

.homeCon-head a.more {
  float: right;
  color: #8b8b8b;
  font-size: 16px;
  display: none;
  padding-top: 34px;
}

.homeCon-head a.more.active {
  display: block;
}

.homeCon-head a.more em {
  font-style: normal;
  display: inline-block;
  height: 16px;
  line-height: 16px;
  float: left;
}

.homeCon-head a.more i {
  float: left;
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url('../images/newPage/more.png') no-repeat 0 0;
}

.homeCon-head a.more:hover i {
  background: url('../images/newPage/more-a.png') no-repeat 0 0;
}

.homeCon-head a:hover {
  color: #ca4300;
}

.homeCon-head ul {
  float: right;
  margin-right: 10px;
  padding-top: 27px;
}

.homeCon-head li {
  float: left;
  height: 30px;
  line-height: 30px;
  color: #333;
  cursor: pointer;
  padding: 0 10px;
  margin-right: 10px;
  font-size: 16px;
}

.homeCon-head li.active,
.homeCon-head li:hover {
  background: #ca4300;
  color: #fff;
} */

/* .homeCon6 {
  padding-bottom: 70px;
}

.homeCon6-body .homeCon6-item {
  float: left;
  width: 395px;
  margin-right: 20px;
}

.homeCon6-body .homeCon6-item:last-child {
  margin-right: 0;
}

.homeCon6-item-bg {
  height: 200px;
  background: #ddd;
  width: 100%;
  text-align: center;
}

.homeCon6-item-bg span {
  color: #fff;
  font-size: 24px;
  display: block;
  height: 25px;
  line-height: 25px;
  text-align: center;
  padding-top: 85px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.homeCon6-item-bg i {
  display: inline-block;
  height: 3px;
  width: 80px;
  background: #fff;
}

.homeCon6-item-list {
  position: relative;
  height: 175px;
  background: #fff;
}

.homeCon6-item-line {
  position: absolute;
  z-index: 1;
  width: 345px;
  padding: 35px 25px 0 25px;
}

.homeCon6-item-line div {
  width: 100%;
  height: 30px;
  line-height: 30px;
  border-bottom: 1px dashed #ddd;
}


.homeCon6-item-list ul {
  padding: 38px 25px 0 25px;
  position: absolute;
  z-index: 2;
  width: 345px;
}

.homeCon6-item-list li {
  float: left;
  padding-right: 20px;
  height: 30px;
  line-height: 30px;
}

.homeCon6-item-list.homeCon6-li li {
  width: 33.33%;
  padding-right: 0;
}

.homeCon6-item-list.homeCon6-li a.more {
  right: 57px;
}

.homeCon6-item-list li a {
  color: #666;
  font-size: 14px;
  padding-left: 10px;
  display: block;
  width: 100%;
  height: 30px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.homeCon6-item-list li a:hover {
  color: #ca4300;
}

.homeCon6-item-list .more {
  color: #fff;
  background: #ca4300;
  display: inline-block;
  width: 72px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  position: absolute;
  right: 40px;
  top: 100px;
  font-size: 13px;
  border-radius: 22px;
  z-index: 2;
}

.homeCon6-item-list .more:hover {
  background: #cc5500;
  transform: .5s;
} */

/* .homeCon7 {}

.homeCon7-body {
  height: 400px;
  padding-bottom: 70px;
}

.homeCon7-body li {
  float: left;
  width: 395px;
  height: 400px;
  margin-right: 20px;
  position: relative;
}

.homeCon7-body li:last-child {
  margin-right: 0;
}

.homeCon7-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 395px;
  height: 400px;
  z-index: 1;
  background: #ddd;
}

.homeCon7-mark {
  background: rgba(0, 0, 0, .5);
  position: absolute;
  top: 0;
  left: 0;
  width: 395px;
  height: 400px;
  z-index: 2;
  display: none;
}

.active .homeCon7-mark {
  display: block;
}

.homeCon7-con {
  position: absolute;
  top: 0;
  left: 0;
  width: 335px;
  height: 340px;
  z-index: 3;
  padding: 30px;
}

.homeCon7-con h2 {
  color: #fff;
  padding: 5px 0;
  font-size: 24px;
}

.homeCon7-con p {
  height: 48px;
  line-height: 24px;
  color: #fff;
  font-size: 15px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  width: 100%;
}

.active .homeCon7-con dl {
  display: block;
}

.homeCon7-con dl {
  padding-top: 130px;
  display: none;
}

.homeCon7-con dd {
  height: 30px;
  line-height: 30px;
}

.homeCon7-con a {
  color: #fff;
  display: block;
  height: 30px;
  line-height: 30px;
  width: 335px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.homeCon7-con a:hover {
  color: #ca4300;
}

.homeCon7-con dd a {
  font-size: 14px;
}

.homeCon7-con dd a:hover {
  color: #ca4300;
  text-decoration: underline;
} */

/* .homeCon8-body {
  padding-bottom: 70px;
} */

/* .homeCon8-item {
  display: none;
}

.homeCon8-item.active {
  display: block;
}

.homeCon8-body .side {
  float: left;
  width: 305px;
  height: 406px;
  border-right: 2px solid #f9f9f9;
  padding: 20px 25px;
  box-sizing: border-box;
  position: relative;
}

.homeCon8-body .side img {
  position: absolute;
  z-index: 1;
  width: 305px;
  height: 406px;
  top: 0;
  left: 0;
}

.homeCon8-body .side a {
  display: inline-block;
  width: 112px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  position: absolute;
  bottom: 50px;
  left: 95px;
  border: 1px solid #fff;
  color: #fff;
  font-size: 16px;
  font-style: normal;
  z-index: 2;
}

.homeCon8-body .side a:hover {
  background: #ca4300;
  border: 1px solid #ca4300;
}

.homeCon8-body .list {
  float: right;
  width: 919px;
  background: #fff;
}

.homeCon8-body .list li {
  float: left;
  width: 255px;
  border-right: 2px solid #f9f9f9;
  padding: 23px 25px;
  border-bottom: 2px solid #f9f9f9;
}

.homeCon8-body .list li:nth-child(3n) {
  border-right: 0;
}

.homeCon8-body .list li:nth-child(n+4) {
  border-bottom: 0;
}

.homeCon8-body .list li:hover h2 span {
  color: #ca4300;
}

.homeCon8-body .list li:hover p {
  color: #333;
}

.homeCon8-body .list h2 {
  font-size: 16px;
  font-weight: normal;
  color: #333;
  width: 255px;
}

.homeCon8-body .list h2 span {
  display: inline-block;
  float: left;
  height: 20px;
  line-height: 20px;
  max-width: 255px;
  overflow: hidden;
  font-size: 16px;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.homeCon8-body .list h2 span.active {
  max-width: 203px;
}

.homeCon8-body .list h2 i {
  margin-left: 5px;
  display: inline-block;
  float: left;
  width: 47px;
  height: 19px;
  background: url('../images/newPage/is_buy.png') no-repeat 0 0;
}

.homeCon8-body .list p {
  font-size: 12px;
  color: #b4b4b4;
  width: 255px;
  height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-top: 5px;
}

.homeCon8-body .list img {
  width: 255px;
  height: 100px;
  background: #ddd;
  display: block;
  margin-top: 11px;
  overflow: hidden;
} */

.home-logos {
}

.home-logos-item {
  height: 254px;
  padding-bottom: 74px;
}

.home-logos-item > div {
  float: left;
  height: 254px;
}

.home-logos-item li:hover {
  background: #fff;
}

.home-logos-item .item1 {
  width: 534px;
  height: 254;
}

.home-logos-item .item1 li {
  float: left;
  border-top: 1px solid #dcdcdc;
  border-left: 1px solid #dcdcdc;
  width: 178px;
  height: 127px;
  box-sizing: border-box;
}

.home-logos-item .item1 li:nth-child(n + 4) {
  border-bottom: 1px solid #dcdcdc;
}

.home-logos-item .item1 img {
  width: 178px;
  height: 127px;
}

.home-logos-item .item2 {
  width: 234px;
  height: 254px;
}

.home-logos-item .item2 li {
  border-top: 1px solid #dcdcdc;
  border-left: 1px solid #dcdcdc;
  width: 234px;
  height: 127px;
  box-sizing: border-box;
}

.home-logos-item .item2 li:last-child {
  border-bottom: 1px solid #dcdcdc;
}

.home-logos-item .item3 {
  height: 254px;
}

.home-logos-item .item3 li {
  float: left;
  border-top: 1px solid #dcdcdc;
  border-left: 1px solid #dcdcdc;
  border-bottom: 1px solid #dcdcdc;
  width: 150px;
  height: 254px;
  box-sizing: border-box;
}

.home-logos-item .item3 li:last-child {
  border-right: 1px solid #dcdcdc;
}

.home-ads {
  background: url("../images/newPage/ads.jpg") no-repeat 50% 50% #e8e8e8;
  height: 100px;
}

.descrip {
  width: 100%;
  height: 418px;
  background: url(../images/banner3.jpg);
  overflow: hidden;
}

.descripBox {
  width: 1226px;
  height: 240px;
  margin: 93px auto 0;
  overflow: visible;
  position: relative;
}

.descripLi.big {
  width: 353px;
  height: 100%;
  border-right: 1px solid #ffffff;
  position: relative;
}

.descripBox .menu {
  position: absolute;
  top: -65px;
  right: 5px;
  text-align: right;
}

.descripBox .menu a {
  color: #fff;
  padding-left: 15px;
  font-size: 14px;
}

.descripBox .menu a:hover {
  color: #ca4300;
}

.descripBox ul {
  width: 1226px;
  height: 100%;
  position: relative;
  padding: 0;
  margin: 0;
}

.bigBox .liTitle {
  font-size: 36px;
  color: white;
  width: 360px;
  position: relative;
  top: -20px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.bigBox .liTime {
  font-size: 13px;
  color: #bcb5b5;
  padding: 0 0 20px;
}

.bigBox .liInfo {
  font-size: 14px;
  color: #e3dfdd;
  /* margin-bottom: 40px; */
  padding-top: 60px;
  text-align: center;
  max-height: 60px;
  line-height: 20px;
  overflow: hidden;
}

.bigBox .learnMore {
  opacity: 1;
}

.descripLi {
  width: 240px;
  height: 100%;
  float: left;
  border-right: 1px solid white;
  position: relative;
  padding: 0 18px;
  overflow: visible !important;
  list-style: none;
}

/* .descripLi:first-child {
  border-left: 1px solid white;
} */

.descripLiLast {
  border-right: 0;
}

.smallBox {
  /*opacity: 0;*/
}

.smallBox .liInfo {
  font-size: 17px;
  color: #ffffff;
  width: 232px;
  text-align: center;
  position: absolute;
  left: 50%;
  margin-left: -116px;
  top: 44px;
}

.smallBox .liTime {
  width: 100px;
  color: white;
  font-size: 14px;
  position: absolute;
  left: 50%;
  margin-left: -50px;
  top: 155px;
}

.descripLi .liTitle {
  opacity: 0;
  height: 0;
}

.descripLi .learnMore {
  display: none;
}

.descripLi.big .liTitle {
  opacity: 1;
  height: auto;
}

.descripLi.big .learnMore {
  display: block;
}

.descripLi .liTime {
  display: block;
  width: 100%;
  text-align: center;
}

.descripLi.big .liTime {
  text-align: left;
}

.descripLi .learnMore {
  position: relative;
  top: 0;
  left: 0;
}

.descripLi.big .bigBox .liInfo {
  padding-top: 0;
  text-align: left;
}

/*search*/
.SearchBox {
  width: 1226px;
  height: auto;
  margin: 0 auto;
}

.searchleftBox {
  width: 849px;
  float: left;
  /*margin-top: 21px;*/
  position: relative;
}

.searchAlls {
  width: 812px;
  height: 63px;
  margin-bottom: 30px;
  padding-left: 37px;
  background: #f8f8f8;
}

.searchAlls a,
.searcrServer a,
.searchRelative a {
  color: #000;
}

.searchAlls .clickActive a,
.searcrServer .clickActive a,
.searchRelative .clickActive a {
  color: #ca4300;
}

.angle {
  width: 0;
  height: 0;
  position: absolute;
  overflow: hidden;
  font-size: 0;
  /*是因为, 虽然宽高度为0, 但在IE6下会具有默认的 */
  line-height: 0;
  /* 字体大小和行高, 导致盒子呈现被撑开的长矩形 */
  border-width: 10px;
  border-style: solid dashed dashed dashed;
  /*IE6下, 设置余下三条边的border-style为dashed,即可达到透明的效果*/
  border-color: transparent transparent #f8f8f8 transparent;
  top: 51px;
  left: 50%;
  transform: translateX(-50%);
  display: none;
}

.angleShow {
  display: block;
}

.angleHide {
  display: none;
}

.searchAll {
  width: auto;
  height: 100%;
  margin-right: 36px;
  font-size: 16px;
  letter-spacing: normal;
  text-align: justify;
  color: #000000;
  line-height: 63px;
  cursor: pointer;
  position: relative;
}

.searchAll:hover {
  color: #ca4300;
}

.searchRelative {
  width: 812px;
  height: auto;
  padding-left: 37px;
  background: #f8f8f8;
  padding-top: 20px;
  padding-bottom: 10px;
}

.searchRelativeT {
  width: auto;
  font-size: 14px;
  line-height: 30px;
  color: #666666;
  margin-right: 42px;
}

.searchRelativeC {
  font-size: 14px;
  line-height: 30px;
  color: #000000;
  margin-right: 32px;
  cursor: pointer;
  white-space: nowrap;
}

.searchRelativeC:hover {
  color: #ca4300;
}

.searcrServer {
  width: 812px;
  height: auto;
  padding-left: 37px;
  background: #f8f8f8;
  margin-bottom: 30px;
  padding-bottom: 20px;
}

.searchConAll {
  width: 100%;
  height: auto;
}

.searchCon {
  width: 100%;
  height: auto;
  border-bottom: 1px solid #eeeeee;
  padding-bottom: 10px;
}

.searchCon1 {
  width: auto;
  height: 22px;
  font-size: 16px;
  color: #000000;
  margin-top: 34px;
  margin-bottom: 20px;
  display: block;
  cursor: pointer;
}

.searchCon1:hover {
  color: #ca4300;
  text-decoration: underline;
}

.searchCon-time {
  width: auto;
  padding-right: 12px;
  margin-right: 5px;
  border-right: 1px solid #cccccc;
  font-size: 14px;
  color: #999999;
  display: inline-block;
  height: 14px;
  line-height: 14px;
}

.searchCon-Mb {
  width: auto;
  font-size: 14px;
  color: #999999;
}

.file_but {
  width: auto;
  /*height: auto;*/
  height: 20px;
  float: right;
  font-size: 14px;
  color: #333333;
  margin-right: 15px;
  vertical-align: center;
  background: url(../images/down.png) no-repeat;
  background-position: 0 0;
  padding-left: 24px;
  line-height: 20px;
  cursor: pointer;
}

.file_but a:hover {
  color: #ca4300;
}

.file_but a {
  width: auto;
  line-height: 19px;
  margin-right: 15px;
}

.buttonIcon {
  display: inline-block;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: url(../images/down.png);

  /*background-size: contain;*/
}

.pageNums {
  width: 400px;
  height: 48px;
  background-color: #f8f8f8;
  margin: 28px auto 50px;
  padding: 0 176px;
  position: relative;
}

.pageNum {
  width: 44px;
  padding: 0 68px;
  height: 20px;
  display: inline-block;
  line-height: 20px;
  text-align: center;
  border-right: 1px solid #dddddd;
  border-left: 1px solid #dddddd;
  left: 300px;
}

.pageNums span {
  font-size: 14px;
  color: #666666;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

.pageNums span:hover {
  color: #ca4300;
}

.lastpage {
  width: 44px;
  height: 20px;
  left: 176px;
}

.nextpage {
  width: 44px;
  height: 20px;
  right: 140px;
}

.searchrightBox {
  width: 326px;
  float: right;
  background-color: #f8f8f8;
}

.searchrightBox1 {
  width: 280px;
  margin: 0 auto;
  padding-bottom: 51px;
}

.yours {
  width: 100%;
  height: 74px;
  border-bottom: 1px solid #cccccc;
  cursor: pointer;
}

.yours:hover .your-word {
  color: #ca4300;
}

.your-word {
  height: 100%;
  line-height: 74px;
  color: #000000;
  /*font-weight: bold;*/
}

.dragbut {
  width: 22px;
  height: 22px;
  float: right;
  margin-top: 28px;
}

.dragbut img {
  width: 100%;
  height: 100%;
}

.yourcon ul {
  height: auto;
  margin-top: 18px;
  padding: 0;
  overflow: hidden;
}

.yourconLi {
  margin-bottom: 18px;

  font-size: 14px;
  cursor: pointer;
  list-style: none;
  /*margin-left: 20px;*/
}

.yourconLi a {
  color: #999999;
}

.yourconLi:hover a {
  color: #ca4300;
  /*text-decoration: underline;*/
}

.clickActive {
  color: #ca4300;
  font-weight: bold;
}

.your-cons {
  height: 0;
  overflow: hidden;
}

.your-cons.first {
  height: auto;
}

.relaActive {
  color: #ca4300;
}

.sebox2 {
  width: 1226px;
  padding-bottom: 40px;
}

/* .messageCon {
  background: #fafafa;
  margin-top: 21px;
  margin-bottom: 20px;
}

.messageConT {
  padding: 10px 0;
  font-size: 32px;
  text-align: center;
  font-weight: 400;
}

.messageConS {
  text-align: center;
  font-size: 16px;
  color: #000000;
  padding-bottom: 20px;
} */

.tip {
  width: 718px;
  padding-top: 10px;
  border-top: 1px solid #eeeeee;
  margin: 0 auto 22px;
  text-align: right;
  color: gray;
  font-size: 14px;
}

.mustStar {
  color: #ca4300;
}

.selectBox {
  width: 882px;
  height: 90px;
  margin-bottom: 24px;
}

.select {
  width: 258px;
  /*display: inline-block;*/
  float: left;
  margin-right: 36px;
}

.selectT {
  font-size: 14px;
  margin-bottom: 18px;
}

/*input.selectC:after{*/
/*content: url("../images/code.jpg");*/
/*display: block;*/
/*}*/
.selectC {
  width: 258px;
  height: 40px;
  border-radius: 5px;
}

.selectC1 {
  width: 258px;
  height: 40px;
  border-radius: 5px;
}

input.selectC {
  border: 1px solid #eeeeee;
}

input.selectC1 {
  border: 1px solid #eeeeee;
}

.content {
  /*margin-bottom: 16px;*/
  margin-bottom: 30px;
}

#textarea {
  width: 100%;
  height: 160px;
  resize: none;
  outline: none;
  border: none;
  border: 1px solid #eeeeee;
  padding: 15px;
  font-size: 14px;
  line-height: 16px;
  box-sizing: border-box;
}

/* .yoursInfo {
  height: auto;
}

.yoursT {
  height: 44px;
  background: #fafafa;
  text-align: center;
  line-height: 44px;
  margin-bottom: 44px;
  font-size: 16px;
}

.yoursInfoC {
  width: 905px;
}

.yoursInfoCC {
  float: left;
  width: 400px;
  margin-right: 50px;
  display: inline-block;
  position: relative;
}

.yoursInfoCC .selectC {
  width: 400px;
  padding: 5px 20px;
  box-sizing: border-box;
  outline: none;
  color: #000000;
  font-size: 14px;
}

.yoursInfoCC .selectC1 {
  width: 400px;
  padding: 5px 20px;
  box-sizing: border-box;
  outline: none;
  color: #000000;
} */

.messFooter {
  width: 216px;
  margin: 0 auto 100px;
}

.submit {
  height: 45px;
  background: #ca4300;
  color: white;
  text-align: center;
  line-height: 45px;
  width: 100%;
  outline: none;
  border: none;
  cursor: pointer;
}

.accept {
  width: 100%;
  height: 23px;
  position: relative;
  overflow: hidden;
}

.acceptIcon {
  float: left;
  display: inline-block;
  width: 13px;
  height: 13px;
  background: #eeeeee;
  margin-right: 12px;
  margin-left: 40px;
  border: 1px solid #ccc;
  margin-top: 4px;
}

.acceptWord {
  display: inline-block;
  white-space: nowrap;
}

.privacy {
  display: inline-block;
  position: absolute;
  width: 64px;
  color: #ca4300;
}

.select2-selection {
  height: 40px;
}

.form-label {
  height: 50px;
}

.select2-container .select2-selection--single {
  height: 40px;
  line-height: 40px;
}

.select2-container--default
.select2-selection--single
.select2-selection__rendered {
  height: 40px;
  line-height: 40px;
}

.select2-container--default
.select2-selection--single
.select2-selection__arrow {
  top: 9px;
}

.select2-container--default .select2-selection--single {
  border-color: #eeeeee;
}

.select2-container--default
.select2-results__option--highlighted[aria-selected] {
  background-color: #f8f8f8;
  color: #ca4300;
}

.select2-container--default .select2-results__option[aria-selected="true"] {
  background-color: #f8f8f8;
  color: #ca4300;
}

.select2-dropdown {
  border-color: #eeeeee;
  z-index: 2001;
}

.acceptIcon.active {
  background: url(../images/accpeted.png);
  width: 15px;
  height: 15px;
  border: 0;
}

/* .code {
  position: absolute;
  background: url(../images/code.jpg) no-repeat;
  width: 80px;
  height: 27px;
  top: 44px;
  right: 60px;
} */

.re {
  width: 22px;
  height: 22px;
  position: absolute;
  background: url(../images/re.png) no-repeat;
  right: 20px;
  top: 46px;
  cursor: pointer;
}

#province {
  padding-left: 0;
}

#province .select2-selection.select2-selection--single {
  height: 50px;
}

#province
.select2-container--default
.select2-selection--single
.select2-selection__rendered {
  height: 48px;
  line-height: 48px;
}

#province
.select2-container--default
.select2-selection--single
.select2-selection__arrow {
  top: 13px;
}

.error {
  font-size: 12px;
  color: #e50000;
  margin: 0;
  margin-top: 5px;
  visibility: hidden;
}

.provice {
  top: 74px;
  z-index: 999;
  background: #ffffff;
  border: 1px solid #eeeeee;
  padding: 10px 0;
  display: none;
}

.citys {
  margin: 0;
  width: 398px;
}

.citys a {
  display: inline-block;
  width: 129px;
  color: #666666;
  text-decoration: none;
  font-size: 14px;
  padding: 5px 0;
  text-align: center;
}

.citys a:hover {
  background: #f8f8f8;
  color: #ca4300;
}

.SearchBox:after {
  content: "";
  clear: both;
  display: block;
}

/**/
.serverInfoT {
  font-size: 30px;
  position: absolute;
  color: #ca4300;
  left: 0;
  top: 58px;
}

.map {
  width: 100%;
  height: 710px;
  position: relative;
}

.advantage {
  width: 100%;
  height: 670px;
  /*background: yellow;*/
  position: relative;
}

.advantageLi {
  list-style: none;
}

.advantageC {
  font-size: 30px;
  color: #4b4948;
  position: absolute;
  left: 0;
  top: 60px;
}

.serverInfoT.s {
  top: 0;
  left: 0;
}

.advantageUl {
  width: 1176px;
  height: 670px;
  padding: 0;
  margin: 0;
  padding-top: 128px;
}

.advantageLi {
  width: 33.3333333%;
  height: 252px;
  /*border: 1px solid rgb(254,102,3);*/
  float: left;
  position: relative;
  margin-bottom: 34px;
}

.advantageAT {
  font-size: 18px;
  text-align: center;
  color: #ca4300;
  margin-bottom: 24px;
  font-weight: bold;
}

.advantageAC {
  padding: 0 30px;
  font-size: 14px;
  color: #6e6c6c;
  width: 238px;
  height: auto;
  white-space: normal;
  word-break: break-all;
  word-wrap: break-word;
}

.advantageA .img {
  width: 84px;
  height: 84px;
  display: block;
  margin: 24px auto 0;
  outline: none;
}

.advantageA {
  position: absolute;
  width: 300px;
  min-height: 254px;
  border: 1px solid #ca4300;
}

.advantageAI1 {
  background: url(../images/advantages.png) no-repeat -168px -55px;
}

.advantageAI2 {
  background: url(../images/advantages.png) no-repeat -252px -55px;
}

.advantageAI3 {
  background: url(../images/advantages.png) no-repeat -336px -55px;
}

.advantageAI4 {
  background: url(../images/advantages.png) no-repeat -420px -55px;
}

.advantageAI5 {
  background: url(../images/advantages.png) no-repeat -84px -55px;
}

.advantageAI6 {
  background: url(../images/advantages.png) no-repeat 0 -55px;
}

.map-img {
  width: 100%;
  height: 100%;
  background: url(../images/map.png) no-repeat 0 0;
}

.mapCountry {
  position: absolute;
  width: auto;
  height: 26px;
}

.mapCountry-word {
  height: 26px;
  display: inline-block;
  font-size: 16px;
  color: #221815;
}

.mapCountry-word:hover {
  color: #ca4300;
}

.mapCountry-img {
  width: 26px;
  height: 26px;
  background: red;
  display: inline-block;
  vertical-align: middle;
}

.mapCountry0 {
  top: 214px;
  left: -13px;
}

.mapCountry1 {
  top: 243px;
  left: 30px;
}

.mapCountry1 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -182px -26px;
}

.mapCountry2 {
  top: 243px;
  left: 226px;
}

.mapCountry2 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -156px -26px;
}

.mapCountry3 {
  top: 263px;
  left: 334px;
}

.mapCountry3 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -130px -26px;
}

.mapCountry4 {
  top: 233px;
  left: 475px;
}

.mapCountry4 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -26px -26px;
}

.mapCountry5 {
  top: 259px;
  left: 503px;
}

.mapCountry5 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -78px -26px;
}

.mapCountry6 {
  top: 309px;
  left: 458px;
}

.mapCountry6 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -104px -26px;
}

.mapCountry7 {
  top: 386px;
  left: -16px;
}

.mapCountry7 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -52px -26px;
}

.mapCountry8 {
  top: 365px;
  left: 180px;
}

.mapCountry8 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat 0px 0px;
}

.mapCountry9 {
  top: 376px;
  left: 418px;
}

.mapCountry9 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -208px -26px;
}

.mapCountry10 {
  top: 407px;
  left: 215px;
}

.mapCountry10 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -26px 0px;
}

.mapCountry11 {
  top: 433px;
  left: 408px;
}

.mapCountry11 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -208px 0px;
}

.mapCountry12 {
  top: 459px;
  left: 354px;
}

.mapCountry12 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -182px 0px;
}

.mapCountry13 {
  top: 482px;
  left: 416px;
}

.mapCountry13 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -156px 0px;
}

.mapCountry14 {
  top: 500px;
  left: 273px;
}

.mapCountry14 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -104px 0px;
}

.mapCountry15 {
  top: 428px;
  left: 277px;
}

.mapCountry15 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -130px 0px;
}

.mapCountry16 {
  top: 448px;
  left: 107px;
}

.mapCountry16 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -52px 0px;
}

.mapCountry17 {
  top: 75px;
  left: 814px;
}

.mapCountry17 .mapCountry-img {
  background: url(../images/advantages.png) no-repeat -0px -26px;
}

.mapCountry18 {
  top: 386px;
  left: 110px;
}

.mapCountry18 .mapCountry-img {
  border-radius: 50%;
  overflow: hidden;
  width: 25px;
  height: 25px;
}

.mapCountry18 .mapCountry-img img {
  height: 25px;
  position: relative;
  left: -6.5px;
}

.mapCountry19 {
  top: 286px;
  left: 513px;
}

.mapCountry19 .mapCountry-img {
  border-radius: 50%;
  overflow: hidden;
  width: 25px;
  height: 25px;
}

.mapCountry19 .mapCountry-img img {
  height: 25px;
  position: relative;
  left: -1.5px;
}

.mapCountry20 {
  top: 583px;
  left: 937px;
}

.mapCountry20 .mapCountry-img {
  border-radius: 50%;
  overflow: hidden;
  width: 25px;
  height: 25px;
}

.mapCountry20 .mapCountry-img img {
  height: 25px;
  position: relative;
  left: -1.5px;
}

.mapCountry21 {
  top: 385px;
  left: 282px;
}

.mapCountry21 .mapCountry-img {
  border-radius: 50%;
  overflow: hidden;
  width: 26px;
  height: 26px;
  background: none;
}

.mapCountry21 .mapCountry-img img {
  height: 26px;
  position: relative;
  left: -1.5px;
}

.mapCountry22 {
  top: 403px;
  left: 358px;
}

.mapCountry22 .mapCountry-img {
  border-radius: 50%;
  overflow: hidden;
  width: 26px;
  height: 26px;
  background: none;
}

.mapCountry22 .mapCountry-img img {
  height: 26px;
  position: relative;
  left: -1.5px;
}

.mapCountry23 {
  top: 555px;
  left: 855px;
}

.mapCountry23 .mapCountry-img {
  border-radius: 50%;
  overflow: hidden;
  width: 26px;
  height: 26px;
  background: none;
}

.mapCountry23 .mapCountry-img img {
  height: 26px;
  position: relative;
  left: -1.5px;
}

/**/
.trwrap {
  width: 1226px;
  height: auto;
  overflow: hidden;
  padding: 12px 0;
  position: relative;
  margin: 0 auto;
  font-size: 14px;
}

.trwrap .check {
  float: left;
}

.trwrap .check input {
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
}

.trwrap .wpage {
  float: right;
  line-height: 25px;
}

.trwrap .wpage span {
  float: left;
  padding: 0 9px;
}

.trwrap .wpage span strong {
  color: #fe7427;
}

.trwrap .wpage a.wpbtn {
  float: left;
  width: 23px;
  height: 23px;
  line-height: 23px;
  text-align: center;
  border: 1px solid #dfdfdf;
  background: url(../images/wpbtn.png) no-repeat 0 0;
}

.trwrap .wpage a.wpbtn.wpbtn-prev {
  background-position: 0 -23px;
}

.trwrap .wpage a.wpbtn.wpbtn-next {
  background-position: -23px -23px;
}

.trwrap .wpage a.wpbtn.disable {
  cursor: default;
  background-color: #f4f4f4;
}

.trwrap .wpage a.wpbtn.wpbtn-prev.disable {
  background-position: 0 0;
}

.trwrap .wpage a.wpbtn.wpbtn-next.disable {
  background-position: -23px 0;
}

.trwrap .keySearch {
  margin-top: 0;
  margin-left: 18px;
}

.showsel {
  display: none;
}

.showsel.show {
  overflow: visible;
}

.showsel.show,
.showsel.show ul {
  display: block;
}

.sspages {
  width: 100%;
  overflow: hidden;
  text-align: center;
}

.sspages a.pagesLi {
  float: none;
  display: inline-block;
}

span.buytag {
  display: inline-block;
  width: auto;
  line-height: 14px;
  font-size: 12px;
  background-color: #ca4300;
  color: #fff;
  padding: 3px;
  font-weight: 300;
  vertical-align: middle;
  margin-left: 4px;
}

span.servicetag {
  display: inline-block;
  width: auto;
  line-height: 14px;
  font-size: 12px;
  background-color: #f00;
  color: #fff;
  padding: 3px;
  font-weight: 300;
  vertical-align: middle;
  margin-left: 4px;
}

span.connn-prev {
  float: left;
  width: 20px;
  height: 20px;
  overflow: hidden;
  margin-top: 30px;
  line-height: 20px;
  color: #999;
  cursor: pointer;
}

span.connn-next {
  float: right;
  width: 20px;
  height: 20px;
  overflow: hidden;
  margin-top: 30px;
  line-height: 20px;
  color: #999;
  cursor: pointer;
}

span.connn-prev:hover,
span.connn-next:hover {
  color: #000;
}

/**/
/* .helpinfo {
  width: 100%;
  min-width: 1226px;
  padding: 61px 0 30px;
  box-sizing: border-box;
  border-bottom: 1px solid #282828;
  background: #111111;
  margin: 0 auto;
  line-height: 1;
}

.helpinfo:after {
  content: "\0020";
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  overflow: hidden;
}

.helpinfo .helpinfo-item {
  float: left;
  width: 17%;

}

.helpinfo .helpinfo-item.helpinfo-email {
  width: 20%;
}

.helpinfo .helpinfo-item li {
  margin-bottom: 19px;
  color: #aaa;
  font-size: 16px;
}

.helpinfo .helpinfo-title {
  color: #fff;
  font-size: 16px;
  margin: 0;
  margin-bottom: 40px;
}

.helpinfo ul a {
  color: #aaa;
}

.helpinfo ul a:hover {
  color: #ca4300;
}

.helpinfo .helpinfo-qrcode {
  width: 100px;
  height: 120px;
  margin-top: 10px;
  float: left;
  text-align: center;
}

.helpinfo .helpinfo-qrcode img {
  max-width: 100px;
  max-height: 100px;
}

.helpinfo .feature-items .feature-item {
  width: 150px;
  margin: 0 23px;
  text-align: center;
  float: left;
}

.helpinfo .feature-items .feature-item img {
  display: block;
  margin: 0 auto 15px auto;
  width: 67px;
  height: 67px;
}

.helpinfo .feature-items .feature-item h4 {
  color: #333;
}

.helpinfo div p {
  color: #aaa;
  color: #aaa;
  margin-top: -20px;
}

.helpinfo-item ul,
.qrcode_section,
.qrcode {
  margin: 0;
  padding: 0;
}

.helpinfo-item ul li,
.qrcode_section li {
  padding: 0;
  margin: 0;
  list-style: none;
} */

/* .footer {
  width: 100%;
  min-width: 1226px;
  height: 73px;
  text-align: center;
  line-height: 73px;
  font-size: 15px;
  font-weight: 300;
  letter-spacing: 0.3px;
  color: #eaeaea;
  background: #111111;
}

.footer .wrap-lg div:nth-child(2) {
  display: none;
}

.footer span {
  color: #d4b87d;
}

.footer-nav {
  line-height: 40px;
  color: #ccc;
}

.footer-nav span {
  color: #ccc;
}

.footer-nav a {
  color: #ccc;
  margin: 0 6px;
}

.footer-nav span:last-child {
  display: none;
} */

/*底部*/
.qrcode_section {
  width: 100%;
  margin-top: 26px;
}

.qrcode_section > li {
  width: 142px;
  float: left;
  background: #2f2f2f;
  padding-top: 10px;
}

.qrcode_section > li.webo {
  margin-right: 10px;
}

.qrcode_section li img {
  width: 51px;
  height: 51px;
  display: inline-block;
  margin-right: -1px;
}

.qrcode_section li span {
  color: #aaa;
  display: inline-block;
  vertical-align: top;
  font-size: 12px;
  width: 80px;
  height: 12px;
  /*margin-left: 5px;*/
  margin-top: 19px;
}

.qrcode {
  width: 296px;
  height: 175px;
  border-radius: 2px;
  overflow: hidden;
  /*   position: relative;*/
}

.qrcode > li {
  float: left;
  width: 114px;
  height: 175px;
  list-style: none;
  background-color: #2f2f2f;
  padding: 3px 14px 17px;
  overflow: hidden;
}

.qrcode > li.qrcodewb {
  margin-right: 10px;
}

.qrcode > li > img {
  display: block;
  width: 114px;
  height: 114px;
  margin-top: 1px;
}

.qrcode > li > p {
  width: 100%;
  font-size: 10px;
  line-height: 1.4;
  margin-top: 8px;
  letter-spacing: 0.2px;
  text-align: center;
  color: #999999;
}

.qrcode > li > p > span {
  display: block;
}

/** order **/
.orderList {
  background: #fff;
}

.orderList * {
  padding: 0;
  margin: 0;
  font-style: normal;
}

.orderList__header {
  background: #fafafa;
  margin: 30px;
  padding-bottom: 18px;
}

.orderList__header p {
  text-align: center;
  color: #888;
}

.orderList__header h2 {
  text-align: center;
  font-weight: 500;
  font-size: 32px;
  padding-top: 30px;
}

.orderList__header__search {
  padding-top: 20px;
  text-align: center;
}

.orderList__header__search input {
  display: inline-block;
  width: 360px;
  height: 40px;
  line-height: 40px;
  border: 1px solid #d8d8d8;
  border-radius: 4px;
  font-size: 14px;
  color: #999;
  padding: 0 10px;
  outline: none;
}

.orderList__header__search button {
  width: 120px;
  height: 44px;
  background: #ca4300;
  border-radius: 4px;
  font-size: 16px;
  color: #fff;
  border: none;
  margin-left: 8px;
  cursor: pointer;
  outline: none;
}

.orderList__header__search button.disabled {
  background: #ddd;
  cursor: not-allowed;
}

.orderList__header__link {
  text-align: center;
  margin-top: 17px;
}

.orderList__header__link span:last-child {
  margin-left: 30px;
  border-left: 2px solid #cbcbcb;
  padding-left: 30px;
}

.orderList__header__link span {
  font-size: 14px;
  height: 30px;
  line-height: 30px;
  display: inline-block;
  color: #666;
}

.orderList__header__link a {
  color: #ca4300;
}

.orderList__status {
  background: #fafafa;
  margin: 30px;
  padding-bottom: 18px;
  clear: both;
  padding: 18px 30px;
  border: 1px solid #eee;
  display: none;
}

.orderList__status:after {
  content: "\0020";
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  overflow: hidden;
}

.orderList__status ul {
  float: left;
  width: 640px;
}

.orderList__status li {
  float: left;
  list-style: none;
  width: 300px;
  height: 40px;
  line-height: 40px;
}

.orderList__status span {
  display: inline-block;
  width: 100px;
  color: #666;
  text-align: right;
  height: 40px;
  line-height: 40px;
  overflow: hidden;
}

.orderList__status em {
  display: inline-block;
  color: #666;
  padding-left: 15px;
  width: 180px;
  height: 40px;
  line-height: 40px;
  overflow: hidden;
}

.orderList__status div {
  float: right;
  width: 80px;
  height: 80px;
  background: url(../images/order/status-active.png);
}

.orderList__status div.disabled {
  background: url(../images/order/status-disabled.png);
}

.orderList__status div.final {
  background: url(../images/order/status-active.png);
}

.orderList__item {
  border: 1px solid #eee;
  margin: 20px 30px;
}

.orderList__item__header {
  background: #fafafa;
  padding: 18px 30px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.orderList__item__header:after {
  content: "\0020";
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  overflow: hidden;
}

.orderList__item__header ul {
  float: left;
  width: 740px;
}

.orderList__item__header li {
  float: left;
  list-style: none;
  width: 300px;
  line-height: 25px;
}

.orderList__item__header span {
  display: inline-block;
  width: 100px;
  color: #666;
  text-align: right;
  height: 25px;
  line-height: 25px;
  overflow: hidden;
  color: #000;
}

.orderList__item__header em {
  display: inline-block;
  color: #666;
  padding-left: 15px;
  width: 165px;
  line-height: 25px;
  overflow: hidden;
  color: #000;
}

li.orderList__item__header--full {
  width: 640px;
  max-height: 125px;
}

.orderList__item__header--full em {
  width: 525px;
  color: #666;
  font-size: 14px;
  max-height: 125px;
  text-align: left;
  overflow: hidden;
}

.orderList__item__header--full span {
  color: #666;
  font-size: 14px;
  max-height: 125px;
}

.orderList__item__header--half {
  width: auto !important;
  margin-right: 20px;
}

/* .orderList__item__header--half.CSName {
  width: 230px !important;
}
.orderList__item__header--half.CSTelephone {
  width: 250px !important;
}
.orderList__item__header--half.CSEmail {
  width: 270px !important;
} */
.orderList__item__header--half span {
  color: #666;
  font-size: 14px;
}

.orderList__item__header--half em {
  color: #666;
  font-size: 14px;
  padding-left: 5px;
  width: auto;
}

.orderList__item__header div {
  color: #ca4300;
  font-size: 16px;
  height: 50px;
  line-height: 50px;
  padding-left: 25px;
  float: right;
  cursor: pointer;
  position: absolute;
  top: 20px;
  right: 20px;
}

.orderList__item__header div.show {
  background: url(../images/order/show.png) no-repeat center left;
}

.orderList__item__header div.hide {
  background: url(../images/order/hide.png) no-repeat center left;
}

.orderList__item__step {
  width: 100%;
  margin: 20px 0 0 200px;
  display: none;
}

.orderList__item__step:after {
  content: "\0020";
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  overflow: hidden;
}

.orderList__item__step .step {
  width: 50px;
  float: left;
  text-align: center;
}

.orderList__item__step .stepLine {
  width: 70px;
  height: 2px;
  background: #ca4300;
  float: left;
  margin-top: 18px;
}

.orderList__item__step i {
  display: inline-block;
  width: 30px;
  height: 30px;
  border: 1px dashed #ddd;
  padding: 1px;
}

.orderList__item__step .step1.status1 i {
  background: url(../images/order/step1-1.png) no-repeat center left;
}

.orderList__item__step .step1.status2 i {
  background: url(../images/order/step1-2.png) no-repeat center left;
}

.orderList__item__step .step1.status3 i {
  background: url(../images/order/step1-3.png) no-repeat center left;
}

.orderList__item__step .step2.status1 i {
  background: url(../images/order/step2-1.png) no-repeat center left;
}

.orderList__item__step .step2.status2 i {
  background: url(../images/order/step2-2.png) no-repeat center left;
}

.orderList__item__step .step2.status3 i {
  background: url(../images/order/step2-3.png) no-repeat center left;
}

.orderList__item__step .step3.status1 i {
  background: url(../images/order/step3-1.png) no-repeat center left;
}

.orderList__item__step .step3.status2 i {
  background: url(../images/order/step3-2.png) no-repeat center left;
}

.orderList__item__step .step3.status3 i {
  background: url(../images/order/step3-3.png) no-repeat center left;
}

.orderList__item__step .step4.status1 i {
  background: url(../images/order/step4-1.png) no-repeat center left;
}

.orderList__item__step .step4.status2 i {
  background: url(../images/order/step4-2.png) no-repeat center left;
}

.orderList__item__step .step4.status3 i {
  background: url(../images/order/step4-3.png) no-repeat center left;
}

.orderList__item__step span {
  display: inline-block;
  width: 50px;
  font-size: 16px;
}

.orderList__item__step .status1 span {
  color: #999;
}

.orderList__item__step .status2 span {
  color: #000;
}

.orderList__item__step .status3 span {
  color: #ca4300;
}

.orderList__item__option {
  margin-top: 20px;
  padding: 0 10px 20px 0;
  display: none;
}

.orderList__item__option:after {
  content: "\0020";
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  overflow: hidden;
}

.orderList__item__option ul {
  float: left;
  width: 100%;
}

.orderList__item__option li {
  float: left;
  list-style: none;
  width: 495px;
  height: 40px;
  line-height: 40px;
}

.orderList__item__option li:last-child {
  width: 280px;
}

.orderList__item__option li:last-child em {
  width: 130px;
}

.orderList__item__option span {
  display: inline-block;
  width: 120px;
  color: #666;
  text-align: right;
  height: 40px;
  line-height: 40px;
  overflow: hidden;
  padding-left: 15px;
}

.orderList__item__option em {
  display: inline-block;
  color: #666;
  padding-left: 15px;
  width: 340px;
  line-height: 40px;
  overflow: hidden;
}

li.orderList__item__option--full {
  width: 640px;
  max-height: 200px;
  height: auto;
}

li.orderList__item__option--full em {
  width: 490px;
  overflow: hidden;
  max-height: 200px;
  float: right;
}

.orderList__item__option .name {
  background: url(../images/order/name.png) no-repeat 34px center;
}

.orderList__item__option .localtion {
  background: url(../images/order/localtion.png) no-repeat 17px center;
}

.orderList__item__option .date {
  background: url(../images/order/date.png) no-repeat center left;
}

/** order **/

.footer_img {
  width: 25%;
  float: right;
}

.theme-footer {
  margin-top: 0;
  line-height: 73px;
  height: 73px;
  text-align: center;
  color: #aaa;
}

.theme-footer a {
  color: #aaa;
  display: inline-block;
  margin: 0 30px;
}

.theme-footer span {
  color: #aaa;
}

.theme-footer img {
  display: inline;
  padding-top: 26px;
}

.minicart .minicart_num {
  margin: 0 1px 0 -2px;
}

.minicart .minicart_num img {
  display: inline-block;
  width: 14px;
  height: 14px;
  vertical-align: middle;
  margin-right: 8px;
  position: relative;
  top: -2px;
}

.kf5-support-chat #kf5-support-btn {
  width: 0;
  height: 0;
  display: none;
  overflow: hidden;
  border: 0;
  padding: 0;
}

.kf5-support-chat #kf5-support-btn span {
  /*color: yellow;*/
}

#kf5-support-block {
  right: 60px;
}

/**/
/* .swiper-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 99;
  width: 100%;
  text-align: center;
}

.swiper-pagination {
  position: relative;
  display: inline-block;
  top: -6px;
}

.swiper-pagination-bullet {
  display: inline-block;
  width: 10px;
  height: 10px;
  border: 1px solid #ca4300;
  border-radius: 50%;
  background: transparent;
  opacity: 1;
  margin: 0 4px;
}

.swiper-pagination-bullet:hover {
  width: 8px;
  height: 8px;
  border-width: 2px;
}

.swiper-pagination-bullet-active {
  background: #ca4300;
}

.swiper-button-next {
  position: relative;
  width: 12px;
  height: 24px;
  overflow: hidden;
  right: auto;
  top: auto;
  left: auto;
  display: inline-block;
  background-size: auto 60%;
  margin-top: 0;
}

.swiper-button-prev {
  position: relative;
  width: 12px;
  height: 24px;
  overflow: hidden;
  right: auto;
  top: auto;
  left: auto;
  display: inline-block;
  background-size: auto 60%;
  margin-top: 0;
}

.swiper-button-next.swiper-button-disabled,
.swiper-button-prev.swiper-button-disabled {
  opacity: 0;
} */

/**/
.buyicon {
  position: absolute;
  display: block;
  width: 82px;
  height: 34px;
  overflow: hidden;
  background: url(../images/buyicon.png) no-repeat 0 0;
  z-index: 2;
  top: 0;
  left: 0;
}

/**/
.topsel {
  width: 100%;
  height: 62px;
  line-height: 62px;
  border-bottom: 3px solid #f2f2f2;
}

.topsel ul {
  margin: 0;
  padding: 0;
}

.topsel ul li {
  float: left;
  width: 120px;
  text-align: center;
  list-style: none;
  font-size: 16px;
  color: #000;
}

.topsel ul li span {
  display: block;
  width: 100%;
  cursor: pointer;
}

.topsel ul li span:hover,
.contact_phone a {
  color: #ca4300;
}

.topsel ul li span.active {
  font-weight: bold;
  cursor: default;
  border-bottom: 3px solid #ca4300;
}

.contact_phone,
.contact_addr {
  padding: 35px 0;
  font-size: 14px;
}

.contact_phone p,
.contact_addr p {
  line-height: 26px;
  padding: 0;
  margin: 0;
}

.contact_phone img {
  display: block;
  max-width: 100%;
  margin-top: 30px;
}

.contact_addr {
  overflow: hidden;
  min-height: 615px;
  font-size: 14px;
}

.contact_addr h3 {
  font-size: 16px;
  margin: 0 0 6px;
}

.contact_addr h5 {
  font-size: 16px;
  margin: 24px 0 12px;
}

.contact_addr ul {
  margin: 0;
  padding: 0;
}

.contact_addr ul li {
  list-style: none;
  padding-left: 2.5em;
  text-indent: -2.5em;
  font-size: 14px;
}

.contact_phone h3 {
  font-size: 16px;
  margin: 12px 0 0;
}

.contactab {
  width: 400px;
  border-collapse: collapse;
  margin-top: 10px;
  text-align: center;
}

.contactab td {
  padding: 8px 12px;
  border: 1px solid #ccc;
}

.contactab thead td {
  background-color: #eee;
  font-weight: bold;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #aaa;
}

input:-moz-placeholder,
textarea:-moz-placeholder {
  color: #aaa;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #aaa;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #aaa;
}

.placeholder {
  color: #aaa;
}

.sgs-detail {
  color: #aaa;
  font-size: 16px;
  min-width: 1226px;
  max-width: 1226px;
  margin: 0 auto;
  position: relative;
  top: -10px;
}

.sgs-detail-title {
  color: #aaa;
  cursor: pointer;
}

.sgs-detail-title:hover {
  color: #ca4300;
}

.chapters:after {
  content: "";
  clear: both;
  display: block;
}

.summary {
  border: 1px dashed #333;
  font-size: 14px;
  color: #000;
}

/*下载课表需要填写的信息窗口*/
.download-btn {
  cursor: pointer;
}

.down-load-pop {
  display: none;
}

.down-load-pop .layout {
  background: rgba(0, 0, 0, 0.7);
  width: 1000px;
  height: 500px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
}

.down-load-pop .popBox {
  width: 0;
  height: 0;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999999;
  background: #fff;
  border-radius: 5px;
  margin: auto;
  text-align: center;
  padding: 30px;
  opacity: 0;
}

.down-load-pop .popBox b {
  font-size: 30px;
  border-bottom: 2px solid #aeaeae;
  display: inline;
  padding-bottom: 20px;
  font-weight: normal;
}

.down-load-pop .popBox p {
  color: #666;
  padding: 20px 0 0 0;
  font-size: 14px;
  margin: 20px 0 0 0;
}

.down-load-pop .popBox em {
  color: #ca4300;
  font-size: 12px;
  font-style: normal;
}

.down-load-pop .popBox input:not([type="checkbox"]) {
  width: 348px;
  height: 48px;
  margin-top: 20px;
  outline: none;
  border: 1px solid #666;
  color: #888;
  padding-left: 10px;
  box-sizing: border-box;
}

.down-load-pop .popBox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  vertical-align: bottom;
}

.down-load-pop .popBox input#phone,
.down-load-pop .popBox input#city {
  margin-right: 0;
}

.down-load-pop .popBox input:focus {
  border: 1px solid #ca4300;
}

.down-load-pop .popBox input.focus {
  border: 1px solid #ca4300;
}

.down-load-pop .popBox textarea {
  border: 1px solid #666;
  width: 405px;
  height: 110px;
  margin-top: 20px;
  padding: 5px;
}

.down-load-pop .popBox .content {
  display: none;
}

.down-load-pop .popBox button {
  background: #ca4300;
  color: #fff;
  height: 48px;
  line-height: 48px;
  text-align: center;
  width: 348px;
  border: 0;
  cursor: pointer;
  margin-top: 20px;
  font-size: 16px;
}

.down-load-pop .popBox .close {
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  right: 10px;
  font-style: normal;
}

/*下载交互第二版*/
.download-btn2 {
  cursor: pointer;
}

.down-load-pop2 {
  display: none;
}

.down-load-pop2 .layout {
  background: rgba(0, 0, 0, 0.7);
  width: 1000px;
  height: 500px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
}

.down-load-pop2 .popBox {
  width: 0;
  height: 0;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999999;
  background: #fff;
  border-radius: 5px;
  margin: auto;
  text-align: center;
  padding: 32px 29px;
  opacity: 0;
  box-sizing: border-box;
}

.down-load-pop2 .popBox b {
  font-size: 30px;
  border-bottom: 2px solid #aeaeae;
  display: inline;
  padding-bottom: 20px;
  font-weight: normal;
}

.down-load-pop2 .popBox p {
  color: #666;
  padding: 20px 0 0 0;
  font-size: 14px;
  margin: 20px 0 10px 0;
}

.down-load-pop2 .popBox em {
  color: #ca4300;
  font-size: 12px;
  font-style: normal;
}

.down-load-pop2 .popBox input[type="text"] {
  width: 176px;
  height: 35px;
  margin-top: 10px;
  outline: none;
  border: 1px solid #dedede;
  color: #888;
  padding-left: 10px;
  box-sizing: border-box;
}

.down-load-pop2 .popBox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  vertical-align: bottom;
}

.down-load-pop2 .popBox input[type="radio"] {
  margin-left: 0;
}

.down-load-pop2 .popBox input[type="radio"]:focus {
  color: #ca4300;
}

.down-load-pop2 .popBox input#phone,
.down-load-pop2 .popBox input#city {
  margin-right: 0;
}

.down-load-pop2 .popBox input:focus,
.down-load-pop2 .popBox textarea:focus {
  border: 1px solid #ca4300;
  outline: none;
}

.down-load-pop2 .popBox input.focus {
  border: 1px solid #ca4300;
}

.down-load-pop2 .popBox textarea {
  border: 1px solid #dedede;
  width: 364px;
  height: 66px;
  margin-top: 10px;
  padding: 5px;
}

.down-load-pop2 .popBox #coopName {
  border: none;
  border-bottom: 1px solid #333;
  flex: 1;
  margin-top: 0;
  height: 17px;
  padding: 0;
  margin-left: 14px;
}

.down-load-pop2 .popBox #coopName:focus {
  border: none;
  border-bottom: 1px solid #333;
}

.down-load-pop2 .popBox .content {
  display: none;
}

.down-load-pop2 .popBox button {
  background: #ca4300;
  color: #fff;
  height: 40px;
  line-height: 40px;
  text-align: center;
  width: 364px;
  border: 0;
  cursor: pointer;
  margin-top: 10px;
  font-size: 16px;
}

.down-load-pop2 .popBox .close {
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  right: 10px;
  font-style: normal;
}

/** 报告真伪 **/
/* .clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.clearfix {
  *+height: 1%;
} */

.docCheck-wrap {
  width: 1226px;
  margin: 0 auto;
}

.docCheck-describe {
  padding-bottom: 90px;
  margin-top: -20px;
}

.docCheck-describe p {
  line-height: 25px;
  padding: 0;
  margin: 0;
  padding-top: 43px;
}

.docCheck-describe b {
  color: #333333;
}

.docCheck-describe span {
  font-size: 14px;
  color: #858585;
  font-style: oblique;
}

.docCheck-tab {
  border-bottom: 1px solid #DEDEDE;
  width: 880px;
}

.docCheck-tab ul {
  float: left;
  margin: 0;
  padding: 0;
  display: flex;
}

.docCheck-tab li {
  color: #878787;
  text-align: center;
  list-style: none;
  width: 250px;
  height: 60px;
  line-height: 59px;
  background: #FFFFFF;
  border-bottom: 1px solid #D2D2D2;
  cursor: pointer;
  margin-bottom: -1px;
  font-size: 18px;
  position: relative;
}

.docCheck-tab li.active {
  color: #ca4300;
  border-bottom: 2px solid #ca4300;
  font-weight: bold;
}

.docCheck-con {
  /* margin-top: 60px; */
}

.docCheck-con-box {
  padding-bottom: 30px;
}

/* .docCheck-con-box.activeBox {
  display: block;
} */

.docCheck-con-box h2 {
  color: #ca4300;
  margin-top: 60px;
}

.docCheck-con-box h3 {
  padding-top: 30px;
  color: #000;
  padding-left: 35px;
}

.docCheck-con-box h3 i {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #000;
}

.docCheck-con-box h3 em {
  font-style: normal;
  padding-left: 10px;
}

.docCheck-con-box > p {
  padding-left: 55px;
  padding-top: 20px;
  line-height: 30px;
}

.docCheck-con-box > p.tips {
  color: #858585;
  font-size: 14px;
}

.docCheck-con-box > p a {
  color: #ca4300;
  text-decoration: underline;
}

.docCheck-con-box > p em {
  color: #ca4300;
  font-style: normal;
}

.docCheck-con-box .step {
  padding-left: 55px;
}

.docCheck-con-box .step p,
.docCheck-con-box .step span {
  float: left;
  display: block;
}

.docCheck-con-box .step p {
  background: #f8f8f8;
  border: 1px solid #d9d9d9;
  border-radius: 10px;
  height: 65px;
  line-height: 65px;
  color: #7d7c7c;
  text-align: center;
  padding: 0 20px;
}

.docCheck-con-box .step i {
  font-size: 30px;
  font-style: normal;
  float: left;
}

.docCheck-con-box .step em {
  font-style: normal;
  float: left;
  padding-left: 15px;
}

.docCheck-con-box .step span {
  /* font-size: 36px; */
  /* color: #d4d4d4; */
  margin: 23px 30px 0 30px;
  display: inline-block;
  width: 16px;
  height: 26px;
  background: url("./../images/DocCheck/arrow.png") no-repeat 0 0;
}

.docCheck-con-box a.btn {
  background: #ca4300;
  width: 270px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  display: block;
  color: #fff;
  border-radius: 10px;
  margin-left: 55px;
  margin-top: 30px;
}

.docCheck-con-box a.btn:hover {
  transition: 0.4s;
  background: #cc5500;
}

.docCheck-con-box img {
  padding-left: 55px;
  padding-top: 30px;
}

.docCheck-con-box ol,
.docCheck-con-box ul {
  padding-left: 55px;
  padding-top: 20px;
}

.docCheck-con-box li {
  line-height: 30px;
}

.docCheck-con-box ul li {
  list-style: none;
}

.docCheck-con-box ul li span {
  display: inline-block;
}

.docCheck-con-box ul li span:first-child {
  width: 270px;
}

.docCheck-con-box ul li span:nth-child(2) {
  width: 220px;
}

/* sgs合作核心供应商 */
.coreSupplier-guidance {
  margin-top: 35px;
  background: #f7f7f7;
  border-radius: 20px;
  padding: 50px 60px;
  position: relative;
}

.coreSupplier-guidance p {
  text-indent: 2em;
  line-height: 25px;
  color: #333;
  font-size: 16px;
}

.coreSupplier-guidance i {
  position: absolute;
  bottom: -55px;
  left: 580px;
  display: block;
  width: 0;
  height: 0;
  border-width: 35px;
  border-style: solid;
  border-color: #f7f7f7 transparent transparent transparent;
}

.coreSupplier-title {
  text-align: center;
  position: relative;
  margin-top: 50px;
  padding-bottom: 50px;
}

.coreSupplier-title .line {
  height: 1px;
  width: 100%;
  background: #333;
  position: absolute;
  top: 13px;
  left: 0;
  z-index: -1;
}

.coreSupplier-title span {
  background: #fff;
  display: inline-block;
  padding: 0px 15px;
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.coreSupplier-title em {
  color: #828282;
  font-style: normal;
  font-size: 14px;
}

.coreSupplier-img {
  text-align: center;
  height: 343px;
}

.coreSupplier-table table {
  width: 100%;
  padding-bottom: 70px;
}

.coreSupplier-table table thead tr {
  background: #eee;
}

.coreSupplier-table table tbody tr:nth-child(odd) {
  background: #fff;
}

.coreSupplier-table table tbody tr:nth-child(even) {
  background: #eee;
}

.coreSupplier-table table th {
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 14px;
  color: #333;
}

.coreSupplier-table table th:nth-child(3) {
  border-left: 3px solid #fff;
}

.coreSupplier-table table td {
  height: 30px;
  line-height: 30px;
  text-align: left;
  padding-left: 60px;
}

.coreSupplier-table table td span {
  font-size: 14px;
  color: #333;
}

.coreSupplier-table table td:nth-child(3n) {
  border-left: 3px solid #fff;
}

.coreSupplier-table table td a {
  background: url("./../../public/images/coreSupplier/icon.png") no-repeat 0 5px;
  display: inline-block;
  padding-left: 20px;
  margin-right: 25px;
  font-size: 12px;
  color: #878787;
}

.coreSupplier-table table td a:hover {
  color: #ca4300;
}

/**
  * 新版首页
  * namespace: n_
**/
.n_wrap {
  width: 1226px;
  margin: 0 auto;
}

.n_wrap * {
  padding: 0;
  margin: 0;
  font-family: "Source Han Sans CN";
}

.n_wrap li,
.n_wrap ul {
  list-style: none;
}

/* .n_topBar {
  background: #fff;
  height: 80px;
  line-height: 80px;
}

.n_topBar .logo {
  display: block;
  float: left;
}

.n_topBar .logo img {
  height: 80px;
  float: left;
}

.n_topBar .logo span {
  display: inline-block;
  height: 22px;
  line-height: 22px;
  float: left;
  width: 198px;
  background: url('./../../public/images/newPage/logo-des.png') no-repeat 0 0;
  margin-left: 15px;
  margin-top: 29px;
}

.n_topBar .menus {
  float: right;
  margin-top: 31px;
  height: 17px;
}

.n_topBar .menus span,
.n_topBar .menus em,
.n_topBar .menus a,
.n_topBar .menus label {
  color: #878787;
  font-size: 14px;
  font-style: normal;
  height: 17px;
  line-height: 17px;
  display: inline-block;
  float: left;
}

.n_topBar .menus em {
  padding: 0 15px;
}

.n_topBar .menus .blod {
  color: #ca4300;
  cursor: pointer;
  font-weight: 700;
}

.n_topBar .menus .segmenttaion {
  color: #e5e5e5;
}

.n_topBar .menus .carBox {
  position: relative;
}

.n_topBar .menus .car {
  width: 20px;
  height: 17px;
}

.n_topBar .menus .carBox i {
  display: block;
  background: #ca4300;
  border-radius: 50%;
  position: absolute;
  top: -3px;
  right: 10px;
  color: #fff;
  font-size: 12px;
  width: 12px;
  text-align: center;
  height: 12px;
  line-height: 12px;
  font-style: normal;
} */

/* .n_searchModule {
  height: 130px;
  background: url('./../../public/images/newPage/search_bg.jpg') 50% 50% no-repeat;
  width: 100%
}

.n_searchModule .input {
  width: 624px;
  height: 40px;
  line-height: 40px;
  margin-left: 300px;
  position: relative;
  padding-top: 43px;

}

.n_searchModule .input i {
  position: absolute;
  top: 51px;
  left: 12px;
  display: block;
  width: 23px;
  height: 23px;
  background: url('./../../public/images/newPage/search_icon.png') 0 0 no-repeat;
}

.n_searchModule input {
  background: #fff;
  border: 0;
  outline: 0;
  color: #b3b3b3;
  font-size: 12px;
  height: 40px;
  line-height: 40px;
  width: 440px;
  float: left;
  padding-left: 48px;
  box-shadow: -0 0 0 green,
      0 0 0 blue,
      10px 10px 13px rgba(0, 0, 0, .3),
      0 0 0 yellow;
}

.n_searchModule button,
.n_searchModule a {
  width: 109px;
  background: #ca4300;
  color: #fff;
  text-align: center;
  font-size: 18px;
  height: 40px;
  line-height: 40px;
  border: 0;
  outline: 0;
  float: left;
  cursor: pointer;
  box-shadow: -0 0 0 green,
      0 0 0 blue,
      10px 10px 13px rgba(0, 0, 0, .3),
      0 0 0 yellow;
}

.n_searchModule button:hover,
.n_searchModule a:hover {
  background: #cc5500;
  transform: .5s;
}

.n_searchModule .linkageWord {
  position: absolute;
  top: 83px;
  left: 300px;
  border: 1px solid #ca4300;
  width: 595px;
  z-index: 2;
  background: #fff;
  display: none;
}

.n_searchModule .linkageWord ul {
  margin: 0;
}

.n_searchModule .linkageWord li {
  height: 35px;
  line-height: 35px;
  overflow: hidden;
  list-style: none;
  padding-left: 15px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 383px;
}

.navSearchBox .linkageWord li:hover {
  background: #f5f5f5;
}

.n_searchModule .hotWord {
  width: 580px;
  margin-left: 300px;
  padding-top: 8px;
  height: 20px;
  overflow: hidden;
}

.n_searchModule .hotWord em,
.n_searchModule .hotWord span {
  color: #e8e8e8;
  font-size: 12px;
  padding-left: 12px;
  height: 20px;
  line-height: 20px;
  font-style: normal;
}

.n_searchModule .hotWord span {
  cursor: pointer;
}

.n_searchModule .hotWord span.hot {
  color: #ca4300;
} */

/* .n_nav {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  height: 38px;
  width: 100%;
}

.n_nav ul {
  width: 1226px;
  margin: 0 auto;
  height: 39px;
  padding: 0;
}

.n_nav li {
  list-style: none;
  float: left;
  height: 39px;
  line-height: 39px;
  text-align: center;

}

.n_nav li.active {
  background: #ca4300;
}

.n_nav li.active a,
.n_nav li.active a:hover {
  color: #fff;
}

.n_nav li a {
  color: #333;
  font-size: 15px;
  width: 160px;
  display: inline-block;
}

.n_nav li a:hover {
  color: #ca4300;

}

.n_nav li span {
  color: #dfdfdf;
}

.n_nav li.active span {
  color: #ca4300;
} */

.n_category {
  height: 412px;
  overflow: hidden;
  width: 100%;
  position: absolute;
}

.n_category #service,
.n_category #industry {
  width: 1226px;
  height: 412px;
  position: relative;
}

.n_category .side {
  margin: 0;
  padding: 0;
  width: 244px;
  height: 412px;
  overflow: hidden;
  float: left;
  background: #fff;
  border-right: 1px solid #f6f6f6;
}

.n_category .side li {
  height: 16px;
  clear: both;
  list-style: none;
  width: 245px;
  height: 33px;
  line-height: 33px;
  cursor: pointer;
}

.n_category .side li.active {
  background: #f6f6f6;
}

.n_category .side .icon {
  display: block;
  width: 16px;
  height: 16px;
  float: left;
  margin: 9px 10px 0 20px;
}
/* 
.n_category .side .icon1 {
  background: url("./../../public/images/newPage/industry/icon-1.jpg") 0 0 no-repeat;
}

.n_category .side .icon2 {
  background: url("./../../public/images/newPage/industry/icon-2.jpg") 0 0 no-repeat;
}

.n_category .side .icon3 {
  background: url("./../../public/images/newPage/industry/icon-3.jpg") 0 0 no-repeat;
}

.n_category .side .icon4 {
  background: url("./../../public/images/newPage/industry/icon-4.jpg") 0 0 no-repeat;
}

.n_category .side .icon5 {
  background: url("./../../public/images/newPage/industry/icon-5.jpg") 0 0 no-repeat;
}

.n_category .side .icon6 {
  background: url("./../../public/images/newPage/industry/icon-6.jpg") 0 0 no-repeat;
}

.n_category .side .icon7 {
  background: url("./../../public/images/newPage/industry/icon-7.jpg") 0 0 no-repeat;
}

.n_category .side .icon8 {
  background: url("./../../public/images/newPage/industry/icon-8.jpg") 0 0 no-repeat;
}

.n_category .side .icon9 {
  background: url("./../../public/images/newPage/industry/icon-9.jpg") 0 0 no-repeat;
}

.n_category .side .icon10 {
  background: url("./../../public/images/newPage/industry/icon-10.jpg") 0 0 no-repeat;
}

.n_category .side .icon11 {
  background: url("./../../public/images/newPage/industry/icon-11.jpg") 0 0 no-repeat;
}

.n_category .side li.active .icon1 {
  background: url("./../../public/images/newPage/industry/icon-1-a.jpg") 0 0 no-repeat;
}

.n_category .side li.active .icon2 {
  background: url("./../../public/images/newPage/industry/icon-2-a.jpg") 0 0 no-repeat;
}

.n_category .side li.active .icon3 {
  background: url("./../../public/images/newPage/industry/icon-3-a.jpg") 0 0 no-repeat;
}

.n_category .side li.active .icon4 {
  background: url("./../../public/images/newPage/industry/icon-4-a.jpg") 0 0 no-repeat;
}

.n_category .side li.active .icon5 {
  background: url("./../../public/images/newPage/industry/icon-5-a.jpg") 0 0 no-repeat;
}

.n_category .side li.active .icon6 {
  background: url("./../../public/images/newPage/industry/icon-6-a.jpg") 0 0 no-repeat;
}

.n_category .side li.active .icon7 {
  background: url("./../../public/images/newPage/industry/icon-7-a.jpg") 0 0 no-repeat;
}

.n_category .side li.active .icon8 {
  background: url("./../../public/images/newPage/industry/icon-8-a.jpg") 0 0 no-repeat;
}

.n_category .side li.active .icon9 {
  background: url("./../../public/images/newPage/industry/icon-9-a.jpg") 0 0 no-repeat;
}

.n_category .side li.active .icon10 {
  background: url("./../../public/images/newPage/industry/icon-10-a.jpg") 0 0 no-repeat;
}

.n_category .side li.active .icon11 {
  background: url("./../../public/images/newPage/industry/icon-11-a.jpg") 0 0 no-repeat;
} */

.n_category .side a {
  color: #333;
  font-size: 15px;
  display: block;
  float: left;
  font-weight: 400;
  padding-left: 30px;
}

.n_category .side a:hover,
.n_category .side li.active a {
  color: #ca4300;
}

.n_category .side span {
  float: right;
  display: block;
  color: #878787;
  font-size: 15px;
  margin-right: 27px;
  /* background: url("./../images/home/<USER>/home_menu_icon.png") no-repeat; */
  width: 6px;
  height: 12px;
  /* margin-top: 11px; */
}

.n_category .side li.active span {
  display: block;
  color: #ca4300;
  /* background: url("./../images/home/<USER>/home_menu_icon_activity.png") no-repeat; */
}

.n_category .main {
  display: none;
  position: absolute;
  left: 244px;
  top: 0;
  z-index: 3;
  background: #fff;
  width: 981px;
  height: 412px;
  border-left: 0;
  border-top: 0;
}

.n_category .main .list {
  display: none;
}

.n_category .main .list.active {
  display: block;
}

.n_category .main-list {
  width: 730px;
  float: left;
  padding: 0 15px 25px 25px;
}

.n_category .main-list dl {
  margin: 19px 0;
  width: 730px;
  height: 52px;
  overflow: hidden;
  text-align: left;
}

.n_category .main-list .main-list-line {
  border-bottom: 1px dashed #ddd;
  width: 730px;
}

.n_category .main-list dl:last-child {
  border-bottom: 0;
}

.n_category .main-list dt,
.n_category .main-list dd {
  float: left;
  width: 158px;
  padding: 0;
  margin: 0;
  text-align: left;
}

.n_category .main-list dt a:hover,
.n_category .main-list dd a:hover,
.n_category .main-list dd a.hot {
  color: #ca4300;
}

.n_category .main-list dt {
  width: 67px;
  line-height: 18px;
  margin-top: 3px;
  margin-right: 30px;
}

.n_category .main-list dt span {
  color: #333;
  display: block;
  height: 36px;
  float: left;
  width: 10px;
  font-size: 20px;
}

.n_category .main-list dt span i {
  display: block;
  width: 5px;
  height: 5px;
  background: #333;
  margin-top: 5px;
}

.n_category .main-list dt a {
  display: block;
  float: left;
  /* width: 57px; */
  font-size: 14px;
  color: #333;
  width: 67px;
  height: 36px;
  font-weight: bold;
}

.n_category .main-list dt em {
  display: block;
  float: left;
  font-size: 14px;
  color: #333;
  width: 67px;
  height: 36px;
  font-weight: bold;
  font-style: normal;
}

.n_category .main-list dd {
  height: 26px;
  line-height: 26px;
}

.n_category .main-list dd i {
  width: 20px;
  height: 14px;
  display: inline-block;
  background: url(../images/nav-hot.png) no-repeat 0 0;
  float: left;
  margin-top: 7px;
}

.n_category .main-list dd a {
  color: #6d6d6d;
  font-size: 14px;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 26px;
  line-height: 26px;
  display: inline-block;
  width: 130px;
  overflow: hidden;
  float: left;
}

.n_category .second {
  float: left;
  width: 200px;
  background: #f5f5f5;
  height: 424px;
}

.n_category .second li {
  height: 33px;
  line-height: 33px;
}

.n_category .second li a {
  color: #333;
  font-size: 14px;
  border-left: 2px solid #f5f5f5;
  padding-left: 20px;
  font-weight: 400;
}

.n_category .second li.active {
  background: #fff;
}

.n_category .second li.active a {
  color: #ca4300;
  border-left: 2px solid #ca4300;
}

.n_category .secondList {
  float: left;
  width: 780px;
}

.n_category .dashedLine {
  width: 100%;
  border-top: 1px dashed #ddd;
  margin: 20px 0;
}

.n_category .ads-mt {
  position: absolute;
  top: 0;
  right: 0;
}

.n_category .main-list2 {
  float: left;
  width: 520px;
  padding: 25px 25px 25px 25px;
  height: 363px;
  overflow: hidden;
  box-sizing: content-box;
}

.n_category .main-list2 label {
  width: 100%;
  overflow: hidden;
  height: 100%;
  display: block;
}

.n_category .main-list2 p {
  font-size: 14px;
  color: #333;
  padding-bottom: 10px;
  font-weight: bold;
}

.n_category .main-list2 span,
.n_category .main-list2 a {
  display: inline-block;
  width: 170px;
  height: 25px;
  line-height: 25px;
  font-size: 14px;
  color: #666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.n_category .main-list2 a.hot,
.n_category .main-list2 a:hover {
  color: #ca4300;
}

.n_category .main-list2 .cleafix {
  font-size: 14px;
  color: #333;
  padding-bottom: 5px;
  font-weight: bold;
  display: block;
}

.n_category .ads {
  width: 185px;
  float: right;
  padding-top: 25px;
  padding-right: 20px;
}

.n_category .ads ul {
  height: 294px;
  overflow: hidden;
}

.n_category .ads li {
  list-style: none;
  margin-bottom: 30px;
}

.n_category .ads li a {
  width: 185px;
  height: 66px;
  background-size: cover;
  display: block;
}

.n_category .ads img {
  width: 185px;
  height: 66px;
  background: #ddd;
}

.n_category .ads h3,
.n_category .ads p {
  width: 185px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

.n_category .ads h3 {
  color: #333;
  font-size: 14px;
  padding-top: 10px;
}

.n_category .ads a:hover h3 {
  color: #ca4300;
}

.n_category .ads p {
  color: #818181;
  font-size: 12px;
  padding-top: 5px;
}

.n_category .ads .moreService {
  text-align: center;
  font-size: 14px;
}

.n_category .ads .moreService a {
  color: #ca4300;
}

.n_category .ads .moreService i {
  display: inline-block;
  width: 15px;
  height: 10px;
  background: url(../images/nav-more.png) no-repeat 0 0;
}

/* .n_banner {
  height: 412px;
  width: 981px;
  position: absolute;
  right: 0;
  top: 0;
}

.n_banner .banner,
.n_banner .swiper-wrapper,
.n_banner .swiper-container {
  height: 412px;
}

.n_banner .swiper-button-prev,
.n_banner .swiper-button-next {
  width: 50px;
  height: 50px;
  top: 165px;
  position: absolute;
}

.n_banner .swiper-button-prev {
  background: url('./../../public/images/newPage/prev.png') 0 0 no-repeat;
  left: 10px;
}

.n_banner .swiper-button-next {
  background: url('./../../public/images/newPage/next.png') 0 0 no-repeat;
  right: 10px;
}

.n_banner .swiper-pagination-bullet {
  width: 9px;
  height: 9px;
}

.n_banner .swiper-pagination-bullet {
  width: 11px;
  height: 11px;
  margin: 0 5px;
}

.n_banner .swiper-pagination-bullet:hover {
  width: 9px;
  height: 9px;
  border-width: 2px;
}

.n_banner .swiper-pagination {
  top: -15px;
}

.n_banner .bannerTitle {
  top: 100px;
}

.n_banner .bannerMore {
  left: 415px;
  bottom: 90px;
}

.n_banner .bannermoreword {
  bottom: 90px;
} */

.dialog {
  width: 100%;
  height: 100vh;
  display: none;
}

.dialog .dialog-mask {
  position: fixed;
  z-index: 99997;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.4);
}

.dialog .dialog-box {
  background: #fff;
  width: 400px;
  border-radius: 10px;
  position: fixed;
  z-index: 999998;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
}

.dialog .dialog-title {
  font-size: 18px;
  font-weight: bold;
}

.dialog .dialog-content {
  padding-top: 20px;
}

.dialog .dialog-btns {
  text-align: right;
  padding-top: 20px;
}

.dialog .dialog-btns button {
  padding: 10px 20px;
  border-radius: 3px;
  margin-left: 10px;
}

.dialog .dialog-btns button:first-child {
  background: #ca4300;
  color: #fff;
}


.isShowLoginBg {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.isLogin_bg {
  position: absolute;
  background: #000;
  width: 100%;
  height: 100%;
  opacity: 0.3;
}

.isLogin_box {
  background: #fff;
  position: absolute;
}

.user_login {
  width: 500px !important;
  padding-top: 40px !important;
}

.login {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}


.el-dialog__wrapper {
  /* display: flex !important; */
  align-items: center !important;
  justify-content: center !important;
  background: none;
}

.Modal_box {
  border-radius: 12px !important;
}


.Modal_box .el-dialog__body {
  padding: 0 !important;
}

.Modal_box .el-dialog {
  width: 850px !important;
  height: 575px !important;
  margin-top: 0 !important;
}

.Modal_box .el-dialog__header {
  border-bottom: none !important;
}

.Modal_box .login-wrap {
  display: flex !important;
  width: 850px !important;
  min-height: 400px !important;
}

.Modal_box .login-wrap .login-text {
  width: 270px !important;
  height: 100% !important;
  background: #fff !important;
  border-left: 1px solid #ccc !important;
  padding: 50px 0px 15px 75px !important;
  margin-bottom: 20px !important;
}

.Modal_box .login-wrap .login-text .title {
  margin-bottom: 15px !important;
}

.Modal_box .login-wrap .login-text .logo {
  margin-bottom: 20px !important;
}

.Modal_box .el-input__suffix-inner {
  font-size: 16px !important;
  font-family: Microsoft YaHei;
  font-weight: 400 !important;
  color: #CA4300 !important;
  margin-right: 22px !important;
  cursor: pointer !important;
}

.Modal_box .link-color {
  color: inherit !important;
}

/* 登录弹窗个性化配置 */
.login-components .login-module .side {
  border-radius: 0 12px 12px 0 !important;
  border-left: 1px solid #ccc !important;
  height: 440px !important;
}
/* 登录弹窗个性化配置 */