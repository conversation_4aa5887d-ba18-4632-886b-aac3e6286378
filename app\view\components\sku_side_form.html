<div :class="{side_line: true, step: !isSuccess}"></div>
<div class="side_wrap" v-if='!isSuccess' v-loading="loading">
  <h2>需要更多信息？</h2>
  <p class="des">我们最快2小时内联系您</p>
  <el-form ref="form" :model="form" :rules="rules">
    <el-form-item label="咨询内容" prop="content">
      <el-input placeholder="请输入您的咨询内容" v-model="form.content" type='textarea' :rows="2" resize='none' maxlength="1000">
      </el-input>
    </el-form-item>
    <el-form-item label="您的称呼" prop="customer">
      <el-input placeholder="请输入您的称呼" v-model="form.customer" 　maxlength="25"></el-input>
    </el-form-item>
    <el-form-item class="side_tab">
      <span :class="{active: tab === 'phone'}" @click="handleTab('phone')">手机号码</span>
      <span :class="{active: tab === 'email'}" @click="handleTab('email')">电子邮箱</span>
    </el-form-item>
    <el-form-item label="   " v-if='tab === "phone"' prop="phone" style="margin-bottom: 0 !important;">
      <el-input placeholder="请输入您的手机号码" v-model="form.phone" 　maxlength="50" ref='phone'
        :disabled='isLogin && disablePhone' @input='handleInputEmail'>
        <template slot="prepend">+86</template>
      </el-input>
    </el-form-item>
    <el-form-item label="   " v-if='tab === "email"' prop="email" style="margin-bottom: 0 !important;">
      <el-input placeholder="请输入您的电子邮箱" v-model="form.email" 　maxlength="80" ref='email'
        :disabled='isLogin && disableMail' @input='handleInputEmail'></el-input>
    </el-form-item>
    <el-form-item label="   " class="side_tips" v-if="!isLogin">
      <p>*自动注册会员，在线查看咨询进度</p>
    </el-form-item>
    <el-form-item label="所在城市" prop="provice">
      <el-select placeholder="请选择所在省份或地区" v-model="form.provice" filterable style="width: 100%;">
        <el-option v-for="(item, index) in provice" :key="item.orgName" :label="item.orgName" :value="item.orgName">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <a class="button" @click='handleSubmit'>立即咨询</a>
      <div style="opacity: 0; height: 0; position: fixed; right: -100px; bottom: -100px;">
        <button id="TencentCaptcha" data-appid="193600977" data-cbfn="callback" type="button">验证</button>
      </div>
    </el-form-item>
    <el-form-item class="side_protocol">
      <el-checkbox v-model="approve">
        <template v-if='contact_approve_show'>提交后自动注册会员，获取后续信息<br /></template>
        我已阅读并同意
        <template v-if='contact_approve_show'><a @click='dialogVisibleReg = true'>注册条款</a>及</template>
        <a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" target="_blank">隐私政策</a>
      </el-checkbox>
    </el-form-item>
  </el-form>
</div>
<div class="side_success" v-else>
  <img src='./../../public/images/sku/email.png' class="side_success_email" />
  <p class="side_success_title">发送成功</p>
  <p class='side_success_des'>您的咨询信息已收到，我们将尽快与您联系！</p>
  <p class='side_success_userinfo' v-if='type === 1'>
    用户账号：{{ form.phone || form.email }}
  </p>
  <p v-if='type === 1' class="side_success_tips">已为您注册SGS在线商城会员<br />可使用账号快捷登陆</p>
  <p v-if='type === 1 || type === 2 || type === 3' class="side_success_tips">到“<a
      href='<%- memberUrl %>/quote/list'>我的咨询</a>”查看咨询进度</p>
  <p v-if='type === 1 || type === 2 || type === 3' class="side_success_countdown"><i
      id='countdown'>{{countdownTime}}</i>秒后自动跳转</p>
  <div class="side_success_ERcode" v-if='type === 4 '>
    <img src='./../../public/images/sku/ERcode.png' />
    <p>
      扫码关注SGS官方微信公众号， 回复“<i>0</i>”赢惊喜礼品！
    </p>
  </div>
</div>
<% include ./registionClause.html %>