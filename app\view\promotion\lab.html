<% include ../common/head.html %>
  <% include ../components/header.html %>
    <link rel="stylesheet" href="<%- locals.static %>/css/promotion/lab.css">
    <div class="lab" id='laboratory' v-cloak>
      <div class="head-box">
        <div class="wrap">
          <a class="page-title" href="/" target="_blank">最受外贸企业信赖的第三方检测认证机构</a>
          <div class="user-content">
            <div class="user-item"><a href="/" target="_blank">首页</a></div>
            <% if (isLogin){ %>
              <div class="user-item"><a href="javascrpit:void(0);">
                  <%- userInfo.userNick || userInfo.userPhone || userInfo.userEmailCopy || userInfo.userEmail %>
                </a></div>
              <div class="user-item"><a href="javascrpit:void(0);" id='logout'>退出</a></div>
              <% }else{ %>
                <div class="user-item"><a href="<%- memberUrl %>/login" target="_blank">登录</a></div>
                <div class="user-item"><a onclick="signup('<%- memberUrl %>')" style="cursor: pointer;">注册</a></div>
                <% } %>
                  <div class="user-item"><a href="/information" target="_blank">资源中心</a></div>
                  <div class="user-item"><a href="/overview/aboutSGS" target="_blank">关于SGS</a></div>
                  <div class="user-item" onclick="addFavorite()">收藏</div>
          </div>
        </div>
      </div>
      <div class="head-img">
        <div class="wrap">
          <a href="/" target="_blank"><img class="logo" src="../../public/images/promotion/lab/logo.jpg" alt=""></a>
          <div class="title">
            {{labDetail.name}}
          </div>
          <div class="cma-cnas">
            <img :src="labDetail.content.icon1" class="thum" alt="">
            <div class="cma-box">
              <img class="cma-img" :src="labDetail.content.pic1" alt="">
            </div>
            <img :src="labDetail.content.icon2" class="thum" alt="">
            <div class="cnas-box">
              <img class="cnas-img" :src="labDetail.content.pic2" alt="">
            </div>
          </div>
          <div class="anchor">
            <img src="../../public/images/promotion/lab/anchor.png" />
            <ul>
              <li v-for='(item, index) of labDetail.content.map' :key='index'>
                <a :href='"#" + item.hash'>{{item.name}}</a>
              </li>
            </ul>
          </div>
          <div class="home_top_hotline">
            <i></i>
            <div class="home_top_hotline_phone clearfix">
              <span class="hotlineText">4000-558-581</span>
            </div>
            <div class="home_top_hotline_intro">
              — 全国受理，就近服务 —
            </div>
          </div>
        </div>
      </div>
      <div class="nav-box">
        <div class="wrap nav-wrap">
          <div class="nav-wrap-box">
            <ul class="tab_ul">
              <li v-for='(item, index) of labDetail.content.navigations' :key='index' v-if='item.name' class="tab_li"
                :class="{activeclass:activeindex===index}" @click="newtab(index)"><a target="_blank"
                  :href="item.link">{{item.name}}</a></li>
            </ul>
            <div class="btns">
              <span class="left" @click="change('left')" v-if='showLeft'></span>
              <span class="right" @click="change('right')" v-if='showRight'></span>
            </div>
          </div>
          <div class="nav-btn kf5-btn handleIMtool">加急出证</div>
        </div>
      </div>
      <div class="banner-box" :style="{backgroundImage: 'url(' + labDetail.content.bannerBg + ')'}">
        <div class="wrap banner-wrap">
          <div class='kf5-btn handleIMtool'>
            <span>立即咨询</span>
          </div>
          <div class="banner_form" v-if='labDetail.content.isForm'>
            <% include ./../components/inner_quote_form.html %>
              <% include ./../pages/quote/modal.html %>
          </div>
          <div class="oiq-plan" v-else>
            <h3>简单<i>3</i>个步骤，获取定制方案</h3>
            <div class="oiq_msg" id='oiq_msg_lab'>
              <ul>
                <% for(let oiq of oiqList){ %>
                  <li>
                    <div class="time">
                      <%- oiq.stateDate %>
                    </div>
                    <div>
                      <b>
                        <%- oiq.city %>
                      </b>
                      <span>
                        <%- oiq.userName %>
                      </span>
                      <span>收到了关于</span>
                      <b class="lastCategory">
                        <%- oiq.lastCategory %>
                      </b>
                      <span>的测试方案</span>
                    </div>
                  </li>
                  <% } %>
              </ul>
            </div>
            <div class="oiq-search">
              <a href='javascrpit:void(0);'
                @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=-1', '&')">
                <input type="text" placeholder="请输入产品关键词搜索产品类别" disabled />
                <i></i>
              </a>
            </div>
            <div class="oiq-category">
              <% if(oiqCategorys && oiqCategorys.length> 0){ %>
                <% for(let oiq of oiqCategorys){ %>
                  <% if(oiq.categoryValuesStr){ %>
                    <a href='javascrpit:void(0);'
                      @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=<%- oiq.categoryValuesStr %>', '&')">
                      <%- oiq.categoryAlias %>
                    </a>
                    <% }else{ %>
                      <span>
                        <%- oiq.categoryAlias %>
                      </span>
                      <% } %>
                        <% } %>
                          <% }else{ %>
                            <a href='javascrpit:void(0);'
                              @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1581,1582,1583,1632', '&')">不锈钢</a>
                            <a href='javascrpit:void(0);'
                              @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1581,1883,1884', '&')">螺栓</a>
                            <a href='javascrpit:void(0);'
                              @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1581,1921,1936', '&')">焊板</a>
                            <a href='javascrpit:void(0);'
                              @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1581,1775,1804', '&')">轴承</a>
                            <a href='javascrpit:void(0);'
                              @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1581,1582,1583,1589', '&')">镀锌板</a>
                            <a href='javascrpit:void(0);'
                              @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1332,1350,1960', '&')">塑料件</a>
                            <a href='javascrpit:void(0);'
                              @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1332,1391,1393', '&')">密封圈</a>
                            <a href='javascrpit:void(0);'
                              @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1332,1382,1388', '&')">复合材料</a>
                            <a href='javascrpit:void(0);'
                              @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1441,1455', '&')">油墨</a>
                            <a href='javascrpit:void(0);'
                              @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1332,1425,1428', '&')">胶带</a>
                            <% } %>
            </div>
            <a class="oiq_start" href='javascrpit:void(0);'
              @click="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1', '?')">
              <span class="oiq_start_text">立即开始</span>
              <span class="oiq_start_layout"></span>
            </a>
          </div>
        </div>
      </div>
      <div class="hot-service" v-for='(v1, index1) of labDetail.content.floors' :key='index1'>
        <div class="about" v-if='v1.type === 1 && (v1.showFloor === 1 || v1.showFloor === undefined)' :id='v1.hash'>
          <div class="wrap">
            <p class="floor-title" v-if='v1.showTitle'>{{v1.title}}</p>
            <div class="main-line" v-if='v1.showTitle'></div>
            <p class="floor-sub-title" v-if='v1.showSubTitle'>{{v1.subTitle}}</p>
          </div>
          <div class="content">
            <div class="img">
              <img :src='v1.floorAboutPic' v-show='showImg'
                @click='v1.videoSource ? showImg = false : showImg = true' />
              <video :src="v1.videoSource" v-show='!showImg' controls="controls" autoplay muted>
                your browser does not support the video tag
              </video>
            </div>
            <div class="textarea">
              <div v-html='v1.content'></div>
              <div class='kf5-btn handleIMtool'>
                <span>立即咨询</span>
              </div>
            </div>
          </div>
        </div>

        <div class="service" v-if='v1.type === 2 && (v1.showFloor === 1 || v1.showFloor === undefined)' :id='v1.hash'>
          <div class="wrap">
            <p class="floor-title" v-if='v1.showTitle'>{{v1.title}}</p>
            <div class="main-line" v-if='v1.showTitle'></div>
            <p class="floor-sub-title" v-if='v1.showSubTitle'>{{v1.subTitle}}</p>
          </div>
          <div class="content">
            <ul class="service_li clearfix">
              <li v-for='(v2, index2) of v1.list' :key='index2'>
                <template v-if='v2.titleLink'>
                  <a target="_blank" :href="v2.titleLink">
                    <div class="service_li_title" :style="{backgroundImage: 'url(' + v2.bg + ')'}">
                      <h2>{{v2.title}}</h2>
                      <span></span>
                    </div>
                  </a>
                </template>
                <template v-else>
                  <div class="service_li_title" :style="{backgroundImage: 'url(' + v2.bg + ')'}">
                    <h2>{{v2.title}}</h2>
                    <span></span>
                  </div>
                </template>
                <div class="service_li_list">
                  <div class="service_li_list_line">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                  <ul class="clearfix">
                    <li v-for='(v3, index3) of v2.items'>
                      <a target="_blank" :href="v3.link" v-if='v3.link'>{{v3.name}}</a>
                      <span v-else>{{v3.name}}</span>
                    </li>
                  </ul>
                </div>
                <div class="service_li_btn">
                  <div class='kf5-btn handleIMtool'>
                    <span>立即询价</span>
                    <i></i>
                  </div>
                </div>
              </li>
            </ul>
            <div class="service-btns">
              <a target="_blank" class="btn1" :href="v1.moreLink">
                更多服务
              </a>
            </div>
          </div>
        </div>

        <div class="solution" v-if='v1.type === 3 && (v1.showFloor === 1 || v1.showFloor === undefined)' :id='v1.hash'>
          <div class="wrap">
            <p class="floor-title" v-if='v1.showTitle'>{{v1.title}}</p>
            <div class="main-line" v-if='v1.showTitle'></div>
            <p class="floor-sub-title" v-if='v1.showSubTitle'>{{v1.subTitle}}</p>
          </div>
          <div class="wrap solution-wrap">
            <div class="swiper-container solution-swiper">
              <div class="swiper-wrapper">
                <div class="swiper-slide solutionCon" v-for='(v1, index1) of solution' :key='index1'
                  :class="{'swiper-no-swiping': solution.length === 1}">
                  <ul class="solution-list">
                    <li class="solutionConLi" v-for='(v2, index2) of v1' :key='index2'>
                      <span class="img" :style="{ backgroundImage: 'url(' + v2.bg + ')' }"></span>
                      <div class="cc">
                        <a target="_blank" :href="v2.link">
                          <span class="solutionConLi-word1">{{v2.title}}</span>
                        </a>
                        <p>{{v2.des}}</p>
                        <i class="kf5-btn handleIMtool">咨询</i>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <template v-if='solution.length > 1'>
              <div class="swiper-bottom">
                <div class="swiper-pagination"></div>
              </div>
              <div class="swiper-button-prev"></div>
              <div class="swiper-button-next"></div>
            </template>
            <div class="solution-btns">
              <a class="btn1" :href="v1.moreLink" target="_blank">
                更多服务
              </a>
            </div>
          </div>
        </div>

        <div class="area" v-if='v1.type === 4 && (v1.showFloor === 1 || v1.showFloor === undefined)' :id='v1.hash'>
          <div class="wrap">
            <p class="floor-title" v-if='v1.showTitle'>{{v1.title}}</p>
            <div class="main-line" v-if='v1.showTitle'></div>
            <p class="floor-sub-title" v-if='v1.showSubTitle'>{{v1.subTitle}}</p>
          </div>
          <div class="content" v-html='v1.content'>
          </div>
        </div>

        <div class="data" v-if='v1.type === 5 && (v1.showFloor === 1 || v1.showFloor === undefined)' :id='v1.hash'>
          <div class="wrap">
            <p class="floor-title" v-if='v1.showTitle'>{{v1.title}}</p>
            <div class="main-line" v-if='v1.showTitle'></div>
            <p class="floor-sub-title" v-if='v1.showSubTitle'>{{v1.subTitle}}</p>
          </div>
          <div class="content" v-html='v1.content'>
          </div>
        </div>

        <div class="certificate" v-if='v1.type === 6  && (v1.showFloor === 1 || v1.showFloor === undefined)'
          :id='v1.hash'>
          <div class="wrap">
            <p class="floor-title" v-if='v1.showTitle'>{{v1.title}}</p>
            <div class="main-line" v-if='v1.showTitle'></div>
            <p class="floor-sub-title" v-if='v1.showSubTitle'>{{v1.subTitle}}</p>
          </div>
          <div class="content" v-html='v1.content'>
          </div>
        </div>

        <div class="news" v-if='v1.type === 7 && (v1.showFloor === 1 || v1.showFloor === undefined)' :id='v1.hash'>
          <div class="wrap" style="width: 1100px;">
            <p class="floor-title" v-if='v1.showTitle'>{{v1.title}}</p>
            <div class="main-line" v-if='v1.showTitle'></div>
            <p class="floor-sub-title" v-if='v1.showSubTitle'>{{v1.subTitle}}</p>
          </div>
          <div class="content">
            <ul>
              <li v-for='(v2, index2) of v1.list' :key='index2'>
                <div class="left">
                  <i></i>
                  <a target="_blank" :href="v2.link">{{ v2.name }}</a>
                </div>
                <div class="right">
                  <i></i>
                  <span>{{v2.visits_num > 9999 ? "9999+" : v2.visits_num}}</span>
                </div>
              </li>
            </ul>
            <div class="more">
              <a target="_blank" :href="v1.moreLink">
                查看更多
                <i></i>
              </a>
            </div>
          </div>
        </div>

        <div class="case" v-if='v1.type === 8 && (v1.showFloor === 1 || v1.showFloor === undefined)' :id='v1.hash'>
          <div class="wrap">
            <p class="floor-title" v-if='v1.showTitle'>{{v1.title}}</p>
            <div class="main-line" v-if='v1.showTitle'></div>
            <p class="floor-sub-title" v-if='v1.showSubTitle'>{{v1.subTitle}}</p>
          </div>
          <div class="wrap news-wrap">
            <div class="swiper-container news-swiper">
              <div class="swiper-wrapper">
                <div class="swiper-slide newsCon" v-for='(item, index) of cases' :key='index'>
                  <div class="card" v-for='(v2, index2) of item' :key='index2'>
                    <a target="_blank" :href="v2.link">
                      <img :src="v2.img" alt="">
                      <h2 class="title">{{v2.title}}</h2>
                      <p class="des">{{v2.des}}</p>
                      <span class="tag">{{v2.tag}}</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <template v-if='cases.length > 1'>
              <div class="swiper-bottom">
                <div class="swiper-pagination1"></div>
              </div>
              <div class="swiper-button-prev"></div>
              <div class="swiper-button-next"></div>
            </template>
            <div class="more">
              <a target="_blank" :href="v1.moreLink">
                查看更多资讯
                <i></i>
              </a>
            </div>
          </div>
        </div>
        <!-- 自定义楼层 -->
        <div class="data" v-if='v1.type === 9 && (v1.showFloor === 1 || v1.showFloor === undefined)' :id='v1.hash'>
          <div class="wrap">
            <p class="floor-title" v-if='v1.showTitle'>{{v1.title}}</p>
            <div class="main-line" v-if='v1.showTitle'></div>
            <p class="floor-sub-title" v-if='v1.showSubTitle'>{{v1.subTitle}}</p>
          </div>
          <div class="content" v-html='v1.content'>
          </div>
        </div>
      </div>
      <div class="contact_ads"></div>
    </div>
    <script src="<%- locals.static %>/js/swiper.min.js"></script>
    <script src="<%- locals.static %>/js/md5.js"></script>
    <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
<% if(locals.env != 'prod'){ %>
<script src="<%- locals.static %>/plugin/v2.test.js"></script>
<% }else{ %>
<script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js" type="text/javascript"></script>
<% } %>
    <script>
      var pid = '<%- mallPid %>',
        pcode = '<%- mallPcode %>'
      var newVue = new Vue({
        name: 'laboratory',
        el: '#laboratory',
        mixins: [quoteMixins],
        data: {
          presentindex: 0,
          width: 1080,
          activeindex: 0,
          showLeft: false,
          showRight: true,
          provice: [],
          host: '<%- host %>',
          props: {
            value: 'orgName',
            label: 'orgName',
            children: 'citys'
          },
          proviceText: ['北京市', '北京市'],
          showSelect: false,
          showCity: '北京市',
          dialogVisibleReg: false,
          loading: false,
          form: {
            type: '业务咨询',
            trade: '',
            tradeName: '其他',
            tradeIndex: '',
            serviceIndex: '',
            service: '',
            serviceName: '其他',
            company: '',
            provice: '',
            content: '',
            customer: '<%- userInfo.userName %>',
            email: '<%- userInfo.userEmail %>',
            phone: '<%- userInfo.userPhone %>',
            imageCode: '',
            frontUrl: window.location.href,
            destCountry: '',
            verifyCode: '',
          },
          showModal: false,
          seconds: 59,
          timer: null,
          countdownTime: 5,
          countdownTimer: null,
          isLogin: <%- isLogin %>,
          disablePhone: <%- disablePhone %>,
          disableMail: <%- disableMail %>,
          pid: 'pid.mall',
          pcode: 'Z0zCnRE3IaY9Kzem',
          rules: {
            provice: [{ required: true, message: '*请选择所在城市' }],
            content: [{ required: true, message: '*请输入咨询内容' }],
            customer: [{ required: true, message: '*请输入您的称呼' }],
          },
          approve: false,
          tab: 'phone',
          host: '<%- host %>',
          isSuccess: false,
          contact_approve_show: true,
          cases: <%- cases %>,
          solution: <%- solution %>,
          labDetail: {
            name: '<%- labDetail.name %>',
            content: <%- labDetail.content %>,
          },
          showImg: true,
          pageInfo: {
            title: '验证手机号',
            sendModel: '已发送到手机号：+86',
            prepend: '手机验证码'
          },
          captchaAppId: '<%- captchaAppId %>',
        },
        mounted: function () {
          var that = this
          this.$nextTick(function () {
            let x = document.getElementsByClassName("nav-wrap-box")[0].offsetWidth;
            //获取所有li宽度
            let all_li_width = document.getElementsByClassName('tab_li')
            let all_width = []
            for (var i = 0; i < all_li_width.length; i++) {
              var lastW = all_li_width[i].offsetWidth;
              all_width.push(lastW)
            }
            let w = eval(all_width.join('+'))
            if (w < x) {
              that.showRight = false
            }
          })
          console.log(this.labDetail)
        },
        methods: {
          newtab: function (index) {
            this.activeindex = index
          },
          change: function (type) {
            var that = this
            //获取所有li宽度
            let all_li_width = document.getElementsByClassName('tab_li')
            // let x = document.getElementsByClassName("tab_ul");
            let tabulW = document.querySelector('.tab_ul')
            let all_width = []
            for (var i = 0; i < all_li_width.length; i++) {
              var lastW = all_li_width[i].offsetWidth;
              all_width.push(lastW)
            }
            let w = eval(all_width.join('+')) + 6 * all_width.length
            let offwid = w - that.width
            if (type === 'left') {//向左
              that.presentindex--
              if (that.presentindex < 0) {
                that.presentindex = 0
              }
              let marginleftall = 0
              for (let i in all_width) {
                if (i < that.presentindex) {
                  marginleftall = marginleftall + all_width[i] + 6;
                }
              }
              const widthnum = tabulW.style.transformnum - 100
              if (widthnum > 0) {
                tabulW.style.transformnum = widthnum
                that.showRight = true
                that.showLeft = true
                tabulW.style.transform = 'translateX(-' + widthnum + 'px)'
              } else {
                that.showLeft = false
                tabulW.style.transform = 'translateX(' + 0 + 'px)'
              }
            } else {
              that.presentindex++
              if (that.presentindex > that.labDetail.content.navigations.length) {
                that.presentindex = that.labDetail.content.navigations.length
              }
              if (that.presentindex * 100 > offwid) {
                tabulW.style.transformnum = offwid
                that.showRight = false
                tabulW.style.transform = 'translateX(-' + offwid + 'px)'
              } else {
                that.showRight = true
                that.showLeft = true
                tabulW.style.transformnum = 100 * that.presentindex
                tabulW.style.transform = 'translateX(-' + 100 * that.presentindex + 'px)'
              }
            }
          },
          jumpOIQAddtionSource: function (url, separator) {
            var source = window.localStorage.getItem('oiqSource');
            window.open(url + separator + decodeURI(source), '_blank')
          },
          cityChange: function (data) {
            if (data && data.length) {
              this.showCity = data[1]
              this.showSelect = false
            }
          },
          showCascader: function (e) {
            e.stopPropagation()
            this.showSelect = !this.showSelect
          },
          qryCity: function () {
            var param = {}
            var timestamp = new Date().valueOf();
            var that = this
            $.ajax({
              type: 'POST',
              url: this.host + '/ticCenter/business/api.v1.center/org/qryCity',
              data: JSON.stringify(param),
              contentType: 'application/json',
              headers: {
                pid: pid,
                sign: this.sing(param, timestamp),
                timestamp: timestamp,
                frontUrl: window.location.href
              },
              success: function (res) {
                if (res.resultCode === '0') {
                  for (var i = 0; i < res.data.length; i++) {
                    // res.data[i].citys = null
                    if (!res.data[i].citys || !res.data[i].citys.length) {
                      delete res.data[i].citys
                    }
                  }
                  const provice = res.data || []
                  const filterIds = ['970000000000', '980000000000', '990000000000', '1000000000000']
                  that.provice = provice.filter(function (v) {
                    return filterIds.indexOf(v.orgId) === -1
                  })
                } else {
                  alert(res.resultMsg)
                }
              },
              fail: function (data) {
                alert(res.resultMsg)
              },
              complete: function (complete) {
              }
            })
          },
        }
      })
    </script>
    <script>
      $(function () {
        var swiper = new Swiper('.solution-swiper', {
          navigation: {
            nextEl: '.solution-wrap .swiper-button-next',
            prevEl: '.solution-wrap .swiper-button-prev',
          },
          pagination: {
            el: '.swiper-pagination',
            clickable: true,
            renderBullet: function (index, className) {
              return '<span class="' + className + '">' + (index + 1) + '</span>';
            },
          },
          speed: 1000,
          loop: true,
          fadeEffect: {
            crossFade: true,
          }
        });

        var newsSwiper = new Swiper('.news-swiper', {
          navigation: {
            nextEl: '.news-wrap .swiper-button-next',
            prevEl: '.news-wrap .swiper-button-prev',
          },
          pagination: {
            el: '.swiper-pagination1',
            clickable: true,
            renderBullet: function (index, className) {
              return '<span class="' + className + '">' + (index + 1) + '</span>';
            },
          },
          speed: 1000,
          loop: true,
          // autoplay: {
          //   delay: 5000,
          //   disableOnInteraction: false,
          // },
          fadeEffect: {
            crossFade: true,
          }
        });
      })
    </script>
    <% include ../footer.html %>