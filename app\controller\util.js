function subString(str, n) {
  var r = /[^\x00-\xff]/g;
  var m;

  if (str.replace(r, '**').length > n) {
    m = Math.floor(n / 2);

    for (var i = m, l = str.length; i < l; i++) {
      if (str.substr(0, i).replace(r, '**').length >= n) {
        var newStr = str.substr(0, i) + '...';
        return newStr;
      }
    }
  }
  return str;
}

/*  fix bug for sentry TIC-VIEW-Y */
async function isMobile(ctx) {
  let isMobile = false;
  const userAgent = ctx.request.headers['user-agent'];
  if (userAgent && userAgent.toLowerCase().indexOf('mobile') != -1) isMobile = true;
  return isMobile;
}

function getIP(req) {
  let ip = req.get('x-forwarded-for'); // 获取代理前的ip地址
  if (ip && ip.split(',').length > 0) {
    ip = ip.split(',')[0];
  } else {
    ip = req.ip;
  }
  const ipArr = ip.match(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/g);
  return ipArr && ipArr.length > 0 ? ipArr[0] : '127.0.0.1';
}

function htmlEncodeByRegExp(str){
  var temp = "";
  if(str.length == 0) return "";
  temp = str.replace(/&/g,"&amp;");
  temp = temp.replace(/</g,"&lt;");
  temp = temp.replace(/>/g,"&gt;");
  temp = temp.replace(/\s/g,"&nbsp;");
  temp = temp.replace(/\'/g,"&#39;");
  temp = temp.replace(/\"/g,"&quot;");
  return temp;
}

async function getCookie(ctx, key) {
  if (!ctx.request.header.cookie) {
    return '';
  }
  const carr = ctx.request.header.cookie.split('; ');
  let sjson = {};
  for (let item of carr) {
    let iarr = item.split('=');
    sjson[iarr[0]] = iarr[1];
  }

  if (sjson) {
    return htmlEncodeByRegExp(sjson[key] || '');
    // return sjson[key]
  } else {
    return '';
  }
}

async function isLogin(ctx) {
  const isLogin = await getCookie(ctx, 'SGS_INFO')
  return isLogin ? true : false
}

async function getHost(env, ctx) {
  let host = ''
  if (env === 'prod' || env === 'prodGray') {
    host = 'https://ticgate.sgs.net';
  } else if (env === 'UAT') {
    host = 'https://ticgateuat.sgs.net';
  } else if (env === 'gray') {
    host = 'https://ticgateuat.sgs.net';
  } else if (env === 'dev') {
    host = 'https://ticgatedev.sgs.net';
  } else if (env === 'test') {
    host = 'https://ticgatetest.sgs.net';
  } else if (env === 'local') {
    // host = 'https://ticgateuat.sgs.net';
    host = 'https://ticgatetest.sgs.net';
  } else {
    host = 'https://ticgateuat.sgs.net';
  }
  return host
}

async function getTokenUrl(env) {
  let tokenUrl = ''
  if (env == 'prod') {
    tokenUrl = 'https://academy.sgsonline.com.cn';
  } else {
    tokenUrl = 'http://*************';
  }
  return tokenUrl
}

async function filterSQL(params) {
  const reg = /document|cookie|href|script|insert|select|update|delete|truncate|exec|drop|count/i;
  return Object.values(params).some(item => {
    if (item) {
      return reg.test(String(item).toLowerCase())
    }
  })
}

async function getUserInfo(ctx) {
  const token = await getCookie(ctx, 'SSO_TOKEN');
  // ctx.logger.error('-----getUserInfo token----', `${token}==`);
  const cartNumber = await getCookie(ctx, 'CARTNUMBER') || 0; //获取购物车上数量
  let userInfo = null
  let orderNum = 0
  let activity = []
  if (token) {
    // if (ctx.session.userInfo) {
    //   userInfo = ctx.session.userInfo
    //   orderNum = ctx.session.orderNum
    // } else {
      userInfo = await ctx.service.external.getUserInfo({}, `${token}==`)
      if (userInfo) {
        const orderNumResult = await ctx.service.index.getOrderNum(`${token}==`, ctx.headers);
        const activityResult = await ctx.service.external.qryActivity({}, `${token}==`);
        ctx.session.activity = []
        if (activityResult && activityResult.length) {
          ctx.session.activity = activityResult.map(v => v.activityCode)
        }
        ctx.session.userInfo = userInfo
        ctx.session.orderNum = orderNumResult
        orderNum = orderNumResult
        activity = ctx.session.activity
      } else {
        ctx.session.activity = []
        ctx.session.userInfo = null
        ctx.session.orderNum = 0
      }
    // }
  } else {
    ctx.session.activity = []
    ctx.session.userInfo = null
    ctx.session.orderNum = 0
  }
  userInfo = {...userInfo, cartNumber, orderNum, activity }
  // ctx.logger.error('-----userInfo----', userInfo);
  return userInfo
}


module.exports = {
  subString,
  isMobile,
  getIP,
  getCookie,
  isLogin,
  getHost,
  getUserInfo,
  getTokenUrl,
  filterSQL,
}
