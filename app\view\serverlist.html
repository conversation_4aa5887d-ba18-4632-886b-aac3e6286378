<% include header.html %>
  <% include ./components/header.html %>
    <div class="banner">
      <div class="swiper-container">
        <div class="swiper-wrapper">

          <% for(item of banner){ %>
            <div class="swiper-slide bannerLi1"
              style="background-image:url(<%- item.img_path.replace(/\/static/g,locals.static) %>)">
              <% if(item.btn_url && !item.btn_value){ %><a href="<%- item.btn_url %>" target="blank"
                  style="display:block;width:100%;height:100%;">
                  <% } %>
                    <div class="baa">
                      <div class="bannerTitle">
                        <%- item.btn_text %>
                      </div>
                      <div class="bannerDetail">
                        <%- item.btn_description %>
                      </div>
                      <% if(item.btn_value){ %>
                        <a class="bannerMore" href="<%- item.btn_url %>" target="_blank">
                          <%- item.btn_value %>
                            <div class="bannermoreBox">

                            </div>
                            <div class="bannermoreword">
                              <%- item.btn_value %>
                            </div>
                        </a>
                        <% } %>
                    </div>
                    <% if(item.btn_url && !item.btn_value){ %>
                </a>
                <% } %>
            </div>
            <% } %>
        </div>
        <!-- Add Arrows -->
        <% if(banner && banner.length> 1){ %>
          <div class="swiper-bottom">
            <div class="swiper-button-prev"></div>
            <div class="swiper-pagination"></div>
            <div class="swiper-button-next"></div>
          </div>
          <% } %>
      </div>
    </div>
    <div class="location">
      <ul>
        <li class="locationLi">
          <a href="/">第三方检测机构</a>
        </li>
        <li class="locationLi icon"></li>
        <li class="locationLi"><a href="./">
            <%- detail.name %>
          </a></li>
        <li class="locationLi icon"></li>
        <li class="locationLi"><a href="javascrpit:void(0);">全部服务</a></li>
      </ul>
    </div>
    <div class="listBigBox1">
      <div class="categorybox">
        <div class="categorybox-wrap">
          <ul class="categoryUl" id="CataUl">
            <li class="categoryLi">服务类别：</li>
            <li style="list-style: none;padding: 0 0 0 70px">
              <ul>
                <li class="categoryLi categoryLi1 clickActive" data-role="<%- detail.id %>"
                  onclick="_getCata(<%- detail.id %>, this)">全部服务</li>
                <% for(var item of children){ %>
                  <li class="categoryLi categoryLi1" data-role="<%- item.id %>" onclick="_getCata(<%- item.id %>, this)">
                    <%- item.name %>
                  </li>
                  <% } %>
              </ul>
            </li>
          </ul>

          <div class="keySearch">
            <div class="keyWord"></div>
            <input type="text" class="keyInput" placeholder="在结果中搜索" id="sKeyword">
            <div class="navSearchIcon listSearch" id='sKeywordHandle'>搜索</div>
          </div>
        </div>


      </div>

      <div class="listBigBox">

        <div class="listBox">
          <ul class="listUl" id="List2">
          </ul>
          <!-- <ul class="serverpages" id="Pages">
            </ul> -->
        </div>
        <div class="pagination" id='serverList'>
          <el-pagination 
            @current-change="handleCurrentChange" 
            :current-page.sync="currPage" 
            :page-size="10"
            @size-change="handleSizeChange" 
            layout="prev, pager, next, jumper" 
            :total="totalNum">
          </el-pagination>
        </div>
      </div>
    </div>

    <script src="<%- locals.static %>/js/swiper.min.js"></script>
    <script>
      var swiper = new Swiper('.swiper-container', {
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
        },
        speed: 1000,
        loop: false,
        autoplay: {
          delay: 5000,
          disableOnInteraction: false,
        },
        effect: 'fade',
        fadeEffect: {
          crossFade: true,
        }
      });

      var scontainer = $('.swiper-container')[0];
      scontainer.onmouseenter = function () {
        swiper.autoplay.stop();
      };
      scontainer.onmouseleave = function () {
        swiper.autoplay.start();
      };

      var pdata = {
        pageNum: 1,
        pageRow: 10,
        catalogId: '',
        name: '',
      }

      $(function () {
        pdata.catalogId = $('.categoryLi1').eq(0).data('role')
        _getData();
      })

      function _getData() {
        var pathname = window.location.pathname;
        console.log(pdata)
        $.post('/getSkuCataApi', pdata, function (r) {
          if (r.resultCode === '0') {
            var data = r.data && r.data.items;
            console.log(data)
            var $_ul = '';
            for (var i = 0; i < data.length; i++) {
              var item = data[i];
              var buy = item.isBuy ? '<span class="buytag">可在线下单</span>' : '';
              var li = '<li class="listLi"><div class="list-img"><img src="' + item.thumbImg.replace(/\/static/g, '<%- locals.static %>') + '" alt="" style="width:284px;height:142px;">';
              if (item.isBuy) {
                li += '<span class="buyicon"></span>';
              }
              li += '</div>';
              li += '<div class="listwordbox"> <div class="listwordTS"><a class="listwordT" href="/sku/' + item.alias + '/' + item.id + '">' + item.name + '</a>' + buy + '<div class="listwordS">' + item.subTitle + '</div></div>';
              li += '<div class="listwordI">' + item.description + '</div>';
              li += '</div></div></li>';
              $_ul += li;
            }
            $('#List2').html($_ul);
            // var $_pages = '';
            // for (var i = 1; i <= Math.ceil(r.total / pdata.pageRow); i++) {
            //   var pclass = 'serverpage';
            //   if (i == r.page) {
            //     pclass += ' pageActive';
            //   }
            //   $_pages += '<li class="' + pclass + '" onclick="setPage(this)">' + i + '</li>';
            // }
            // $('#Pages').html($_pages);

            $('#CataUl>li').each(function (i, item) {
              var zid = $(item).data('role');
              if (zid == pdata.catalogId) {
                $(item).addClass('clickActive');
              }
            })
            if (newVue && newVue._data) {
              newVue._data.totalNum = r.data.totalNum
              newVue._data.currPage = r.data.pageNo
            }
          } else {
            alert(r.resultMsg)
          }
        });
      }

      function _getCata(id, obj) {
        if ($(obj).hasClass('clickActive')) {
          return;
        }
        pdata.pageNum = 1;
        pdata.catalogId = id;
        // pdata.catalogId = 
        _getData();
        $(obj).addClass('clickActive').siblings().removeClass('clickActive');
      }

      $("#sKeywordHandle").on('click', function () {
        var keyword = $('#sKeyword').val().trim();
        pdata.name = keyword;
        _getData();
      });

      function setPage(obj) {
        var page = $(obj).text();
        pdata.pageNum = page;
        _getData();
        var st = $('.location:first').offset().top - 120;
        $('html,body').stop().animate({ scrollTop: st });
      }

      var newVue = new Vue({
        name: 'serverList',
        el: '#serverList',
        data: {
          totalNum: 0,
          currPage: 1
        },
        methods: {
          handleCurrentChange: function (val) {
            pdata.pageNum = val
            _getData()
          },
          handleSizeChange: function (val) {
            var pathname = window.location.pathname;
            if (pathname.substr(pathname.length - 1, 1) === '/') {
              window.location.href = pathname + '?page=' + val;
            } else {
              window.location.href = pathname + '/?page=' + val;
            }
          },
        }
      });
    </script>
    <% include footer.html %>