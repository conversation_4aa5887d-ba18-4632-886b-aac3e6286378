<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta http-equiv="Content-Security-Policy">
  <% if(locals.env !='prod' ){ %>
    <meta name="baidu-site-verification" content="rVqh4kcxit" />
    <% }else{ %>
      <meta name="baidu-site-verification" content="LAo7KbfttB" />
      <% } %>
        <title>
          <%= detail.page_title || detail.name || detail.title %>
            <% if(locals.env !='prod' ){ %>(测试版)<% } %>
        </title>
        <meta name="keywords" content="<%- detail.page_keywords %>" />
        <meta name="description" content="<%- detail.page_description %>" />
        <meta name="format-detection" content="telephone=no">
        <link rel="icon" href="<%- locals.static %>/favicon.ico" type="image/x-icon" />
        <link rel="shortcut icon" href="<%- locals.static %>/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="<%- locals.static %>/css/swiper.min.css">
        <link rel="stylesheet" href="<%- locals.static %>/css/select2.min.css">
        <link rel="stylesheet" href="<%- locals.static %>/css/style.css">
        <link rel="stylesheet" href="<%- locals.static %>/css/style_less.css">
        <link rel="stylesheet" href="<%- locals.static %>/css/index.css">
        <link rel="stylesheet" href="<%- locals.static %>/css/extend.css">
        <script src="<%- locals.static %>/js/jquery.min.js"></script>
        <script src="<%- locals.static %>/js/js.cookie.min.js"></script>
        <script src="<%- locals.static %>/js/navigation.js"></script>
        <script src="<%- locals.static %>/svg-sprite.js"></script>
        <script uac_busitype="<%- uac_busiType || '' %>" uac_busisubtype="<%- uac_busiSubType || '' %>"
          uac_busicode="<%- uac_busiCode || '' %>"></script>
        <% if(locals.env !='prod' ){ %>
          <!-- Google Tag Manager -->
          <script>
            (function (w, d, s, l, i) {
              w[l] = w[l] || [];
              w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
              });
              var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
              j.async = true;
              j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
              f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-W9K275F');
          </script>
          <!-- Google Analytics -->
          <script>
            (function (i, s, o, g, r, a, m) {
              i['GoogleAnalyticsObject'] = r;
              i[r] = i[r] || function () {
                (i[r].q = i[r].q || []).push(arguments)
              }, i[r].l = 1 * new Date();
              a = s.createElement(o),
                m = s.getElementsByTagName(o)[0];
              a.async = 1;
              a.src = g;
              m.parentNode.insertBefore(a, m)
            })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

        // ga('create', 'UA-126797271-5', 'auto');
        // ga('send', 'pageview');
        // ga('require', 'ec');
          </script>
          <!-- End Google Analytics -->
          <% }else{ %>
            <script>
              (function (w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                  'gtm.start': new Date().getTime(),
                  event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                  j = d.createElement(s),
                  dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
              })(window, document, 'script', 'dataLayer', 'GTM-WQLR3T8');
            </script>
            <!-- End Google Tag Manager -->
            <!-- Google Analytics -->
            <script>
              (function (i, s, o, g, r, a, m) {
                i['GoogleAnalyticsObject'] = r;
                i[r] = i[r] || function () {
                  (i[r].q = i[r].q || []).push(arguments)
                }, i[r].l = 1 * new Date();
                a = s.createElement(o),
                  m = s.getElementsByTagName(o)[0];
                a.async = 1;
                a.src = g;
                m.parentNode.insertBefore(a, m)
              })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

        // ga('create', 'UA-126797271-5', 'auto');
        // ga('send', 'pageview');
        // ga('require', 'ec');
            </script>
            <!-- End Google Analytics -->
            <% } %>
              <script>
                window._agl = window._agl || [];
                (function () {
                  _agl.push(
                    ['production', '_f7L2XwGXjyszb4d1e2oxPybgD']
                  );
                  (function () {
                    var agl = document.createElement('script');
                    agl.type = 'text/javascript';
                    agl.async = true;
                    agl.src = 'https://fxgate.baidu.com/angelia/fcagl.js?production=_f7L2XwGXjyszb4d1e2oxPybgD';
                    var s = document.getElementsByTagName('script')[0];
                    s.parentNode.insertBefore(agl, s);
                  })();
                })();
              </script>
              <script>
                $.ajaxSetup({
                  beforeSend: function (xhr) {
                    //可以设置自定义标头
                    xhr.setRequestHeader('ia', sessionStorage.getItem('ia') || '');
                    xhr.setRequestHeader('si', Cookies.get('sessionId') || '');
                  },
                  headers: {
                    si: Cookies.get('sessionId') || '',
                    ia: sessionStorage.getItem('ia') || ''
                  }
                });
              </script>
</head>

<body>
  <% if(locals.env !='prod' ){ %>
    <!-- Google Tag Manager (noscript) -->
    <noscript>
      <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-W9K275F" height="0" width="0"
        style="display:none;visibility:hidden"></iframe>
    </noscript>
    <% }else{ %>
      <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WQLR3T8" height="0" width="0"
          style="display:none;visibility:hidden"></iframe>
      </noscript>
      <!-- End Google Tag Manager (noscript) -->
      <% } %>
        <div id="detail" v-cloak v-loading="caseLoading" element-loading-text="加载中"
          element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)">
          <div class="headBox">
            <div class="head">
              <div class="sgs"><a href="/" rel='nofollow'>SGS通标丨在线商城</a></div>
              <ul class="headUl">
                <li class="headLi">
                  您好，
                  <% if(!isLogin){ %>
                    <a title="登录" rel="nofollow" class="mainColor" @click="headerRegister">请登录/注册</a>
                    <% } else { %>
                      <a title="昵称" href='<%- memberUrl %>/member/center' rel="nofollow">
                        <%- userInfo.userNick || userInfo.userPhone || userInfo.userEmailCopy || userInfo.userEmail %>
                      </a>
                      <% if(userInfo.activity.includes('PROMOTION001')){ %>
                        <% if(userInfo.levelId===1){ %>
                          <a href='<%- memberUrl %>/member/center#member-coupon' rel="nofollow"><img class="pro_icon"
                              src="/static/images/pro_b.png"></a>
                          <% } else { %>
                            <a href='<%- memberUrl %>/member/center#member-coupon' rel="nofollow"><img class="pro_icon"
                                src="/static/images/pro_a.png"></a>
                            <% } %>
                              <% } else if(userInfo.orderNum && userInfo.orderNum.lv3> 0) { %>
                                <a href="<%- memberUrl %>/promember?promosource=header" class="mainColor"
                                  style="margin-left: 5px;" rel="nofollow">免费升级会员</a>
                                <% } %>
                                  <% } %>
                                    <% if(!isLogin){ %>
                                      <!--<a onclick="signup('<%- memberUrl %>')" title="注册" rel="nofollow" style="cursor: pointer;margin-left: 18px;">注册</a>-->
                                      <% } else { %>
                                        <a id='logout' href='javascrpit:;' title="退出登录" rel="nofollow"
                                          style="margin-left: 18px;">退出</a>
                                        <% } %>
                </li>
                <li class="headLi head_cart">
                  <i>|</i>
                  <a rel="nofollow" href="<%- memberUrl %>/member/center">服务中心</a>
                </li>
                <li class="headLi">
                  <a rel="nofollow" href="<%- memberUrl %>/quote/list">我的咨询</a>
                </li>
                <li class="headLi" style="margin-right:0">
                  <a onclick="goOrderList()">我的订单</a>
                </li>
                <li class="headLi">
                  <i>|</i>
                  <a rel="nofollow" data-agl-cvt="15" href="<%- storeUrl %>/cart.html" class="minicart shop_cart"
                    id="minicart_">
                    <span class="minicart_num">
                      <img src="/static/images/home/<USER>/car.png">
                      购物车（<b data-type="" class="up-cat-number">
                        <%- userInfo.cartNumber || 0 %>
                      </b>）
                    </span>
                  </a>
                </li>
                <li class="headLi">
                  <span onclick="phoneDialog()" style="cursor:pointer" data-agl-cvt="2">
                    咨询热线<em id='topbar_hotphone'>4000-558-581</em>
                  </span>
                </li>
                <li class="headLi">
                  <a href="/ticket/en" target="_blank" rel="nofollow" style="cursor:pointer">CONTACT US</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="navwrap">
            <div class="nav detailNav">
              <div class="navBox">
                <div>
                  <div class="navImg">
                    <a href="/"><img src="<%- locals.static %>/images/logo.png" alt="第三方认证检测机构"></a>
                  </div>
                  <div class="navList">
                    <ul class="mainNav">
                      <li id="serviceHome" class="n_category">
                        <span>我们的服务</span>
                        <div class="dragAngel"></div>
                        <div class="allNav1 clearfix" id='serviceNav'></div>
                      </li>
                      <li id='tradeHome' class="n_category">
                        <span>您的行业</span>
                        <div class="dragAngel"></div>
                        <div class="allNav1" id='tradeNav'></div>
                      </li>
                      <li class='nav_first--li'>
                        <a target="_blank" href="/information" rel='nofollow'>资源中心</a>
                        <div class="dragAngel"></div>
                        <% if(informationTypes && informationTypes.length> 0){ %>
                          <dl class="nav_second">
                            <% for (var item of informationTypes){ %>
                              <dd>
                                <a target="_blank" href='/information?type=<%- item.id %>'>
                                  <%- item.name %>
                                </a>
                              </dd>
                              <% } %>
                              <dd>
                                <a target="_blank" href="/news">集团新闻</a>
                              </dd>
                                <dd>
                                  <a target="_blank" href='/files'>资料下载</a>
                                </dd>
                          </dl>
                          <% } %>
                      </li>
                      <li>
                        <div class="srcoll">
                          <div class="srcolling">
                            <div class="srcolling-child">
                              <span>名师讲堂</span>
                              <span>海量课程</span>
                              <span>持续上新</span>
                              <span>免费畅学</span>
                            </div>
                          </div>
                        </div>
                        <a target="_blank" href="https://a.sgsonline.com.cn/" rel='nofollow'>在线讲堂</a>
                      </li>
                      <li>
                        <a href="<%- memberUrl %>/questionnaire/step1" rel='nofollow' target="_blank">快速报价</a>
                      </li>
                      <li class="order-online">
                        <a href="<%- portalHost %>/order-online" rel='nofollow' target="_blank">检测超市</a>
                      </li>
                      <li class='nav_first--li'>
                        <a target="_blank" href="/overview/aboutSGS" rel='nofollow'>关于SGS</a>
                        <div class="dragAngel"></div>
                        <dl class="nav_second">
                          <dd>
                            <a target="_blank" href='/overview/aboutSGS'>SGS简介</a>
                          </dd>
                        </dl>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="navSearchBox" id="navSearchBox"
                  data-path="<% if(locals.env != 'prod'){ %>http://cmsuat.sgsonline.com.cn<% }else{ %>https://www.sgsonline.com.cn<% } %>">
                  <button id='searchBtn'><i class="navSearchIconIcon"></i></button>
                  <input type="text" autocomplete="off" class="navSearch" id="keyWord" name="q" placeholder=""
                    maxlength="100" />
                  <div class="hotKeyword">
                    <% if(hotKeyword && hotKeyword.length> 0){ %>
                      <% for (var index in hotKeyword){ %>
                        <% if (index < 3) { %>
                          <span>
                            <%- hotKeyword[index].name %>
                          </span>
                          <% } %>
                            <% } %>
                              <% } %>
                  </div>
                  <div class="linkageWord">
                    <ul></ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <script src="<%- locals.static %>/js/extend.js"></script>
          <script>
            $(function () {
              window.addEventListener("hashchange", myFunction, false)

              function myFunction() {
                window.location.reload()
              }
            })
          </script>

          <% include ./components/header.html %>
            <div class="myLocation_box">
              <div class="myLocation">
                <div class="myLocationBox">
                  <div class="myLocation-word1"><a href="/">第三方检测机构</a></div>
                  <div class="myLocation-icon1 icon"></div>
                  <div class="myLocation-word1">资讯中心</div>
                  <div class="myLocation-icon1 icon"></div>
                  <div class="myLocation-word2">
                    <%- detailMall.title %>
                  </div>
                </div>
              </div>
              <div class="newsBox" style="overflow: hidden;position: relative;">
                <div class="newsBox_left">
                  <div class="leftBox">
                    <h1 class="news_title">
                      <%- detailMall.title %>
                    </h1>
                    <div class="subhead">
                      <% if(detailMall.original){ %>
                        <div class="original">原创</div>
                        <% } %>
                          <div class="subhead1">
                            <%- detailMall.publishTime %>
                          </div>
                          <div class="subhead2">
                            <%- detailMall.name %>
                          </div>
                          <% if(detailMall.author){ %>
                            <div class="subhead2">作者： <%- detailMall.author %>
                            </div>
                            <% } %>
                              <div class="subhead2">访问次数： <%- detailMall.hits %>
                              </div>
                              <div class="subhead3">点赞： {{ Likes }}</div>
                    </div>
                    <div class="con1">
                      <!-- <%- detailMall.content.replace(/\/static/g, locals.static) %> -->
                      <%- detailMall.content %>
                    </div>
                    <!-- <div class="likes">
                      <div :class="state?'CollectionTrue':'Collection'" @click="setCollection_zan">
                        <div class="Collection_zan" id="Collection_zan"></div>
                        <span>{{ state ? '已赞' : '点赞' }}</span>
                      </div>
                      <div class="Collection" onclick="addFavorite()">
                        <div class="Collection_cang"></div>
                        <span>收藏</span>
                      </div>
                    </div> -->
                    <% if(detailMall.original){ %>
                      <div class="original_box">
                        本文著作权归SGS所有，商业转载请联系获得正式授权，非商业请注明出处
                      </div>
                      <% } %>
                        <div class="chapters">

                        </div>
                  </div>
                  <div class="rightBox">
                    <div class="rightBox1">
                      <% if(recommends.length){ %>
                        <div class="rela">相关服务：</div>
                        <div class="rightContitleBoxRecommends">
                          <% for(var item of recommends){ %>
                            <div class="item over">
                              <a href="<%- item.relateContentUrl %>" target="_blank">
                                <img src="<%- item.relateContentImage %>" alt="<%- item.relateContentName %>">
                                <div class="content">
                                  <p class="main"><%- item.relateContentName %></p>
                                  <p class="sub"><%- item.relateContentSubTitle %></p>
                                </div>
                              </a>
                            </div>
                          <% } %>
                        </div>
                      <% } %>
                      <div class="rela">相关内容：</div>
                      <ul class="rightContitleBox">
                        <% for(var item of otherCase){ %>
                          <li>
                            <a class="rightContitle" href="/case/<%- detail.alias %>/detail-<%- item.id %>.html">
                              <%- item.title %>
                            </a>
                          </li>
                          <% } %>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="bannerInfo" style="float: right;margin-right: 7px;" id="sku_detail">
                  <% if(!detail.is_message){ %>
                    <% include ./components/sku_side_form.html %>
                      <% }else{ %>
                        <% include ./components/sku_side_step.html %>
                          <% } %>
                            <% include ./pages/quote/modal.html %>
                </div>
              </div>
              <div class="icon_box">
                <div :class="state?'iconTrue':'icon'" id="Collection_zan" @click="setCollection_zan">
                  <!--                <div class="icon_img"></div>-->
                </div>
                <div class="icon_rate" onclick="addFavorite()">
                  <!--                <div class="icon_rate_img"></div>-->
                </div>
              </div>
              <% include ./components/login.html %>
            </div>
        </div>

        <script src="../../public/js/case.js" type="text/javascript"></script>
        <% if(locals.env != 'prod'){ %>
        <script src="<%- locals.static %>/plugin/v2.test.js"></script>
        <% }else{ %>
        <script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js" type="text/javascript"></script>
        <% } %>
        <script src="<%- locals.static %>/js/FileSaver.js"></script>
        <script src="<%- locals.static %>/js/md5.js"></script>
        <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
        <script>
          $(function () {
            var isLogin = '<%- isLogin %>'
            var ele = $('.icon_box')
            var startPos = $(ele).offset().top;
            $.event.add(window, 'scroll', function () {
              var p = $(window).scrollTop()
              if (p > startPos) {
                $(ele).css('position', 'fixed');
                $(ele).css('top', '50px');
              } else {
                $(ele).css('position', 'absolute');
                $(ele).css('top', '77px');
              }
            })
          })
          var newVue = new Vue({
            name: 'sku_detail',
            el: '#detail',
            mixins: [quoteMixins],
            data: function () {
              var validatorUserPhone = function (rule, value, callback) {
                var reg = /^1[2|3|4|5|6|7|8|9][0-9]\d{8}$/
                if (!value) {
                  return callback(new Error('*请输入手机号'));
                } else if (!reg.test(value)) {
                  return callback(new Error('*请输入正确的手机号码'));
                } else {
                  return callback()
                }
              }
              var validatorEmail = function (rule, value, callback) {
                var reg =   /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/
                if (!value) {
                  return callback(new Error('*请输入邮箱地址'));
                } else if (!reg.test(value)) {
                  return callback(new Error('*请输入正确的邮箱地址'));
                } else {
                  return callback()
                }
              }
              return {
                showLoginModal: false,
                closeBtn: false,
                isRedirect: 1,
                form: {
                  type: '业务咨询',
                  trade: '',
                  tradeName: '其他',
                  tradeIndex: '',
                  serviceIndex: '',
                  service: '',
                  serviceName: '其他',
                  company: '',
                  provice: '',
                  content: '',
                  frontTitle: '<%- detail.name %>',
                  customer: '<%- userInfo.userNick %>',
                  email: '<%- userInfo.userEmail %>',
                  phone: '<%- userInfo.userPhone %>',
                  imageCode: '',
                  frontUrl: window.location.href,
                  destCountry: '',
                  verifyCode: '',
                  busiCode: '<%- detail.id %>'
                },
                parameter: {
                  regMode: 'case-' + '<%- detailMall.catalogCode %>',
                  regSource: 'mall',
                  regFrom: window.location.href,
                  busiCode: '<%- detail.id %>'
                },
                showModal: false,
                seconds: 59,
                timer: null,
                countdownTime: 5,
                countdownTimer: null,
                isLogin: <%- isLogin %>,
                disablePhone: <%- disablePhone %>,
                disableMail: <%- disableMail %>,
                catalogCode: '<%- detailMall.catalogCode %>',
                isPublic: '<%- detailMall.isPublic %>',
                loading: false,
                caseLoading: false,
                isSuccess: false,
                approve: false,
                tab: 'phone',
                provice: [],
                host: '<%- host %>',
                pid: 'pid.mall',
                pcode: 'Z0zCnRE3IaY9Kzem',
                rules: {
                  provice: [{ required: true, message: '*请选择所在城市' }],
                  content: [{ required: true, message: '*请输入您的咨询内容' }],
                  customer: [{ required: true, message: '*请输入您的称呼' }],
                  // email: [{ validator: validatorEmail }],
                  // phone: [{ validator: validatorUserPhone }],
                },
                type: 0,
                dialogVisibleReg: false,
                contact_approve_show: true,
                state: 0,
                loginAction: false,
                domain: '<%- domain %>',
                apihost: '<%- apihost %>',
                Likes: 0,
                loginSource: '',
                isLiking: false, // 是否点赞中
              }
            },
            methods: {
              handleCloseLogin: function () {
                this.showLoginModal = false
                if (this.loginSource === 'header') {
                  location.reload()
                }
              },
              handleChange: function () {
              },
              handleJumpStep1: function () {
                window.open('<%- memberUrl %>/questionnaire/step1')
              },
              headerRegister: function () {
                this.showLoginModal = true
                this.closeBtn = true
                this.loginSource = 'header'
              },
              setCollection_zan: function () {
                if (!this.isLogin) {
                  this.loginAction = true
                  this.showLoginModal = true
                  this.closeBtn = true
                } else {
                  this.setCollection()
                }
              },
              //获取点赞状态
              getLikeType: function () {
                var param = {
                  likeType: 'case-' + '<%- detailMall.catalogCode %>',
                  businessCode: '<%- detail.id %>'
                }
                var that = this
                $.ajax({
                  type: 'POST',
                  url: '/case/collection',
                  data: JSON.stringify(param),
                  contentType: 'application/json',
                  success: function (res) {
                    if (res.resultCode === '0') {
                      that.state = res.data
                    } else {
                      that.$message.error(res.resultMsg)
                    }
                  },
                  fail: function (data) {
                    that.$message.error(res.resultMsg)
                  },
                  complete: function (complete) {
                  }
                })
              },
              //设置点赞
              setCollection: function (type) {
                var that = this
                console.log('that.isLiking', that.isLiking)
                // 处于点赞接口调用中,禁用后续动作
                if (that.isLiking) return
                that.isLiking = true
                var param = {
                  likeType: 'case-' + '<%- detailMall.catalogCode %>',
                  businessCode: '<%- detail.id %>',
                  state: this.state === 1 ? 0 : 1,
                  pageUrl: window.location.href,
                }
                $.ajax({
                  type: 'POST',
                  url: '/case/successCallback',
                  data: JSON.stringify(param),
                  contentType: 'application/json',
                  success: function (res) {
                    if (res.resultCode === '0') {
                      if (type) {
                        that.$nextTick(function () {
                          setTimeout(function () {
                            location.reload()
                          }, 2000);
                        })
                      } else {
                        if (that.state) {
                          that.Likes--
                        } else {
                          that.Likes++
                        }
                        that.getLikeType()
                        that.caseLoading = false
                      }
                      that.$message.success(that.state === 1 ? '取消成功' : '点赞成功')
                    } else if (res.resultCode === '9978') {
                      that.$message.error(res.resultMsg)
                      that.loginAction = true
                      that.showLoginModal = true
                      that.closeBtn = true
                    } else {
                      that.$message({
                        duration: 2000,
                        message: res.resultMsg,
                        type: 'info',
                        onClose: () => {
                          window.location.reload()
                        }
                      });
                    }
                    setTimeout(() => {
                      that.isLiking = false
                    }, 1000)
                  },
                  fail: function (data) {
                    that.$message.error(res.resultMsg)
                  },
                  complete: function (complete) {
                  }
                })
              },
              successCallback: function (type) {
                if (this.isPublic === '1') {
                  location.reload()
                }
                var that = this
                that.handleCloseLogin()
                if (that.loginAction) {
                  that.caseLoading = true
                  that.getLikeType()
                  that.setCollection(true)
                }

              },
              getStateNum: function () {
                var that = this
                $.ajax({
                  type: 'POST',
                  url: '/get/statByBusi',
                  data: JSON.stringify({
                    id: '<%- detail.id %>'
                  }),
                  contentType: 'application/json',
                  success: function (res) {
                    that.Likes = res.data.likeNum
                  },
                  fail: function (data) {
                    that.$message.error(res.resultMsg)
                  },
                  complete: function (complete) {
                  }
                })
              }
            },
            mounted: function () {
              var that = this
              if (!that.isLogin) {

                that.dialogVisibleReg = false
                document.getElementsByClassName('register-terms')

              } else {
                that.contact_approve_show = false
                that.getLikeType()
              }
              if (!that.isLogin && that.isPublic === '1') {
                that.showLoginModal = true
                that.closeBtn = false
              }
              that.getStateNum()
            }
          });
        </script>
        <% include footer.html %>