<% include header.html %>
  <% include ./components/header.html %>

    <!--    <% if(!isLogin){ %>-->
    <!--      <div class="download_login n_wrap">-->
    <!--        <span>-->
    <!--          登录可获得更多资料-->
    <!--        </span>-->
    <!--        <a title="登录" href="<%- memberUrl %>/login?next_origin=www&next_path=/files" rel="nofollow"-->
    <!--          id='download_login'>立即登录</a>-->
    <!--        &lt;!&ndash; <i id='download_login-close'></i> &ndash;&gt;-->
    <!--      </div>-->
    <!--      <% } %>-->
    <div v-loading="loading" element-loading-text="加载中" element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)">
      <div class="files" id='files' v-cloak>
        <div class="SearchBox" style="padding-bottom: 55px;">
          <div class="searchleftBox">
            <div class="download_tab" id='download_tab'>
              <ul>
                <li v-for='(type, index) of types' :key='index' :class='{active: currType === type.id}'
                  @click='handlerTab(type.id)'>
                  {{ type.name }}
                  <i></i>
                </li>
              </ul>
            </div>
            <div class="download_filter">
              <div class="download_filter_action">
                <span>所有分类</span>
                <em @click='handlerAction'>展开筛选</em>
              </div>
              <div class="download_filster_industry" v-show='actionFlag'>
                <span class="download_filter_tit">行业分类：</span>
                <div class="download_filster_checkboxGroup" id='download_industry'>
                  <label v-for='(item, indexIndustry) of tradeNavi' :key='indexIndustry' :class='{active: item.isCheck}'
                    @click='handlerIndustry(item)'>
                    <i></i>
                    <span>{{ item.name }}</span>
                  </label>
                </div>
              </div>
              <div class='download_filster_service' v-show='actionFlag'>
                <span class="download_filter_tit">服务类型：</span>
                <div class="download_filster_checkboxGroup" id='download_service'>
                  <label v-for='(item, indexService) of serviceNavi' :key='indexService' :class='{active: item.isCheck}'
                    @click='handlerService(item)'>
                    <i></i>
                    <span>{{ item.name }}</span>
                  </label>
                </div>
              </div>
              <div class="download_filster_keyword">
                <span class="download_filter_tit">关键词：</span>
                <div>
                  <input type="text" placeholder="请输入关键词" v-model='formParam.title' />
                  <button id='download_filster_keyword-btn' @click='getFilesList'>搜索</button>
                </div>
              </div>
              <div class="download_filster_date">
                <span class="download_filter_tit">发布日期：</span>
                <div>
                  <input type="text" class="dSearch-input" placeholder="请选择发布日期" name="time" id="time" readonly=""
                    value="<%- time %>" @click='handlerChangeTime' />
                </div>
              </div>
              <!-- <div class="download_filter_result">
                  <span class="download_filter_tit">您已选择：</span>
                  <div class="download_filter_result--items">
                    <div id='download_filter_result'>
                      <label v-for='(item, index) of isActives' :key='index'>
                        <span>{{ item.name }}</span>
                        <i @click='handleCloseItem(item)'>x</i></label>
                      <em id='reset' @click='handleClear'>清除</em>
                    </div>
                  </div>
                </div> -->
            </div>
            <div class="files_list" id='files_list'>
              <div v-if="filesList && filesList.length > 0">
                <ul>
                  <li v-for='(item, index) of filesList' :key='index'>
                    <!--                            <label @click='handlerList(item)'-->
                    <!--                                   :class="{files_list_checkbox: true, active: item.isActive, disabled: item.fileType =='link' || item.fileTyp == 'other' || (!isLogin && !item.isPublic)}"></label>-->
                    <i :class="'files_list_icon ' + item.fileType"></i>
                    <div class="files_list_con">
                      <div class='files_list_con_tit'>
                        <em>{{ item.title }}</em>
                        <!-- <i class="vip" v-if="!item.isPublic">VIP</i> -->
                      </div>
                      <div class="files_list_con_tag">
                        <span>{{ item.gmtPublishTime && item.gmtPublishTime.split(' ')[0] }}</span>
                        <span>下载：{{ item.downloadNum }}</span>
                        <span style="border: 0;">{{ item.size }}（MB）</span>
                        <em>
                          <span v-if='item.service' v-for='(serviceItem, serviceIndex) of item.service.split(",")'
                            :key='serviceIndex'>
                            {{ serviceItem }}
                          </span>
                          <span v-if='item.trade' v-for='(tradeItem, tradeIndex) of item.trade.split(",")'
                            :key='tradeIndex'>
                            {{ tradeItem }}
                          </span>
                        </em>
                        <i v-if='item.service.split(",").length +  item.trade.split(",").length > 5'>...</i>
                      </div>
                    </div>
                    <div class="files_list_con_btn">
                      <label id='download' v-if='item.fileType != "other" && item.fileType !="link"'>
                        <i class="download"></i>
                        <a href='javascript:void(0);' @click='handlerDownLoad(item)'>下载</a>
                      </label>
                      <label v-else>
                        <i></i>
                        <a href='javascript:void(0);'></a>
                      </label>
                      <label id='share'>
                        <i class="share"></i>
                        <a v-if='item.isPublic || isLogin' @click="EmailSharing(item)">邮件分享</a>
                        <a v-else href="javascript:void(0)" @click="EmailSharing(item)">邮件分享</a>
                        <!--                                    @click='handlerSearch(item)'-->
                      </label>
                    </div>
                  </li>
                </ul>
                <!--                    <div class="files_list_option">-->
                <!--                        <label id='slectAll' @click='handlerSelectAll' :class="{active: selectAllFlag}">-->
                <!--                            <i class="files_list_option-all"></i>-->
                <!--                            <em>全选</em>-->
                <!--                        </label>-->
                <!--                        <label id='shareAll'>-->
                <!--                            <i class="files_list_option-share"></i>-->
                <!--                            <a :href='selectAllAttribute' @click="ALLChangeEmailSharing">邮件分享</a>-->
                <!--                        </label>-->
                <!--                    </div>-->
              </div>
              <div v-else>
                <!-- <h3 v-if="searchhint==1">【加载中......】</h3> -->
                <h3>【很抱歉，没有为您找到对应的结果，请扩大筛选范围】</h3>
              </div>
            </div>
            <div class="pagination" style="margin: 20px 0;" v-if='totalNum'>
              <el-pagination @current-change="handleCurrentChange" :current-page.sync="currPage" :page-size="10"
                @size-change="handleSizeChange" layout="prev, pager, next, jumper" :total="totalNum">
              </el-pagination>
            </div>
          </div>
          <div class="searchrightBox bannerInfo">
            <% include ./components/sku_side_form.html %>
              <% include ./pages/quote/modal.html %>
          </div>
        </div>
        <% include ./components/login.html %>
      </div>
    </div>
    </div>
    <script src="../../public/js/information.js" type="text/javascript"></script>
    <script src="<%- locals.static %>/js/laydate.js"></script>
    <script src="<%- locals.static %>/js/FileSaver.js"></script>
    <% if(locals.env != 'prod'){ %>
    <script src="<%- locals.static %>/plugin/v2.test.js"></script>
    <% }else{ %>
    <script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js" type="text/javascript"></script>
    <% } %>
    <script src="<%- locals.static %>/js/md5.js"></script>
    <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
    <script>
      var newVue = new Vue({
        name: 'files',
        el: '#files',
        mixins: [quoteMixins],
        data: {
          types: <%- JSON.stringify(filesType) %>,
          tradeNavi: <%- JSON.stringify(tradeNavi) %>,
          serviceNavi: <%- JSON.stringify(serviceNavi) %>,
          isLogin: <%- isLogin %>,
          url: '<%- url %>',
          currType: 0,
          filesList: [],
          totalNum: 0,
          currPage: 1,
          isActives: [],
          formParam: {
            _csrf: '<%- csrf %>',
            type: '',
            pageNum: 1,
            is_publish: 1,
            service: "",
            trade: "",
            title: "",
            time: '',
          },
          actionFlag: false,
          selectAllFlag: false,
          selectAllAttribute: 'javascript:void(0)',
          searchhint: 1,
          showLoginModal: false,
          closeBtn: false,
          isRedirect: 1,
          parameter: {
            regMode: 'resources',
            regSource: 'mall',
            regFrom: window.location.href,
            busiCode: '<%- detail.id %>'
          },
          loading: false,
          entranceDownload: false,
          entranceDownloadid: '',
          isPublic: true,
          loginType: '',
          paramData: {},
          domain: '<%- domain %>',
          apihost: '<%- apihost %>',
          sendSharingUrl: '',
          isReloadPage: false,
          // 留言表单数据
          form: {
            type: '业务咨询',
            trade: '',
            tradeName: '其他',
            tradeIndex: '',
            serviceIndex: '',
            service: '',
            serviceName: '其他',
            company: '',
            provice: '',
            content: '',
            frontTitle: '<%- detail.name %>',
            customer: '<%- userInfo.userName %>',
            email: '<%- userInfo.userEmail %>',
            phone: '<%- userInfo.userPhone %>',
            imageCode: '',
            frontUrl: window.location.href,
            destCountry: '',
            verifyCode: '',
            busiCode: '<%- detail.id %>'
          },
          showModal: false,
          seconds: 59,
          timer: null,
          countdownTime: 5,
          countdownTimer: null,
          disablePhone: <%- disablePhone %>,
          disableMail: <%- disableMail %>,
          isSuccess: false,
          approve: false,
          tab: 'phone',
          provice: [],
          rules: {
            provice: [{ required: true, message: '*请选择所在城市' }],
            content: [{ required: true, message: '*请输入您的咨询内容' }],
            customer: [{ required: true, message: '*请输入您的称呼' }],
            // email: [{ validator: validatorEmail }],
            // phone: [{ validator: validatorUserPhone }],
          },
          dialogVisibleReg: false,
          contact_approve_show: true,
          loginSource: '',
          caseLoading: false,
          pageInfo: {
            title: '验证手机号',
            sendModel: '已发送到手机号：+86',
            prepend: '手机验证码'
          },
          captchaAppId: '<%- captchaAppId %>',
          pid: 'pid.mall',
          pcode: 'Z0zCnRE3IaY9Kzem',
          host: '<%- host %>',
        },
        methods: {
          initLaydate: function () {
            var that = this;
            laydate.render({
              elem: '#time',
              range: true,
              theme: '#ca4300',
              done: function (value, date, endDate) {
                if (value) {
                  $('#time').addClass("active")
                } else {
                  $('#time').removeClass("active")
                }
                // param.time = value
                // param.pageNum = 1;
                // getFileList();
                if (value) {
                  let timeArr = value.split(' - ')
                  that.formParam.time = `${timeArr[0]} 00:00:00/${timeArr[1]} 23:59:59`
                } else {
                  that.formParam.time = ''
                }

                that.currPage = 1
                that.getFilesList()
              }
            });
          },
          handlerChangeTime: function () {
            this.initLaydate()
          },
          createShareAll: function () {
            var title = '',
              body = '分享：',
              num = 0
            for (var i = 0; i < this.filesList.length; i++) {
              if (this.filesList[i].isActive) {
                num++
                title += this.filesList[i].title;
                body += this.filesList[i].title + ' ' + this.filesList[i].path + ' ';
              }
            }
            if (num) {
              var info = encodeURI("mailto:?subject=" + title + "&body=" + body);
              info = info.length > 2047 ? info.substr(0, 2000) + '...max length 1024' : info
              this.selectAllAttribute = info
            } else {
              this.selectAllAttribute = info = 'javascript:void(0)'
            }
          },
          handlerSelectAll: function () {
            for (var i = 0; i < this.filesList.length; i++) {
              if (this.filesList[i].fileType !== 'link' && this.filesList[i].fileTyp !== 'other') {
                this.filesList[i].isActive = !this.selectAllFlag
              }
            }
            this.selectAllFlag = !this.selectAllFlag
            this.createShareAll()
          },
          handlerList: function (item) {
            if (item.fileType == 'link' || item.fileTyp == 'other' || (!this.isLogin && !item.isPublic)) {
              return false
            } else {
              item.isActive = !item.isActive;
            }
          },
          handlerAction: function () {
            this.actionFlag = !this.actionFlag;
          },
          handlerTab: function (id) {
            this.formParam.type = id ? id : ''
            this.currType = id
            this.currPage = 1
            this.formParam.pageNum = 1
            this.getFilesList()
          },
          getFilesList: function () {
            var that = this
            var loading = this.$loading({
              lock: true,
              text: '加载中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
              fullscreen: false
            });
            that.searchhint = 1
            // jQuery.ajaxSettings.timeout = '15000';
            this.formParam.pageRow = 10
            this.formParam.gmtPublishTime = this.formParam.time
            jQuery.post('/getFileListApi', this.formParam, function (result) {
              if (result.resultCode === '0') {
                that.filesList = result.data.items
                that.totalNum = result.data.totalNum
                loading.close();
                jQuery('html,body').stop().animate({ scrollTop: 0 });
                if (that.filesList.length == 0) {
                  that.searchhint = 2
                }
              } else {
                that.$message({
                  message: result.resultMsg,
                  type: 'error'
                });
                that.searchhint = 2
                loading.close();
              }
            }).error(function (xhr, status, info) {
              that.searchhint = 2
              loading.close();
            })
          },
          getFilePath: function (id) {
            var that = this
            var loading = this.$loading({
              lock: true,
              text: '下载中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
              fullscreen: false
            });
            jQuery.post('/getFilePath', { id: id, _csrf: '<%- csrf %>' }, function (result) {
              if (result.success) {
                if (result.path.indexOf('http') < 0) {
                  that.downloadImg(that.url + result.path)
                } else {
                  that.downloadImg(result.path)
                }
              }

              if (that.isReloadPage) {
                setTimeout(function () {
                  that.loading = false
                  location.reload()
                }, 2000);
              } else {
                loading.close();
              }
            })
          },
          handlerIndustry: function (item) {
            if (item.isCheck) {
              for (var i = 0; i < this.tradeNavi.length; i++) {
                this.tradeNavi[i].isCheck = false
              }
              this.formParam.trade = ''
            } else {
              for (var i = 0; i < this.tradeNavi.length; i++) {
                this.tradeNavi[i].isCheck = false
              }
              item.isCheck = true
              this.formParam.trade = item.id
            }
            this.createActivesList()
          },
          handlerService: function (item) {
            if (item.isCheck) {
              for (var i = 0; i < this.serviceNavi.length; i++) {
                this.serviceNavi[i].isCheck = false
              }
              this.formParam.service = ''
            } else {
              for (var i = 0; i < this.serviceNavi.length; i++) {
                this.serviceNavi[i].isCheck = false
              }
              item.isCheck = true
              this.formParam.service = item.id
            }
            this.createActivesList()
          },
          createActivesList: function () {
            this.isActives = []
            for (var i = 0; i < this.tradeNavi.length; i++) {
              if (this.tradeNavi[i].isCheck) {
                this.isActives.push(this.tradeNavi[i])
              }
            }
            for (var i = 0; i < this.serviceNavi.length; i++) {
              if (this.serviceNavi[i].isCheck) {
                this.isActives.push(this.serviceNavi[i])
              }
            }
            this.currPage = 1
            this.formParam.pageNum = 1
            this.getFilesList()
          },
          handleCloseItem: function (item) {
            item.isCheck = false
            for (var i = 0; i < this.tradeNavi.length; i++) {
              if (this.tradeNavi[i].id === item.id && this.tradeNavi[i].name === item.name) {
                this.formParam.trade = ''
              }
            }
            for (var i = 0; i < this.serviceNavi.length; i++) {
              if (this.serviceNavi[i].id === item.id && this.serviceNavi[i].name === item.name) {
                this.formParam.service = ''
              }
            }
            this.createActivesList()
          },
          handleClear: function () {
            this.isActives = []
            for (var i = 0; i < this.tradeNavi.length; i++) {
              this.tradeNavi[i].isCheck = false
            }
            for (var i = 0; i < this.serviceNavi.length; i++) {
              this.serviceNavi[i].isCheck = false
            }
            this.formParam.service = ''
            this.formParam.trade = ''
            this.createActivesList()
          },
          handleSizeChange: function (val) {
            this.formParam.pageNum = val
            this.getFilesList()
          },
          handleCurrentChange: function (val) {
            this.formParam.pageNum = val
            this.getFilesList()
          },
          handlerDownLoad: function (item) {
            var isToken = this.checkToken()
            if (item.isPublic || this.isLogin && isToken) {
              this.isReloadPage = false
              this.getFilePath(item.id);
            } else {
              // window.location.href = '<%- memberUrl %>/login';
              window.localStorage['inforPageIndex'] = this.formParam.pageNum
              console.log('登录弹框！')
              this.isReloadPage = true
              this.parameter.regFrom = item.title
              this.parameter.busiCode = item.id
              this.showLoginModal = true
              this.closeBtn = true
              this.entranceDownload = true
              this.entranceDownloadid = item.id
            }
          },
          downloadImg: function (url) {
            if (!!window.ActiveXObject || "ActiveXObject" in window) {
              var filename = url.substr(url.lastIndexOf('/') + 1, url.length);
              var xhr = new XMLHttpRequest();
              xhr.open('GET', encodeURI(url), true);
              xhr.responseType = 'blob';
              xhr.setRequestHeader("Authorization", "Basic a2VybWl0Omtlcm1pdA==")
              xhr.onload = function () {
                if (xhr.status === 200) {
                  //将图片文件用浏览器中下载
                  saveAs(xhr.response, filename);
                  document.execCommand(xhr.response, '', filename);
                }
              }
              xhr.send();
            } else {
              var a = document.createElement('a');          // 创建一个a节点插入的document
              var event = new MouseEvent('click')           // 模拟鼠标click点击事件
              a.download = ''                  // 设置a节点的download属性值
              a.href = url;                                 // 将图片的src赋值给a节点的href
              a.dispatchEvent(event);                      // 触发鼠标点击事件
            }
          },
          // 校验token
          checkToken: function () {
            var that = this
            let isCheck
            $.ajax({
              type: 'POST',
              url: '/check/token',
              async: false,
              data: JSON.stringify({}),
              contentType: 'application/json',
              success: function (res) {
                if (res.resultCode === '0') {
                  isCheck = true;
                } else {
                  isCheck = false;
                }
              },
              fail: function (data) {
                that.$message.error(res.resultMsg)
              },
              complete: function (complete) {
              }
            })
            return isCheck
          },
          ALLChangeEmailSharing: function () {
            var that = this
            console.log(that.filesList)
            var idlist = []
            for (var i = 0; i < this.filesList.length; i++) {
              if (this.filesList[i].isActive) {
                idlist.push(this.filesList[i].id)
              }
            }
            var isPublicArr = this.filesList.some(info => {
              return !info.isPublic
            })
            if (isPublicArr) {
              console.log('登录弹框！')
              this.parameter.regFrom = item.title
              this.parameter.busiCode = item.id
              this.showLoginModal = true
              this.closeBtn = true
            } else {
              that.sendSharingData({
                ids: idlist
              })
            }
          },
          EmailSharing: function (item) {
            console.log(item)
            this.sendSharingUrl = "mailto:?subject=分享：" + item.title + "&body=" + item.title + " " + this.url + item.path
            var isToken = this.checkToken()
            var that = this
            var param = {
              ids: [item.id]
            }
            if (item.isPublic || that.isLogin && isToken) {
              that.isReloadPage = false
              that.sendSharingData(param)
            } else {
              window.localStorage['inforPageIndex'] = this.formParam.pageNum
              console.log('登录弹框！')
              that.isReloadPage = true
              that.isPublic = false
              that.loginType = 'share'
              that.paramData = param
              that.parameter.regFrom = item.title
              that.parameter.busiCode = item.id
              that.showLoginModal = true
              that.closeBtn = true
            }

          },
          sendSharingData: function (param) {
            var that = this
            $.ajax({
              type: 'POST',
              url: '/file/emailSharing',
              data: JSON.stringify(param),
              contentType: 'application/json',
              success: function (res) {
                if (res.resultCode === '0') {
                  var a = document.createElement('a');
                  var event = new MouseEvent('click')
                  a.download = ''
                  a.href = that.sendSharingUrl;
                  a.dispatchEvent(event);
                  if (that.isReloadPage) {
                    setTimeout(function () {
                      location.reload()
                    }, 1000)
                  }

                }
              },
              fail: function (data) {
                that.$message.error(res.resultMsg)
              },
              complete: function (complete) {
              }
            })
          },
          handleCloseLogin: function () {
            this.showLoginModal = false
            if (this.entranceDownload) {
              this.entranceDownload = false
            }
          },
          successCallback: function (type) {
            var that = this
            if (!that.isPublic && that.loginType === 'share') {
              console.log(that.paramData)
              that.sendSharingData(that.paramData)
            }
            if (that.entranceDownload) {
              that.loading = true
              that.getFilePath(that.entranceDownloadid)
            }
            that.handleCloseLogin()
          },
          headerRegister: function () {
            this.showLoginModal = true
            this.closeBtn = true
            this.loginSource = 'header'
          },
          handleCloseLogin: function () {
            this.showLoginModal = false
          },
          // successCallback: function (type) {
          //   var that = this
          //   that.showLoginModal = false
          //   that.caseLoading = true
          //   location.reload()
          // },
          handleChange: function () { },
          handleJumpStep1: function () {
            window.open('<%- memberUrl %>/questionnaire/step1')
          },
        },
        mounted: function () {
          // 默认要初始化插件，否者第一次点击无效
          var index = localStorage.getItem("inforPageIndex")
          if (index) {
            this.formParam.pageNum = index
            this.currPage = index
          }
          this.getFilesList()
          setTimeout(function () {
            window.localStorage.removeItem('inforPageIndex')
          }, 1000);

          this.initLaydate()
          var obj = {
            name: '全部',
            id: 0,
            is_show: 1
          }
          this.types.unshift(obj)
          var that = this
        },
      })

      $(function () {
        // calendar controls
        // side right

        var $_yours = $(".yours");
        $_yours.click(function () {
          var $_con = $(this).siblings(".your-cons");
          if ($_con.css("height") == "0px") {
            $_con.animate({ "height": 486 }).end().parents(".your").siblings(".your").children(".your-cons").animate({ "height": 0 });
            $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jianhao.png');
            $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(0).attr("src", "<%- locals.static %>/images/jiahao.png");
            $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(1).attr("src", "<%- locals.static %>/images/jiahao.png")
          } else {
            $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jiahao.png');
            $_con.animate({ "height": 0 });
          }
        })

      })
    </script>

    <% include footer.html %>