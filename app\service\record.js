'use strict';

const Service = require('egg').Service
const moment = require('moment');

class RecordService extends Service {
  async new(params) {
    return {
      result: {}
    };
  }

  async successRecord(params) {
    const {
      app
    } = this;
    const row = {
      url: params.url,
      ip: params.ip
    }
    const result = await app.mysql.insert('success_record', row);
    return {
      result
    };
  }

  async downlaodRecord(params) {
    const {
      app
    } = this;
    const row = {
      file_name: params.file_name || '',
      group: params.group || '',
      user_name: params.user_name,
      user_phone: params.user_phone,
      user_email: params.user_email,
      create_time: moment().format('YYYY-MM-DD HH:mm:ss')
    }
    const detail = await app.mysql.get('catalog', {
      id: params.file_type
    });

    row.file_type = detail.name;
    let data_type = 'res',
      data_id = params.id;
    let sql = 'select checkflow.id,checkflow.data_id,checkflow.tran_status,checkflow.tran_admin_time,admin.`name` from checkflow left join admin on checkflow.tran_admin_id=admin.id where checkflow.is_overdue=0 and checkflow.data_type=\'' + data_type + '\' and checkflow.data_id=' + data_id;
    const result = await app.mysql.query(sql);
    if (result.length) {
      row.group = result[0].name
    } else {
      row.group = ''
    }
    await app.mysql.insert('analysis_download', row);
  }

  async getDetail(data_type, data_id) {
    const {
      app
    } = this;

    let sql = 'select checkflow.id,checkflow.data_id,checkflow.tran_status,checkflow.tran_admin_time,admin.`name` from checkflow left join admin on checkflow.tran_admin_id=admin.id where checkflow.is_overdue=0 and checkflow.data_type=\'' + data_type + '\' and checkflow.data_id=' + data_id;
    const result = await app.mysql.query(sql);
    if (result && result.length > 0) {
      let item = result[0];
      if (item.name == null) {
        item.name = '';
      }
      if (item.tran_admin_time == null) {
        item.tran_admin_time = '';
      } else {
        item.tran_admin_time = moment(item.tran_admin_time).format('YYYY-MM-DD HH:mm:ss');
      }
      return result[0];
    }
    return null;
  }
}

module.exports = RecordService;