# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ᱢᱟᱲᱟᱝ ᱥᱟᱦᱴᱟ
previous_label=ᱢᱟᱲᱟᱝᱟᱜ
next.title=ᱤᱱᱟᱹ ᱛᱟᱭᱚᱢ ᱥᱟᱦᱴᱟ
next_label=ᱤᱱᱟᱹ ᱛᱟᱭᱚᱢ

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=ᱥᱟᱦᱴᱟ
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=ᱨᱮᱭᱟᱜ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} ᱠᱷᱚᱱ {{pagesCount}})

zoom_out.title=ᱦᱤᱲᱤᱧ ᱛᱮᱭᱟᱨ
zoom_out_label=ᱦᱤᱲᱤᱧ ᱛᱮᱭᱟᱨ
zoom_in.title=ᱢᱟᱨᱟᱝ ᱛᱮᱭᱟᱨ
zoom_in_label=ᱢᱟᱨᱟᱝ ᱛᱮᱭᱟᱨ
zoom.title=ᱡᱩᱢ
presentation_mode.title=ᱩᱫᱩᱜ ᱥᱚᱫᱚᱨ ᱚᱵᱚᱥᱛᱟ ᱨᱮ ᱚᱛᱟᱭ ᱢᱮ
presentation_mode_label=ᱩᱫᱩᱜ ᱥᱚᱫᱚᱨ ᱚᱵᱚᱥᱛᱟ ᱨᱮ
open_file.title=ᱨᱮᱫ ᱡᱷᱤᱡᱽ ᱢᱮ
open_file_label=ᱡᱷᱤᱡᱽ ᱢᱮ
print.title=ᱪᱷᱟᱯᱟ
print_label=ᱪᱷᱟᱯᱟ
download.title=ᱰᱟᱩᱱᱞᱚᱰ
download_label=ᱰᱟᱩᱱᱞᱚᱰ
bookmark.title=ᱱᱤᱛᱚᱜᱟᱜ ᱧᱮᱞ (ᱱᱚᱶᱟ ᱡᱷᱚᱨᱠᱟ ᱨᱮ ᱱᱚᱠᱚᱞ ᱟᱨ ᱵᱟᱝ ᱡᱷᱤᱡᱽ ᱢᱮ )
bookmark_label=ᱱᱤᱛᱚᱜᱟᱜ ᱧᱮᱞ

# Secondary toolbar and context menu
tools.title=ᱦᱟᱹᱛᱤᱭᱟᱹᱨ ᱠᱚ
tools_label=ᱦᱟᱹᱛᱤᱭᱟᱹᱨ ᱠᱚ
first_page.title=ᱯᱩᱭᱞᱩ ᱥᱟᱦᱴᱟ ᱥᱮᱫ ᱪᱟᱞᱟᱜ ᱢᱮ
first_page_label=ᱯᱩᱭᱞᱩ ᱥᱟᱦᱴᱟ ᱥᱮᱫ ᱪᱟᱞᱟᱜ ᱢᱮ
last_page.title=ᱢᱩᱪᱟᱹᱫ ᱥᱟᱦᱴᱟ ᱥᱮᱫ ᱪᱟᱞᱟᱜ ᱢᱮ
last_page_label=ᱢᱩᱪᱟᱹᱫ ᱥᱟᱦᱴᱟ ᱥᱮᱫ ᱪᱟᱞᱟᱜ ᱢᱮ
page_rotate_cw.title=ᱜᱷᱚᱰᱤ ᱦᱤᱥᱟᱹᱵ ᱛᱮ ᱟᱹᱪᱩᱨ
page_rotate_cw_label=ᱜᱷᱚᱰᱤ ᱦᱤᱥᱟᱹᱵ ᱛᱮ ᱟᱹᱪᱩᱨ
page_rotate_ccw.title=ᱜᱷᱚᱰᱤ ᱦᱤᱥᱟᱹᱵ ᱛᱮ ᱩᱞᱴᱟᱹ ᱟᱹᱪᱩᱨ
page_rotate_ccw_label=ᱜᱷᱚᱰᱤ ᱦᱤᱥᱟᱹᱵ ᱛᱮ ᱩᱞᱴᱟᱹ ᱟᱹᱪᱩᱨ

cursor_text_select_tool.title=ᱚᱞ ᱵᱟᱪᱷᱟᱣ ᱦᱟᱹᱛᱤᱭᱟᱨ ᱮᱢ ᱪᱷᱚᱭ ᱢᱮ
cursor_text_select_tool_label=ᱚᱞ ᱵᱟᱪᱷᱟᱣ ᱦᱟᱹᱛᱤᱭᱟᱨ
cursor_hand_tool.title=ᱛᱤ ᱦᱟᱹᱛᱤᱭᱟᱨ ᱮᱢ ᱪᱷᱚᱭ ᱢᱮ
cursor_hand_tool_label=ᱛᱤ ᱦᱟᱹᱛᱤᱭᱟᱨ

scroll_page.title=ᱥᱟᱦᱴᱟ ᱜᱩᱲᱟᱹᱣ ᱵᱮᱵᱷᱟᱨ ᱢᱮ
scroll_page_label=ᱥᱟᱦᱴᱟ ᱜᱩᱲᱟᱹᱣ
scroll_vertical.title=ᱥᱤᱫᱽ ᱜᱩᱲᱟᱹᱣ ᱵᱮᱵᱷᱟᱨ ᱢᱮ
scroll_vertical_label=ᱥᱤᱫᱽ ᱜᱩᱲᱟᱹᱣ
scroll_horizontal.title=ᱜᱤᱛᱤᱡ ᱛᱮ ᱜᱩᱲᱟᱹᱣ ᱵᱮᱵᱷᱟᱨ ᱢᱮ


# Document properties dialog box
document_properties_file_name=ᱨᱮᱫᱽ ᱧᱩᱛᱩᱢ :
document_properties_file_size=ᱨᱮᱫᱽ ᱢᱟᱯ :
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} ᱵᱟᱭᱤᱴ ᱠᱚ)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} ᱵᱟᱭᱤᱴ ᱠᱚ)
document_properties_title=ᱧᱩᱛᱩᱢ :
document_properties_author=ᱚᱱᱚᱞᱤᱭᱟᱹ :
document_properties_subject=ᱵᱤᱥᱚᱭ :
document_properties_keywords=ᱠᱟᱹᱴᱷᱤ ᱥᱟᱵᱟᱫᱽ :
document_properties_creation_date=ᱛᱮᱭᱟᱨ ᱢᱟᱸᱦᱤᱛ :
document_properties_modification_date=ᱵᱚᱫᱚᱞ ᱦᱚᱪᱚ ᱢᱟᱹᱦᱤᱛ :
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=ᱵᱮᱱᱟᱣᱤᱡ :
document_properties_producer=PDF ᱛᱮᱭᱟᱨ ᱚᱰᱚᱠᱤᱡ :
document_properties_version=PDF ᱵᱷᱟᱹᱨᱥᱚᱱ :
document_properties_page_count=ᱥᱟᱦᱴᱟ ᱞᱮᱠᱷᱟ :
document_properties_page_size=ᱥᱟᱦᱴᱟ ᱢᱟᱯ :
document_properties_page_size_unit_inches=ᱤᱧᱪ
document_properties_page_size_unit_millimeters=ᱢᱤᱢᱤ
document_properties_page_size_orientation_portrait=ᱯᱚᱴᱨᱮᱴ
document_properties_page_size_orientation_landscape=ᱞᱮᱱᱰᱥᱠᱮᱯ
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ᱪᱤᱴᱷᱤ
document_properties_page_size_name_legal=ᱠᱟᱹᱱᱩᱱᱤ
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
document_outline_label=ᱫᱚᱞᱤᱞ ᱛᱮᱭᱟᱨ ᱛᱮᱫ
attachments.title=ᱞᱟᱴᱷᱟ ᱥᱮᱞᱮᱫ ᱠᱚ ᱩᱫᱩᱜᱽ ᱢᱮ
attachments_label=ᱞᱟᱴᱷᱟ ᱥᱮᱞᱮᱫ ᱠᱚ
thumbs.title=ᱪᱤᱛᱟᱹᱨ ᱟᱦᱞᱟ ᱠᱚ ᱩᱫᱩᱜᱽ ᱢᱮ
thumbs_label=ᱪᱤᱛᱟᱹᱨ ᱟᱦᱞᱟ ᱠᱚ
findbar.title=ᱫᱚᱞᱤᱞ ᱨᱮ ᱯᱟᱱᱛᱮ
findbar_label=ᱥᱮᱸᱫᱽᱨᱟᱭ ᱢᱮ

# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title={{page}} ᱥᱟᱦᱴᱟ
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}} ᱥᱟᱦᱴᱟ ᱨᱮᱭᱟᱜ ᱪᱤᱛᱟᱹᱨ ᱟᱦᱞᱟ

# Find panel button title and messages
find_previous.title=ᱟᱭᱟᱛ ᱦᱤᱸᱥ ᱨᱮᱭᱟᱜ ᱯᱟᱹᱦᱤᱞ ᱥᱮᱫᱟᱜ ᱚᱰᱚᱠ ᱧᱟᱢ ᱢᱮ
find_next.title=ᱟᱭᱟᱛ ᱦᱤᱸᱥ ᱨᱮᱭᱟᱜ ᱤᱱᱟᱹ ᱛᱟᱭᱚᱢ ᱚᱰᱚᱠ ᱧᱟᱢ ᱢᱮ
find_highlight=ᱡᱷᱚᱛᱚ ᱩᱫᱩᱜ ᱨᱟᱠᱟᱵ
find_match_case_label=ᱡᱚᱲ ᱠᱟᱛᱷᱟ
find_reached_top=ᱫᱚᱞᱤᱞ ᱨᱮᱭᱟᱜ ᱪᱤᱴ ᱨᱮ ᱥᱮᱴᱮᱨ, ᱞᱟᱛᱟᱨ ᱠᱷᱚᱱ ᱞᱮᱛᱟᱲ
find_reached_bottom=ᱫᱚᱞᱤᱞ ᱨᱮᱭᱟᱜ ᱢᱩᱪᱟᱹᱫ ᱨᱮ ᱥᱮᱴᱮᱨ, ᱪᱚᱴ ᱠᱷᱚᱱ ᱞᱮᱛᱟᱲ
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_not_found=ᱛᱚᱯᱚᱞ ᱫᱚᱱᱚᱲ ᱵᱟᱝ ᱧᱟᱢ ᱞᱮᱱᱟ

# Error panel labels
error_more_info=ᱵᱟᱹᱲᱛᱤ ᱞᱟᱹᱭ ᱥᱚᱫᱚᱨ
error_less_info=ᱠᱚᱢ ᱞᱟᱹᱭ ᱥᱚᱫᱚᱨ
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ᱠᱷᱚᱵᱚᱨ : {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=ᱵᱟᱝ : {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ᱨᱮᱫᱽ : {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ᱜᱟᱨ : {{line}}
rendering_error=ᱥᱟᱦᱴᱟ ᱮᱢ ᱡᱚᱦᱚᱠ ᱢᱤᱫ ᱵᱷᱩᱞ ᱦᱩᱭ ᱮᱱᱟ ᱾

# Predefined zoom values
page_scale_width=ᱥᱟᱦᱴᱟ ᱚᱥᱟᱨ
page_scale_fit=ᱥᱟᱦᱴᱟ ᱠᱷᱟᱯ
page_scale_auto=ᱟᱡᱼᱟᱡ ᱛᱮ ᱦᱩᱰᱤᱧ ᱞᱟᱹᱴᱩ ᱛᱮᱭᱟᱨ
page_scale_actual=ᱴᱷᱤᱠ ᱢᱟᱨᱟᱝ ᱛᱮᱫ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages
loading_error=PDF ᱞᱟᱫᱮ ᱡᱚᱦᱚᱜ ᱢᱤᱫ ᱵᱷᱩᱞ ᱦᱩᱭ ᱮᱱᱟ ᱾
invalid_file_error=ᱵᱟᱝ ᱵᱟᱛᱟᱣ ᱟᱨᱵᱟᱝᱠᱷᱟᱱ ᱰᱤᱜᱟᱹᱣ PDF ᱨᱮᱫᱽ ᱾
missing_file_error=ᱟᱫᱟᱜ PDF ᱨᱮᱫᱽ ᱾

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} ᱢᱚᱱᱛᱚ ᱮᱢ]
password_label=ᱱᱚᱶᱟ PDF ᱨᱮᱫᱽ ᱡᱷᱤᱡᱽ ᱞᱟᱹᱜᱤᱫ ᱫᱟᱱᱟᱝ ᱥᱟᱵᱟᱫᱽ ᱟᱫᱮᱨ ᱢᱮ ᱾
password_invalid=ᱵᱷᱩᱞ ᱫᱟᱱᱟᱝ ᱥᱟᱵᱟᱫᱽ ᱾ ᱫᱟᱭᱟᱠᱟᱛᱮ ᱫᱩᱦᱲᱟᱹ ᱪᱮᱥᱴᱟᱭ ᱢᱮ ᱾
password_ok=ᱴᱷᱤᱠ

printing_not_supported=ᱦᱚᱥᱤᱭᱟᱨ : ᱪᱷᱟᱯᱟ ᱱᱚᱣᱟ ᱯᱟᱱᱛᱮᱭᱟᱜ ᱫᱟᱨᱟᱭ ᱛᱮ ᱯᱩᱨᱟᱹᱣ ᱵᱟᱭ ᱜᱚᱲᱚᱣᱟᱠᱟᱱᱟ ᱾
printing_not_ready=ᱦᱩᱥᱤᱭᱟᱹᱨ : ᱪᱷᱟᱯᱟ ᱞᱟᱹᱜᱤᱫ PDF ᱯᱩᱨᱟᱹ ᱵᱟᱭ ᱞᱟᱫᱮ ᱟᱠᱟᱱᱟ ᱾
web_fonts_disabled=ᱣᱮᱵᱽ ᱪᱤᱠᱤ ᱵᱟᱝ ᱦᱩᱭ ᱦᱚᱪᱚ ᱠᱟᱱᱟ : ᱵᱷᱤᱛᱤᱨ ᱛᱷᱟᱯᱚᱱ PDF ᱪᱤᱠᱤ ᱵᱮᱵᱷᱟᱨ ᱵᱟᱝ ᱦᱩᱭ ᱠᱮᱭᱟ ᱾

