.NGOform * {
  margin: 0 auto;
  padding: 0;
  color: #333e48;
  font-family: National2;
  font-size: 18px;
  font-style: normal;
  font-weight: 300;
  text-decoration: none;
  line-height: 1.25;
}

ul,
li {
  /* list-style: none; */
}

img {
  border: 0;
}

button {
  border: 0;
  outline: none;
  cursor: pointer;
}

em,
i {
  font-weight: normal;
}


.NGOform input {
  font-family: National2;
  font-size: 18px;
  font-style: normal;
  font-weight: 300;
  text-decoration: none;
  outline-color: #f05b24;
  color: #333e48;
  background-color: rgba(0, 0, 0, 0.10);
  border: 0;
  max-width: 100%;
  background: #FFF;
  border: 1px solid #999;
  box-sizing: border-box;
  padding: 6px;
  -webkit-transition: all 0.1s linear;
  transition: all 0.1s linear;
  border-radius: 0;
  background-clip: padding-box;
}

.wrap {
  width: 1226px;
  margin: 0 auto;
}

.NGOform .step {
  padding: 80px 50px 0 50px;
  min-height: 500px;
}

.NGOform .tool {
  text-align: right;
  padding-top: 20px;
}

.NGOform .tool button {
  margin-left: 20px;
  background: transparent;
}

.button-group {
  text-align: center;
  /* padding-top: 40px; */
  padding-bottom: 100px;
  position: fixed;
  bottom: 0;
  background: #fff;
  width: 100%;
  padding-top: 20px;
  padding-bottom: 20px;
  box-shadow: 0 0 2px #ddd;
}

.button-group button {
  background-color: #f05b24;
  color: #fff;
  font-family: National2;
  font-size: 15px;
  font-weight: 400;
  float: none !important;
  font-size: 15px;
  border: 1px solid transparent;
  box-shadow: none;
  margin-left: 0;
  opacity: 1;
  filter: alpha(opacity=100);
  border: 1px solid transparent;
  height: 100%;
  min-width: 100px;
  padding-top: 10px;
  padding-bottom: 10px;
  white-space: normal;
  margin-bottom: 10px;
  padding: 15px 25px;
  text-shadow: none;
  font-size: 16px;
  font-family: inherit;
  font-weight: normal;
  color: #000;
  outline: none;
  opacity: .9;
  -webkit-transition: all 0.2s linear;
  transition: all 0.2s linear;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
  color: #fff;
}

.button-group button#submit,
.button-group button#prev {
  display: none;
}

.button-group .tips {
  display: none;
}

.NGOform {
  position: relative;
  padding-bottom: 160px;
}

.NGOform h1 {
  font-family: National2;
  font-size: 34px;
  font-style: normal;
  font-weight: 700;
  text-decoration: none;
  color: #f05b24;
  line-height: 1.25;
  text-align: center;
  padding-top: 100px;
}

.NGOform h2 {
  font-family: National2;
  font-size: 34px;
  font-style: normal;
  font-weight: 400;
  text-decoration: none;
  color: #f05b24;
  line-height: 1.25;
  padding-top: 32px;
}

.NGOform .step {
  display: none;
}

.NGOform .des p {
  padding-top: 32px;
  line-height: 1.5;
}

.NGOform .des ul {
  padding-top: 32px;
  padding-left: 20px;
  list-style: unset;
}

.NGOform .des li {
  line-height: 1.5;
  list-style: unset;
}

.NGOform .step2 div {
  padding-top: 15px;
}

.NGOform .step2 input {
  width: 100%;
}

.NGOform dl {
  margin-top: 20px;
  padding: 15px 5px;
  border: 1px solid #fff;
}
.NGOform dd {
  border: 1px solid #fff;
}

.NGOform dl.emptyAnser dt,
.NGOform dd.emptyAnser {
  /* border: 1px solid #f00; */
  /* border-radius: 3px; */
  color: #f00;
}

.NGOform dl dl {
  margin-top: 10px;
  padding: 0;
}

.NGOform dt {
  font-family: helvetica neue,
    helvetica,
    arial,
    sans-serif;
  font-size: 26px;
  font-style: normal;
  font-weight: 400;
  text-decoration: none;
  color: #333e48;
  line-height: 1.25;
  /* padding: 0 0 24px 0; */
}

.NGOform dd {
  padding: 5px 0;
  border-radius: 3px;
  margin-top: 5px;
}

.NGOform dd label {
  cursor: pointer;
}

.NGOform dd:hover,
.NGOform dd.active {
  /* background: #ddd; */
}

.NGOform dd label {
  margin-right: 20px;
}

.NGOform dd input {
  margin-right: 10px;
}

.NGOform dd input[type='radio'],
.NGOform dd input[type='checkbox'] {
  width: 17px;
  height: 17px;
}

.NGOform dd input[type='text'] {
  margin: 0;
  border-radius: 3px;
  width: 100%;
}


.NGOform .step3 input[type='text'] {
  width: 100%;
}

.NGOform .shade {
  background: rgba(255, 255, 255, 0.9);
  height: 100vh;
  width: 100%;
  position: fixed;
  z-index: 999999;
  top: 0;
  left: 0;
  text-align: center;
  line-height: 100vh;
  display: none;
}

.answerEnd {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9999;
  display: none;
}

.answerEnd .box {
  width: 500px;
  background: #fff;
  padding: 20px 10px 40px 10px;
  border-radius: 5px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  position: fixed;
  z-index: 10000;
  cursor: inherit;
}

.answerEnd .box h2 {
  font-weight: 400;
  text-align: center;
}

.answerEnd .box p {
  text-align: center;
}

.answerEnd .box .resultHtml,
.answerEnd .box .text1 {
  text-align: left;
  padding: 0 20px;
}

.answerEnd .box button {
  /* width: 50px; */
  /* height: 33px; */
  /* line-height: 28px; */
  text-align: center;
  color: #fff;
  font-size: 14px;
  margin-right: 20px;
  background: #f05b24;
  padding: 10px 15px;
  border-radius: 3px;
}

.answerEnd .box button.cancel {
  background: #fff;
  border: 1px solid #f05b24;
  color: #f05b24;
}

.answerEnd .btn {
  text-align: center;
}

.NGOform .result {
  display: none;
}

/* .NGOform .result #reportDown {
  color: #f05b24;
  text-decoration: underline;
  padding-top: 20px;
  cursor: pointer;
} */

.button-group .tips a {
  color: #f05b24;
}

.resultBox div {
  padding-top: 20px;
}

.resultBox a {
  color: #f05b24;
  padding-left: 20px;
}

.resultBox a i {
  color: #f05b24;
}

.resultBox #isEnd,
.resultBox #noHasExport,
.resultBox #reportDown {
  display: none;
}

a#fixedSave {
  /* position: fixed; */
  /* bottom: 50px; */
  /* z-index: 2; */
  color: #f05b24;
  text-decoration: underline;
  /* width: 60px; */
  /* height: 35px; */
  /* line-height: 35px; */
  /* border-radius: 3px; */
  /* font-size: 16px; */
  /* text-align: center; */
  /* cursor: pointer; */
  padding-left: 20px;
  display: none;
}