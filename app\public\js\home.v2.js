$(function () {
  $('#n_nav li').hover(function () {
    $(this).find('dl').show();
  }, function () {
    $(this).find('dl').hide();
  });

  $(".informaition_info_tit span").on('click', function () {
    $(this).addClass('active').siblings().removeClass('active');
    var index = $(this).index();
    if (index) {
      $('#home_informaition').hide()
      $('#home_informaition_1').show()
      $(this).siblings('a').attr('href', '/information')
    } else {
      $('#home_informaition').show()
      $('#home_informaition_1').hide()
      $(this).siblings('a').attr('href', '/information?type=88')
    }
  });

   //滚动插件
   $.fn.extend({
    Scroll: function (selector) {
      var box = selector,
        ul = selector.find('ul');
      var doubleDom = ul.html();
      ul.append(doubleDom);

      function sildeAnimate() {
        ul.stop().animate({
          top: '-100px'
        }, 2000, function () {
          var cloneDom = ul.find('li:lt(5)')
          ul.append(cloneDom);
          ul.css({
            top: 0
          })
        })
      }
      var sildeAnimateInterval = setInterval(function () {
        sildeAnimate()
      }, 4000);
      box.hover(function () {
        clearInterval(sildeAnimateInterval)
      }, function () {
        sildeAnimateInterval = setInterval(function () {
          sildeAnimate()
        }, 4000);
      });
    },
    // 首页OIQ滚动
    HomeOiq: function (selector) {
      var box = selector,
        ul = selector.find('ul');
      var doubleDom = ul.html();
      ul.append(doubleDom);

      function sildeAnimateOiq() {
        ul.stop().animate({
          top: '-40px'
        }, 1000, function () {
          var cloneDom = ul.find('li:lt(1)')
          ul.append(cloneDom);
          ul.css({
            top: 0
          })
        })
      }
      var sildeAnimateOiqInterval = setInterval(function () {
        sildeAnimateOiq()
      }, 2000);
      box.hover(function () {
        clearInterval(sildeAnimateOiqInterval)
      }, function () {
        sildeAnimateOiqInterval = setInterval(function () {
          sildeAnimateOiq()
        }, 2000);
      });
    },
    // 首页在线下单滚动
    HomeOnineOrder: function (selector) {
      var box = selector,
        ul = selector.find('ul');
      var doubleDom = ul.html();
      ul.append(doubleDom);

      function sildeAnimateOnlineOrder() {
        ul.stop().animate({
          top: '-20px'
        }, 1000, function () {
          var cloneDom = ul.find('li:lt(1)')
          ul.append(cloneDom);
          ul.css({
            top: 0
          })
        })
      }
      var sildeAnimateOnlineOrderInterval = setInterval(function () {
        sildeAnimateOnlineOrder()
      }, 2000);
      box.hover(function () {
        clearInterval(sildeAnimateOnlineOrderInterval)
      }, function () {
        sildeAnimateOnlineOrderInterval = setInterval(function () {
          sildeAnimateOnlineOrder()
        }, 2000);
      });
    },
  });

  $("#home_informaition").Scroll($("#home_informaition"));
  $("#home_informaition_1").Scroll($("#home_informaition_1"));
  $("#oiq_msg").HomeOiq($("#oiq_msg"));
  $("#oiq_msg_lab").HomeOiq($("#oiq_msg_lab"));
  $("#onlineOrder_msg").HomeOnineOrder($("#onlineOrder_msg"));

  // 
  $(function () {
    var liWidth = $('.homeCon-head ul li').width();
    $('.homeCon-head ul li').hover(function () {
      var index = $(this).index();
      $(this).closest('ul').find('.homeCon-head-bg').stop().animate({
        left: liWidth * index + 'px'
      }, 300);
    });

    $('.homeCon-head ul').hover(function () {

    }, function () {
      var liWidth = $(this).find('li').width();
      $(this).find('li').each(function (index) {
        if ($(this).hasClass("active")) {
          $(this).closest('ul').find('.homeCon-head-bg').stop().animate({
            left: liWidth * index + 'px'
          }, 100);
        }
      })
    });
  });

  // 
  $('.homeCon7-body').find('li').hover(function () {
    $(this).find('.homeCon7-mark').stop().animate({
      opacity: '0.5'
    }, 200);
  }, function () {
    $(this).find('.homeCon7-mark').stop().animate({
      opacity: '0'
    }, 200);
  });

  // 2021年3月30日
  // TIC-3668
  // 头部搜索框点击事件
  $(".home_top_search_btn").on('click', function () {
    var value = $(this).closest('div').find('input').val().trim();
    if (value) {
      if (value === '在线询价' || value === '在线报价') {
        window.open('/oiq/start', '_blank')
      } else {
        hotKeywordClick(value)
      }
    }
  });

  // 输入框输入、回车事件
  $('.home_top_search_input').on('keyup', function (e) {
    var value = $(this).val().trim()
    var keycode = e.keyCode;
    if (value && keycode == 13) $(".home_top_search_btn").trigger("click");
    hotKeywordQry(value);
  })

  // 记录热词被点击
  function hotKeywordClick(value) {
    var param = {
      keyword: value,
      _csrf: '<%- csrf %>'
    }
    $.ajax({
      type: 'POST',
      url: '/hotWord/click',
      data: param,
      success: function (res) {},
      fail: function (data) {},
      complete: function (complete) {
        window.open('/search?q=' + encodeURIComponent(value), '_blank')
      }
    })
  }

  // 输入框获取焦点事件
  $(".home_top_search_input").on('focus', function () {
    $('.home_top_search_hotWord').hide();
    var value = $(this).val().trim()
    hotKeywordQry(value);
  })

  // 输入框失去焦点事件
  $(".home_top_search_input").on('blur', function () {
    var searhVal = $('.home_top_search_input').val().trim();
    if (searhVal === '') $('.home_top_search_hotWord').show();
    $('.home_top_search_history').fadeOut()
  })

  function hotKeywordQry(value) {
    var param = {
        keyword: value,
        pageNum: 1,
        pageRow: 6,
      },
      linkageWordDom = '';
    $.ajax({
      type: 'POST',
      url: '/hotWord/qry',
      data: param,
      success: function (res) {
        var data = typeof res === 'string' ? JSON.parse(res) : res;
        var datas = data.data
        if (data.resultCode === '0' && datas.items.length) {
          linkageWordDom = '';
          var items = datas.items;
          for (var i = 0; i < items.length; i++) {
            if (items[i].keyword) {
              linkageWordDom += '<li data-keyword="' + items[i].keyword + '">' + items[i].title + '</li>'
            }
          }
        } else {
          linkageWordDom = ''
        }
        $('.home_top_search_history').empty().append(linkageWordDom);
        $('.home_top_search_history').show();
      },
      fail: function (data) {},
      complete: function (complete) {}
    })
  }

  // 历史搜索点击事件
  $('.home_top_search_history').on("click", 'li', function () {
    var value = $(this).data('keyword');
    $(".home_top_search_input").val(value)
    $(".home_top_search_btn").trigger('click');
    $(this).hide();
  });
})