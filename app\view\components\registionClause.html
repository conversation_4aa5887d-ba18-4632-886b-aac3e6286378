<script src="https://cnstatic.sgsonline.com.cn/tic/staticVariables/registerHtml.js"></script>
<div v-if="dialogVisibleReg">
    <el-dialog append-to-body='true' :visible.sync="dialogVisibleReg" class="register-terms"
               :close-on-press-escape="false" :close-on-click-modal="false" ref="dialog" :show-close="false"
               width="850px">
        <div class="popup_container w_popup_container">
            <div v-html="registerHtml"></div>
            <div class="online_rule_submit">
                <div class="online_rule_submit_button active dialog-btn-close" @click="dialogVisibleReg = false">
                    关闭
                </div>
            </div>
        </div>
    </el-dialog>
</div>