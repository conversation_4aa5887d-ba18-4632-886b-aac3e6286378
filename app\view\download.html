<% include header.html %>

<div class="download_login n_wrap">
    <span>
        登录可获得更多资料
    </span>
    <a title="登录" href="<%- memberUrl %>/login" rel="nofollow">立即登录</a>
    <i>x</i>
</div>
<div class="SearchBox" style="padding-bottom: 55px;">
    <div class="searchleftBox">
        <div class="download_tab">
            <ul>
                <li class="active">
                    全部
                </li>
                <li>
                    服务宣传册
                </li>
                <li>
                    服务申请表
                </li>
                <li>
                    技术刊物
                </li>
                <li>
                    资质证书
                </li>
                <li>
                    服务指南
                </li>
                <li>
                    其他资源
                </li>
            </ul>
        </div>
        <div class="download_filter">
            <div class="download_filster_industry">
                <span class="download_filter_tit">行业分类：</span>
                <div class="download_filster_checkboxGroup">
                    <label>
                        <i></i>
                        <span>内特容1</span>
                    </label>
                    <label>
                        <i class="active"></i>
                        <span>内特容2</span>
                    </label>
                </div>
            </div>
            <div class='download_filster_service'></div>
            <div class="download_filster_keyword"></div>
            <div class="download_filster_date"></div>
            <div class="download_filster_result"></div>
        </div>
    </div>
    <div class="searchrightBox">
        <div class="searchrightBox1">
            <div class="your">
                <div class="yours">
                    <span class="your-word">行业解决方案</span>
                    <span class="dragbut">
                        <img src="<%- locals.static %>/images/jianhao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons first">
                    <div class="yourcon">
                        <ul>
                            <% if(tradeNavi && tradeNavi.length > 0){ %>
                            <% for (var item of tradeNavi){ %>
                            <li class="yourconLi">
                                <a href="/industry/<%- item.alias %>/<%- locals.keywordid %>">
                                    <%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="your">
                <div class="yours">
                    <span class="your-word">我们的服务</span>
                    <span class="dragbut">
                        <img src="<%- locals.static %>/images/jiahao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons">
                    <div class="yourcon">
                        <ul>
                            <% if(serviceNavi && serviceNavi.length > 0){ %>
                            <% for (var item of serviceNavi){ %>
                            <li class="yourconLi">
                                <a href="/service/<%- item.alias %>/<%- locals.keywordid %>">
                                    <%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="your">
                <div class="yours">
                    <span class="your-word">热点服务</span>
                    <span class="dragbut">
                        <img src="<%- locals.static %>/images/jiahao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons">
                    <div class="yourcon">
                        <ul>
                            <% if(hot && hot.length > 0){ %>
                            <% for (var item of hot){ %>
                            <li class="yourconLi">
                                <a href="/sku/<%- item.alias %>/<%- item.id %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<script src="<%- locals.static %>/js/addr.js"></script>
<script>
    $(function () {
        // side right 
        var $_yours = $(".yours");
        $_yours.click(function () {
            var $_con = $(this).siblings(".your-cons");
            if ($_con.css("height") == "0px") {
                $_con.animate({ "height": 486 }).end().parents(".your").siblings(".your").children(".your-cons").animate({ "height": 0 });
                $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jianhao.png');
                $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(0).attr("src", "<%- locals.static %>/images/jiahao.png");
                $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(1).attr("src", "<%- locals.static %>/images/jiahao.png")
            } else {
                $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jiahao.png');
                $_con.animate({ "height": 0 });
            }
        })
        //
    })
</script>

<% include footer.html %>