.jiance {
  background: #eee;
}
.jiance * {
  box-sizing: border-box;
}
.jiance .red {
  color: #ca4300;
}
.jiance .head-box {
  height: 40px;
  background: #333333;
  padding: 5px 0;
  font-size: 12px;
  color: #C1C1C1;
  line-height: 30px;
}
.jiance .head-box .page-title {
  display: inline-block;
  color: #C1C1C1;
}
.jiance .user-content {
  float: right;
  display: flex;
}
.jiance .user-content .user-item {
  margin-left: 27px;
}
.jiance .user-content .user-item a {
  color: #C1C1C1;
}
.jiance .head-img {
  height: 90px;
  background: #FFFFFF;
}
.jiance .head-img .logo1 {
  height: 43px;
}
.jiance .head-img .logo2 {
  height: 43px;
}
.jiance .head-img .wrap {
  display: flex;
  align-items: center;
  position: relative;
  height: 100%;
}
.jiance .head-img .logo {
  margin-right: 17px;
  vertical-align: bottom;
}
.jiance .head-img .title {
  height: 43px;
  margin-right: 25px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.jiance .head-img .title .row1 {
  font-size: 14px;
  color: #333333;
  line-height: 14px;
}
.jiance .head-img .title .row2 {
  font-size: 12px;
  color: #848484;
  line-height: 12px;
}
.jiance .head-img .title .row2 img {
  vertical-align: middle;
  margin-right: 6px;
}
.jiance .head-img .detail-text {
  height: 43px;
  margin-right: 180px;
  font-size: 12px;
  color: #848484;
  line-height: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  font-style: italic;
}
.jiance .head-img .detail-text span {
  color: #CA4300;
}
.jiance .head-img .detail-text .row1 {
  line-height: 14px;
}
.jiance .head-img .head-line {
  width: 16px;
  height: 41px;
  margin-right: 20px;
}
.jiance .head-img .cma-cnas {
  position: relative;
}
.jiance .head-img .cma-cnas .cma-box {
  position: absolute;
  left: 0;
  top: 0;
  width: 50px;
  height: 44px;
  cursor: pointer;
  z-index: 3;
}
.jiance .head-img .cma-cnas .cma-box .cma-img {
  position: absolute;
  left: -100px;
  bottom: -354px;
  display: none;
}
.jiance .head-img .cma-cnas .cma-box:hover .cma-img {
  display: block;
}
.jiance .head-img .cma-cnas .cnas-box {
  position: absolute;
  left: 113px;
  top: 0;
  width: 50px;
  height: 44px;
  cursor: pointer;
  z-index: 3;
}
.jiance .head-img .cma-cnas .cnas-box .cnas-img {
  position: absolute;
  left: -100px;
  bottom: -354px;
  display: none;
}
.jiance .head-img .cma-cnas .cnas-box:hover .cnas-img {
  display: block;
}
.jiance .head-img .home_top_hotline {
  position: absolute;
  right: 0;
  top: 25px;
}
.jiance .head-img .home_top_hotline i {
  width: 40px;
  height: 40px;
  background: url(../../images/promotion/jiance-new/phone.png) no-repeat;
  position: absolute;
  left: -55px;
  top: 0;
}
.jiance .head-img .home_top_hotline .home_top_hotline_phone span {
  font-size: 26px;
  font-weight: bold;
  color: #ca4300;
  line-height: 26px;
}
.jiance .head-img .home_top_hotline .home_top_hotline_intro {
  font-size: 12px;
  color: #878787;
  text-align: right;
  line-height: 1;
  margin-top: 2px;
}
.jiance .nav-box {
  background: #ECECEC;
}
.jiance .nav-wrap {
  height: 50px;
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  color: #333333;
}
.jiance .nav-wrap .nav-label {
  padding-top: 3px;
  width: 94px;
  color: #878787;
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 1;
}
.jiance .nav-wrap .nav-list {
  display: flex;
  align-self: flex-end;
}
.jiance .nav-wrap .nav-list .nav-li {
  position: relative;
}
.jiance .nav-wrap .nav-list .nav-li-title,
.jiance .nav-wrap .nav-list .nav-li-title a {
  padding: 12px 15px 15px;
  height: 43px;
  line-height: 1;
  cursor: pointer;
  color: #333;
}
.jiance .nav-wrap .nav-list .nav-li:hover .nav-li-title {
  border-radius: 12px 12px 0 0;
  background: #ca4300;
  color: #fff;
}
.jiance .nav-wrap .nav-list .nav-li:hover .nav-li-title a {
  color: #fff;
}
.jiance .nav-wrap .nav-list .nav-li:hover .nav-sub-list {
  display: block;
}
.jiance .nav-wrap .nav-list .nav-sub-list {
  position: absolute;
  width: 205px;
  height: 420px;
  background: #FFFFFF;
  box-shadow: 0px 5px 29px 0px rgba(4, 0, 0, 0.3);
  border-radius: 0px 0px 6px 6px;
  left: 0;
  bottom: -420px;
  display: none;
  padding-top: 11px;
  z-index: 100;
}
.jiance .nav-wrap .nav-list .nav-sub-list .nav-list-icon {
  width: 13px;
  height: 13px;
  background: #FFFFFF;
  position: absolute;
  left: 36px;
  top: -5px;
  transform: rotate(45deg);
}
.jiance .nav-wrap .nav-list .nav-sub-list .nav-sub-li {
  width: 205px;
  height: 30px;
  line-height: 30px;
}
.jiance .nav-wrap .nav-list .nav-sub-list .nav-sub-li a {
  display: inline-block;
  font-size: 14px;
  color: #333333;
  line-height: 30px;
  width: 100%;
  text-align: center;
}
.jiance .nav-wrap .nav-list .nav-sub-list .nav-sub-li a:hover {
  background: #ca4300;
  color: #fff;
}
.jiance .nav-wrap .nav-list .nav-sub-list .nav-sub-li-btn {
  width: 205px;
  height: 42px;
  border-radius: 0px 0px 6px 6px;
  background: #474747;
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 42px;
  margin-top: 11px;
  cursor: pointer;
}
.jiance .nav-wrap .nav-list .nav-sub-list .nav-sub-li-btn a {
  display: inline-block;
  width: 100%;
  color: #FFFFFF;
  text-align: center;
}
.jiance .nav-wrap .nav-list .nav-sub-list .nav-sub-li-btn:hover {
  background: #ca4300;
}
.jiance .nav-wrap .nav-btn {
  width: 146px;
  height: 50px;
  background: #ca4300;
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 50px;
  text-align: center;
  z-index: 2;
  position: relative;
  cursor: pointer;
}
.jiance .nav-wrap .nav-btn:hover:before {
  width: 146px;
}
.jiance .nav-wrap .nav-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 50px;
  background: #cc5500;
  z-index: -1;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.jiance .banner-box {
  width: 100%;
  height: 500px;
  position: relative;
}
.jiance .banner-box .banner-box-form {
  position: absolute;
  z-index: 2;
  top: 0;
  height: 500px;
  width: 420px;
  right: calc(50% - 613px);
}
.jiance .banner-box .swiper-container,
.jiance .banner-box .swiper-slide {
  height: 500px;
}
.jiance .banner-box .swiper-slide1 {
  background: url("../../images/promotion/jiance-new/banner.jpg") no-repeat center center;
}
.jiance .banner-box .swiper-slide2 {
  background: url("../../images/promotion/jiance-new/banner1.jpg") no-repeat center center;
}
.jiance .banner-box .swiper-slide-wrap {
  width: 1226px;
  margin: 0 auto;
  padding-top: 310px;
}
.jiance .banner-box .swiper-slide-wrap .more-btn {
  color: #fff;
  padding-left: 200px;
  font-size: 13px;
}
.jiance .banner-box .swiper-slide-wrap .kf5-btn {
  width: 180px;
  height: 50px;
  background: #ca4300;
  text-align: center;
  font-size: 20px;
  color: #FFFFFF;
  line-height: 50px;
  outline: none;
  cursor: pointer;
  display: inline-block;
  position: relative;
  border-radius: 3px;
  margin-left: 140px;
  margin-top: 30px;
}
.jiance .banner-box .swiper-slide-wrap .kf5-btn .btn-text {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
}
.jiance .banner-box .swiper-slide-wrap .kf5-btn .btn-active {
  width: 0;
  height: 100%;
  background: #cc5500;
  position: absolute;
  top: 0;
  left: 0;
}
.jiance .banner-box .swiper-slide-wrap .kf5-btn:hover .btn-active {
  width: 100%;
  transition: 0.4s;
}
.jiance .banner-box .swiper-bottom {
  position: absolute;
  left: 50%;
  bottom: 50px;
}
.jiance .banner-box .swiper-bottom .swiper-pagination {
  width: 140px;
}
.jiance .banner-box .swiper-bottom .swiper-pagination-bullet {
  width: 50px !important;
  height: 5px !important;
  background: #fff !important;
  margin: 0 10px;
  border-radius: 0 !important;
  opacity: 1 !important;
}
.jiance .banner-box .swiper-bottom .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: #CA4300 !important;
}
.jiance .video-wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  z-index: 2001;
}
.jiance .video-dialog {
  position: relative;
  margin: 15vh auto 50px;
  background: #000;
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
  width: 898px;
  height: 619px;
}
.jiance .video-dialog .close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
}
.jiance .v-modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  background: #000;
}
.jiance .floor-title {
  font-size: 30px;
  font-weight: bold;
  color: #333333;
  text-align: center;
  padding: 0;
  line-height: 30px;
  width: 1026px;
  margin: auto;
}
.jiance .main-line {
  margin: 12px auto 24px;
  width: 60px;
  height: 3px;
  background: #ca4300;
}
.jiance .floor-sub-title {
  font-size: 16px;
  color: #878787;
  opacity: 0.87;
  text-align: center;
  margin: 17px auto 0;
  width: 1042px;
  line-height: 30px;
}
.jiance .hot-service {
  padding-top: 60px;
}
.jiance .hot-service .hot-service-list {
  height: 500px;
  position: relative;
  display: flex;
  padding: 0 113px;
  justify-content: space-between;
  margin-top: 47px;
}
.jiance .hot-service .hot-service-list .hot-service-item {
  width: 64px;
}
.jiance .hot-service .hot-service-list .hot-service-item .hot-service-title {
  cursor: pointer;
  position: relative;
  height: 83px;
  display: block;
}
.jiance .hot-service .hot-service-list .hot-service-item .service-icon {
  position: absolute;
  top: 0;
  left: 17px;
}
.jiance .hot-service .hot-service-list .hot-service-item .active-icon {
  display: none;
}
.jiance .hot-service .hot-service-list .hot-service-item .active-down-icon {
  display: none;
  position: absolute;
  bottom: 0;
  left: 24px;
}
.jiance .hot-service .hot-service-list .hot-service-item .title-text {
  position: relative;
  left: 0;
  top: 46px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  line-height: 16px;
}
.jiance .hot-service .hot-service-list .hot-service-item.is-active .active-icon,
.jiance .hot-service .hot-service-list .hot-service-item.no-list:hover .active-icon,
.jiance .hot-service .hot-service-list .hot-service-item.is-active .active-down-icon,
.jiance .hot-service .hot-service-list .hot-service-item.no-list:hover .active-down-icon {
  display: block;
}
.jiance .hot-service .hot-service-list .hot-service-item.is-active .normal-icon,
.jiance .hot-service .hot-service-list .hot-service-item.no-list:hover .normal-icon {
  display: none;
}
.jiance .hot-service .hot-service-list .hot-service-item.is-active .title-text,
.jiance .hot-service .hot-service-list .hot-service-item.no-list:hover .title-text {
  color: #ca4300;
}
.jiance .hot-service .hot-service-list .hot-service-item.is-active .hot-service-content {
  display: block;
}
.jiance .hot-service .hot-service-list .hot-service-content {
  position: absolute;
  top: 100px;
  left: 0;
  width: 1226px;
  height: 400px;
  padding: 39px 47px 0 334px;
  display: none;
}
.jiance .hot-service .hot-service-list .hot-service-content .hot-service-top {
  border-bottom: 2px solid #D4D4D4;
  position: relative;
  padding-left: 18px;
  padding-bottom: 14px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  line-height: 23px;
}
.jiance .hot-service .hot-service-list .hot-service-content .hot-service-top img {
  vertical-align: middle;
  margin-left: 62px;
}
.jiance .hot-service .hot-service-list .hot-service-content .hot-service-top a {
  font-size: 14px;
  color: #878787;
  line-height: 23px;
  float: right;
}
.jiance .hot-service .hot-service-list .hot-service-content .hot-service-top:after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 167px;
  height: 2px;
  background: #ca4300;
}
.jiance .hot-service .hot-service-list .hot-service-content .sub-list-content {
  margin-top: 33px;
  display: flex;
}
.jiance .hot-service .hot-service-list .hot-service-content .sub-list {
  width: 211px;
}
.jiance .hot-service .hot-service-list .hot-service-content .sub-list li {
  padding-left: 21px;
  font-size: 15px;
  line-height: 15px;
  position: relative;
  margin-bottom: 23px;
}
.jiance .hot-service .hot-service-list .hot-service-content .sub-list li a {
  color: #333333;
}
.jiance .hot-service .hot-service-list .hot-service-content .sub-list li:hover a {
  color: #ca4300;
  text-decoration: underline;
}
.jiance .hot-service .hot-service-list .hot-service-content .sub-list li:before {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  left: 0;
  top: 5px;
  background: #ca4300;
}
.jiance .hot-service .hot-service-btns {
  margin-top: 16px;
}
.jiance .hot-service .hot-service-btns .btn1,
.jiance .hot-service .hot-service-btns .btn2 {
  width: 142px;
  height: 45px;
  border: 1px solid #ca4300;
  font-size: 18px;
  color: #ca4300;
  line-height: 43px;
  text-align: center;
  margin-right: 34px;
  display: inline-block;
  cursor: pointer;
}
.jiance .hot-service .hot-service-btns .btn1:hover,
.jiance .hot-service .hot-service-btns .btn2:hover {
  background: #ca4300;
  color: #fff;
}
.jiance .hot-service .hot-service-btns .btn3 {
  display: inline-block;
  width: 220px;
  height: 45px;
  background: #ca4300;
  text-align: center;
  font-size: 18px;
  color: #FFFFFF;
  line-height: 45px;
  outline: none;
  cursor: pointer;
  position: relative;
  vertical-align: bottom;
}
.jiance .hot-service .hot-service-btns .btn3 .btn-text {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
}
.jiance .hot-service .hot-service-btns .btn3 .btn-active {
  width: 0;
  height: 100%;
  background: #cc5500;
  position: absolute;
  top: 0;
  left: 0;
}
.jiance .hot-service .hot-service-btns .btn3:hover .btn-active {
  width: 100%;
  transition: 0.4s;
}
.jiance .hot-service .hot-service-btns .btn4 {
  height: 45px;
  padding: 2.5px 0;
  display: inline-flex;
  vertical-align: bottom;
}
.jiance .hot-service .hot-service-btns .btn4 i {
  width: 40px;
  height: 40px;
  background: url(../../images/promotion/jiance-new/phone.png) no-repeat;
  margin-right: 16px;
}
.jiance .hot-service .hot-service-btns .btn4 .text {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.jiance .hot-service .hot-service-btns .btn4 .text .home_top_hotline_intro {
  font-size: 12px;
  color: #878787;
  line-height: 12px;
}
.jiance .hot-service .hot-service-btns .btn4 .text .hotlineText {
  font-size: 24px;
  color: #ca4300;
  line-height: 24px;
  font-weight: bold;
}
.jiance .hot-service .hot-service-btns .btn5 {
  width: 220px;
  display: inline-flex;
  height: 45px;
  line-height: 45px;
  background: #ca4300;
  font-size: 18px;
  color: #fff;
  justify-content: center;
  margin-left: 54px;
}
.jiance .solution {
  margin-top: 60px;
}
.jiance .solution .solution-wrap {
  position: relative;
}
.jiance .solution .solution-swiper {
  margin-top: 35px;
  position: relative;
}
.jiance .solution .solutionCon {
  height: auto;
  overflow: hidden;
  padding-bottom: 0;
}
.jiance .solution .solutionConLi {
  width: 593px;
  height: 162px;
  float: left;
  position: relative;
  margin-bottom: 26px;
  margin-right: 38px;
  list-style: none;
  background: #fff;
}
.jiance .solution .solutionConLi .img {
  float: left;
  width: 237px;
  height: 162px;
  display: block;
  background: #ccc;
  cursor: pointer;
  background-size: cover;
}
.jiance .solution .solutionConLi .cc {
  padding-left: 265px;
  text-align: left;
  padding-right: 20px;
}
.jiance .solution .solutionConLi .solutionConLi-word1 {
  display: block;
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: 1.17;
  letter-spacing: normal;
  color: #333333;
  cursor: pointer;
  padding: 5px 0;
  margin: 20px 0 5px 0;
  font-weight: bold;
}
.jiance .solution .solutionConLi .cc p {
  font-size: 14px;
  line-height: 20px;
  height: 60px;
  overflow: hidden;
  color: #999;
}
.jiance .solution .solutionConLi em {
  display: inline-block;
  margin-top: 10px;
  width: 62px;
  text-align: center;
  height: 28px;
  line-height: 28px;
  border-radius: 14px;
  color: #ca4300;
  border: 1px solid #ca4300;
  margin-left: 20px;
  cursor: pointer;
}
.jiance .solution .solutionConLi em:hover {
  background: #ca4300;
  color: #fff;
}
.jiance .solution .solutionConLi:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  -webkit-transform: translate3d(0, -2px, 0);
  transform: translate3d(0, -2px, 0);
  transition: 0.4s;
}
.jiance .solution .serverRight0 {
  margin-right: 0;
}
.jiance .solution .swiper-button-next,
.jiance .solution .swiper-button-prev {
  width: 34px;
  height: 76px;
  top: 298px;
  outline: none;
}
.jiance .solution .swiper-button-prev {
  left: -52px;
  background: url("../../images/promotion/jiance-new/solution-prev.png");
}
.jiance .solution .swiper-button-next {
  right: -52px;
  background: url("../../images/promotion/jiance-new/solution-next.png");
}
.jiance .solution .swiper-pagination {
  left: 552.5px;
}
.jiance .solution .swiper-pagination-bullet {
  width: 16px;
  height: 16px;
  background: #eee;
  border-radius: 50%;
  font-size: 12px;
  color: #878787;
  line-height: 16px;
  outline: none;
  opacity: 1;
}
.jiance .solution .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: #ca4300;
  color: #fff;
}
.jiance .solution .swiper-pagination-bullet:not(:last-child) {
  margin-right: 5px;
}
.jiance .solution .solution-btns {
  text-align: center;
  margin-top: 51px;
}
.jiance .solution .solution-btns .btn1 {
  display: inline-block;
  width: 220px;
  height: 45px;
  background: #ca4300;
  text-align: center;
  font-size: 18px;
  color: #FFFFFF;
  line-height: 45px;
  outline: none;
  cursor: pointer;
  position: relative;
  vertical-align: bottom;
}
.jiance .solution .solution-btns .btn1 .btn-text {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
}
.jiance .solution .solution-btns .btn1 .btn-active {
  width: 0;
  height: 100%;
  background: #cc5500;
  position: absolute;
  top: 0;
  left: 0;
}
.jiance .solution .solution-btns .btn1:hover .btn-active {
  width: 100%;
  transition: 0.4s;
}
.jiance .data-content {
  width: 100%;
  height: 400px;
  background: url("../../images/promotion/jiance-new/data-bg.jpg") no-repeat center center;
  padding-top: 61px;
  margin-top: 47px;
}
.jiance .data-content .shuju-list {
  display: flex;
  padding-left: 63px;
  margin-top: 38px;
}
.jiance .data-content .shuju-item {
  margin-top: 50px;
  padding: 12px 0;
}
.jiance .data-content .shuju-item .shuju-detail {
  font-size: 33px;
  font-family: Univers Condensed;
  font-weight: bold;
  color: #ca4300;
  line-height: 1;
}
.jiance .data-content .shuju-item .shuju-detail .shuju-unit {
  font-size: 22px;
}
.jiance .data-content .shuju-item .shuju-tips {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
  line-height: 16px;
  margin-top: 24px;
}
.jiance .data-content .shuju-divider {
  margin-top: 50px;
  width: 17px;
  height: 100px;
}
.jiance .panoramic {
  margin-top: 54px;
}
.jiance .panoramic .panoramic-wrap {
  height: 200px;
  background: url(../../images/promotion/jiance-new/panoramic-bg.jpg);
  position: relative;
}
.jiance .panoramic .panoramic-wrap .panoramic-text {
  position: absolute;
  top: 79px;
  left: 65px;
  width: 587px;
  font-size: 36px;
  color: #FFFFFF;
  line-height: 36px;
}
.jiance .panoramic .panoramic-wrap .panoramic-num-text {
  font-size: 14px;
  color: #FFFFFF;
  line-height: 1;
  position: absolute;
  top: 40px;
  right: 99px;
}
.jiance .panoramic .panoramic-wrap .panoramic-btn {
  width: 175px;
  height: 54px;
  border: 1px solid #FFFFFF;
  font-size: 24px;
  color: #FFFFFF;
  line-height: 52px;
  text-align: center;
  position: absolute;
  top: 73px;
  right: 82px;
  z-index: 2;
}
.jiance .panoramic .panoramic-wrap .panoramic-btn:hover {
  border: 1px solid #ca4300;
}
.jiance .panoramic .panoramic-wrap .panoramic-btn:hover:before {
  width: 175px;
}
.jiance .panoramic .panoramic-wrap .panoramic-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 54px;
  background: #ca4300;
  z-index: -1;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}
.jiance .discount {
  margin-top: 66px;
}
.jiance .discount .discount-btns {
  width: 1226px;
  margin: auto;
  display: flex;
  justify-content: space-between;
}
.jiance .discount .discount-btns .btn1 {
  width: 430px;
  height: 82px;
  background: #FFFFFF;
  border: 3px solid #D2D2D2;
  padding: 23px 0 25px 23px;
  display: flex;
  align-items: center;
  position: relative;
}
.jiance .discount .discount-btns .btn1 .click-btn {
  position: absolute;
  width: 212px;
  height: 100%;
  top: 0;
  left: 0;
  cursor: pointer;
}
.jiance .discount .discount-btns .btn1 .btn-icon {
  width: 50px;
  margin-right: 23px;
}
.jiance .discount .discount-btns .btn1 .btn-text {
  font-size: 22px;
  color: #ca4300;
  margin-right: 29px;
}
.jiance .discount .discount-btns .btn1 .position-icon {
  width: 17px;
  margin-right: 10px;
}
.jiance .discount .discount-btns .btn1 .position-text {
  font-size: 16px;
  color: #333333;
  margin-right: 10px;
}
.jiance .discount .discount-btns .btn1 .change-position .change-btn {
  font-size: 12px;
  color: #ca4300;
  cursor: pointer;
}
.jiance .discount .discount-btns .btn1 .change-position #provice {
  position: absolute;
  width: 200px;
  left: 214px;
  bottom: -20px;
}
.jiance .discount .discount-btns .btn2 {
  width: 430px;
  height: 82px;
  background: #ca4300;
  padding: 27px 0 23px 64px;
  display: flex;
  align-items: center;
  font-size: 22px;
  color: #FFFFFF;
  line-height: 22px;
  cursor: pointer;
}
.jiance .discount .discount-btns .btn2 img {
  width: 28px;
  margin-right: 16px;
}
.jiance .discount .discount-btns .btn2 .text1 {
  border-right: 1px solid #F2F2F2;
  padding-right: 14px;
  margin-right: 14px;
}
.jiance .discount .discount-btns .btn3 {
  width: 291px;
  height: 82px;
  background: #FFFFFF;
  border: 3px solid #D2D2D2;
  padding: 18px 0 18px 36px;
  display: flex;
}
.jiance .discount .discount-btns .btn3 i {
  width: 40px;
  height: 40px;
  background: url(../../images/promotion/jiance-new/phone.png) no-repeat;
  margin-right: 16px;
}
.jiance .discount .discount-btns .btn3 .text {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.jiance .discount .discount-btns .btn3 .text .home_top_hotline_intro {
  font-size: 12px;
  color: #878787;
  line-height: 12px;
}
.jiance .discount .discount-btns .btn3 .text .hotlineText {
  font-size: 24px;
  color: #ca4300;
  line-height: 24px;
  font-weight: bold;
}
.jiance .news {
  margin-top: 63px;
}
.jiance .news .news-wrap {
  position: relative;
}
.jiance .news .news-swiper {
  margin-top: 49px;
  height: 463px;
  width: 1226px;
}
.jiance .news .news-swiper .swiper-wrapper {
  height: 100%;
}
.jiance .news .news-swiper .newsCon {
  position: relative;
}
.jiance .news .news-swiper .newsCon .title {
  width: 429px;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 24px;
  position: absolute;
}
.jiance .news .news-swiper .newsCon .left-content {
  width: 500px;
  height: 433px;
  background: #1C1C1C;
  position: absolute;
  left: 0;
  top: 0;
}
.jiance .news .news-swiper .newsCon .left-content .title {
  left: 38px;
  bottom: 34px;
  font-size: 18px;
  line-height: 30px;
}
.jiance .news .news-swiper .newsCon .middle-content {
  width: 325px;
  height: 433px;
  position: absolute;
  left: 537.5px;
  top: 0;
}
.jiance .news .news-swiper .newsCon .middle-content .card2 {
  display: block;
  width: 325px;
  height: 285px;
  background: #1C1C1C;
  margin-bottom: 36px;
  position: relative;
}
.jiance .news .news-swiper .newsCon .middle-content .card2 .title {
  left: 31px;
  bottom: 21px;
  width: 262px;
}
.jiance .news .news-swiper .newsCon .middle-content .card4 {
  display: block;
  width: 325px;
  height: 112px;
  position: relative;
}
.jiance .news .news-swiper .newsCon .middle-content .card4 .title {
  left: 31px;
  bottom: 34px;
  width: 262px;
}
.jiance .news .news-swiper .newsCon .right-content {
  width: 326px;
  height: 433px;
  position: absolute;
  left: 900px;
  top: 0;
}
.jiance .news .news-swiper .newsCon .right-content .card3 {
  display: block;
  width: 325px;
  height: 285px;
  background: #1C1C1C;
  margin-bottom: 36px;
  position: relative;
}
.jiance .news .news-swiper .newsCon .right-content .card3 .title {
  left: 31px;
  bottom: 21px;
  width: 262px;
}
.jiance .news .news-swiper .newsCon .right-content .news-btns {
  display: flex;
}
.jiance .news .news-swiper .newsCon .right-content .news-btn {
  width: 163px;
  height: 111px;
  background: #ca4300;
  text-align: center;
  font-size: 20px;
  color: #FFFFFF;
  line-height: 20px;
  padding-top: 17px;
  cursor: pointer;
}
.jiance .news .news-swiper .newsCon .right-content .news-btn .img {
  margin-bottom: 16px;
}
.jiance .news .news-swiper .newsCon .right-content .news-btn:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  transition: 0.4s;
}
.jiance .news .news-swiper .newsCon .right-content .btn2 {
  background: #333333;
}
.jiance .news .news-swiper .newsCon .card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  transition: 0.4s;
}
.jiance .news .swiper-button-next,
.jiance .news .swiper-button-prev {
  width: 34px;
  height: 76px;
  top: 298px;
  outline: none;
}
.jiance .news .swiper-button-prev {
  left: -52px;
  background: url("../../images/promotion/jiance-new/solution-prev.png");
}
.jiance .news .swiper-button-next {
  right: -52px;
  background: url("../../images/promotion/jiance-new/solution-next.png");
}
.jiance .news .swiper-pagination {
  left: 552.5px;
}
.jiance .contact_ads {
  margin-top: 39px;
  background: url(../../images/newPage/ads.jpg) no-repeat 50% 50% #e8e8e8;
  height: 100px;
}
