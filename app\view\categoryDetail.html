<% include header.html %>
<div class="banner" id='categoryDetail'>
    <div class="swiper-container" style="background: #AAA">
        <div class="swiper-wrapper">
            <% for(item of banner){ %>
            <div class="swiper-slide bannerLi1"
                style="background-image:url(<%- item.img_path.replace(/\/static/g,locals.static) %>)">
                <% if(item.btn_url && !item.btn_value){ %><a href="<%- item.btn_url %>" target="blank"
                    style="display:block;width:100%;height:100%;"><% } %>
                    <div class="baa">
                        <div class="bannerTitle"><%- item.btn_text %></div>
                        <div class="bannerDetail"><%- item.btn_description %></div>
                        <% if(item.btn_value){ %>
                        <a class="bannerMore" href="<%- item.btn_url %>" target="blank">
                            <%- item.btn_value %>
                            <div class="bannermoreBox">

                            </div>
                            <div class="bannermoreword"><%- item.btn_value %></div>
                        </a>
                        <% } %>
                    </div>
                    <% if(item.btn_url && !item.btn_value){ %></a><% } %>
            </div>
            <% } %>
        </div>
        <!-- Add Arrows -->
        <% if(banner && banner.length > 1){ %>
        <div class="swiper-bottom">
            <div class="swiper-button-prev"></div>
            <div class="swiper-pagination"></div>
            <div class="swiper-button-next"></div>
        </div>
        <% } %>
    </div>
</div>
<div class="location">
    <ul>
        <li class="locationLi">
            <a href="/">第三方检测机构</a>
        </li>
        <li class="locationLi icon"></li>
        <li class="locationLi">
            <a href="javascrpit:void(0);"><%- detail.name %></a>
        </li>
    </ul>
</div>

<div class="introduce">
    <div class="introduceTitle"><%- detail.name %></div>
    <div class="introduceLeft">
        <div class="introduceLeft-word">
            <%- detail.description %>
        </div>
        <a class="introduceLeft-question" href="/baojia" style="display: inline-block">

            <span class="introduceLeft-questionbox1"></span>
            <% for(let [i,item] of children.entries()){ %>
            <em id='/<%- item.id %>' style="opacity: 0;"></em>
            <% } %>
            <span class="introduceLeft-question1">咨询报价</span>
        </a>
    </div>
    <div class="introduceRight" style="background:none">
        <img alt='<%- detail.name %>'
            src="<% if(detail.cover_img){ %><%- detail.cover_img.replace(/\/static/g,locals.static) %><% } %>"
            style="max-width:100%">
    </div>
</div>
<div class="server">
    <div class="serverBox sebox2">
        <div class="serverHead">
            <div class="connn" style="width:1140px;height:77px;overflow:hidden;position: relative;">


                <div class="connnw" style="width:1088px;overflow: hidden;position: relative;">
                    <!--<div class="bor">
                </div>-->
                    <ul style="width:1000%;float:left">
                        <% for(let [i,item] of children.entries()){ %>
                        <!--serverLiActive-->
                        <li class="serverLi">
                            <a href="/<% if(detail.parent_id == 1){ %>industry<% }else{ %>service<% } %>/<%- detail.alias %>/#/<%- item.id %>"
                                data-role="<%- item.id %>"><%- item.name %></a>
                        </li>
                        <% } %>

                    </ul>
                </div>
            </div>
            <div class="allServer"><a
                    href="/<% if(detail.parent_id == 1){ %>industry<% }else{ %>service<% } %>/<%- detail.alias %>/service"><% if(alias != 'training'){ %>全部服务<% }else{ %>全部课程<% } %></a>
            </div>
            <div class="serverIcon"></div>
        </div>
        <div class="serverCon">
          <% for([n,item] of children.entries()){ %>
          <div class="showsel">
              <ul>
                  <% for([s,sku] of item.skus.entries()){ %>
                  <li class="serverConLi" <% if(s>7){ %>style="display:none" <% } %>>
                      <a
                          href="/sku/<%- sku.alias %>/<%- sku.id %>">
                          <img alt='<%- sku.name %>' src="<%- sku.thumb_img.replace(/\/static/g,locals.static) %>"
                              alt="">
                          <% if(sku.is_buy == 1){ %><span class="buyicon"></span><% } %>
                          <span class="serverConLiWorBac">
                              <span class="serverConLiWord1"><%- sku.name %></span>
                              <span class="serverConLiWord2"><%- sku.sub_title %></span>
                          </span>
                      </a>
                  </li>
                  <% } %>
              </ul>
              <div class="sspages">

              </div>
          </div>
          <% } %>
      </div>

      <!--
          <div class="pages">
              <ul>
                  <li class="pagesLi pagesLiActive">1</li>
                  <li class="pagesLi">2</li>
                  <li class="pagesLi">3</li>
              </ul>
          </div>
          -->
  </div>
</div>

    </div>
</div>
<% if(detail.page_type == 1){ %>
<% if(solution.length > 0){ %>
<div class="solution">
    <div class="serverBox sebox2" style="background-color: #fff">
        <div class="serverHead">
            <ul>
                <li class="serverLi solu">解决方案</li>
            </ul>
        </div>

        <div class="solutionCon">
            <ul>
                <% for([i,item] of solution.entries()){ %>
                <li class="solutionConLi" <% if(i>3){ %>style="display:none" <% } %>>
                    <a
                        href="/sku/<%- item.alias %>/<%- item.id %>">
                        <span class="img"
                            style="background-image:url(<%- item.thumb_img.replace(/\/static/g,locals.static) %>)"></span>
                        <div class="cc">
                            <span class="solutionConLi-word1"><%- item.name %></span>
                            <p><%- item.description %></p>
                        </div>
                    </a>
                </li>
                <% } %>
            </ul>
            <div class="sspages">

            </div>
        </div>
        <!--
            <div class="pages">
                <ul>
                    <li class="pagesLi pagesLiActive">1</li>
                    <li class="pagesLi">2</li>
                    <li class="pagesLi">3</li>
                </ul>
            </div>
            -->
    </div>
</div>
<% } %>

<% if(cases.length > 0){ %>
<div class="cooperation">
    <div class="cooperationBox" id='case'>
        <div class="serverHead">
            <ul>
                <% if(informationTypes && informationTypes.length > 0){ %>
                <% for (var [index, item] of informationTypes.entries()){ %>
                <% if(item.list.length > 0){ %>
                <li class="serverLi">
                    <%- item.name %>
                    <a href='/information?type=<%- item.id %>' target="_blank">查看更多<i></i></a>
                </li>
                <% } %>
                <% } %>
                <% } %>
            </ul>
        </div>
        <% if(informationTypes && informationTypes.length > 0){ %>
        <% for (var [index, item] of informationTypes.entries()){ %>
        <% if(item.list.length > 0){ %>
        <div class="cooperationDemo">
            <ul>
                <% for (var [cadeIndex, caseItem] of item.list.entries()){ %>
                <li>
                    <% if(cadeIndex < 4 && caseItem.isHot){ %>
                    <i class="new"></i>
                    <% }else{ %>
                    <i></i>
                    <% } %>
                    <a href="/case/article/detail-<%- caseItem.id %>.html"
                        target="_blank"><%- caseItem.title %></a>
                    <time><%- caseItem.gmt_publish_time %></time>
                </li>
                <% } %>
            </ul>
            <% if(item.list.length > 8){ %>
            <div class="cooperationDemo_slide">
                <span></span>
            </div>
            <% } %>
        </div>
        <% } %>
        <% } %>
        <% } %>
    </div>
</div>
<% } %>

<% if(resource.length > 0){ %>
<div class="resources">
    <div class="resourceBox" id='files'>
        <div class="serverHead">
            <ul>
                <% if(filesType && filesType.length > 0){ %>
                <% for (var [index, item] of filesType.entries()){ %>
                <% if(item.list.length > 0){ %>
                <li class="serverLi">
                    <%- item.name %>
                    <a href='/files?type=<%- item.id %>' target="_blank">查看更多<i></i></a>
                </li>
                <% } %>
                <% } %>
                <% } %>
            </ul>
        </div>
        <% if(filesType && filesType.length > 0){ %>
        <% for (var [index, item] of filesType.entries()){ %>
        <% if(item.list.length > 0){ %>
        <div class="serverBody">
            <ul class="clearfix">
                <% for (var filesItem of item.list){ %>
                <li>
                    <a class="<%- filesItem.type %>" href="javascript:void(0)" data-id='<%- filesItem.id %>' data-is_public='<%- filesItem.is_public %>'><%- filesItem.title %></a>
                </li>
                <% } %>
            </ul>
            <% if(item.list.length > 12){ %>
            <div class="serverBody_slide">
                <span></span>
            </div>
            <% } %>
        </div>
        <% } %>
        <% } %>
        <% } %>
    </div>
</div>
<% } %>
<% } %>

<script src="<%- locals.static %>/js/swiper.min.js"></script>
<script src="<%- locals.static %>/js/FileSaver.js"></script>
<script>
        <% if (banner && banner.length > 1) { %>
        var swiper = new Swiper('.swiper-container', {
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            speed: 1000,
            loop: false,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            effect: 'fade',
            fadeEffect: {
                crossFade: true,
            }
        });

        var scontainer = $('.swiper-container')[0];
        scontainer.onmouseenter = function () {
            swiper.autoplay.stop();
        };
        scontainer.onmouseleave = function () {
            swiper.autoplay.start();
        };
        <% } %>
        $(function () {

            $("#case .serverHead li").eq(0).addClass('demo');
            $("#case .serverHead li").on("click", function () {
                var index = $(this).index();
                $(this).addClass('demo').siblings().removeClass('demo');
                $("#case").find('.cooperationDemo').eq(index).fadeIn().siblings('.cooperationDemo').hide();
            });
            $("#case .cooperationDemo").eq(0).show();
            $('.cooperationDemo_slide span').on("click", function () {
                if ($(this).hasClass('active')) {
                    $(this).closest('div').siblings('ul').css({
                        maxHeight: '140px'
                    })
                } else {
                    $(this).closest('div').siblings('ul').css({
                        maxHeight: '100%'
                    })
                }
                $(this).toggleClass('active');
            });
            $("#files .serverHead li").eq(0).addClass('demo');
            $("#files .serverHead li").on("click", function () {
                var index = $(this).index();
                $(this).addClass('demo').siblings().removeClass('demo');
                $("#files").find('.serverBody').eq(index).fadeIn().siblings('.serverBody').hide();
            });
            $("#files .serverBody").eq(0).show();
            $('.serverBody_slide span').on("click", function () {
                if ($(this).hasClass('active')) {
                    $(this).closest('div').siblings('ul').css({
                        maxHeight: '140px'
                    })
                } else {
                    $(this).closest('div').siblings('ul').css({
                        maxHeight: '100%'
                    })
                }
                $(this).toggleClass('active');
            });
            var slen = 8, ulen = 4;
            // <% if(detail.alias == 'testing'){ %>slen = 12;<% } %>
            // 展开合起更多资讯中心
            $('.cooperationDemo-more-icon').on('click', function () {
                if ($('.cooperationDemo ul').hasClass('limitHeight')) {
                    $('.cooperationDemo-more-icon span').html('收起')
                    $('.cooperationDemo-more-icon img').attr('src', '<%- locals.static %>/images/up.png')
                } else {
                    $('.cooperationDemo-more-icon span').html('展开')
                    $('.cooperationDemo-more-icon img').attr('src', '<%- locals.static %>/images/open.png')
                }
                $('.cooperationDemo ul').toggleClass('limitHeight')
            })
            // 展开合起更多相关资源
            $('.resource-more-icon').on('click', function () {
                if ($('.resourceCon').hasClass('limitHeight')) {
                    $('.resource-more-icon span').html('收起')
                    $('.resource-more-icon img').attr('src', '<%- locals.static %>/images/up.png')
                } else {
                    $('.resource-more-icon span').html('展开')
                    $('.resource-more-icon img').attr('src', '<%- locals.static %>/images/open.png')
                }
                $('.resourceCon').toggleClass('limitHeight')
            })
            $('.showsel').each(function () {
                var len = $('li', this).length;
                var $p = $(this).children('.sspages');
                if (len > slen) {
                    $p.show();
                    for (var i = 1; i <= Math.ceil(len / slen); i++) {
                        i == 1 ? $p.append('<a href="javascrpit:void(0);" class="pagesLi pagesLiActive">' + i + '</a>') : $p.append('<a href="javascrpit:void(0);" class="pagesLi">' + i + '</a>');
                    }
                }
            }).on('click', '.sspages>a', function () {
                var i = $(this).text();
                $(this).addClass('pagesLiActive').siblings().removeClass('pagesLiActive');

                var $c = $(this).parent().prev();

                $('li', $c).each(function (n, item) {
                    if (n >= (i - 1) * slen && n < i * slen) {
                        $(item).show();
                    } else {
                        $(item).hide();
                    }
                });
            });

            $('.solutionCon').each(function () {
                var len = $('li', this).length;
                var $p = $(this).children('.sspages');
                if (len > ulen) {
                    $p.show();
                    for (var i = 1; i <= Math.ceil(len / ulen); i++) {
                        i == 1 ? $p.append('<a href="javascrpit:void(0);" class="pagesLi pagesLiActive">' + i + '</a>') : $p.append('<a href="javascrpit:void(0);" class="pagesLi">' + i + '</a>');
                    }
                }
            }).on('click', '.sspages>a', function () {
                var i = $(this).text();
                $(this).addClass('pagesLiActive').siblings().removeClass('pagesLiActive');

                var $c = $(this).parent().prev();

                $('li', $c).each(function (n, item) {
                    if (n >= (i - 1) * ulen && n < i * ulen) {
                        $(item).show();
                    } else {
                        $(item).hide();
                    }
                });
            });

            var sw = 0, flw = 0;
            $('.serverHead:first ul li').each(function (i, item) {
                sw += $(this).outerWidth();
            });

            if (sw > 1088) {
                $('.serverHead .connn').prepend('<span class="connn-prev">&lt;</span><span class="connn-next">&gt;</span>');
            }

            var $ful = $('.serverHead .connn ul');
            $('.serverHead .connn').on('click', 'span.connn-prev', function () {
                flw += 1088;
                if (flw > 0) {
                    flw = 0;
                }
                $ful.stop().animate({ 'margin-left': flw });
            });

            $('.serverHead .connn').on('click', 'span.connn-next', function () {
                flw -= 1088;
                if (flw < 1088 - sw) {
                    flw = 1088 - sw;
                }

                $ful.stop().animate({ 'margin-left': flw });
            });

            // 资源下载添加权限
            var isLogin = <%- isLogin %>,
                url ='<%- url %>';
            $('#files .serverBody a').on('click', function() {
                var id =  $(this).data('id'),
                    is_public =  $(this).data('is_public');
                verification(id, is_public)
            })
            function verification(id, is_public) {
                if (is_public || isLogin) {
                    getFilePath(id);
                } else {
                    window.location.href = '<%- memberUrl %>/login';
                }
            }
            function getFilePath(id) {
                jQuery.post('/getFilePath', { id: id, _csrf: '<%- csrf %>' }, function (result) {
                    if (result.success) {
                        if (result.path.indexOf('http') < 0) {
                            downloadImg(url + result.path)
                        } else {
                            downloadImg(result.path)
                        }
                    }
                })
            }
            function downloadImg(url) {
                if (!!window.ActiveXObject || "ActiveXObject" in window) {
                    var filename = url.substr(url.lastIndexOf('/') + 1, url.length);
                    var xhr = new XMLHttpRequest();
                    xhr.open('GET', encodeURI(url), true);
                    xhr.responseType = 'blob';
                    xhr.setRequestHeader("Authorization", "Basic a2VybWl0Omtlcm1pdA==")
                    xhr.onload = function () {
                        if (xhr.status === 200) {
                            //将图片文件用浏览器中下载
                            saveAs(xhr.response, filename);
                            document.execCommand(xhr.response, '', filename);
                        }
                    }
                    xhr.send();
                } else {
                    var a = document.createElement('a');          // 创建一个a节点插入的document
                    var event = new MouseEvent('click')           // 模拟鼠标click点击事件
                    a.download = ''                  // 设置a节点的download属性值
                    a.href = url;                                 // 将图片的src赋值给a节点的href
                    a.dispatchEvent(event);                      // 触发鼠标点击事件
                }
            }
        })
</script>
<% include footer.html %>
