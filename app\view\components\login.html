<div class="isShowLoginBg" v-if="showLoginModal" v-cloak>
  <el-dialog class="loginDialog" 
    :visible.sync="showLoginModal" 
    width="900px" 
    append-to-body='true' 
    custom-class="Modal_box"
    :show-close="closeBtn" 
    :before-close="handleCloseLogin" 
    :close-on-click-modal="false" 
    :close-on-press-escape="false"
    :lock-scroll="true">
    <tic-login-merge 
      :isRedirect="isRedirect" 
      :domain="domain" 
      :success-callback="successCallback"
      :parameter="parameter" 
      :env="env">
    </tic-login-merge>
  </el-dialog>
</div>