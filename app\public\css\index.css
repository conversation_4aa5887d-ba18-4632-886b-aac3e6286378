.n_home {
  background: #f1f1f1;
}
.n_home .home_topbar {
  height: 35px;
  line-height: 35px;
  background: #E3E3E3;
}
.n_home .home_topbar .home_topbar_wrap {
  width: 1226px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}
.n_home .home_topbar .home_topbar_wrap a {
  color: #626262;
  cursor: pointer;
}
.n_home .home_topbar .home_topbar_wrap a:hover {
  color: #ca4300;
}
.n_home .home_topbar .home_topbar_menu * {
  color: #626262;
  height: 13px;
  line-height: 13px;
}
.n_home .home_topbar .home_topbar_menu span {
  padding-left: 15px;
}
.n_home .home_topbar .home_topbar_menu span.myorder {
  cursor: pointer;
}
.n_home .home_topbar .home_topbar_menu span.myorder:hover {
  color: #ca4300;
}
.n_home .home_topbar .home_topbar_menu .home_topbar_menu-border {
  border-left: 1px solid #626262;
  margin-left: 15px;
}
.n_home .home_topbar .home_topbar_menu .home_topbar_menu-car {
  background: url('../images/home/<USER>/car.png') no-repeat left center;
  padding-left: 15px;
}
.n_home .home_top {
  background: #fff;
  height: 89px;
  border-bottom: 1px solid #E5E5E5;
}
.n_home .home_top .home_top_logo {
  float: left;
  margin-top: 5px;
  width: 350px;
  height: 80px;
  align-items: center;
  display: flex;
}
.n_home .home_top .home_top_logo img {
  height: 53px;
  margin-right: 22px;
}
.n_home .home_top .home_top_logo .slogan {
  font-size: 14px;
  color: #333333;
  line-height: 22px;
}
.n_home .home_top .home_top_logo .slogan img {
  width: 19px;
  height: 14px;
  margin-right: 5px;
  vertical-align: sub;
}
.n_home .home_top .home_top_logo .slogan .sub-title {
  font-size: 12px;
  color: #959595;
}
.n_home .home_top .home_top_search {
  width: 468px;
  border: 1px solid #ca4300;
  border-radius: 20px;
  height: 38px;
  line-height: 38px;
  float: left;
  position: relative;
  margin: 25px 0 25px 65px;
}
.n_home .home_top .home_top_search .home_top_search_input,
.n_home .home_top .home_top_search .home_top_search_btn {
  border: 0;
  outline: 0;
  position: absolute;
  border-radius: 20px;
}
.n_home .home_top .home_top_search .home_top_search_input {
  left: 1px;
  width: 445px;
  padding-left: 15px;
  height: 38px;
  line-height: 38px;
  top: 0;
  font-size: 14px;
  color: #333;
}
.n_home .home_top .home_top_search .home_top_search_btn {
  width: 80px;
  background: url('../images/home/<USER>/shearch_icon.png') no-repeat center center #ca4300;
  right: -40px;
  cursor: pointer;
  z-index: 3;
  height: 40px;
  line-height: 40px;
  top: -1px;
}
.n_home .home_top .home_top_search .home_top_search_btn:hover {
  background: url('../images/home/<USER>/shearch_icon.png') no-repeat center center #f49000;
}
.n_home .home_top .home_top_search .home_top_search_hotWord {
  position: absolute;
  z-index: 2;
  text-align: right;
  top: 5px;
  right: 56px;
  height: 20px;
  font-size: 12px;
}
.n_home .home_top .home_top_search .home_top_search_hotWord a {
  display: inline-block;
  width: 54px;
  padding: 0 3px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  overflow: hidden;
  background: #F3F3F3;
  color: #878787;
  border-radius: 5px;
  margin-left: 5px;
}
.n_home .home_top .home_top_search .home_top_search_hotWord a:hover {
  background: #ca4300;
  color: #fff;
}
.n_home .home_top .home_top_search .home_top_search_history {
  display: none;
  position: absolute;
  top: 39px;
  left: 19px;
  width: 100%;
  z-index: 5;
  background: #fff;
  border-top: 0;
  width: 410px;
  height: 215px;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 15px 0;
}
.n_home .home_top .home_top_search .home_top_search_history li {
  height: 36px;
  line-height: 36px;
  font-size: 14px;
  padding: 0 25px;
  cursor: pointer;
  overflow: hidden;
}
.n_home .home_top .home_top_search .home_top_search_history li:hover {
  background: #F5F5F5;
}
.n_home .home_top .home_top_hotline {
  float: right;
  width: 192px;
  margin-top: 20px;
}
.n_home .home_top .home_top_hotline .home_top_hotline_phone {
  height: 28px;
  line-height: 28px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px;
  margin-bottom: 5px;
}
.n_home .home_top .home_top_hotline .home_top_hotline_phone * {
  height: 28px;
  line-height: 28px;
  display: block;
  float: left;
}
.n_home .home_top .home_top_hotline .home_top_hotline_phone em {
  color: #727272;
  font-size: 15px;
}
.n_home .home_top .home_top_hotline .home_top_hotline_phone i {
  width: 20px;
  height: 19px;
  background: url('../images/home/<USER>/phone_icon.png') no-repeat;
}
.n_home .home_top .home_top_hotline .home_top_hotline_phone span {
  font-size: 23px;
  font-weight: bold;
  color: #ca4300;
  width: 150px;
}
.n_home .home_top .home_top_hotline .home_top_hotline_intro {
  font-size: 12px;
  color: #C9C9C9;
  text-align: right;
}
.home-ads {
  background: #e8e8e8;
}
.home-ads .wrap {
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.home-ads .wrap .bottom-item {
  display: flex;
  align-items: center;
  color: #333;
  font-size: 16px;
  line-height: 16px;
}
.home-ads .wrap .bottom-item:not(:last-child) {
  margin-right: 100px;
}
.home-ads .wrap .bottom-item svg {
  width: 32px;
  height: 37px;
  margin-right: 20px;
}
