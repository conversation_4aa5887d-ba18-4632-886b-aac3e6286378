<% include header.html %>
<% include ./components/header.html %>
<div class="bgBanner">
    <img src="<%- locals.static %>/images/bgbanner.png" alt="">
    <div class="bgBannerW">
        <div class="bgBannerW1">SGS 新闻中心</div>
    </div>
</div>
<div class="location">
    <ul>
        <li class="locationLi">
            <a href="/">第三方检测机构</a>
        </li>
        <li class="locationLi icon"></li>
        <li class="locationLi">
            <a href="javascrpit:void(0);">新闻</a>
        </li>
    </ul>
</div>
<div class="newsList" id='newsList' v-cloak>
    <div class="nlBox">
        <div class="nlSearch">
            <div class="nlSearchBox">
                <el-form :inline="true" :model="form" class="demo-form-inline">
                    <el-form-item label="新闻标题：">
                        <el-input size="mini" v-model="form.title" placeholder="请输入新闻标题..."></el-input>
                    </el-form-item>
                    <el-form-item label="新闻日期：">
                        <el-date-picker
                            size="mini"
                            placeholder="请选择新闻日期..."
                            type="daterange"
                            @change='handleChangeDate'
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            v-model="createdDateView"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="新闻类型：">
                        <el-select size="mini" v-model="form.catalogId" placeholder="请选择新闻类型...">
                            <el-option v-for='(item, index) of newTypes' :key='index' :label="item.name"
                                :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="mini" type="primary" @click="handlerSearch" class='el-icon-search'></el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="nlLists">
            <ul class="nlListsUl" v-if='newList.length'>
                <li class="nlListsLi" v-for="(item, index) of newList" :key='index'>
                    <span class="nlListsI"></span>
                    <a :href="'/news/article/detail-' + item.id + '.html'" class="nlListsA">{{ item.title }}</a>
                    <img v-if='item.isTop' style="vertical-align: middle;" src="<%- locals.static %>/images/hot.png">
                    <span class="nlListsT">{{ item.gmtPublishTime }}</span>
                </li>
            </ul>
            <div v-else>
                <p v-if="searchhint==1">加载中......</p>
                <p v-else>很抱歉，没有为您找到对应的结果，请扩大筛选范围</p>
            </div>
            
        </div>
        <div class="pagination" v-if='newList.length' style="text-align: right; width: 1226px;">
            <el-pagination layout="prev, pager, next, jumper" @current-change="handleCurrentChange"
                @size-change="handleSizeChange" :current-page.sync="form.pageNum" :page-size="form.pageRow"
                :total="totalNum">
            </el-pagination>
        </div>
    </div>
</div>

<script>
    var newVue = new Vue({
        name: 'newsList',
        el: '#newsList',
        data: {
            createdDateView: [],
            form: {
                pageRow: 10,
                pageNum: 1,
                title: '',
                gmtPublishTime: '',
                catalogId: ''
            },
            totalNum: 0,
            newList: [],
            newTypes: <%- JSON.stringify(newsTypeList) %>,
            searchhint:1  //1 加载中  2 >15s没数据
        },
        methods: {
            handleCurrentChange: function (val) {
                this.form.pageNum = val
                this.qryNewsList()
            },
            handleSizeChange: function (val) {
                this.form.pageNum = val
                this.qryNewsList()
            },
            handlerSearch: function () {
                this.form.pageNum = 1
                this.qryNewsList()
            },
            handleChangeDate(date) {
                if (date && date[0] && date[1]) {
                    let start = date[0],
                        end = date[1];
                    end = end.substr(0, end.indexOf(" ")) + " 23:59:59";
                    this.form.gmtPublishTime = start + "/" + end;
                    this.createdDateView = [start, end];
                } else {
                    this.form.gmtPublishTime = "";
                }
            },
            qryNewsList: function () {
                var that = this
                var loading = this.$loading({
                    lock: true,
                    text: '加载中...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                    fullscreen: false
                });
                that.searchhint=1
                axios.post('/news/list', this.form,{timeout:15000})
                    .then(function (res) {
                        if (res.status === 200 && res.data.resultCode === '0') {
                            var datas = res.data.data;
                            that.totalNum = datas.totalNum;
                            that.newList = datas.items;
                            if(datas.length==0){
                                that.searchhint=2
                            }
                        } else {
                            that.$message({
                                message: '获取数据异常，请稍后重试。',
                                type: 'warning'
                            });
                            that.searchhint=2
                        }
                        loading.close();
                    })
                    .catch(function (error) {
                        that.$message({
                            message: error,
                            type: 'warning'
                        });
                        that.searchhint=2
                        loading.close();
                    });
            }
        },
        mounted: function () {
            this.newTypes.unshift({
                id: '',
                name: '全部'
            })
            this.qryNewsList()
        }
    });
</script>
<% include footer.html %>
