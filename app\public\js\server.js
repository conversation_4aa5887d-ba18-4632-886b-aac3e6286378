$(function () {
    var $_pages = $(".serverpage");
    var $_uls = $(".server1>ul");
    $_pages.click(function () {
        var index = $(this).index();
        $_uls.eq(index).css({"display":"block"}).siblings(".server1>ul").css("display","none");
    })

    $(".serverTabLi").click(function () {

        $(this).addClass("liActive").siblings(".serverTabLi").removeClass("liActive");

    })

    $(".serverpage").click(function () {
        $(this).addClass("pageActive").siblings(".serverpage").removeClass("pageActive");
    })

        // var $_serverli = $(this);
        // var $_con = $(".serverCon ul");
        // var index = $(this).index();

        // $_con.eq(index).css({"display":"block"}).siblings($_con).css({"display":"none"});
        //
        //
        // console.log($_bor);
        // $_bor.animate({left:138*index});

})