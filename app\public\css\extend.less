@import './mixin.less';

.clearfix:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.navwrap {
  height: 100px;
  background: #fff;
}

.navwrap * {
  box-sizing: content-box;
}

.navBox {
  width: 1226px;
  margin: 0 auto;
  background: white;
  position: relative;
  height: 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav {
  width: 100%;
  height: 0;
  margin: 0 auto;
  background: #ffffff;
}

.nav.fix {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2000 !important;
  height: 100px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.navwrap {
  height: 100px;
  background: #fff;
}

.navUl {
  float: left;
  width: auto;
  height: 100%;
  margin-left: 50px;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0;
}

.navLi {
  width: auto;
  height: 100px;
  line-height: 100px;
  font-size: 16px;
  color: #333333;
  float: left;
  position: relative;
  cursor: pointer;
  text-align: center;
  margin: 0 25px;
  list-style: none;
}

.navLi a {
  color: #333333;
}

.navLi a:hover {
  color: @mainColor;
  font-weight: bold;
}

.navLi.s:hover {
  color: @mainColor;
  font-weight: bold;
}

.navLi.t {
  margin-right: 0;
}

.navLi.t:hover {
  color: @mainColor;
  font-weight: bold;
}

.navBor {
  position: absolute;
  width: 1px;
  background: #eeeeee;
  height: auto;
  top: 10px;
  left: 200px;
}

.navImg {
  width: 113px;
  height: 53px;
  margin-top: 24px;
  float: left;
  padding-bottom: 20px;
}

.navImg img {
  width: 100%;
  height: 100%;
}

.detailNav {
  border-bottom: 1px solid #eeeeee;
  height: 100px;
  overflow: visible;
}

.navList {
  float: left;
}

.navList>ul {
  float: left;
  width: auto;
  height: 100%;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0;
  margin-left: 40px !important;
}

.mainNav {
  .n_category {
    overflow: visible;
  }
}
.mainNav>li {
  width: auto;
  height: 100px;
  line-height: 100px;
  font-size: 15px!important;
  color: rgb(51, 51, 51);
  float: left;
  cursor: pointer;
  text-align: center;
  list-style: none;
  position: relative;
}

.mainNav>li>a,
.mainNav>li>span {
  color: rgb(51, 51, 51);
  display: inline-block;
  padding: 0 13px !important;
}

.mainNav>li>a:hover,
.mainNav>li>span:hover {
  color: @mainColor;
  font-weight: bold;
}

.n_category {
  height: 412px;
  overflow: hidden;
  width: 100%;
  position: absolute;
}

.n_category #service,
.n_category #industry {
  width: 1226px;
  height: 412px;
  position: relative;
}

.n_category .side {
  margin: 0;
  padding: 0;
  width: 244px;
  height: 412px;
  overflow: hidden;
  float: left;
  background: #fff;
  border-right: 1px solid #f6f6f6;
}

.n_category .side li {
  height: 16px;
  clear: both;
  list-style: none;
  width: 245px;
  height: 33px;
  line-height: 33px;
  cursor: pointer;
}

.n_category .side li.active {
  background: #f6f6f6;
}

.n_category .side .icon {
  display: block;
  width: 16px;
  height: 16px;
  float: left;
  margin: 9px 10px 0 20px;
}

.n_category .side a {
  color: #333;
  font-size: 15px;
  display: block;
  float: left;
  font-weight: 400;
  padding-left: 30px;
}

.n_category .side a:hover,
.n_category .side li.active a {
  color: #ca4300;
}

.n_category .side span {
  float: right;
  display: block;
  fill: #B5B5B5;
  font-size: 15px;
  margin-right: 27px;
  /*background: url("./../images/home/<USER>/home_menu_icon.png") no-repeat;*/
  width: 6px;
  height: 12px;
}

.n_category .side span .right-icon {
  width: 6px;
  height: 12px;
}

.n_category .side li.active span {
  display: block;
  fill: #ca4300;
  /*background: url("./../images/home/<USER>/home_menu_icon_activity.png") no-repeat;*/
}

.n_category .main {
  display: none;
  position: absolute;
  left: 245px;
  top: 0;
  z-index: 3;
  background: #fff;
  width: 981px;
  height: 412px;
  border-left: 0;
  border-top: 0;
}

.n_category .main .list {
  display: none;
}

.n_category .main .list.active {
  display: block;
}

.n_category .main-list {
  width: 730px;
  float: left;
  padding: 0 15px 25px 25px;
}

.n_category .main-list dl {
  margin: 19px 0;
  width: 730px;
  height: 52px;
  overflow: hidden;
  text-align: left;
}

.n_category .main-list .main-list-line {
  border-bottom: 1px dashed #ddd;
  width: 730px;
}

.n_category .main-list dl:last-child {
  border-bottom: 0;
}

.n_category .main-list dt,
.n_category .main-list dd {
  float: left;
  width: 158px;
  padding: 0;
  margin: 0;
  text-align: left;
}

.n_category .main-list dt a:hover,
.n_category .main-list dd a:hover,
.n_category .main-list dd a.hot {
  color: #ca4300;
}

.n_category .main-list dt {
  width: 67px;
  line-height: 18px;
  margin-top: 3px;
  margin-right: 30px;
}

.n_category .main-list dt span {
  color: #333;
  display: block;
  height: 36px;
  float: left;
  width: 10px;
  font-size: 20px;
}

.n_category .main-list dt span i {
  display: block;
  width: 5px;
  height: 5px;
  background: #333;
  margin-top: 5px;
}

.n_category .main-list dt a {
  display: block;
  float: left;
  /* width: 57px; */
  font-size: 14px;
  color: #333;
  width: 67px;
  height: 36px;
  font-weight: bold;
}

.n_category .main-list dt em {
  display: block;
  float: left;
  font-size: 14px;
  color: #333;
  width: 67px;
  height: 36px;
  font-weight: bold;
  font-style: normal;
}

.n_category .main-list dd {
  height: 26px;
  line-height: 26px;
}

.n_category .main-list dd i {
  width: 20px;
  height: 14px;
  display: inline-block;
  background: url(../images/nav-hot.png) no-repeat 0 0;
  float: left;
  margin-top: 7px;
}

.n_category .main-list dd a {
  color: #6d6d6d;
  font-size: 14px;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 26px;
  line-height: 26px;
  display: inline-block;
  width: 130px;
  overflow: hidden;
  float: left;
}

.n_category .second {
  float: left;
  width: 200px;
  background: #f5f5f5;
  height: 424px;
}

.n_category .second li {
  height: 33px;
  line-height: 33px;
}

.n_category .second li a {
  color: #333;
  font-size: 14px;
  border-left: 2px solid #f5f5f5;
  padding-left: 20px;
  font-weight: 400;
}

.n_category .second li.active {
  background: #fff;
}

.n_category .second li.active a {
  color: #ca4300;
  border-left: 2px solid #ca4300;
}

.n_category .secondList {
  float: left;
  width: 780px;
}

.n_category .dashedLine {
  width: 100%;
  border-top: 1px dashed #ddd;
  margin: 20px 0;
}

.n_category .ads-mt {
  position: absolute;
  top: 0;
  right: 0;
}

.n_category .main-list2 {
  float: left;
  width: 520px;
  padding: 25px 25px 25px 25px;
  height: 363px;
  overflow: hidden;
}

.n_category .main-list2 label {
  width: 100%;
  overflow: hidden;
  height: 100%;
  display: block;
  font-weight: normal !important;
}

.n_category .main-list2 p {
  font-size: 14px;
  color: #333;
  padding-bottom: 10px;
  font-weight: bold;
}

.n_category .main-list2 span,
.n_category .main-list2 a {
  display: inline-block;
  width: 170px;
  height: 25px;
  line-height: 25px;
  font-size: 14px;
  color: #666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.n_category .main-list2 a.hot,
.n_category .main-list2 a:hover {
  color: #ca4300;
}

.n_category .main-list2 .cleafix {
  font-size: 14px;
  color: #333;
  padding-bottom: 5px;
  font-weight: bold;
  display: block;
}

.n_category .ads {
  width: 185px;
  float: right;
  padding-top: 25px;
  padding-right: 20px;
}

.n_category .ads ul {
  height: 294px;
  overflow: hidden;
}

.n_category .ads li {
  list-style: none;
  margin-bottom: 30px;
}

.n_category .ads li a {
  width: 185px;
  height: 66px;
  background-size: cover;
  display: block;
}

.n_category .ads img {
  width: 185px;
  height: 66px;
  background: #ddd;
}

.n_category .ads h3,
.n_category .ads p {
  width: 185px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.n_category .ads h3 {
  color: #333;
  font-size: 14px;
  padding-top: 10px;
}

.n_category .ads a:hover h3 {
  color: #ca4300;
}

.n_category .ads p {
  color: #818181;
  font-size: 12px;
  padding-top: 5px;
}

.n_category .ads .moreService {
  text-align: center;
  font-size: 14px;
  line-height: 20px;
}

.n_category .ads .moreService a {
  color: #ca4300;
}

.n_category .ads .moreService i {
  display: inline-block;
  width: 15px;
  height: 10px;
  background: url(../images/nav-more.png) no-repeat 0 0;
}


/* 在线下单滚动动画 */
.navList {
  .order-online {
    position: relative;
  }

  .srcoll {
    position: absolute;
    top: 15px;
    left: 10px;
    width: 65px;
    height: 18px;
    line-height: 18px;
    font-size: 12px;
    color: #fcfdff;
    border-radius: 9px;
    background: -webkit-gradient(linear, left top, right top, from(#ffb94b), to(#ff7224));
    background: linear-gradient(90deg, #ffb94b, #ff7224);

    &::before {
      left: 12px;
      right: unset;
      top: 16px;
      border-color: #ffa733 transparent transparent #ffa733;
      content: " ";
      position: absolute;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 4px;
    }

    .srcolling {
      height: 100%;
      overflow: hidden;

      .srcolling-child {
        -webkit-animation-name: change;
        animation-name: change;
        -webkit-animation-duration: 8s;
        animation-duration: 8s;
        -webkit-animation-iteration-count: infinite;
        animation-iteration-count: infinite;

        span {
          display: block;
          width: 100%;
          height: 18px;
        }
      }
    }

    @keyframes change {
      0%, 20% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
      }
      25%, 45% {
        -webkit-transform: translateY(-25%);
        transform: translateY(-25%);
      }
      50%, 70% {
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
      }
      75%, 95% {
        -webkit-transform: translateY(-75%);
        transform: translateY(-75%);
      }
      100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
      }
    }
    @-webkit-keyframes change {
      0%, 20% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
      }
      25%, 45% {
        -webkit-transform: translateY(-25%);
        transform: translateY(-25%);
      }
      50%, 70% {
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
      }
      75%, 95% {
        -webkit-transform: translateY(-75%);
        transform: translateY(-75%);
      }
      100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
      }
    }
  }
}

/* 在线下单滚动动画 */

#tradeNav,
#serviceNav {
  display: none;
}

.allNav1 {
  background: #fff;
  overflow: hidden;
}
.allNav1 * {
  padding: 0;
  margin: 0;
}

.allNav1 ul, .allNav1 li {
  padding: 0;
  margin: 0;
  text-align: left;
  line-height: normal;
}

#tradeHome .allNav1 {
  left: -258px;
}

#serviceHome .allNav1 {
  left: -154px;
}

.allNav1 {
  height: 100px;
  width: 1226px;
  position: absolute;
  top: 100px;
  left: 0px;
  z-index: 100;
  border-top: 4px solid @mainColor;
  min-height: 424px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* 添加四周阴影 */
}

.allNav .side li.active a.cursorDefault {
  cursor: default;
}

.allNav li {
  list-style: none;
}

.allNav .side {
  float: left;
  width: 230px;
  background: rgba(75, 75, 75, 1);
  height: 100%;
}

.allNav .side li {
  height: 36px;
  line-height: 36px;
  text-align: left;
}

.allNav .side li:hover,
.allNav .side li.active {
  background: #fff;
}

.allNav .side li:hover a,
.allNav .side li.active a {
  color: @mainColor;
  cursor: pointer;
  font-weight: bold;
}

.allNav .side a {
  color: #fff;
  font-size: 15px;
  padding-left: 30px;
  display: inline-block;
  height: 24px;
  line-height: 24px;
}

.allNav .main {
  background: #fff;
  height: 468px;
  float: left;
  width: 966px;
  padding: 0 30px 0 0;
  overflow: hidden;
  position: relative;
}

.allNav .main .list {
  display: none;
  width: 996px;
  height: 468px;
  overflow-y: auto;
}

.main-list {
  min-height: 277px;
}

.main-list2 {
  min-height: 275px;
}

.allNav .main .list.active {
  display: block;
}

.allNav .main .list dl {
  border-bottom: 1px dashed #999;
  padding-bottom: 15px;
  margin: 0 30px;
}

.allNav .main .list dl:last-child {
  border-bottom: 0;
  padding-bottom: 0;
}

.allNav .main .list dt {
  text-align: left;
  padding: 15px 0 0 0;
  height: 20px;
  line-height: 20px;
  clear: both;
}

.allNav .main .list dt span {
  background: #4B4B4B;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  display: block;
  margin-top: 8px;
  float: left;
  color: #fff;
}

.allNav .main .list dt a,
.allNav .main .list dt em {
  color: #4B4B4B;
  font-size: 14px;
  display: block;
  float: left;
  padding-left: 5px;
  font-style: normal;
}

.allNav .main .list dt em.cursorDefault {
  cursor: default;
}

.allNav .main .list dd {
  float: left;
  height: 20px;
  line-height: 20px;
  padding-top: 10px;
  text-align: left;
  margin: 0;
  margin-right: 20px;
  padding-left: 10px;
}

.allNav .main .list dd a {
  color: #999;
  font-size: 14px;
}

.allNav .main .list a:hover {
  color: @mainColor;
}

.allNav .main .list dd i {
  width: 20px;
  height: 14px;
  display: inline-block;
  background: url(../images/nav-hot.png) no-repeat 0 0;
}

.allNav .main .second {
  float: left;
  width: 233px;
  background: #F5F5F5;
  height: 468px;
  position: absolute;
  top: 0;
  left: 0;
}

.allNav .main .second li {
  height: 36px;
  line-height: 36px;
  text-align: left;
}

.allNav .main .second li.active,
.allNav .main .second li:hover {
  background: #fff;
}

.allNav .main .second li.active a,
.allNav .main .second li:hover a {
  border-left: 4px solid @mainColor;
}

.allNav .main .second li a {
  color: #4B4B4B;
  font-size: 15px;
  display: inline-block;
  height: 16px;
  line-height: 16px;
  border-left: 4px solid #F5F5F5;
  padding-left: 30px;
}

.allNav .main .secondList {
  width: 690px;
  float: right;
  height: 468px;
  padding: 0 30px;
}

.allNav .main .secondList li {
  display: none;
  width: 100%;
  clear: both;
  padding-top: 10px;
}

.allNav .main .secondList li.active {
  display: block;
}

.allNav .main .secondList p {
  text-align: left;
  color: #4B4B4B;
  font-size: 15px;
  clear: both;
  height: 24px;
  line-height: 24px;
  padding-top: 10px;
}

.allNav .main .secondList .main-list2 p {
  cursor: default;
}

.allNav .main .secondList p:first-child {
  padding-top: 0;
}

.allNav .main .secondList span {
  height: 25px;
  line-height: 25px;
  padding-top: 5px;
  text-align: left;
  margin: 0;
  padding-right: 30px;
  float: left;
}

.allNav .main .secondList i {
  width: 20px;
  height: 14px;
  display: inline-block;
  background: url(../images/nav-hot.png) no-repeat 0 0;
}

.allNav .main .secondList li a {
  color: #999;
  font-size: 14px;
}

.allNav .ads {
  margin: 0 30px;
}

.allNav .secondList .ads {
  margin: 0;
}

.allNav .secondList .ads.ads-mt {
  margin-top: -81px;
}

.allNav .main .secondList .ads ul {
  width: 690px;
  height: 100px;
  overflow: hidden;
  margin-bottom: 20px;
}

.allNav .main .secondList .ads li {
  display: block;
  clear: none;
  width: 321px;
}

.allNav .main .secondList .ads li img {
  width: 146px;
  height: 72px;
  margin-right: 10px;
}

.allNav .main .secondList .ads li .info {
  width: 165px;
  height: 72px;
}

.allNav .main .secondList .ads li h3 {
  width: 165px;
}

.allNav .main .secondList .ads li p {
  color: #999;
  width: 165px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 13px;
  height: 14px;
  line-height: 14px;
  text-align: left;
  margin-top: 3px;
  padding: 0;
}

.allNav .main .secondList .ads li em,
.allNav .main .secondList .ads li button {
  color: #fff;
  width: 72px;
  height: 24px;
  line-height: 24px;
  font-size: 13px;
  margin-top: 12px;
}

.allNav .moreService {
  text-align: right;
  border-bottom: 1px dashed #999;
  padding: 10px 0;
  font-size: 14px;
  height: 22px;
  line-height: 22px;
  margin: 9px 0;
}

.allNav .moreService {
  text-align: right;
  border-bottom: 1px dashed #999;
  padding: 10px 0;
  font-size: 14px;
  height: 22px;
  line-height: 22px;
  margin: 9px 0;
}

.allNav .moreService i {
  display: inline-block;
  width: 15px;
  height: 10px;
  background: url(../images/nav-more.png) no-repeat 0 0;
}

.allNav .secondList .moreService i {
  display: inline-block;
  width: 15px;
  height: 10px;
  background: url(../images/nav-more.png) no-repeat 0 0;
}

.allNav .moreService a {
  color: @mainColor;
}

.allNav .main .secondList li a.moreServiceA {
  color: @mainColor;
}

.allNav .ads ul {
  padding-bottom: 20px;
  width: 100%;
  height: 100px;
  overflow: hidden;
}

.allNav .ads li {
  float: left;
  margin-top: 20px;
}

.allNav .ads li:nth-child(even) {
  float: right;
}

.allNav .ads img {
  width: 180px;
  height: 90px;
  float: left;
  margin-right: 10px;
}

.allNav .ads .mini img {
  width: 146px;
  height: 72px;
  margin-right: 10px;
}

.allNav .ads .info {
  float: right;
  width: 240px;
  height: 90px;
}

.allNav .ads .mini .info {
  height: 72px;
}

.allNav .ads h3 {
  width: 240px;
  font-size: 16px;
  color: #252525;
  font-weight: normal;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 18px;
  line-height: 18px;
  text-align: left;
}

.allNav .ads h3:hover {
  color: @mainColor;
}

.allNav .ads p {
  color: #999;
  width: 240px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 13px;
  height: 14px;
  line-height: 14px;
  text-align: left;
  margin-top: 3px;
}

.allNav .ads li button,
.allNav .ads li em {
  font-size: 15px;
  color: #fff;
  border: 0;
  outline: 0;
  background: @mainColor;
  cursor: pointer;
  display: block;
  width: 90px;
  height: 34px;
  line-height: 34px;
  margin-top: 21px;
  float: left;
  font-style: normal;
}

.allNav .ads .mini li button,
.allNav .ads .mini li em {
  width: 72px;
  height: 24px;
  line-height: 24px;
  font-size: 13px;
  margin-top: 13px;
  display: block;
  font-style: normal;
}

.allNav .ads li em:hover,
.allNav .ads .mini li em:hover {
  color: #fff;
  background: #fe7427;
}

.navSearchBox {
  height: 46px;
  float: right;
  position: relative;
  width: 400px;
  right: 0;
  top: 0;
}

.navSearchBox .hotKeyword {
  position: absolute;
  right: 50px;
  top: 13px;
}

.navSearchBox .hotKeyword span {
  background: #ddd;
  color: #fff;
  padding: 0 5px;
  font-size: 12px;
  cursor: pointer;
  display: inline-block;
  margin-right: 3px;
  height: 20px;
  line-height: 20px;
}

.navSearchBox .hotKeyword span:hover {
  background: #ca4300;
}

.navSearchBox .linkageWord {
  position: absolute;
  top: 45px;
  left: 0;
  border: 1px solid #ca4300;
  width: 398px;
  z-index: 2;
  background: #fff;
  display: none;
}

.navSearchBox .linkageWord ul {
  padding: 5px 0;
  margin: 0;
}

.navSearchBox .linkageWord li {
  height: 35px;
  line-height: 35px;
  overflow: hidden;
  list-style: none;
  padding-left: 15px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 370px;
}

.navSearchBox .linkageWord li:hover {
  background: #f5f5f5;
}

.navSearchBox form {
  width: 100%;
  height: 100%;
}

.navSearch {
  width: 342px;
  height: 40px;
  border: none;
  outline: none;
  border: 1px solid #ca4300;
  float: right;
  padding: 2px 0 2px 10px;
  font-size: 16px;
  line-height: 40px;
}

.navSearchBox button {
  width: 46px;
  height: 46px;
  background: #ca4300;
  float: right;
  position: relative;
  cursor: pointer;
  overflow: hidden;
  border: 0;
}

.navSearchIconIcon {
  width: 20px;
  height: 20px;
  background: url(../images/search.png) no-repeat 50% 50%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.navImg,
.navSearch {
  box-sizing: content-box;
}

.nav_first--li {
  position: relative;
}

.nav_first--li .nav_second {
  background: #333333;
  position: absolute;
  top: 84px;
  left: -17px;
  z-index: 2;
  width: 150px;
  display: none;
  border-top: 4px solid @mainColor;
}

.nav_first--li .nav_second dd {
  height: 35px;
  line-height: 35px;
  text-align: center;
  margin: 0;
}

.nav_first--li .nav_second dd a {
  color: #fff;
  display: inline-block;
  height: 35px;
  line-height: 35px;
  width: 100%;
}

.nav_first--li .nav_second dd a:hover {
  background: #fff;
  color: @mainColor;
  font-weight: bold;
}

.dragAngel {
  width: 0;
  height: 0;
  position: absolute;
  overflow: hidden;
  font-size: 0;
  line-height: 0px;
  border-width: 0px 10px 11px;
  border-style: solid solid solid solid;
  border-left-color: transparent;
  border-right-color: transparent;
  border-top-color: transparent;
  border-bottom-color: @mainColor;
  display: none;
  left: 44px;
  top: 89px;
}

.navSearchBox button {
  box-sizing: border-box;
}

// sgs-floating-menu
.sgs-floating-menu {
  width: 56px;
  height: 345px;
  position: fixed;
  right: 15px;
  bottom: 15%;
  z-index: 10000;
  font-size: 14px;

  .menu {
    li {
      width: 56px;
      height: 56px;
      background: @mainColor;
      cursor: pointer;
      color: #333;
      font-size: 12px;
      text-align: center;
      position: relative;
      margin-bottom: 10px;
      border-radius: 3px;
      box-shadow: 2px 2px 5px 0px rgba(0, 0, 0, 0.3);

      &.hide {
        display: none;
      }

      &.show {
        display: block !important;
      }

      &:hover {
        background: #444444;

        &.phone {
          .menu-icon {
            i {
              background: url('./../images/fixedMenu/floating.menu.tel.hover.gif') no-repeat;
            }
          }
        }
      }

      &.phone {
        .menu-icon {
          i {
            display: inline-block;
            width: 30px;
            height: 28px;
            margin: 5px 0 0 0;
            background: url('./../images/fixedMenu/floating.menu.tel.gif') no-repeat;
          }
        }
      }

      .menu-icon {
        margin: 5px 0 0 0px;
      }

      span.icon-desc {
        list-style: none;
        color: #FEFEFE;
        font-size: 12px;
        text-align: center;
        position: relative;
        border-radius: 8px;
      }

      img {
        display: inline-block;
        width: 30px;
        height: 28px;
        margin: 5px 0 0 0;
      }

      .menu-more-phone {
        display: block;
        width: 188px;
        height: 56px;
        line-height: 56px;
        color: #FFF;
        font-size: 22px;
        overflow: hidden;
        white-space: nowrap;
        background: #444444;
        position: absolute;
        top: 0;
        right: 56px;
      }

      .qrCode-img {
        position: absolute;
        top: -120px;
        right: 66px;
        width: 191px;
        height: 271px;
      }

      .menu-more {
        height: 56px;
        line-height: 56px;
        position: absolute;
        top: 0;
        right: 42px;

        span {
          color: white;
          width: 120px;
          height: 100%;
          font-size: 14px;
          background: #ca4300;
          position: relative;
          display: block;
        }
      }

      &.feedback,
      &.question {
        height: 30px;
        line-height: 30px;
        background: #E5E5E5;
        border-radius: 3px;

        .icon-desc {
          color: #696969;
        }

        a {
          color: #646464;
        }

        &:hover {
          background: #444;

          span,
          a {
            color: #fff;
          }
        }
      }

      &.sgs-discount {
        img {
          width: 56px;
          height: 26px;
        }

        span {
          color: #FFFEA2;
        }
      }
    }

    a.shop_cart {
      z-index: 10000;
    }

    .phone .menu-more span a:hover {
      font-weight: bold;
      text-decoration: underline;
    }

    .servce {
      position: relative;
    }

    .zns {
      position: absolute;
      top: 0;
      right: 58px;
      width: 136px;
      padding: 0 2px;
      background-color: #ca4300;
      display: none;

      li {
        width: 100%;
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        margin: 0;
        border-bottom: 1px dashed #FFF;

        &:last-child {
          border: none;
        }

        a {
          color: white;
          display: block;

          &:hover {
            text-decoration: underline;
            font-weight: bold;
          }
        }
      }
    }
  }

  .floating-menu-feedback {
    display: none;
    width: 471px;
    min-height: 348px;
    background: white;
    border: 1px solid #BFBFBF;
    position: absolute;
    top: -35px;
    right: 66px;

    .button-close {
      width: 12px;
      height: 12px;
      cursor: pointer;

      position: relative;
      top: -28px;
      right: -410px;
    }

    .block-menu-feedback {
      margin: 26px 0 0 27px;

      .err {
        border: 1px solid #f00;
      }

      .err_tips {
        color: #f00;
        //height: 20px;
        line-height: 20px;
      }
    }

    .block-content {
      width: 422px;
      height: 111px;
      background: #F2F2F2;
      margin-top: 13px;
      border-radius: 5px;
      overflow: hidden;
      border: 1px solid #fff;

      textarea {
        width: 422px;
        height: 83px;
        line-height: 18px;
        font-size: 14px;
        color: #878787;
        background: #F2F2F2;
        outline: none;
        border: 0px;
        resize: none;
        margin: 13px;
        font-family: Roboto,
          "Helvetica Neue",
          Arial,
          Helvetica,
          "PingFang SC",
          "Hiragino Sans GB",
          "Heiti SC",
          "Microsoft YaHei",
          "WenQuanYi Micro Hei",
          sans-serif;
      }
    }

    .block-input-area {
      min-height: 62px;

      input {
        color: #878787;
        width: 192px;
        height: 30px;
        background: #F2F2F2;
        border-radius: 5px;
        border: 0px;
        padding-left: 12px;
        margin-top: 10px;
        border: 1px solid #fff;
      }

      &>div {
        float: left;
      }
    }

    .block-form-action {
      margin-top: 23px;
      text-align: center;

      .button-submit {
        color: #333;
        font-size: 14px;
        width: 58px;
        height: 30px;
        border: 1px solid #BFBFBF;
        border-radius: 3px;
        background: #F2F2F2;
        outline: none;
        cursor: pointer;

        &:hover {
          background: @mainColor;
          border: 1px solid @mainColor;
          color: #fff;
        }
      }

    }

    em {
      color: #f00;
    }
  }

  input,
  textarea {
    &:focus {
      border: 0;
      outline: 0;
    }
  }
}

// sgs-floating-menu

// modal
.modal {
  position: absolute;
  right: 66px;
  width: 423px;
  height: 180px;
  background: #FFFFFF;
  border: 1px solid #BFBFBF;
  top: 50%;
  transform: translate(0, -50%);
  text-align: center;
  z-index: 9;
  display: none;

  .modal_content {
    padding-top: 47px;
  }

  i {
    background: url('./../images/fixedMenu/success.png') no-repeat 0 0;
    display: block;
    float: left;
    width: 35px;
    height: 35px;
    margin-left: 55px;
  }

  span {
    height: 35px;
    line-height: 35px;
    display: block;
    float: left;
    padding-left: 18px;
  }

  p {
    font-size: 14px;
    color: #333333;
    line-height: 35px;
    height: 35px;
  }

  .modal_button {
    margin-top: 21px;
  }

  button {
    width: 60px;
    height: 32px;
    line-height: 32px;
    background: #F2F2F2;
    border: 1px solid #BFBFBF;
    border-radius: 3px;
    cursor: pointer;

    &:hover {
      background: @mainColor;
      border: 1px solid @mainColor;
      color: #fff;
    }
  }
}

// modal

.ld-chat-launcher {
  // right: -500px !important;
  // visibility: hidden !important;
  // top: 500px !important;
  // right: -80px !important;
}