'use strict';

/**
 * @param {Egg.Application} app - egg application
 */
module.exports = app => {
  const {
    router,
    controller
  } = app;

  const urlRecode = app.middlewares.urlrecord();
  const cdnPath = app.config.cdn;
  if (cdnPath && cdnPath != '') {
    app.locals.static = cdnPath;
  } else {
    app.locals.static = '/static';
  }
  app.locals.env = app.config.env;

  router.get('/', urlRecode, controller.home.homepage);
  router.get('/fromMobile', urlRecode, controller.home.homepage);
  router.get('/(index|index.html)', urlRecode, controller.home.homepage);

  // TIC-3720
  router.get('/sku/:alias/:id', urlRecode, controller.home.sku);
  router.get('/news/:alias/detail-:id.html', urlRecode, controller.home.newsDetail);
  router.get('/case/:alias/detail-:id.html', urlRecode, controller.home.caseDetail);
  router.get('/(service|industry)/:id', urlRecode, controller.home.tradeDetail);
  // router.get('/sku/randomSku', controller.home.randomTag);
  router.get('/(service|industry)/:id/service', urlRecode, controller.home.tradeSkus);
  router.get('/news', urlRecode, controller.home.newsList);
  router.get('/case', urlRecode, controller.home.caseList);
  // router.get('/getCataSkus', urlRecode, controller.home.getCataSkus);
  // router.get('/getTrainCataSkus', urlRecode, controller.home.getTrainCataSkus);
  router.get('/overview/:alias', urlRecode, controller.home.other);
  router.get('/search', urlRecode, controller.home.search);
  router.get('/baojia', urlRecode, controller.home.baojia);
  router.get('/hotline', urlRecode, controller.home.hotline);
  router.get('/locatin', urlRecode, controller.home.redirect404);
  router.get('/setPlatform', urlRecode, controller.home.setPlatform);
  router.get('/clickrecord', controller.external.click400);
  router.get('/success', controller.home.success);
  router.get('/successRecord', controller.home.successRecord);
  router.get('/seoList', urlRecode, controller.home.seoList); // 服务推广
  router.get('/files', controller.home.files); // 资料下载
  router.get('/information', controller.home.information); // 资讯中心
  router.get('/information/detail-:id.html', controller.home.informationDetail); // 资讯详情
  router.get('/404', controller.home.page404); // 404错误页面
  router.get('/sitemap', controller.home.sitemap); // 网站地图
  /* TIC-5482 历史错误页面重定向到404 */
  router.get('/(service|industry)/:alias/:id', urlRecode, controller.home.redirect404);
  router.get('/(service|industry)/:alias/case/detail-:id.html', urlRecode, controller.home.redirect404);
  /* TIC-6554 历史错误页面重定向到404 */
  router.get('/sku/:id', urlRecode, controller.home.redirect404);
  router.get('/news/detail-:id.html', urlRecode, controller.home.redirect404);
  router.get('/case/detail-:id.html', urlRecode, controller.home.redirect404);

  // contact v2.0
  router.get('/quote', controller.home.quote); // 业务咨询
  router.get('/contact', controller.home.contact); // 建议反馈

  /*  活动列表  */
  router.get('/order', controller.home.order) // 订单查询
  router.get('/foodsafeguard', controller.home.foodsafeguard) // 农残兽残
  router.get('/nycl', controller.home.foodsafeguard) // 农药残留查询
  router.get('/shoucan', controller.home.foodsafeguard) // 兽残限量查询
  router.get('/teanycl', controller.home.foodsafeguard) // 茶业农残限量查询
  router.get('/DocCheck', controller.home.DocCheck) // 报告真伪
  router.get('/DocCheck/en', controller.home.DocCheckEn) // 报告真伪英文版
  router.get('/certified-client-directory', controller.home.DocCheck) // 体系认证证书查验
  router.get('/coreSupplier', controller.home.coreSupplier) // 核心合作供应商
  router.get('/event/NGOform', controller.home.NGOform); // NGO 表单
  router.get('/promotion/gaofenzi', controller.home.gaoFenZi); // 高分子行业分析
  router.get('/promotion/qichechanye', controller.home.qichechanye); // 汽车产业
  router.get('/promotion/iso9001', controller.home.iso9001); // iso9001
  router.get('/promotion/fangyi', controller.active.fangyi); // 防疫物资
  router.get('/Testing-and-Certification-Center', controller.home.jiance); // 检测认证中心
  // router.get('/sgs-tuv-saar', controller.home.jiance); // SGS TUV SAAR

  // 英文页面
  router.get('/ticket/en', urlRecode, controller.en.ticket); // 业务咨询
  router.get('/promotion/PPE-service', controller.active.fangyiEn); // 防疫物资

  // oiq
  router.get('/oiq', urlRecode, controller.oiq.landingPage); // 着陆页
  router.get('/oiq/start', urlRecode, controller.oiq.start); // 落地页

  // lab 实验室
  router.get('/lab/:alias', urlRecode, controller.home.labPage)

  // hl/medical
  router.get('/hl/medical', urlRecode, controller.home.hlMedical);

  /* post 接口 */
  router.post('/search', controller.external.searchSolr);
  router.post('/ticket/post', controller.home.ticketpost);
  router.post('/ticket/opinion', controller.external.tictetOpinion);
  router.post('/ticket/sku', controller.external.tictetSKU);
  router.post('/getCode', controller.home.getCode);
  router.post('/getFilePath', controller.home.getFilePath);
  router.post('/getFileList', controller.home.getFileList);
  router.post('/downloadRecord/add', controller.post.addDownloadRecord);
  router.post('/getInformationList', controller.home.getInformationList);
  router.post('/query/navigation', controller.home.queryNavigation);
  router.post('/query/skuById', controller.home.querySkuById);  // 检测中心页面，查询特有sku数据
  router.post('/sendSms', controller.external.sendSms);
  router.post('/submitTicket', controller.external.submitTicket);
  router.post('/submitTicketByEmail', controller.external.submitTicketByEmail);
  router.post('/qryPackage', controller.external.qryPackage);
  router.post('/hotWord/qry', controller.external.qryHotWord);
  router.post('/hotWord/click', controller.external.clickHotWord);
  router.post('/news/list', controller.external.qryNewsList);
  router.post('/case/list', controller.external.qryCaseList);
  router.post('/class/list', controller.external.qryClassList);
  router.post('/getOrderListUrl', controller.external.getOrderListUrl);
  router.post('/getOiqList', controller.external.getOiqList);
  router.post('/logout', controller.external.logout);
  router.post('/IMtool', controller.external.IMtool);
  router.post('/getNavication', controller.external.getNavication);
  router.post('/sendSPM', controller.external.sendSPM);
  router.post('/getCodeQuote', controller.external.getCodeQuote);
  router.post('/getFileListApi', controller.external.getFileListApi);
  router.post('/getSkuCataApi', controller.external.getSkuCataApi);
  router.post('/getSkuTagApi', controller.external.getSkuTagApi);
  router.post('/case/collection', controller.external.collection); // 点赞
  router.post('/case/successCallback', controller.external.successCallback);
  router.post('/file/emailSharing', controller.external.emailSharing);
  router.post('/check/token', controller.external.checkToken);
  router.post('/get/statByBusi', controller.external.statByBusi);
  router.post('/tencentCaptchaApi', controller.external.tencentCaptchaApi); //滑动验证码验证
  router.post('/getUserInfo', controller.external.getUserInfo);
  router.post('/loginReg', controller.external.loginReg);
};
