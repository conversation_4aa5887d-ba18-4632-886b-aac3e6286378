<% include header.html %>
  <% include ./components/header.html %>
    <div class="swiper-container"
      style="height: 150px; background:url('<%- locals.static %>/images/DocCheck/banner.png') 50% 50%">
      <h1 class="docCheck-title wrap">Verify SGS Documents Online</h1>
      <div class="docCheck-sub-title wrap">
        <span>Official verification</span>
        <span>Authenticity inquiry</span>
        <span>Instant feedback</span>
      </div>
    </div>
    <div class="docCheck-wrap wrap" id="docCheck" v-cloak>
      <div class="wrap docCheck-content">
        <div class="docCheck-left" v-if="checkType === 0">
          <div class="docCheck-con" id='con'>
            <div class="docCheck-con-box" v-show="tabIndex === 0" v-loading="checkLoading" :element-loading-text="loadingText[loadingTextIndex]">
              <div class="lang-tab">
                <a href="/DocCheck">中文</a>
                <a href="javascript:;" class="is-active">EN</a>
              </div>
              <el-form class="form-content" :model="form" :rules="rules" ref="form" label-width="104px"
                label-position="right">
                <el-row>
                  <el-col :span="12">
                    <el-form-item prop="userName" label="Name">
                      <el-input placeholder="Please enter your name" v-model="form.userName" maxlength="20"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="companyName" label="Company Name" label-width="120px">
                      <el-input placeholder="Please enter your company name" v-model="form.companyName" maxlength="200"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="userEmail" label="Email" class="tips-item">
                      <el-input placeholder="Please enter your email" v-model="form.userEmail" maxlength="80" :disabled="isLogin && userInfo.userEmail !== ''"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="userPhone" label="Phone" class="phone-item" label-width="120px">
                      <el-input placeholder="Please enter your phone number" v-model="form.userPhone" maxlength="50" :disabled="isLogin && userInfo.userPhone !== ''">
<!--                        <el-dropdown slot="prepend" @command="handleCommand">-->
<!--                          <div class="phone-prepend" style="cursor: pointer;">-->
<!--                            <span>{{phoneLabel}}</span>-->
<!--                            <i class="el-icon-caret-bottom"></i>-->
<!--                          </div>-->
<!--                          <el-dropdown-menu slot="dropdown">-->
<!--                            <el-dropdown-item command="Other">Other</el-dropdown-item>-->
<!--                            <el-dropdown-item command="+86">+86</el-dropdown-item>-->
<!--                          </el-dropdown-menu>-->
<!--                        </el-dropdown>-->
                        <div slot="prepend" class="phone-prepend" style="cursor: pointer;">
                          <span>{{phoneLabel}}</span>
                        </div>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item prop="url" label="Upload File" class="file-upload-item" v-if="checkType === 0">
                  <el-upload class="upload-demo" :show-file-list="false"
                    :action="host + '/ticCenter/business/api.v0.platform/fileUpload/uploadOss'"
                    :before-upload="beforeUpload" :on-success="onSuccess" :on-error="onError" accept="">
                    <el-button size="small" class="upload-btn" v-loading="uploadDisabled">Select File</el-button>
                  </el-upload>
                  <span v-if="fileList.length" class="file-name">{{fileList[0].name}}</span>
                  <span v-else>Please upload one document at one time</span>
                </el-form-item>
                <div class="upload-tips">
                  Please upload full file of the document, ensure page is clear and not obscured. PDF, JPG, PNG, ZIP file formats are available,
                  maximum file size 20MB.
                </div>
                <div class="upload-tips" v-if="!isLogin">
                  <div class="check-box">
                    <el-checkbox v-model="isChecked" @change="handleChangeChecked"></el-checkbox>
                    <div>
                      I agree the <span @click="dialogVisibleReg = true">Terms of Registration</span>，and SGS can use my data for the purposes of dealing with my request, in accordance with the <a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" target="_blank">SGS Online Privacy Statement</a>.
                    </div>
                  </div>
                  <div class="isCheckedTips" v-if="isCheckedTips">is required</div>
                </div>
                <div class="upload-tips" v-else>
                  <div class="check-box">
                    <el-checkbox v-model="isChecked" @change="handleChangeChecked"></el-checkbox>
                    <div>
                      I agree that SGS can use my data for the purposes of dealing with my request,in accordance with the <a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" target="_blank">SGS Online Privacy Statement</a>.
                    </div>
                  </div>
                  <div class="isCheckedTips" v-if="isCheckedTips">is required</div>
                </div>
                <button class="submit-btn el-button el-button--primary" monitor-uac-node="portal.user.tools.0007.verify"
                  @click.prevent="verifyForm(true)">Send Your Request</button>
              </el-form>
            </div>
          </div>
        </div>
        <div class="docCheck-left result" v-if="checkType !== 0" id="checkResult">
          <div class="pdf-result">
            <div class="tips" v-if="checkType === 1">
              <div class="title">Verification result</div>
              <div class="result-title">
                <img src="<%- locals.static %>/images/DocCheck/success.png" alt="">
                The file you submitted is a genuine SGS document.
              </div>
              <p>File Name：<span class="red-color">{{form.fileName}}</span></p>
              <div class="sub-tips" style="padding-left: 0;justify-content: center;">
                <div style="flex-shrink: 0;">Note:</div>
                <div>
                  A genuine SGS document indicates that the document is issued by SGS and has not been modified.
                </div>
              </div>
            </div>
            <div class="tips" v-if="checkType === 2">
              <div class="title">Verification result</div>
              <div class="result-title">
                <img src="<%- locals.static %>/images/DocCheck/qrCode.png" alt="">
                The QR code in your file is recognized as below original SGS document:
              </div>
              <p>Please compare the consistency with your file.</p>
            </div>
            <div class="tips" v-if="checkType === 3 || checkType === 4">
              <div class="title">Verification result</div>
              <div class="result-title" v-if="checkType === 3" style="padding-left: 57px;align-items: flex-start;">
                <img src="<%- locals.static %>/images/DocCheck/error.png" alt="">
                Your file has not been found in our online database. Your inquiry has been referred to our document verification team for manual checking. They will contact you by email in due course.
              </div>
              <div class="result-title" v-if="checkType === 4" style="padding-left: 57px;align-items: flex-start;">
                <img src="<%- locals.static %>/images/DocCheck/email.png" alt="">
                Sent to manual verification. Your inquiry has been referred to our document verification team for manual checking. They will contact you by email in due course.
              </div>
            </div>
            <template v-if="checkType === 5 || checkType === 6">
              <div class="tips">
                <div class="title">Verification result</div>
                <div class="result-title">
                  <img src="<%- locals.static %>/images/DocCheck/qrCode.png" alt="">
                  Original SGS document of QR code in your file {{checkType === 5 ? 'has been invalidated' : 'is unauthorized access.'}}
                </div>
              </div>
              <div>
                <div class="pdf-tips">QR code recognition result of your file:</div>
                <div class="invalidated-tips" v-if="checkType === 5">
                  This report has been invalidated <br>
                  此报告已失效。
                </div>
                <div class="invalidated-tips" v-else>
                  请注意，此报告已取消! <br>
                  Unauthorized Access!
                </div>
              </div>
            </template>
            <div class="tips" v-if="checkType === 7">
              <div class="title">Verification result</div>
              <div class="result-title">
                <img src="<%- locals.static %>/images/DocCheck/error.png" alt="">
                This is not an original SGS document.
              </div>
              <p>your submitting the document <span class="red-color">{{form.fileName}}</span> is not an original SGS document. </p>
              <p>This document is thus of no value whatsoever and we advise you to not rely on it for any purpose. </p>
              <div class="sub-tips" style="padding-left: 0;text-align: center;">
                <div style="color: red;font-weight: bold;">
                  Considering the issuance and use of this document constitutes a misrepresentation as well as misappropriation and misuse of the SGS trademark, we kindly request you to abstain from using or releasing this document to any third party.
                </div>
              </div>
            </div>
            <div v-if="checkType === 2&&reload">
              <div class="pdf-tips">The Original SGS Document:</div>
              <iframe :src="`<%- locals.static %>/pdfjs/web/viewer.html?file=${pdfUrl}`" frameborder="0" width="100%"
                height="600"></iframe>
            </div>
            <el-button class="submit-btn" type="primary" @click="goCheck">Send a new request</el-button>
          </div>
        </div>
        <div class="docCheck-right">
          <div class="title">
            <span>帮助中心</span>
            <a href="https://portal.sgsonline.com.cn/question/64" class="more-link" target="_blank">查看更多></a>
          </div>
          <ul>
            <li><a href="https://portal.sgsonline.com.cn/question/65" target="_blank">在线查验的流程是什么？</a></li>
            <li><a href="https://portal.sgsonline.com.cn/question/64" target="_blank">报告查验范围有哪些？</a></li>
            <li><a href="https://portal.sgsonline.com.cn/question/64" target="_blank">有多份报告如何批量查验？</a></li>
            <li><a href="https://portal.sgsonline.com.cn/question/64" target="_blank">为什么转人工查验，周期是多久？</a></li>
            <li><a href="https://portal.sgsonline.com.cn/question/64" target="_blank">“此报告已失效”是什么情况？</a></li>
          </ul>
          <div class="other-page">
            <img src="<%- locals.static %>/images/DocCheck/otherPage.png" alt="">
            <a href="/certified-client-directory" target="_blank">体系认证证书查验</a>
          </div>
          <div class="contact-text">
            <img src="<%- locals.static %>/images/DocCheck/tel.png" alt="">
            <p>如遇任何问题，请联系我们</p>
            <p style="margin: 16px 0;">
              <span>客服热线：</span>
              <span style="color:#CA4300;">4000-558-581</span>
            </p>
            <p>
              <span>工作时间：</span>
              <span>
                周一~周六 9:00至17:00 <br>
                <span style="margin-top: 3px;display: inline-block">(节假日除外)</span>
              </span>
            </p>
          </div>
        </div>
      </div>
      <el-dialog class="docCheck-dialog" :visible.sync="confirmDialog" width="500px" :close-on-click-modal="false"
        :close-on-press-escape="false" :show-close="false" v-if="confirmDialog">
        <img src="<%- locals.static %>/images/DocCheck/close.png" alt="" @click="confirmDialog = false">
        <div style="padding: 0 60px;">The file you upload is a ZIP file, it will be sent to SGS for manual verification. Or you may upload a PDF, JPG or PNG file for instant verification.</div>
        <div style="margin: 22px 0 33px;">Do you want to continue?</div>
        <div class="dialog-footer">
          <el-button @click="handleConfirm">Yes</el-button>
          <el-button type="primary" @click="confirmDialog = false">No</el-button>
        </div>
      </el-dialog>
      <el-dialog class="docCheck-dialog" :visible.sync="personCheckDialog" width="500px" :close-on-click-modal="false"
                 :close-on-press-escape="false" :show-close="false" v-if="personCheckDialog">
        <img src="<%- locals.static %>/images/DocCheck/close.png" alt="" @click="personCheckDialog = false">
        <div>You file will be sent to manual verification.</div>
        <div class="dialog-tips">
          <div style="flex-shrink: 0;">Note:</div>
          <div>
            <div>1.We will contact you in 2 working days for documents issued by SGS China.</div>
            <div>2.Documents issued by SGS Hong Kong, SGS Macao, SGS Taiwan and other locations will be replyed in 5-7 working days.</div>
          </div>
        </div>
        <div class="dialog-footer">
          <el-button @click="personCheckDialog = false">No</el-button>
          <el-button type="primary" @click="handlerCheck(recordId)">Yes</el-button>
        </div>
      </el-dialog>
      <% include ./components/login.html %>
        <!-- 注册协议 -->
        <% include ./components/registionClause.html %>
          <!-- 获取验证码弹窗 -->
      <tic-login-verify-code ref="loginVerifyCode" :env="env" :captcha-app-id="captchaAppId" v-model="showModal" :captcha-show="captchaShow" :number="form.userEmail" :data="pageInfo" :login-success="loginSuccess" :verify-params="verifyParams" :is-en="true" />
    </div>
    <script uac_busitype="user" uac_busisubtype="tools" uac_busicode="0007"></script>
    <% if(locals.env !='prod' ){ %>
      <script src="<%- locals.static %>/plugin/v2.test.js"></script>
      <% }else{ %>
        <script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js"
          type="text/javascript"></script>
        <% } %>
          <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
          <script src="<%- locals.static %>/js/md5.js"></script>
          <script>
            const validatorUserPhone = (rule, value, callback) => {
              const reg = /^1[2|3|4|5|6|7|8|9][0-9]\d{8}$/
              if (value && !reg.test(value)) {
                return callback(new Error('Phone is invalid'));
              } else {
                return callback()
              }
            }
            const validatorEmail = (rule, value, callback) => {
              const reg = /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/
              if (!value) {
                return callback(new Error('Email is required'));
              } else if (!reg.test(value)) {
                return callback(new Error('Email is invalid'));
              } else {
                return callback()
              }
            }
            const session = {
              set(key, val) {
                if (window) typeof val == 'string' ? window.sessionStorage.setItem(key, val) : window.sessionStorage.setItem(key, JSON.stringify(val))
              },
              get(key) {
                if (window) {
                  let val = window.sessionStorage.getItem(key)
                  try {
                    return typeof val == 'string' ? JSON.parse(val) : val
                  } catch (e) {
                    return val
                  }
                }
              },
              clear() {
                window.sessionStorage.clear()
              },
              remove(key) {
                window.sessionStorage.removeItem(key)
              }
            }

            var newVue = new Vue({
              name: 'docCheck',
              el: '#docCheck',
              data: {
                showLoginModal: false,
                isRedirect: 1,
                domain: '<%- domain %>',
                tabs: ['检测报告查验', '体系认证报告查验'],
                tabIndex: <%- tabIndex %>,
                form: {
                  userName: '',
                  userPhone: '',
                  userEmail: '',
                  companyName: '',
                  url: '',
                  fileName: '',
                  verifyCode: ''
                },
                rules: {
                  userName: [{ required: true, message: 'Name is required' }],
                  userPhone: [{ validator: validatorUserPhone }],
                  userEmail: [{ required: true, message: 'Email is required' }, { validator: validatorEmail }],
                  url: [{ required: true, message: 'File is not uploaded' }],
                },
                fileList: [],
                checkRegister: false,
                tipsVisible: false,
                checkType: 0,
                appendToBody: true,
                closeBtn: true,
                env: '<%- env %>',
                // env: 'test',
                parameter: {
                  regSource: 'mall',
                  regMode: 'USER_REG',
                  regFrom: location.href
                },
                checkLoading: false,
                pdfUrl: '',
                host: '<%- host %>',
                memberUrl: '<%- memberUrl %>',
                reportNo: '',
                uploadDisabled: false,
                isLogin: <%- isLogin %>,
                userInfo: {
                  userPhone: '<%- userInfo.userPhone %>',
                  userEmail: '<%- userInfo.userEmail %>',
                  userName: '<%- userInfo.userNick %>',
                  companyName: '<%- userInfo.companyName %>'
                },
                pid: 'pid.mall',
                pcode: 'Z0zCnRE3IaY9Kzem',
                reload: true,
                isConfirm: false,
                confirmDialog: false,
                /* 新流程相关交互 */
                dialogVisibleReg: false,
                isChecked: false,
                showModal: false,
                seconds: 59,
                timer: null,
                loading: false,
                pageInfo: {
                  title: 'Verify your email',
                  sendModel: 'Verification code sent to',
                  prepend: 'Verification code'
                },
                captchaAppId: '<%- captchaAppId %>',
                isCheckedTips: false,
                phoneLabel: '+86',
                loadingText: [
                  'Uploading file',
                  'Uploading file.',
                  'Uploading file..',
                  'Uploading file...',
                  'Analyzing file data',
                  'Analyzing file data.',
                  'Analyzing file data..',
                  'Analyzing file data...',
                  'Searching online database',
                  'Searching online database.',
                  'Searching online database..',
                  'Searching online database...',
                  'Generating verification result',
                  'Generating verification result.',
                  'Generating verification result..',
                  'Generating verification result...'
                ],
                loadingTextIndex: 0,
                captchaShow: false,
                verifyParams: {
                  projectName: 'TIC_TEMPLATE',
                  verifyType: 3
                },
                recordId: '',
                personCheckDialog: false
              },
              computed: {
                isZip() {
                  return /\.(zip)$/.test(this.form.fileName.toLowerCase())
                }
              },
              watch: {
                checkLoading(val) {
                  let timer
                  if (val) {
                    timer = setInterval(() => {
                      if (this.loadingTextIndex < this.loadingText.length - 1) {
                        this.loadingTextIndex += 1
                      } else {
                        this.loadingTextIndex -= 3
                      }
                    }, 1000)
                  } else {
                    if (timer) clearInterval(timer)
                    this.loadingTextIndex = 0
                  }
                }
              },
              created() {
                if (location.pathname === '/certified-client-directory') {
                  this.tabIndex = 1
                  return
                }
                console.log('created-------------------', this.userInfo, this.form)
                if (this.userInfo.userPhone || this.userInfo.userEmail) {
                  this.form.userPhone = this.userInfo.userPhone
                  this.form.userEmail = this.userInfo.userEmail
                  this.form.userName = this.userInfo.userName
                  this.form.companyName = this.userInfo.companyName
                }
                this.form = JSON.parse(JSON.stringify(this.form))
                const isZip = session.get('isZip')
                const form = session.get('checkForm')
                const data = session.get('checkResult')
                session.remove('checkForm')
                session.remove('checkResult')
                session.remove('isZip')
                if (form) {
                  // this.form = Object.assign(form, this.form)
                  this.form = Object.assign(this.form, form)
                  this.form.fileName = form.fileName
                }
                if (isZip) {
                  this.checkType = 4
                  return
                }
                if (!data) {
                  this.checkType = 0
                  return
                }
                if (data.success && data.qryType === 1) {
                  this.checkLoading = true
                  if (data.success) {
                    this.checkType = data.qryType === 0 ? 1 : 2
                  } else {
                    this.checkType = data.failType === 1 ? 5 : data.failType === 3 ? 6 : data.failType === 4 ? 7 : 3
                  }
                  this.reportNo = data.reportNo
                  this.getBlob(data)
                } else {
                  this.setCheckResult(data)
                }
              },
              methods: {
                handleCommand(val) {
                  this.phoneLabel = val
                },
                phoneDialog() {
                  phoneDialog()
                },
                sing: function (param, timestamp) {
                  var pmd5 = md5((this.pid + this.pcode).toUpperCase());
                  var str = JSON.stringify(param) + pmd5.toUpperCase();
                  return md5(str + timestamp);
                },
                setCheckResult(data, url) {
                  this.checkLoading = false
                  if (data.success) {
                    this.checkType = data.qryType === 0 ? 1 : 2
                  } else {
                    this.checkType = data.failType === 1 ? 5 : data.failType === 3 ? 6 : data.failType === 4 ? 7 : 3
                  }
                  this.reportNo = data.reportNo
                  this.recordId = data.recordId
                  this.pdfUrl = url
                  document.body.scrollTop = document.documentElement.scrollTop = 0;
                  if (url) {
                    this.reload = false
                    this.$nextTick(() => this.reload = true)
                  }
                },
                handleConfirm() {
                  this.isConfirm = true
                  this.confirmDialog = false
                  this.verifyForm()
                },
                verifyForm(needLogin) {
                  this.$refs.form.validate((valid) => {
                    if (valid) {
                      if (this.isZip && !this.isConfirm) {
                        this.confirmDialog = true
                        return
                      }
                      if (!this.isChecked) {
                        // this.$message.error('请勾选同意注册条款')
                        this.isCheckedTips = true
                        return
                      } else {
                        this.isCheckedTips = false
                      }
                      if (!this.isLogin && needLogin) {
                        this.showModal = true
                      } else {
                        this.handlerCheck()
                      }
                    }
                  })
                },
                handlerCheck(recordId) {
                  this.checkLoading = true
                  var that = this
                  var timestamp = new Date().valueOf();
                  let accessToken = Cookies.get('SSO_TOKEN') || '';
                  const url = recordId ? '/ticMember/business/api.v2.user/tool/updateReportRecord' : this.isZip ? '/ticMember/business/api.v2.user/tool/reportAuthSaveRecordByZip' : '/ticMember/business/api.v2.user/tool/reportAuthSaveRecord'
                  const params = JSON.parse(JSON.stringify(this.form))
                  params.recordId = recordId
                  $.ajax({
                    type: 'POST',
                    url: this.host + url,
                    data: JSON.stringify(params),
                    contentType: 'application/json',
                    headers: {
                      pid: this.pid,
                      sign: this.sing(params, timestamp),
                      timestamp: timestamp,
                      frontUrl: window.location.href,
                      accessToken,
                      appId: '99999999',
                      lang: 'en_US'
                    },
                    success: function (res) {
                      if (res.resultCode === '0') {
                        that.personCheckDialog = false
                        if (location.pathname + location.hash === '/DocCheck/en#topbar') {
                          if (that.isZip || recordId) {
                            that.checkLoading = false
                            that.checkType = 4
                            return
                          }
                          if (res.data.success && res.data.qryType === 1) {
                            that.getBlob(res.data)
                          } else {
                            that.setCheckResult(res.data)
                          }
                          return
                        }
                        const form = { ...that.form }
                        form.url = ''
                        session.set('checkResult', res.data)
                        session.set('checkForm', form)
                        if (that.isZip) session.set('isZip', true)
                        location.href = `/DocCheck/en#topbar`
                        setTimeout(() => {
                          location.reload()
                        })
                      } else {
                        that.checkLoading = false
                        if (res && res.resultCode === '9978') {
                          // that.showLoginModal = true
                          that.showModal = true
                          that.pageInfo.number = that.form.userEmail
                        } else {
                          that.$message.error(res.resultMsg)
                        }
                      }
                    },
                    fail: function (error) {
                      console.log(error);
                      that.$message.error('网络异常')
                    },
                    complete: function (complete) {
                    }
                  })
                },
                getBlob(datas) {
                  const params = {
                    url: datas.qrCode,
                    name: '报告.pdf',
                  }
                  var timestamp = new Date().valueOf();
                  axios({
                    data: params,
                    url: this.host + '/ticCenter/business/api.v0.platform/fileUpload/getFileStream',
                    method: "post",
                    responseType: "blob",
                    headers: {
                      pid: this.pid,
                      sign: this.sing(params, timestamp),
                      timestamp: timestamp,
                      frontUrl: window.location.href,
                      appId: '99999999'
                    },
                  }).then(res => {
                    console.log(res);
                    let data = res.data;
                    console.log(data);
                    let fileReader = new FileReader();
                    fileReader.onload = (e) => {
                      try {
                        this.checkLoading = false
                        let jsonData = JSON.parse(e.target.result);  // 说明是普通对象数据，后台转换失败
                        this.$message.error(jsonData.resultMsg || '网络异常')
                      } catch (err) {   // 解析成对象失败，说明是正常的文件流
                        let url
                        if (res && res.data && res.data.size) {
                          var blob = new Blob([res.data], {
                            type: "application/actet-stream;charset=utf-8"
                          });
                          // @ts-ignore
                          if (window.createObjectURL != undefined) { // basic
                            // @ts-ignore
                            url = window.createObjectURL(blob)
                          } else if (window.webkitURL != undefined) { // webkit or chrome
                            url = window.webkitURL.createObjectURL(blob);
                          } else if (window.URL != undefined) { // mozilla(firefox)
                            url = window.URL.createObjectURL(blob);
                          }
                        } else {
                          this.$message.error('网络异常')
                        }
                        this.setCheckResult(datas, url)
                      }
                    }
                    fileReader.readAsText(data)
                  }).catch(e => {
                    this.checkLoading = false
                  });
                },
                // 继续验证
                goCheck() {
                  // this.fileList = []
                  // this.form.url = ''
                  // this.checkType = 0
                  // document.body.scrollTop = document.documentElement.scrollTop = 0;
                  // this.isConfirm = false
                  window.location.reload()
                },
                // 登录成功后的回调
                // successCallback() {
                //   this.showLoginModal = false
                //   this.isLogin = true
                //   this.handlerCheck(false)
                //   reloadHeader()
                // },
                // 关闭登录弹窗
                handleCloseLogin() {
                  this.showLoginModal = false
                },
                beforeUpload(file) {
                  const isLt10M = file.size / 1024 / 1024 < 20;
                  let isImg = true
                  if (!/\.(jpg|jpeg|png|pdf|zip)$/.test(file.name.toLowerCase())) {
                    this.$message.error('请上传PDF,JPG，PNG,ZIP文件');
                    isImg = false
                  }

                  if (!isLt10M) {
                    this.$message.error('上传文件大小不能超过 20MB!');
                  }
                  if (isLt10M && isImg) {
                    this.uploadDisabled = true
                  }
                  return isLt10M && isImg;
                },
                onSuccess(res, file, fileList) {
                  this.uploadDisabled = false
                  if (res.resultCode === "0") {
                    this.fileList = [{
                      name: file.name,
                      url: res.data.fileName
                    }]
                    this.form.url = res.data.fileName
                    this.form.fileName = file.name
                  } else {
                    this.$message.error(res.resultMsg)
                  }
                },
                onError(res, file, fileList) {
                  this.uploadDisabled = false
                  this.$message.error('网络异常')
                },
                loginSuccess() {
                  this.isLogin = true
                  reloadHeader()
                  if (this.personCheckDialog) {
                    this.handlerCheck(this.recordId)
                    return
                  }
                  this.handlerCheck()
                },
                handleChangeChecked: function (val) {
                  if (val) this.isCheckedTips = false
                }
              },
            })
          </script>

          <% include footer.html %>
