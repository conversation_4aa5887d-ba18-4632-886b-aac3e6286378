<% include ../header.html %>
<% include ./../components/header.html %>
<link rel="stylesheet" href="<%- locals.static %>/css/promotion/qichechanye.css">
<div class="qichechanye">
  <div class="banner">
    <div class="wrap">
      <button class='download-btn2' data-url="http://10.205.236.189:7001/static/upload/2019/07/16/sgs%20%EF%BC%88%E4%B8%AD%E6%96%87%EF%BC%89%E6%96%B0%E7%89%88pfmea%20sod%20classification.pdf">
        <img style="vertical-align: middle;" src="<%- locals.static %>/images/promotion/qichechanye/download.png" alt="" data-url="http://10.205.236.189:7001/static/upload/2019/07/16/sgs%20%EF%BC%88%E4%B8%AD%E6%96%87%EF%BC%89%E6%96%B0%E7%89%88pfmea%20sod%20classification.pdf">
        下载资源
      </button>
    </div>
  </div>
  <div class="bg-white">
    <div class="wrap">
      <div class="title">
        <h2>热门服务</h2>
        <p>SGS致力于向汽车行业提供贯穿全价值链的质量保障服务，从零部件品质到整车安全，覆盖汽车全产业链各个细分领域；SGS是少数几家被国内汽车主机厂都<br />
          认可的第三方认证机构之一，拥有100多名IATF 16949审核员；同时，SGS拥有的客户数量也领先同侪。</p>
        <span></span>
      </div>
      <div class="hotService clearfix" id="hotService">
        <div class="item leftBox">
          <div class="left">
            <i></i>
            <h2>国际汽车行业质量管理体系</h2>
            <p>
              借助SGS的IATF 16949 审核、认证和培训服务，成为具有世界视野的汽车厂商。
              <a target="_blank" href="/sku/IATF16949/227">查看详情>></a>
            </p>
            <button class="handleIMtool">立即申请认证</button>
          </div>
          <div class="right"></div>
        </div>
        <div class="item rightBox">
          <div class="left">
            <i></i>
            <h2>过程审核</h2>
            <p>
              优化您的组织和供应链过程，控制过程风险。<br /><a target="_blank" href="/sku/product/234">查看详情>></a>
            </p>
            <button class="handleIMtool">立即申请审核</button>
          </div>
          <div class="right"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="bg-orange">
    <div class="wrap value">
      <h3>体系认证的价值</h3>
      <div class="card">
        <ul class="clearfix">
          <li>
            <div class="value-tit">
              <b>01</b>
              <span>国际客户认可</span>
            </div>
            <p>得到国际品牌客户认可，避免国外客户对供应商的重复审核，节省成本。</p>
            <i></i>
          </li>
          <li>
            <div class="value-tit">
              <b>02</b>
              <span>满足客户要求</span>
            </div>
            <p>满足客户强制性标准要求，获取客户订单。</p>
            <i></i>
          </li>
          <li>
            <div class="value-tit">
              <b>03</b>
              <span>降本增效</span>
            </div>
            <p>持续关注企业运营业绩，改进过程绩效指标，以实现降本增效。</p>
            <i></i>
          </li>
          <li>
            <div class="value-tit">
              <b>04</b>
              <span>提升产品质量</span>
            </div>
            <p>运用系统开发和改进方法，保证产品质量和交付业绩。</p>
            <i></i>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="bg-white">
    <div class="wrap">
      <div class="title">
        <h2>相关培训课程</h2>
        <span></span>
      </div>
      <div class="wrap trainingCourse clearfix" id='trainingCourse'>
        <div class="img">
          <div class="pic pic1"></div>
          <div class="pic pic2"></div>
          <div class="pic pic3"></div>
          <div class="pic pic4"></div>
        </div>
        <div class="lists">
          <ul id='lists'></ul>
          <!--<div class="phone">
            <div class="phone-label">专属咨询热线：</div>
            <div class="phone-content">010-53153974</div>
          </div>-->
        </div>
      </div>
    </div>
  </div>
  <div class="bg-gray ">
    <div class="wrap">
      <div class="title">
        <h2>汽车行业全面解决方案</h2>
        <p>质量、环境、能源、安全是汽车行业的主要挑战，SGS可以为汽车行业提供一站式全面解决方案</p>
        <span></span>
      </div>
      <div class="case">
        <div class="round"></div>
        <div class="line1"></div>
        <div class="line2"></div>
        <div class="line3"></div>
        <div class="line4"></div>
        <div class="text1">
          <ul>
            <li>
              <span></span>
              <a href='/sku/IATF16949/227' target="_blank">IATF 16949认证</a>
            </li>
            <li>
              <span></span>
              <a>供应链过程审核及产品审核</a>
            </li>
            <li>
              <span></span>
              <a>供应商质量提升</a>
            </li>
          </ul>
        </div>
        <div class="text2">
          <ul>
            <li>
              <span></span>
              <a href='/sku/product/225' target="_blank">ISO 14001 及ISO 45001认证</a>
            </li>
            <li>
              <span></span>
              <a href='/sku/product/166' target="_blank">环境及安全法律法规收集</a>
            </li>
            <li>
              <span></span>
              <a>符合性评估</a>
            </li>
            <li>
              <span></span>
              <a>环境及安全绩效提升</a>
            </li>
          </ul>
        </div>
        <div class="text3">
          <ul>
            <li>
              <span></span>
              <a href='/sku/product/185' target="_blank">功能安全解决方案：ISO 26262</a>
            </li>
            <li>
              <span></span>
              <a>信息安全解决方案</a>
            </li>
            <li>
              <span></span>
              <a>物流安全解决方案</a>
            </li>
          </ul>
        </div>
        <div class="text4">
          <ul>
            <li>
              <span></span>
              <a>节能诊断及专项节能咨询</a>
            </li>
            <li>
              <span></span>
              <a href='/sku/product/245' target="_blank">能源管理ISO 50001, GB/T 23331</a>
            </li>
            <li>
              <span></span>
              <a href='/sku/product/228' target="_blank">温室气体排放验证ISO 14064</a>
            </li>
            <li>
              <span></span>
              <a href='/sku/product/544' target="_blank">产品碳足迹PAS 2050:2008</a>
            </li>
            <li>
              <span></span>
              <a href='/sku/product/542' target="_blank">水平衡及AWS水联盟认证</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="bg-white">
    <div class="wrap">
      <div class="title">
        <h2>汽车行业供应链审核服务</h2>
        <p>
          随着动力能源技术革新、电子产品涌入、智能化、加工工艺进步，汽车产品功能、客户体验快速升级，掀起了汽车行业高速发展的新时代的浪潮。<br />
          而这样的变化使得国内汽车产业面临全新的产业变革时代，在当前复杂的市场环境下，我们为汽车行业供应链企业提供全面的审核服务。
        </p>
        <span></span>
      </div>
      <div class="auditServices">
        <div class="a box1">
          <span>年度供应商审核</span>
          <p>
            风险识别、评估
            <br />
            预防与绩效考核
          </p>
        </div>
        <div class="a box2">
          <span>项目启动审核</span>
          <p>
            爬坡与量产前
            <br />
            准备审核
          </p>
        </div>
        <div class="a box3">
          <span>质量体系审核</span>
          <p>
            <a href="/promotion/iso9001" target="_blank">ISO 9001</a>
            <br />
            <a href="/sku/product/227" target="_blank">IATF 16949</a>
            <br />
            VDA 6.1
          </p>
        </div>
        <div class="a box4">
          <span>产品审核</span>
          <p>
            <a href="/sku/product/635" target="_blank">VDA 6.5</a>
            <br />
            产品特性审核
          </p>
        </div>
        <div class="a box5">
          <span>新供应商评估</span>
          <p>
            <a href="/sku/product/234" target="_blank">VDA 6.3</a>
            <br />
            SGS标准或根据
            <br />
            客户需求定制
          </p>
        </div>
        <div class="a box6">
          <span>供应链安全审核</span>
          <p>
            <a href="/sku/iso27001/447" target="_blank">信息安全审核</a>
            <br />
            物流安全审核
          </p>
        </div>
        <div class="a box7">
          <span>过程审核</span>
          <p>
            <a href="/sku/product/234" target="_blank">VDA 6.3</a>
            <br />
            CQI
            <br />
            BIQS
            <br />
            客户特殊要求
          </p>
        </div>
        <div class="a box8">
          <span>环境及社会责任</span>
          <p>
            <a href="/sku/product/225" target="_blank">ISO 14001</a>
            <br />
            <a href="/sku/product/226" target="_blank">ISO 45001</a>
            <br />
            <a href="/sku/product/529" target="_blank">环境法规评估</a>
            <br />
            <a href="/sku/product/553" target="_blank">社会责任评估</a>
          </p>
        </div>
        <div class="e box9"></div>
        <div class="c box10"></div>
        <div class="b box11"></div>
        <div class="d box12"></div>
        <div class="b box13"></div>
        <div class="d box14"></div>
        <!--<div class="phone">
          <div class="phone-label">专属咨询热线：</div>
          <div class="phone-content">010-53153974</div>
        </div>-->
      </div>
    </div>
  </div>
  <div class="bg-bg">
    <div class="wrap">
      <div class="title">
        <h2>汽车OEM经销商网络管理服务</h2>
        <p>
          在业务快速发展的今天，企业的整车销售与售后服务管理应该随着企业的快速发展而同步发展，所以建立规范的销售与售后服务的工作流程尤为重要。<br />我们为您提供全面的OEM人力资源外包、经销商培训，让您在激烈的市场竞争中脱颖而出！
        </p>
        <span></span>
      </div>
      <div class="manageService" id='manageService'>
        <div class="card card1">
          <div>
            <i></i>
          </div>
          <h3>新经销商建店</h3>
          <p>
            经销商标准审计
            <br />
            经销商标准本地化调研与咨询
            <br />
            5S培训及审核
          </p>
          <button class="handleIMtool">咨询客服</button>
        </div>
        <div class="card card2">
          <div>
            <i></i>
          </div>
          <h3>整车/二手车销售</h3>
          <p>
            电动汽车业务、库存管理
            <br />
            激励政策审计、票据凭证核查
            <br />
            二手车现场核查
          </p>
          <button class="handleIMtool">咨询客服</button>
        </div>
        <div class="card card3">
          <div>
            <i></i>
          </div>
          <h3>售后服务</h3>
          <p>
            原厂零配件外销辅导
            <br />
            保修业务管理
            <br />
            经销商售后业务优化辅导
          </p>
          <button class="handleIMtool">咨询客服</button>
        </div>
      </div>
    </div>
  </div>
  <div class="bg-white">
    <div class="wrap">
      <div class="title">
        <h2>汽车行业检验、测试、认证服务</h2>
        <p>
          SGS致力于向汽车行业提供贯穿全价值链的质量保障服务，从零部件品质到整车安全，我们还可以提供汽车轻量化部件测试、<br />整车及零部件性能和功能测试、汽车电子产品测试、汽车电子产品测试、整车及零部件产品认证、整车及零部件检验一站式服务。
        </p>
        <span></span>
      </div>
      <div class="carService clearfix" id="carService">
        <div class="list">
          <ul></ul>
          <button class="handleIMtool">咨询客服</button>
        </div>
        <div class="img">
          <div class="box1"></div>
          <div class="box2"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="bg-gray">
    <div class="wrap">
      <div class="title">
        <h2>关键优势</h2>
        <span></span>
      </div>
      <div class="wrap advantage" id="advantage">
        <ul>
          <li>
            <h3>IATF认可</h3>
            <span>国际资质</span>
            <p>SGS是目前IATF 16949：2016的认可委——IATF所认可的41家认证机构中，较早获得认可的认证机构之一。</p>
          </li>
          <li>
            <h3><em data-max='50'>0</em>+</h3>
            <span>师资力量</span>
            <p>在该服务领域，全国拥有专业讲师50多名，讲师具有多年的国内外汽车主机厂、供应链企业的工作经验，同时具有丰富的审核、培训经验。</p>
          </li>
          <li>
            <h3><em data-max='500'>0</em>+</h3>
            <span>审核经验</span>
            <p>100多名IATF 16949三方审核员及20多名VDA 6.3外部审核员，十年及以上汽车行业工作经验；行业内500多家供应链审核项目经验。</p>
          </li>
          <li>
            <h3><em data-max='4000'>0</em>+</h3>
            <span>客户认可</span>
            <p>在中国大陆，SGS拥有数量可观的ISO/TS 16949及IATF 16949：2016的认证客户，达到4000家以上。</p>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="bg-white">
    <div class="wrap">
      <div class="title">
        <h2>增值服务</h2>
        <span></span>
      </div>
      <div class="wrap valueService">
        <ul class="clearfix">
          <li>
            <div class="info">
              <i class="icon1"></i>
              <p>定期举办研讨会、沙龙<br />高峰论坛等活动</p>
            </div>
            <div class="shadow"></div>
          </li>
          <li>
            <div class="info">
              <i class="icon2"></i>
              <p>邀您参与<br />客户答谢会</p>
            </div>
            <div class="shadow"></div>
          </li>
          <li>
            <div class="info">
              <i class="icon3"></i>
              <p>提供订阅SGS专刊<br />《管理新航标》</p>
            </div>
            <div class="shadow"></div>
          </li>
          <li>
            <div class="info">
              <i class="icon4"></i>
              <p>众多行业相关线上微课<br />在线直播讲课</p>
            </div>
            <div class="shadow"></div>
          </li>
          <li>
            <div class="info">
              <i class="icon5"></i>
              <p>各类交流分享群<br />经理人俱乐部等</p>
            </div>
            <div class="shadow"></div>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="bg-white">
    <div class="wrap">
      <div class="title">
        <h2>更多审核、认证、培训服务</h2>
        <span></span>
      </div>
      <div class="wrap moreService" id='moreService'>
        <ul class="clearfix"></ul>
      </div>
    </div>
  </div>
  <div class="bg-white">
    <div class="wrap">
      <div class="title">
        <h2>相关资料下载</h2>
        <span></span>
      </div>
      <div class="wrap dwon">
        <ul class="clearfix">
          <!--<li class="download-btn2" data-url="/static/upload/2019/11/29/汽车行业资源包合集.zip">
            <a href="javascrpit:void(0);" data-url="/static/upload/2019/11/29/汽车行业资源包合集.zip">汽车行业资源包合集</a>
          </li>-->
          <!--<li>
            <a href="/static/upload/2020/04/23/pf17-%E3%80%90%E6%B1%BD%E8%BD%A6%E3%80%91-sgs-iatf%2016949-a4-cn-17-v1-lr-1906.pdf">IATF 16949 flyer</a>
          </li>-->
          <li>
            <a href="/static/upload/2019/04/29/sgs-vda%206.3-a4-cn-17-v1-lr-1904.pdf">VDA 6.3 flyer</a>
          </li>
          <li>
            <a
              href="/static/upload/2019/04/29/sgs-%E6%B1%BD%E8%BD%A6%E5%88%B6%E9%80%A0%E5%95%86%E5%AE%9A%E5%88%B6%E5%8C%96%E6%9C%8D%E5%8A%A1-a4-cn-17-v2-lr-1904.pdf">SGS汽车制造商定制化服务
              flyer</a>
          </li>
          <!--<li>
            <a
              href="/static/upload/2018/12/10/pfmea%E7%B3%BB%E7%BB%9F%E4%BA%A7%E5%93%81%E8%AF%95%E7%94%A8%E5%8D%8F%E8%AE%AE%E4%B9%A6.doc">E-PFMEA产品试用协议书</a>
          </li>-->
        </ul>
      </div>
    </div>
  </div>
  <div class="bg-gray">
    <div class="wrap">
      <div class="title">
        <h2>留言咨询</h2>
        <span></span>
      </div>
      <!--<div class="phone phone-message">
        <span class="phone-label">专属咨询热线：</span>
        <span class="phone-content">010-53153974</span>
      </div>-->
      <div class="pageMessage wrap">
        <!--<p>留言或咨询客服领取E-PFMEA“互联网+质量”云平台免费使用权（30天）</p>-->
        <div class="from clearfix" id='iso9001' v-cloak>
          <input type="text" name='customer1' id='customer1' placeholder="姓名" maxlength='50' />
          <input type="text" name='phone1' id='phone1' maxlength="11" placeholder="手机号码" />
          <input type="text" name='email1' id='email1' placeholder="电子邮箱" maxlength='80' />
          <select id='service1'>
            <option value='-1'>请选择服务类型</option>
            <option value='24'>审核与评估</option>
            <option value='32'>培训服务</option>
            <option value='28'>体系认证</option>
          </select>
          <input placeholder="请简述您的需求" name='content1' type="text" class="input-big" id='content1' />
          <button id='submit1' @click='handleSubmit'>提交</button>
          <% include ./../pages/quote/modal.html %>
        </div>
        <span>工程师2小时内与您联系（工作日9：00-17：00）</span>
      </div>
    </div>
  </div>
</div>
<div class="pop" id='submitPop'>
  <div class="layout"></div>
  <div class="popBox">
    <h2 class="tit">
      提示
    </h2>
    <div class="cont">
      您的业务咨询已提交，我们会尽快与您取得联系！
    </div>
    <div class="btn">
      <span>确定</span>
    </div>
  </div>
</div>

<!--下载课表需要填写的信息窗口-->
<div class="down-load-pop">
  <div class="layout"></div>
  <div class='popBox'>
    <div class="title">
      <b>资源下载</b>
      <p>填写信息免费下载</p>
    </div>
    <div class="content">
      <i class="close">X</i>
      <input type="text" name='customer' id='customer2' placeholder="您的姓名" />
      <input type="text" name='phone' id='phone2' maxlength="11" placeholder="手机号码" />
      <input type="text" name='email' id='email2' placeholder="电子邮箱" />
      <div style="margin-top: 10px;">
        <label><input type="checkbox" name="" id="check">我接受</label><a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" style="color:#FE660B;text-decoration:none;" target="_blank">《隐私政策》</a>
      </div>
      <button id='submit2'>提交并下载</button>
    </div>
  </div>
</div>

<!--第二版下载课表需要填写的信息窗口-->
<div class="down-load-pop2">
  <div class="layout"></div>
  <div class='popBox'>
    <div class="title">
      <b>资源下载</b>
      <p>填写信息免费下载</p>
    </div>
    <div class="content">
      <i class="close">X</i>
      <div style="display: flex;flex-wrap: wrap;justify-content: space-between;">
        <input type="text" name='customer' id='customer3' placeholder="您的姓名" />
        <input type="text" name='phone' id='phone3' maxlength="11" placeholder="手机号码" />
        <input type="text" name='email' id='email3' placeholder="电子邮箱" />
        <input type="text" name='email' id='company3' placeholder="企业名称" />
        <textarea id="textarea3" placeholder="请简述您下载本资源最希望解决的问题" style="overflow-y: auto"></textarea>
      </div>
      <div style="text-align: left">
        <div style="font-size: 14px;margin-top:10px;">贵公司是否已与SGS有合作</div>
        <div style="font-size: 14px;margin-top:10px;display: flex">
          <input type="radio" name="isCoop" value="1">
          <span>是，IATF 16949认证</span>
        </div>
        <div style="font-size: 14px;margin-top:10px;display: flex">
          <input type="radio" name="isCoop" value="2">
          <span>否</span>
          <input type="text" name='coopName' id='coopName' placeholder="请输入您企业的IATF 16949认证机构" />
        </div>
      </div>
      <div style="margin-top: 20px;">
        <label><input type="checkbox" name="" id="check3">我接受</label><a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" style="color:#FE660B;text-decoration:none;" target="_blank">《隐私政策》</a>
      </div>
      <button id='submit3'>提交并下载</button>
    </div>
  </div>
</div>

<script>
  var newVue = new Vue({
    name: 'iso9001',
    el: '#iso9001',
    data: {
      form: {
        type: 'IATF 16949',
        trade: 68,
        tradeName: '汽车与零部件',
        service: '',
        serviceName: '其他',
        content: '',
        customer: '<%- userInfo.userName %>',
        email: '<%- userInfo.userEmail %>',
        phone: '<%- userInfo.userPhone %>',
        provice: '浙江省',
        company: 'sgs',
        os_type: 'pc',
        frontUrl: window.location.href,
        _csrf: '<%- csrf %>',
        imageCode: '',
        verifyCode: '',
      },
      showModal: false,
      seconds: 59,
      timer: null,
      loading: false,
      host: '<%- host %>',
      isLogin: <%- isLogin %>,
      pid: 'pid.mall',
      pcode: 'Z0zCnRE3IaY9Kzem',
      pageInfo: {
        title: '验证手机号',
        sendModel: '已发送到手机号：+86',
        prepend: '手机验证码'
      },
    },
    methods: {
      handleClose: function () {
        this.showModal = false
        $("#submit1").prop('disabled', false);
        this.form.verifyCode = ''
      },
      sing: function (param, timestamp) {
        var pmd5 = md5((this.pid + this.pcode).toUpperCase());
        var str = JSON.stringify(param) + pmd5.toUpperCase();
        return md5(str + timestamp);
      },
      handleSubmit: function () {
        var that = this
        var data = {
          type: 'IATF 16949',
          trade: 68,
          tradeName: '汽车与零部件',
          service: $("#service1").val(),
          serviceName: $("#service1").find("option:selected").text(),
          content: $('#content1').val(),
          customer: $('#customer1').val().trim(),
          phone: $('#phone1').val().trim(),
          email: $('#email1').val().trim(), // 不能为空，不能为非法邮箱
          provice: '浙江省',
          company: 'sgs',
          os_type: 'pc',
          frontUrl: window.location.href,
          _csrf: '<%- csrf %>'
        }
        that.form.service = data.service
        that.form.serviceName = data.serviceName
        that.form.content = data.content
        that.form.customer = data.customer
        that.form.phone = data.phone
        that.form.email = data.email
        if (!$('#customer1').val().trim()) {
          alert('请输入姓名！')
        } else if (!$('#phone1').val().trim()) {
          alert('请输入手机号码！')
        } else if (!/^1\d{10}$/.test($('#phone1').val().trim())) {
          alert('请输入正确的手机号码！')
        } else if (!$('#email1').val().trim()) {
          alert('请输入电子邮箱')
        } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email1').val().trim())) {
          alert('请输入正确电子邮箱')
        } else if (data.service == '-1') {
          alert('请选择服务类型！')
        } else if (!$('#content1').val()) {
          alert('请输入需求信息！')
        } else {
          if (!this.isLogin) {
            $("#submit1").prop('disabled', true);
            this.showModal = true
            this.form.phone = $('#phone1').val()
            this.form.service = $("#service1").val()
            this.form.serviceName = $("#service1").find("option:selected").text()
            this.sendCode()
          } else {
            this.handleModalSubmit()
          }
        }
      },
      sendCode: function () {
        if (!this.timer) {
          var param = {
            projectName: 'TIC_TEMPLATE',
            verifyMode: 1,
            verifyNumber: this.form.phone,
            verifyType: 2
          }
          var timestamp = new Date().valueOf();
          var that = this
          $.ajax({
            type: 'POST',
            url: this.host + '/ticCenter/business/api.v1.center/verify/getCode',
            data: JSON.stringify(param),
            contentType: 'application/json',
            headers: {
              pid: this.pid,
              sign: this.sing(param, timestamp),
              timestamp: timestamp,
               frontUrl: window.location.href
            },
            success: function (res) {
              if (res.resultCode === '0') {
                that.timer = setInterval(function () {
                  if (that.seconds > 1) {
                    that.seconds -= 1
                  } else {
                    clearInterval(that.timer)
                    that.timer = null
                    that.seconds = 59
                  }
                }, 1000)
              } else {
                that.$message.error(res.resultMsg)
              }
            },
            fail: function (data) {
              that.$message.error(res.resultMsg)
            },
            complete: function (complete) {
            }
          })
        }
      },
      handleModalSubmit: function () {
        var that = this
        var param = that.form
        var timestamp = new Date().valueOf();
        var url = '/submitTicket';
        param.verifyMode = param.phone ? 'phone' : 'email'
        $.ajax({
          type: 'POST',
          url: url,
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: this.pid,
            sign: that.sing(param, timestamp),
            timestamp: timestamp,
             frontUrl: window.location.href
          },
          success: function (res) {
            that.loading = false
            if (res.resultCode === '0') {
              Cookies.set("SSO_TOKEN", res.data.token, { domain: ".sgsmall.com.cn", expires: 7 });
              that.showModal = false
              $('#submitPop').show();
              $("#submit1").prop('disabled', false);
            } else if(res.resultCode === '9978') {
              Cookies.remove('SSO_TOKEN', { domain: '.sgsmall.com.cn' })
              that.isLogin = false
              that.showModal = true
              that.sendCode()
            } else {
              that.$message.error(res.resultMsg)
            }
          },
          fail: function (data) {
            that.loading = false
            that.$message.error(res.resultMsg)
          },
          complete: function (complete) {
          }
        })
      },
    },
    mounted: function () {
      if (this.isLogin) {
        if ($("#phone1").val()) $("#phone").attr('disabled', true)
        $("#phone1").val(this.form.phone)
        $("#email1").val(this.form.email)
        $("#customer1").val(this.form.customer)
      }
    }
  });

  $(function () {
    var pw = $('html,body').width(),
      ph = $('html,body').height();
    $('.down-load-pop .layout').css({
      width: pw,
      height: ph
    })
    $('#hotService .item').hover(function () {
      $(this).addClass('activity').siblings().removeClass('activity');
    }, function () {
      $(this).removeClass("activity");
    });
    // $("#hotService button").on("click", function () {
    //   $('#kf5-support-btn').trigger('click');
    // })
    // $("#manageService button").on("click", function () {
    //   $('#kf5-support-btn').trigger('click');
    // })
    // $("#carService button").on("click", function () {
    //   $('#kf5-support-btn').trigger('click');
    // })
    //
    var trainingCourse = $('#trainingCourse');
    var trainingCourseList = $('#trainingCourse #lists');
    var trainingCourseDatas = [
      {
        text: 'IATF 16949:2016汽车行业质量管理体系内审员培训课程',
        href: '/sku/product/292',
        isHot: true
      },
      {
        text: 'IATF 16949:2016五大核心工具培训课程',
        href: '/sku/product/290',
        isHot: true
      },
      {
        text: 'VDA 6.3 & VDA 6.5过程审核&产品审核 （2016版）培训课程',
        href: '/sku/product/586',
        isHot: false
      },
      {
        text: 'ISO 26262汽车功能安全—AFSP培训课程',
        href: '/sku/product/763',
        isHot: true
      },
      {
        text: '汽车行业产品安全代表（PSB）培训课程',
        href: '/sku/product/772',
        isHot: false
      },
      {
        text: 'FMEA 潜在失效模式分析新版培训课程',
        href: '/sku/fmea/990',
        isHot: false
      },
      {
        text: '国际材料数据系统（IMDS 11.0）培训课程',
        href: '/sku/product/588',
        isHot: false
      },
      {
        text: 'CQI-8 分层审核 (Layered Process Audit)培训课程',
        href: '/sku/product/581',
        isHot: false
      },
      {
        text: 'CQI-9 热处理系统评估培训课程',
        href: '/sku/product/868',
        isHot: false
      }
    ];
    var trainingCourseDom = '';
    for (var i = 0; i < trainingCourseDatas.length; i++) {
      trainingCourseDom += '<li>' +
        '<span></span>' +
        '<a href="' + trainingCourseDatas[i].href + '" target="_blank">' + trainingCourseDatas[i].text + '</a>';
      if (trainingCourseDatas[i].isHot) {
        trainingCourseDom += '<i></i>';
      }
      trainingCourseDom += '</li>';
    }
    trainingCourseList.empty();
    trainingCourseList.append(trainingCourseDom)
    var auditServices = $('.auditServices .a')
    auditServices.hover(function () {
      $(this).addClass('active').siblings().removeClass('active')
    }, function () {
      $(this).removeClass('active')
    })
    //
    var carServiceBox = $('#carService ul');
    var carServiceDom = '';
    var carServiceDatas = [
      // todo
      // {
      //   text: '社会责任评估',
      //   href: '/industry/automotive/'
      // },
      {
        text: '汽车轻量化部件测试',
        href: '/industry/automotive/'
      },
      {
        text: '整车/零部件性能测试',
        href: '/industry/automotive/'
      },
      {
        text: '汽车电子产品测试',
        href: '/industry/automotive/'
      },
      {
        text: '汽车禁限用物质测试',
        href: '/industry/automotive/'
      },
      {
        text: '车内空气测试',
        href: '/industry/automotive/'
      },
      {
        text: '整车/零部件检验',
        href: '/industry/automotive/'
      },
      {
        text: '整车/零部件产品认证',
        href: '/industry/automotive/'
      }
    ];
    for (var i = 0; i < carServiceDatas.length; i++) {
      carServiceDom += '<li class="clearfix">' +
        '<span></span>' +
        '<a href="' + carServiceDatas[i].href + '" target="_blank">' + carServiceDatas[i].text + '</a>' +
        '<em>>></em>' +
        '</li>';
    }
    carServiceBox.empty().append(carServiceDom);
    var carServiceFirst = true
    function carServiceAnimate() {
      $('#carService .box1').animate({
        right: 0
      }, 1000)
      $('#carService .box2').animate({
        right: '465px'
      }, 1000)
    }
    // advantage
    var advantageFirst = true
    function advantageAnimate() {
      advantageFirst = false
      var box1 = $("#advantage li em").eq(0);
      var min1 = 0;
      var max1 = box1.data('max');
      var box2 = $("#advantage li em").eq(1);
      var min2 = 0;
      var max2 = box2.data('max');
      var box3 = $("#advantage li em").eq(2);
      var min3 = 0;
      var max3 = box3.data('max');
      setInterval(function () {
        if (min1 < max1) {
          min1 = min1 + 1
          box1.html(min1)
        }
      }, 1)

      setInterval(function () {
        if (min2 < max2) {
          min2 = min2 + 10
          box2.html(min2)
        }
      }, 1)
      setInterval(function () {
        if (min3 < max3) {
          min3 = min3 + 50
          box3.html(min3)
        }
      }, 1)
    }
    // moreService
    var moreServiceBox = $("#moreService ul");
    var moreServiceDom = '';
    var moreServiceDatas = [
      {
        tit: '信息安全认证',
        more: '/service/certification/#/30',
        lists: [
          {
            text: 'ISO/IEC 27001 信息安全管理',
            href: '/sku/iso27001/447'
          },
          {
            text: 'ISO 22301 业务连续性管理',
            href: '/sku/product/443'
          },
          {
            text: 'ISO/IEC  20000 信息技术管理',
            href: '/sku/product/231'
          },
          {
            text: 'ISO/IEC  27017 云安全管理',
            href: '/sku/product/519'
          },
          {
            text: 'ISO/IEC  27018 公共云个人信息管理',
            href: '/sku/product/519'
          },
          {
            text: 'GDPR通用数据保护条例',
            href: '/sku/product/1003'
          }
        ]
      },
      {
        tit: 'CQI系列课程',
        more: '/service/training/#/35',
        lists: [
          {
            text: 'CQI-8分层审核培训',
            href: '/sku/product/581'
          },
          {
            text: 'CQI-9热处理系统评估培训',
            href: '/sku/product/868'
          },
          {
            text: 'CQI-11电镀系统评估培训',
            href: '/sku/product/833'
          },
          {
            text: 'CQI-12喷漆系统评估培训',
            href: '/sku/product/834'
          },
          {
            text: 'CQI-14汽车保修管理指南',
            href: '/sku/product/835'
          },
          {
            text: 'CQI-15焊接系统评估培训',
            href: '/sku/product/836'
          }
        ]
      },
      {
        tit: '质量管理系列课程',
        more: '/service/training/#/35',
        lists: [
          {
            text: '全面质量管理（TQM）',
            href: '/sku/product/614'
          },
          {
            text: '质量成本管理',
            href: '/sku/product/618'
          },
          {
            text: '高级质量经理人管理魔方',
            href: '/sku/product/611'
          },
          {
            text: '新QC七大手法',
            href: '/sku/product/617'
          },
          {
            text: '抽样检验与品质保证',
            href: '/sku/product/767'
          },
          {
            text: '解决问题的8D原则与方法',
            href: '/sku/product/612'
          }
        ]
      },
      {
        tit: '标准体系系列课程',
        more: '/service/training/#/35',
        lists: [
          {
            text: '福特：Q1基础知识',
            href: '/sku/product/872'
          },
          {
            text: 'ISO 26262:2011汽车功能安全基础培训',
            href: '/sku/product/763'
          },
          {
            text: '汽车行业产品安全代表（PSB）',
            href: '/sku/product/772'
          },
          {
            text: 'IATF 16949（2016版）二方审核员培训',
            href: '/sku/product/859'
          },
          {
            text: 'IATF 16949（2016版）不符合项分析应对',
            href: '/sku/product/858'
          },
          {
            text: 'Automotive SPICE 3.1',
            href: '/sku/product/829'
          }
        ]
      }
    ]
    for (var i = 0; i < moreServiceDatas.length; i++) {
      moreServiceDom += '<li>' +
        '<dl>' +
        '<dt>' + moreServiceDatas[i].tit + '</dt>';
      for (var j = 0; j < moreServiceDatas[i].lists.length; j++) {
        moreServiceDom += '<dd>' +
          '<a href="' + moreServiceDatas[i].lists[j].href + '" target="_blank">' + moreServiceDatas[i].lists[j].text + '</a>' +
          '</dd>';
      }
      moreServiceDom += '</dl>' +
        '<a href="' + moreServiceDatas[i].more + '" target="_blank" class="more">MORE+</a>' +
        '</li>';
    }
    moreServiceBox.empty().append(moreServiceDom);
    // 滚动事件监听
    $(window).scroll(function () {
      $('.detailNav').removeClass('fix');
      if ($(this).scrollTop() >= 1514) {
        // 相关培训课程
        trainingCourse.find('.pic1').animate({
          top: '50px'
        }, 1000)
        trainingCourse.find('.pic2').animate({
          top: '20px'
        }, 1000)
        trainingCourse.find('.pic3').animate({
          top: '260px'
        }, 1500)
        trainingCourse.find('.pic4').animate({
          top: '207px'
        }, 1500)
      }
      if ($(this).scrollTop() >= 5240) {
        if (advantageFirst) {
          advantageAnimate()
        }
      }
      if ($(this).scrollTop() >= 4450) {
        if (carServiceAnimate) {
          carServiceAnimate()
        }
      }
    })
    // 提交表单
    $('.pop .layout').css({
      width: $('html,body').width(),
      height: $('html,body').height()
    })
    $('.pop .btn').on('click', function () {
      $('.pop').hide();
      $('#customer').val('');
      $('#customer1').val('');
      $('#phone').val('');
      $('#phone1').val('');
      $('#email').val('');
      $('#email1').val('');
      $('#content').val('');
      $('#content1').val('');
      $("#service").val('-1')
      $("#service1").val('-1')
    });
    $("#submit").on("click", function () {
      var data = {
        type: 'IATF 16949',
        trade: 68,
        tradeName: '汽车与零部件',
        service: $("#service").val(),
        serviceName: $("#service").find("option:selected").text(),
        content: $('#content').val(),
        customer: $('#customer').val().trim(),
        phone: $('#phone').val().trim(),
        email: $('#email').val().trim(), // 不能为空，不能为非法邮箱
        provice: '浙江省',
        company: 'sgs',
        os_type: 'pc',
        frontUrl: window.location.href,
        _csrf: '<%- csrf %>'
      }
      if (!$('#customer').val().trim()) {
        alert('请输入姓名！')
      } else if (!$('#phone').val().trim()) {
        alert('请输入手机号码！')
      } else if (!/^1\d{10}$/.test($('#phone').val().trim())) {
        alert('请输入正确的手机号码！')
      } else if (!$('#email').val().trim()) {
        alert('请输入电子邮箱')
      } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email').val().trim())) {
        alert('请输入正确电子邮箱！')
      } else if (data.service == '-1') {
        alert('请选择服务类型！')
      } else if (!$('#content').val()) {
        alert('请输入需求信息！')
      } else {
        $.ajax({
          url: "/ticket/post",
          type: 'POST',
          data: data,
          headers: {
            frontUrl: window.location.href
          },
          success: function (res) {
            if (res.success) {
              $('#submitPop').show();
            } else {
              alert(res.data)
            }
          },
          fail: function (e) {
          }
        })
      }
    })
    var BANNER_DOWNLOAD_URL = ''
    // 下载控制处理
    $('.download-btn').on('click', function(e) {
      var _target = $(e.target)
      BANNER_DOWNLOAD_URL = _target.attr('data-url')
      var qichechanye = localStorage.getItem('qichechanye')
      if (!qichechanye) {
        $('.down-load-pop').show();
        $('.down-load-pop .popBox').animate({
          width: 349,
          height: 400,
          opacity: 1
        }, 200, function () {
          $('.down-load-pop .content').show();
        })
      } else if (JSON.parse(qichechanye).isSend) {
        window.open(BANNER_DOWNLOAD_URL);
      }
    })
    $('.down-load-pop .close').on('click', function () {
      $('.down-load-pop .content').hide();
      $('.down-load-pop .popBox').animate({
        width: 0,
        height: 0,
        opacity: 0
      }, 200, function () {
        $('.down-load-pop').hide();
      })
    });
    $("#submit2").on("click", function () {
      var data = {
        type: 'IATF 16949',
        trade: '68',
        tradeName: '汽车与零部件',
        service: '28',
        serviceName: '体系认证',
        content: '页面文件下载',
        customer: $('#customer2').val().trim(),
        phone: $('#phone2').val().trim(),
        email: $('#email2').val().trim(), // 不能为空，不能为非法邮箱
        provice: '其他地区',
        company: '系统定义',
        os_type: 'pc',
        frontUrl: window.location.href,
        _csrf: '<%- csrf %>'
      }
      if (!$('#customer2').val().trim()) {
        alert('请输入姓名！')
      } else if (!$('#phone2').val().trim()) {
        alert('请输入手机号码！')
      } else if (!/^1\d{10}$/.test($('#phone2').val().trim())) {
        alert('请输入正确的手机号码！')
      } else if (!$('#email2').val().trim()) {
        alert('请输入电子邮箱')
      } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email2').val().trim())) {
        alert('请输入正确电子邮箱')
      } else if (!$('#check').prop("checked")) {
        alert('请同意隐私政策！')
      } else {
        $.ajax({
          url: "/ticket/post",
          type: 'POST',
          data: data,
          headers: {
            frontUrl: window.location.href
          },
          success: function (res) {
            if (res.success) {
              $('.down-load-pop').hide();
              localStorage.setItem('qichechanye', JSON.stringify({ isSend: true })); // 用于校验后期下载文件是否可以直接下载
              // window.location.href = BANNER_DOWNLOAD_URL
              window.open(BANNER_DOWNLOAD_URL);
            } else {
              alert(res.data)
            }
          },
          fail: function (e) {
          }
        })
      }
    })

    // 第二版下载交互需求
    $('.down-load-pop2 .layout').css({
        width: pw,
        height: ph
    })
    var downloadUrl
      cookieKey = 'qichechanye'
    $('.download-btn2').on('click', function(e) {
        var _target = $(e.target)
        downloadUrl = _target.attr('data-url')
        var download = localStorage.getItem('qichechanye')
        if (!download) {
            $('.down-load-pop2').show();
            $('.down-load-pop2 .popBox').animate({
                width: 422,
                height: 531,
                opacity: 1
            }, 200, function () {
                $('.down-load-pop2 .content').show();
            })
        } else if (JSON.parse(download).isSend) {
            // window.location.href = downloadUrl
            window.open(downloadUrl)
        }
    })
    $('.down-load-pop2 .close').on('click', function () {
        $('.down-load-pop2 .content').hide();
        $('.down-load-pop2 .popBox').animate({
            width: 0,
            height: 0,
            opacity: 0
        }, 200, function () {
            $('.down-load-pop2').hide();
        })
    });
    $("#submit3").on("click", function () {
        var company = $('#company3').val().trim()
        var content = $('#textarea3').val().trim()
        var isCoop = $('input[name="isCoop"]:checked').val()
        var data = {
            type: isCoop === '1' ? 'IATF 16949' : $('#coopName').val().trim(),
            trade: '',
            tradeName: '其他',
            service: '',
            serviceName: '其他',
            content: content,
            customer: $('#customer3').val().trim(),
            phone: $('#phone3').val().trim(),
            email: $('#email3').val().trim(), // 不能为空，不能为非法邮箱
            provice: '其他地区',
            company: company,
            os_type: 'pc',
            frontUrl: window.location.href,
            _csrf: '<%- csrf %>'
        }
        if (!$('#customer3').val().trim()) {
            alert('请输入姓名！')
        } else if (!$('#phone3').val().trim()) {
            alert('请输入手机号码！')
        } else if (!/^1\d{10}$/.test($('#phone3').val().trim())) {
            alert('请输入正确的手机号码！')
        } else if (!$('#email3').val().trim()) {
            alert('请输入电子邮箱')
        } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email3').val().trim())) {
            alert('请输入正确电子邮箱')
        } else if (!company) {
            alert('请输入企业名称')
        } else if (!content) {
            alert('请简述您下载本资源最希望解决的问题')
        } else if (!isCoop) {
            alert('请选择贵公司是否已与SGS有合作')
        } else if (!data.type) {
            alert('请输入您企业的IATF 16949认证机构')
        } else if (!$('#check3').prop("checked")) {
            alert('请同意隐私政策！')
        } else {
            $.ajax({
                url: "/ticket/post",
                type: 'POST',
                data: data,
                headers: {
                  frontUrl: window.location.href
                },
                success: function (res) {
                    if (res.success) {
                        $('.down-load-pop2').hide();
                        localStorage.setItem(cookieKey, JSON.stringify({ isSend: true })); // 用于校验后期下载文件是否可以直接下载
                        // window.location.href = downloadUrl
                        window.open(downloadUrl)
                    } else {
                        alert(res.data)
                    }
                },
                fail: function (e) {
                }
            })
        }
    })
  })
</script>
<% include ../footer.html %>
