'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class InformaitionService extends Service {
  // 获取资讯中心分类
  async getType() {
    const list = await this.app.mysql.select('catalog', {
      columns: ['id', 'name', 'alias', 'is_show', 'is_activity'],
      where: {
        is_delete: 0,
        parent_id: 5,
        is_navi: 1,
        is_show: 1
      },
      orders: [
        ['sort_num', 'desc'],
      ]
    });

    return {
      list
    };
  }
  // 获取资讯中心列表
  async getList(params) {
    const {
      app
    } = this;
    let query = ['n.is_delete=0'];
    if (params.type && params.type != 0) {
      query.push('c.id=' + params.type);
    }

    if (params.title) {
      query.push('n.title LIKE "%' + params.title + '%"');
    }

    if (params && typeof params.is_publish == 'number') {
      query.push('n.is_publish=' + params.is_publish);
    }

    // 合并行业和服务
    if (params && (params.service || params.trade)) {
      let str = '';
      if (params.service && params.trade) {
        str = params.service + ',' + params.trade;
      }
      if (params.service && !params.trade) {
        str = params.service
      }
      if (!params.service && params.trade) {
        str = params.trade
      }
      const result = str.split(',');
      if (result.length > 1) {
        let temp = '('
        result.forEach(item => {
          temp += `cr.catalog_id=${item} or `
        })
        temp = temp.substr(0, temp.length - 3)
        temp += ')'
        query.push(temp);
      } else {
        query.push(`cr.catalog_id=${result[0]}`);
      }
    }

    let baseCategoryIds_service = [];
    let baseCategoryIds_trade = []

    if (params && params.allowServiceList != null && params.allowServiceList.length > 0) {
      for (let idvalue of params.allowServiceList) {
        if (idvalue != null) {
          baseCategoryIds_service.push(idvalue);
        }
      }
    }

    if (params && params.allowTradeList != null && params.allowTradeList.length > 0) {
      for (let idvalue of params.allowTradeList) {
        if (idvalue != null) {
          baseCategoryIds_trade.push(idvalue);
        }
      }
    }

    if (params.time) {
      let time = params.time.split(' - ');
      query.push('n.gmt_publish_time>="' + time[0] + '"');
      query.push('n.gmt_publish_time<"' + time[1] + '"');
    }


    // if (baseCategoryIds_service.length == 0 && this.ctx.session.userInfo.type == 2) {
    //   query.push('1=2');
    // }

    // if (baseCategoryIds_trade.length == 0 && this.ctx.session.userInfo.type == 2) {
    //   query.push('1=2');
    // }

    let page = params.page || 1,
      limit = params.limit || 10;
    let start = (page - 1) * limit;

    var countsql = 'SELECT count(distinct(n.id)) as countval FROM cases n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.case_id=n.id';

    var sql = 'SELECT n.id,n.title,n.content,n.hits,n.is_top,n.is_publish,n.original,n.gmt_publish_time AS time,c.name AS typeName FROM cases n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.case_id=n.id';

    if (baseCategoryIds_trade.length > 0) {
      countsql += " left join (select case_id from catalog_relation where catalog_type=1 and case_id is not null and catalog_id not in (" + baseCategoryIds_trade.join(',') + ")) tmptrade on n.id=tmptrade.case_id";

      sql += " left join (select case_id from catalog_relation where catalog_type=1 and case_id is not null and catalog_id not in (" + baseCategoryIds_trade.join(',') + ")) tmptrade on n.id=tmptrade.case_id";

      query.push('tmptrade.case_id is null');
    }

    if (baseCategoryIds_service.length > 0) {
      countsql += " left join (select case_id from catalog_relation where catalog_type=2 and case_id is not null and catalog_id not in (" + baseCategoryIds_service.join(',') + ")) tmpservice on n.id=tmpservice.case_id";

      sql += " left join (select case_id from catalog_relation where catalog_type=2 and case_id is not null and catalog_id not in (" + baseCategoryIds_service.join(',') + ")) tmpservice on n.id=tmpservice.case_id";

      query.push('tmpservice.case_id is null');
    }

    countsql += ' WHERE ' + query.join(' AND ');
    const total = await app.mysql.query(countsql);

    sql += ' WHERE ' + query.join(' AND ') + ' GROUP BY n.id ORDER BY n.is_top DESC,n.gmt_create DESC LIMIT ' + start + ',' + limit;
    const list = await app.mysql.query(sql);

    for (let item of list) {
      item.time = moment(item.time).format('YYYY-MM-DD');
      // 格式化纯文本
      // item.abstract = item.content.replace(/\n/g, '').replace(/<p.*?>|<\/p.*?>|<article.*?>|<\/article.*?>|<ul.*?>|<\/ul.*?>|<ol.*?>|<\/ol.*?>|<li.*?>|<\/li.*?>|<div.*?>|<\/div.*?>|<strong.*?>|<\/strong.*?>|<span.*?>|<\/span.*?>|<h1.*?>|<\/h1.*?>|<h2.*?>|<\/h2.*?>|<h3.*?>|<\/h3.*?>|<h4.*?>|<\/h4.*?>|<table.*?>|<\/table.*?>|<tbody.*?>|<\/tbody.*?>|<tr.*?>|<\/tr.*?>|<td.*?>|<\/td.*?>|<br.*?>|<img.*?>|/gi, '');
      item.abstract = item.content.replace(/<[^>]+>/g, "");
      // 获取html文本中的第一张图片
      item.content.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/, function (match, capture) {
        item.thumb = capture
      });


      let tradeList = [],
        serviceList = [];
      let tradeCata = await this.app.mysql.select('catalog_relation', {
        where: {
          case_id: item.id,
          catalog_type: 1
        }
      });
      let serviceCate = await this.app.mysql.select('catalog_relation', {
        where: {
          case_id: item.id,
          catalog_type: 2
        }
      });
      let catalogList = await this.app.mysql.query(`select a.alias from catalog a,catalog_relation b where case_id=${item.id} and b.catalog_id=a.id and a.alias!='subclass'`)
      item.catalog = catalogList

      if (tradeCata && tradeCata.length > 0) {
        for (let ti of tradeCata) {
          let tradeInfo = await this.app.mysql.get('catalog', {
            id: ti.catalog_id
          });
          //let tradeParantInfo = await this.app.mysql.get('catalog', { id: tradeInfo.parent_id });
          //tradeList.push(tradeParantInfo.name + '-' + tradeInfo.name);
          if (tradeInfo) {
            tradeList.push(tradeInfo.name);
          }

        }
      }

      if (serviceCate && serviceCate.length > 0) {
        for (let si of serviceCate) {
          let serviceInfo = await this.app.mysql.get('catalog', {
            id: si.catalog_id
          });
          //let serviceParantInfo = await this.app.mysql.get('catalog', { id: serviceInfo.parent_id });
          //serviceList.push(serviceParantInfo.name + '-' + serviceInfo.name);
          if (serviceInfo) {
            serviceList.push(serviceInfo.name);
          }

        }
      }

      item.tradeList = tradeList;
      item.serviceList = serviceList;

      // let checkflowObj = await this.ctx.service.checkflow.getDetail('case', item.id);

      // item.tran_result = checkflowObj;

    }

    return {
      list: list,
      total: total[0]["countval"],
      page: page,
      limit: limit
    }
  }
  // 获取资讯中心详情
  async getDetail(params) {
    const {
      app
    } = this;

    const detail = await app.mysql.get('cases', {
      id: id
    });

    // 获取更新访问次数
    let hits = detail.hits
    if (hits) {
      hits += 1
    } else {
      hits = 1
    }
    // detail.hits = hits > 999 ? '999+' : hits
    const row = {
      id,
      hits: hits
    }
    // await app.mysql.update('cases', row)

    const tag = await app.mysql.select('tag_relation', {
      columns: ['id', 'sku_id', 'tag_name'],
      where: {
        'sku_id': detail.id
      },
    });

    const tradeCata = await app.mysql.select('catalog_relation', {
      where: {
        news_id: id,
        catalog_type: 1
      }
    });
    const serviceCata = await app.mysql.select('catalog_relation', {
      where: {
        news_id: id,
        catalog_type: 2
      }
    });
    const tagList = await app.mysql.select('tag_relation', {
      where: {
        news_id: id
      }
    });

    if (tradeCata && tradeCata.length > 0) {
      for (let ti of tradeCata) {
        let tradeInfo = await app.mysql.get('catalog', {
          id: ti.catalog_id
        });
        let tradeParantInfo = await app.mysql.get('catalog', {
          id: tradeInfo.parent_id
        });
        ti.parentName = tradeParantInfo.name;
        ti.name = tradeInfo.name;
        ti.id = tradeInfo.id;
      }
    }

    if (serviceCata && serviceCata.length > 0) {
      for (let si of serviceCata) {
        let serviceInfo = await app.mysql.get('catalog', {
          id: si.catalog_id
        });
        let serviceParantInfo = await app.mysql.get('catalog', {
          id: serviceInfo.parent_id
        });
        si.parentName = serviceParantInfo.name;
        si.name = serviceInfo.name;
        si.id = serviceInfo.id;
      }
    }

    if (detail.is_publish == 1) {
      detail.is_publish = true;
    } else {
      detail.is_publish = false;
    }

    if (detail.is_top == 1) {
      detail.is_top = true;
    } else {
      detail.is_top = false;
    }

    detail.tag = [];
    for (let item of tagList) {
      detail.tag.push(item.tag_name);
    }

    detail.tradeCata = tradeCata;
    detail.serviceCata = serviceCata;

    if (detail.content) {
      detail.content = detail.content.replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
    }

    return {
      detail
    };
  }
}

module.exports = InformaitionService;
