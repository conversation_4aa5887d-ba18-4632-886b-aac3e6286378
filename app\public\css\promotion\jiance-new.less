@import './../mixin.less';

.jiance {
  background: #eee;

  * {
    box-sizing: border-box;
  }

  .red {
    color: #ca4300;
  }

  .head-box {
    height: 40px;
    background: #333333;
    padding: 5px 0;
    font-size: 12px;
    color: #C1C1C1;
    line-height: 30px;

    .page-title {
      display: inline-block;
      color: #C1C1C1;
    }
  }

  .user-content {
    float: right;
    display: flex;

    .user-item {
      margin-left: 27px;

      a {
        color: #C1C1C1;
      }
    }
  }

  .head-img {
    height: 90px;
    background: #FFFFFF;

    .logo1 {
      height: 43px;
    }

    .logo2 {
      height: 43px;
    }

    .wrap {
      display: flex;
      align-items: center;
      position: relative;
      height: 100%;
    }

    .logo {
      margin-right: 17px;
      vertical-align: bottom;
    }

    .title {
      height: 43px;
      margin-right: 25px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .row1 {
        font-size: 14px;
        color: #333333;
        line-height: 14px;
      }

      .row2 {
        font-size: 12px;
        color: #848484;
        line-height: 12px;

        img {
          vertical-align: middle;
          margin-right: 6px;
        }
      }
    }

    .detail-text {
      height: 43px;
      margin-right: 180px;
      font-size: 12px;
      color: #848484;
      line-height: 12px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      font-style: italic;

      span {
        color: #CA4300;
      }

      .row1 {
        line-height: 14px;
      }
    }

    .head-line {
      width: 16px;
      height: 41px;
      margin-right: 20px;
    }

    .cma-cnas {
      position: relative;

      .cma-box {
        position: absolute;
        left: 0;
        top: 0;
        width: 50px;
        height: 44px;
        cursor: pointer;
        z-index: 3;

        .cma-img {
          position: absolute;
          left: -100px;
          bottom: -354px;
          display: none;
        }

        &:hover .cma-img {
          display: block;
        }
      }

      .cnas-box {
        position: absolute;
        left: 113px;
        top: 0;
        width: 50px;
        height: 44px;
        cursor: pointer;
        z-index: 3;

        .cnas-img {
          position: absolute;
          left: -100px;
          bottom: -354px;
          display: none;
        }

        &:hover .cnas-img {
          display: block;
        }
      }
    }

    .home_top_hotline {
      position: absolute;
      right: 0;
      top: 25px;

      i {
        width: 40px;
        height: 40px;
        background: url(../../images/promotion/jiance-new/phone.png) no-repeat;
        position: absolute;
        left: -55px;
        top: 0;
      }

      .home_top_hotline_phone {
        span {
          font-size: 26px;
          font-weight: bold;
          color: #ca4300;
          line-height: 26px;
        }
      }

      .home_top_hotline_intro {
        font-size: 12px;
        color: #878787;
        text-align: right;
        line-height: 1;
        margin-top: 2px;
      }
    }

  }

  .nav-box {
    background: #ECECEC;
  }

  .nav-wrap {
    height: 50px;
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    color: #333333;

    .nav-label {
      padding-top: 3px;
      width: 94px;
      color: #878787;
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 1;
    }

    .nav-list {
      display: flex;
      align-self: flex-end;

      .nav-li {
        position: relative;
      }

      .nav-li-title,
      .nav-li-title a {
        padding: 12px 15px 15px;
        height: 43px;
        line-height: 1;
        cursor: pointer;
        color: #333;
      }

      .nav-li:hover {
        .nav-li-title {
          border-radius: 12px 12px 0 0;
          background: #ca4300;
          color: #fff;
        }

        .nav-li-title a {
          color: #fff;
        }

        .nav-sub-list {
          display: block;
        }
      }

      .nav-sub-list {
        position: absolute;
        width: 205px;
        height: 420px;
        background: #FFFFFF;
        box-shadow: 0px 5px 29px 0px rgba(4, 0, 0, 0.3);
        border-radius: 0px 0px 6px 6px;
        left: 0;
        bottom: -420px;
        display: none;
        padding-top: 11px;
        z-index: 100;

        .nav-list-icon {
          width: 13px;
          height: 13px;
          background: #FFFFFF;
          position: absolute;
          left: 36px;
          top: -5px;
          transform: rotate(45deg);
        }

        .nav-sub-li {
          width: 205px;
          height: 30px;
          line-height: 30px;

          a {
            display: inline-block;
            font-size: 14px;
            color: #333333;
            line-height: 30px;
            width: 100%;
            text-align: center;

            &:hover {
              background: #ca4300;
              color: #fff;
            }
          }
        }

        .nav-sub-li-btn {
          width: 205px;
          height: 42px;
          border-radius: 0px 0px 6px 6px;
          background: #474747;
          font-size: 16px;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 42px;
          margin-top: 11px;
          cursor: pointer;

          a {
            display: inline-block;
            width: 100%;
            color: #FFFFFF;
            text-align: center;
          }

          &:hover {
            background: #ca4300;
          }
        }
      }
    }

    .nav-btn {
      width: 146px;
      height: 50px;
      background: #ca4300;
      font-size: 16px;
      font-weight: bold;
      color: #FFFFFF;
      line-height: 50px;
      text-align: center;
      z-index: 2;
      position: relative;
      cursor: pointer;

      &:hover:before {
        width: 146px;
      }

      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 50px;
        background: #cc5500;
        z-index: -1;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }
    }
  }

  .banner-box {
    width: 100%;
    height: 500px;
    position: relative;

    .banner-box-form {
      position: absolute;
      z-index: 2;
      top: 0;
      height: 500px;
      width: 420px;
      right: calc(50% - 613px);
    }

    .swiper-container,
    .swiper-slide {
      height: 500px;
    }

    .swiper-slide1 {
      background: url("../../images/promotion/jiance-new/banner.jpg") no-repeat center center;
    }

    .swiper-slide2 {
      background: url("../../images/promotion/jiance-new/banner1.jpg") no-repeat center center;
    }

    .swiper-slide-wrap {
      width: 1226px;
      margin: 0 auto;
      padding-top: 310px;

      .more-btn {
        color: #fff;
        padding-left: 200px;
        font-size: 13px;
      }

      .kf5-btn {
        width: 180px;
        height: 50px;
        background: #ca4300;
        text-align: center;
        font-size: 20px;
        color: #FFFFFF;
        line-height: 50px;
        outline: none;
        cursor: pointer;
        display: inline-block;
        position: relative;
        border-radius: 3px;
        margin-left: 140px;
        margin-top: 30px;

        .btn-text {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 2;
          width: 100%;
        }

        .btn-active {
          width: 0;
          height: 100%;
          background: #cc5500;
          position: absolute;
          top: 0;
          left: 0;
        }

        &:hover .btn-active {
          width: 100%;
          transition: 0.4s;
        }
      }
    }

    .swiper-bottom {
      position: absolute;
      left: 50%;
      bottom: 50px;
      // width: 50px;
      .swiper-pagination {
        width: 140px;
      }
      .swiper-pagination-bullet {
        width: 50px !important;
        height: 5px !important;
        background: #fff !important;
        margin: 0 10px;
        border-radius: 0 !important;
        opacity: 1 !important;

        &.swiper-pagination-bullet-active {
          background: #CA4300 !important;
        }
      }
    }
  }

  .video-wrapper {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
    z-index: 2001;
  }

  .video-dialog {
    position: relative;
    margin: 15vh auto 50px;
    background: #000;
    border-radius: 2px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
    box-sizing: border-box;
    width: 898px;
    height: 619px;

    .close-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      cursor: pointer;
    }
  }

  .v-modal {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: .5;
    background: #000;
  }

  .floor-title {
    font-size: 30px;
    font-weight: bold;
    color: #333333;
    text-align: center;
    padding: 0;
    line-height: 30px;
    width: 1026px;
    margin: auto;
  }

  .main-line {
    margin: 12px auto 24px;
    width: 60px;
    height: 3px;
    background: #ca4300;
  }

  .floor-sub-title {
    font-size: 16px;
    color: #878787;
    opacity: 0.87;
    text-align: center;
    margin: 17px auto 0;
    width: 1042px;
    line-height: 30px;
  }

  .hot-service {
    padding-top: 60px;

    .hot-service-list {
      height: 500px;
      position: relative;
      display: flex;
      padding: 0 113px;
      justify-content: space-between;
      margin-top: 47px;

      .hot-service-item {
        width: 64px;

        .hot-service-title {
          cursor: pointer;
          position: relative;
          height: 83px;
          display: block;
        }

        .service-icon {
          position: absolute;
          top: 0;
          left: 17px;
        }

        .active-icon {
          display: none;
        }

        .active-down-icon {
          display: none;
          position: absolute;
          bottom: 0;
          left: 24px;
        }

        .title-text {
          position: relative;
          left: 0;
          top: 46px;
          font-size: 16px;
          font-weight: bold;
          color: #333333;
          line-height: 16px;
        }

        &.is-active,
        &.no-list:hover {

          .active-icon,
          .active-down-icon {
            display: block;
          }

          .normal-icon {
            display: none;
          }

          .title-text {
            color: #ca4300;
          }
        }

        &.is-active .hot-service-content {
          display: block;
        }
      }

      .hot-service-content {
        position: absolute;
        top: 100px;
        left: 0;
        width: 1226px;
        height: 400px;
        padding: 39px 47px 0 334px;
        display: none;

        .hot-service-top {
          border-bottom: 2px solid #D4D4D4;
          position: relative;
          padding-left: 18px;
          padding-bottom: 14px;
          font-size: 16px;
          font-weight: bold;
          color: #333333;
          line-height: 23px;

          img {
            vertical-align: middle;
            margin-left: 62px;
          }

          a {
            font-size: 14px;
            color: #878787;
            line-height: 23px;
            float: right;
          }

          &:after {
            content: "";
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 167px;
            height: 2px;
            background: #ca4300;
          }

        }

        .sub-list-content {
          margin-top: 33px;
          display: flex;
        }

        .sub-list {
          width: 211px;

          li {
            padding-left: 21px;
            font-size: 15px;
            line-height: 15px;
            position: relative;
            margin-bottom: 23px;

            a {
              color: #333333;
            }

            &:hover a {
              color: #ca4300;
              text-decoration: underline;
            }

            &:before {
              content: "";
              position: absolute;
              width: 5px;
              height: 5px;
              left: 0;
              top: 5px;
              background: #ca4300;
            }
          }
        }
      }
    }

    .hot-service-btns {
      margin-top: 16px;

      .btn1,
      .btn2 {
        width: 142px;
        height: 45px;
        border: 1px solid #ca4300;
        font-size: 18px;
        color: #ca4300;
        line-height: 43px;
        text-align: center;
        margin-right: 34px;
        display: inline-block;
        cursor: pointer;

        &:hover {
          background: #ca4300;
          color: #fff;
        }
      }

      .btn3 {
        display: inline-block;
        width: 220px;
        height: 45px;
        background: #ca4300;
        text-align: center;
        font-size: 18px;
        color: #FFFFFF;
        line-height: 45px;
        outline: none;
        cursor: pointer;
        position: relative;
        vertical-align: bottom;

        .btn-text {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 2;
          width: 100%;
        }

        .btn-active {
          width: 0;
          height: 100%;
          background: #cc5500;
          position: absolute;
          top: 0;
          left: 0;
        }

        &:hover .btn-active {
          width: 100%;
          transition: 0.4s;
        }
      }

      .btn4 {
        height: 45px;
        padding: 2.5px 0;
        display: inline-flex;
        vertical-align: bottom;

        i {
          width: 40px;
          height: 40px;
          background: url(../../images/promotion/jiance-new/phone.png) no-repeat;
          margin-right: 16px;
        }

        .text {
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .home_top_hotline_intro {
            font-size: 12px;
            color: #878787;
            line-height: 12px;
          }

          .hotlineText {
            font-size: 24px;
            color: #ca4300;
            line-height: 24px;
            font-weight: bold;
          }
        }
      }

      .btn5 {
        width: 220px;
        display: inline-flex;
        height: 45px;
        line-height: 45px;
        background: #ca4300;
        font-size: 18px;
        color: #fff;
        justify-content: center;
        margin-left: 54px;
      }
    }
  }

  .solution {
    margin-top: 60px;

    .solution-wrap {
      position: relative;
    }

    .solution-swiper {
      margin-top: 35px;
      position: relative;
    }

    .solutionCon {
      height: auto;
      overflow: hidden;
      padding-bottom: 0;
    }

    .solution-list {}

    .solutionConLi {
      width: 593px;
      height: 162px;
      float: left;
      position: relative;
      margin-bottom: 26px;
      margin-right: 38px;
      list-style: none;
      background: #fff;

      .img {
        float: left;
        width: 237px;
        height: 162px;
        display: block;
        background: #ccc;
        cursor: pointer;
        background-size: cover;
      }

      .cc {
        padding-left: 265px;
        text-align: left;
        padding-right: 20px;
      }

      .solutionConLi-word1 {
        display: block;
        font-size: 16px;
        font-weight: normal;
        font-style: normal;
        font-stretch: normal;
        line-height: 1.17;
        letter-spacing: normal;
        color: #333333;
        cursor: pointer;
        padding: 5px 0;
        margin: 20px 0 5px 0;
        font-weight: bold;
      }

      .cc p {
        font-size: 14px;
        line-height: 20px;
        height: 60px;
        overflow: hidden;
        color: #999;
      }

      em {
        display: inline-block;
        margin-top: 10px;
        width: 62px;
        text-align: center;
        height: 28px;
        line-height: 28px;
        border-radius: 14px;
        color: @mainColor;
        border: 1px solid @mainColor;
        margin-left: 20px;
        cursor: pointer;

        &:hover {
          background: @mainColor;
          color: #fff;
        }
      }
    }

    .solutionConLi:hover {
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
      -webkit-transform: translate3d(0, -2px, 0);
      transform: translate3d(0, -2px, 0);
      transition: 0.4s;
    }

    .serverRight0 {
      margin-right: 0;
    }

    .swiper-button-next,
    .swiper-button-prev {
      width: 34px;
      height: 76px;
      top: 298px;
      outline: none;
    }

    .swiper-button-prev {
      left: -52px;
      background: url("../../images/promotion/jiance-new/solution-prev.png");
    }

    .swiper-button-next {
      right: -52px;
      background: url("../../images/promotion/jiance-new/solution-next.png");
    }

    .swiper-pagination {
      left: 552.5px;
    }

    .swiper-pagination-bullet {
      width: 16px;
      height: 16px;
      background: #eee;
      border-radius: 50%;
      font-size: 12px;
      color: #878787;
      line-height: 16px;
      outline: none;
      opacity: 1;

      &.swiper-pagination-bullet-active {
        background: #ca4300;
        color: #fff;
      }

      &:not(:last-child) {
        margin-right: 5px;
      }
    }

    .solution-btns {
      text-align: center;
      margin-top: 51px;

      .btn1 {
        display: inline-block;
        width: 220px;
        height: 45px;
        background: #ca4300;
        text-align: center;
        font-size: 18px;
        color: #FFFFFF;
        line-height: 45px;
        outline: none;
        cursor: pointer;
        position: relative;
        vertical-align: bottom;

        .btn-text {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 2;
          width: 100%;
        }

        .btn-active {
          width: 0;
          height: 100%;
          background: #cc5500;
          position: absolute;
          top: 0;
          left: 0;
        }

        &:hover .btn-active {
          width: 100%;
          transition: 0.4s;
        }
      }
    }
  }

  .data-content {
    width: 100%;
    height: 400px;
    background: url("../../images/promotion/jiance-new/data-bg.jpg") no-repeat center center;
    padding-top: 61px;
    margin-top: 47px;

    .shuju-list {
      display: flex;
      padding-left: 63px;
      margin-top: 38px;
    }

    .shuju-item {
      margin-top: 50px;
      padding: 12px 0;

      .shuju-detail {
        font-size: 33px;
        font-family: Univers Condensed;
        font-weight: bold;
        //font-style: italic;
        color: #ca4300;
        line-height: 1;

        //background: linear-gradient(235deg, #FE8A02 0%, #ca4300 100%);
        //-webkit-background-clip: text;
        //-webkit-text-fill-color: transparent;
        .shuju-unit {
          font-size: 22px;
        }
      }

      .shuju-tips {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #333333;
        line-height: 16px;
        margin-top: 24px;
      }
    }

    .shuju-divider {
      margin-top: 50px;
      width: 17px;
      height: 100px;
    }
  }

  .panoramic {
    margin-top: 54px;

    .panoramic-wrap {
      height: 200px;
      background: url(../../images/promotion/jiance-new/panoramic-bg.jpg);
      position: relative;

      .panoramic-text {
        position: absolute;
        top: 79px;
        left: 65px;
        width: 587px;
        font-size: 36px;
        color: #FFFFFF;
        line-height: 36px;
      }

      .panoramic-num-text {
        font-size: 14px;
        color: #FFFFFF;
        line-height: 1;
        position: absolute;
        top: 40px;
        right: 99px;
      }

      .panoramic-btn {
        width: 175px;
        height: 54px;
        border: 1px solid #FFFFFF;
        font-size: 24px;
        color: #FFFFFF;
        line-height: 52px;
        text-align: center;
        position: absolute;
        top: 73px;
        right: 82px;
        z-index: 2;

        &:hover {
          border: 1px solid #ca4300;
        }

        &:hover:before {
          width: 175px;
        }

        &:before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 54px;
          background: #ca4300;
          z-index: -1;
          -webkit-transition: 0.2s;
          transition: 0.2s;
        }
      }
    }
  }

  .discount {
    margin-top: 66px;

    .discount-btns {
      width: 1226px;
      margin: auto;
      display: flex;
      justify-content: space-between;

      .btn1 {
        width: 430px;
        height: 82px;
        background: #FFFFFF;
        border: 3px solid #D2D2D2;
        padding: 23px 0 25px 23px;
        display: flex;
        align-items: center;
        position: relative;

        .click-btn {
          position: absolute;
          width: 212px;
          height: 100%;
          top: 0;
          left: 0;
          cursor: pointer;
        }

        .btn-icon {
          width: 50px;
          margin-right: 23px;
        }

        .btn-text {
          font-size: 22px;
          color: #ca4300;
          margin-right: 29px;
        }

        .position-icon {
          width: 17px;
          margin-right: 10px;
        }

        .position-text {
          font-size: 16px;
          color: #333333;
          margin-right: 10px;
        }

        .change-position {
          .change-btn {
            font-size: 12px;
            color: #ca4300;
            cursor: pointer;
          }

          #provice {
            position: absolute;
            width: 200px;
            left: 214px;
            bottom: -20px;
          }
        }
      }

      .btn2 {
        width: 430px;
        height: 82px;
        background: #ca4300;
        padding: 27px 0 23px 64px;
        display: flex;
        align-items: center;
        font-size: 22px;
        color: #FFFFFF;
        line-height: 22px;
        cursor: pointer;

        img {
          width: 28px;
          margin-right: 16px;
        }

        .text1 {
          border-right: 1px solid #F2F2F2;
          padding-right: 14px;
          margin-right: 14px;
        }
      }

      .btn3 {
        width: 291px;
        height: 82px;
        background: #FFFFFF;
        border: 3px solid #D2D2D2;
        padding: 18px 0 18px 36px;
        display: flex;

        i {
          width: 40px;
          height: 40px;
          background: url(../../images/promotion/jiance-new/phone.png) no-repeat;
          margin-right: 16px;
        }

        .text {
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .home_top_hotline_intro {
            font-size: 12px;
            color: #878787;
            line-height: 12px;
          }

          .hotlineText {
            font-size: 24px;
            color: #ca4300;
            line-height: 24px;
            font-weight: bold;
          }
        }
      }
    }
  }

  .news {
    margin-top: 63px;

    .news-wrap {
      position: relative;
    }

    .news-swiper {
      margin-top: 49px;
      height: 463px;
      width: 1226px;

      //overflow: hidden;
      .swiper-wrapper {
        height: 100%;
      }

      .newsCon {
        //display: flex;
        //justify-content: space-between;
        position: relative;

        .title {
          width: 429px;
          font-size: 16px;
          color: #FFFFFF;
          line-height: 24px;
          position: absolute;
        }

        .left-content {
          width: 500px;
          height: 433px;
          background: #1C1C1C;
          position: absolute;
          left: 0;
          top: 0;

          .title {
            left: 38px;
            bottom: 34px;
            font-size: 18px;
            line-height: 30px;
          }
        }

        .middle-content {
          width: 325px;
          height: 433px;
          position: absolute;
          left: 537.5px;
          top: 0;

          .card2 {
            display: block;
            width: 325px;
            height: 285px;
            background: #1C1C1C;
            margin-bottom: 36px;
            position: relative;

            .title {
              left: 31px;
              bottom: 21px;
              width: 262px;
            }
          }

          .card4 {
            display: block;
            width: 325px;
            height: 112px;
            position: relative;

            .title {
              left: 31px;
              bottom: 34px;
              width: 262px;
            }
          }
        }

        .right-content {
          width: 326px;
          height: 433px;
          position: absolute;
          left: 900px;
          top: 0;

          .card3 {
            display: block;
            width: 325px;
            height: 285px;
            background: #1C1C1C;
            margin-bottom: 36px;
            position: relative;

            .title {
              left: 31px;
              bottom: 21px;
              width: 262px;
            }
          }

          .news-btns {
            display: flex;
          }

          .news-btn {
            width: 163px;
            height: 111px;
            background: #ca4300;
            text-align: center;
            font-size: 20px;
            color: #FFFFFF;
            line-height: 20px;
            padding-top: 17px;
            cursor: pointer;

            .img {
              margin-bottom: 16px;
            }

            &:hover {
              box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
              //-webkit-transform: translate3d(0, -2px, 0);
              //transform: translate3d(0, -2px, 0);
              transition: 0.4s;
            }
          }

          .btn2 {
            background: #333333;
          }
        }

        .card:hover {
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
          //-webkit-transform: translate3d(0, -2px, 0);
          //transform: translate3d(0, -2px, 0);
          transition: 0.4s;
        }
      }
    }

    .swiper-button-next,
    .swiper-button-prev {
      width: 34px;
      height: 76px;
      top: 298px;
      outline: none;
    }

    .swiper-button-prev {
      left: -52px;
      background: url("../../images/promotion/jiance-new/solution-prev.png");
    }

    .swiper-button-next {
      right: -52px;
      background: url("../../images/promotion/jiance-new/solution-next.png");
    }

    .swiper-pagination {
      left: 552.5px;
    }
  }

  .contact_ads {
    margin-top: 39px;
    background: url(../../images/newPage/ads.jpg) no-repeat 50% 50% #e8e8e8;
    height: 100px;
  }
}