<% include header_new.html %>
  <% include ./components/header.html %>
    <div id="sku_detail_new" v-cloak v-loading="caseLoading" element-loading-text="加载中"
      element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)">
      <% include ./components/header-login.html %>
      <div class="location detailLoca">
        <ul>
          <li class="locationLi"><a href="/">首页</a></li>
          <li class="locationLi icon"></li>
          <% if(detail.catalog_name){ %>
            <li class="locationLi"><a
                href="/<% if(detail.catalogParent == 1){ %>industry<% }else{ %>service<% } %>/<%- detail.catalogAlias %>/">
                <%- detail.catalog_name %>
              </a>
            </li>
            <li class="locationLi icon"></li>
            <% } %>
              <li class="locationLi">
                <%- detail.name %>
              </li>
        </ul>
      </div>
      <div class="titleBox">
        <div class="mainTitle">
          <h1 style="font-size: 42px; padding: 0; margin: 0; width: 1130px;">
            <%- detail.name %>
          </h1>
          <!-- <span class="msg-icon" onclick="$('#kf5-support-btn').click();"></span> -->
          <span class="msg-icon handleIMtool"></span>
          <span class="sc-icon" onclick="addFavorite()"></span>
        </div>
        <div class="subTitle">
          <%- detail.description %>
        </div>
      </div>
      <div style="width: 1226px; height: 20px; margin: 10px auto;">
        <% if(anchorList.length){ %>
          <ul class="linkBox">
            <% for(var item of anchorList){ %>
              <li class="link-item">
                <a href="javascrpit:void(0);" onclick="clickAnchor('#<%- item.id %>')">
                  <%- item.name %>
                </a>
              </li>
              <% } %>
          </ul>
          <% } %>
      </div>
      <div class="detailBox" style="overflow: hidden; position: relative;">
        <div class="bannerInfo" style="float: right;" id='sku_detail' v-cloak>
          <% if(detail.is_message){ %>
            <% include ./components/sku_side_form.html %>
              <% }else{ %>
                <% include ./components/sku_side_step.html %>
                  <% } %>
                    <% include ./pages/quote/modal.html %>
        </div>
        <div style="float: left">
          <% if(detail.banner_type==0){ %>
            <div class="detailbannerBox">
              <!-- alt='<% if(detail.page_keywords.indexOf(",") > -1){ %><%- detail.page_keywords.substr(0, detail.page_keywords.indexOf(",")).trim() %><% }else{ %><%- detail.page_keywords.trim() %><% } %>' -->
              <div class="detailbanner">
                <img
                  alt="<%- detail.img_alt %>"
                  src="<%- detail.cover_img.replace(/\/static/g, locals.static) %>" />
              </div>
            </div>
            <% }else{ %>
              <div class="detailbannerContent">
                <%- detail.banner_content.replace(/\/static/g, locals.static) %>
              </div>
              <% } %>
  
                <% detail.content.forEach(function(item, index){%>
                  <div class="serverConBox">
                    <% if(!item.hide_title){ %>
                      <div class="serverTitleBox" id='content<%- index %>'>
                        <h2 class="serverCon">
                          <%- item.title %>
                        </h2>
                      </div>
                      <% } %>
                        <div class="rtfc" style="clear:both;width:825px;height:auot;overflow:hidden;">
                          <%- item.content.replace(/\/static/g,locals.static) %>
                        </div>
                  </div>
                  <% }) %>
                    <% if(defaultSort.length> 0){ %>
                      <% for(var itemSort of defaultSort){ %>
                        <% if(itemSort.name==='case' ){ %>
                          <% if(skuCases.length> 0){ %>
                            <div class="serverConBox">
                              <div class="serverTitleBox" id='case0'>
                                <h2 class="serverCon">
                                  <%- skuCases[0].floor_title !=0 ? skuCases[0].floor_title : '合作案例' %>
                                </h2>
                              </div>
                              <% if(skuCases[0].anchor_show){ %>
                                <ul class="rtfc hzal-ul">
                                  <% }else{ %>
                                    <ul class="rtfc hzal-ul">
                                      <% } %>
                                        <% for(var item of skuCases){ %>
                                          <li>
                                            <a href="/case/<%- item.alias || 'article' %>/detail-<%- item.id %>.html"
                                              target="_blank">
                                              <span class="hzal-icon"></span>
                                              <span class="hzal-title">
                                                <%- item.title %>
                                              </span>
                                            </a>
                                          </li>
                                          <% } %>
                                    </ul>
                            </div>
                            <% } %>
                              <% } %>
                                <% if(itemSort.name==='resource' ){ %>
                                  <% if(skuResource.length> 0){ %>
                                    <div class="serverConBox">
                                      <div class="serverTitleBox" id='resource0'>
                                        <h2 class="serverCon">
                                          <%- skuResource[0].title !=0 ? skuResource[0].title : '相关资料' %>
                                        </h2>
                                      </div>
                                      <% if(skuResource[0].anchor_show){ %>
                                        <ul class="rtfc xgzl-ul clearfix">
                                          <% }else{ %>
                                            <ul class="rtfc xgzl-ul clearfix">
                                              <% } %>
                                                <% for(var item of skuResource){ %>
                                                  <li>
                                                    <a href="javascript:void(0)"
                                                      onclick="downloadFile('<%- item.resource_id %>', '<%- item.is_public %>', '<%- item.link %>')">
                                                      <%- item.name %>
                                                        <span class="xgzl-icon"></span>
                                                    </a>
                                                  </li>
                                                  <% } %>
                                            </ul>
                                    </div>
                                    <% } %>
                                      <% } %>
                                        <% if(itemSort.name==='qa' ){ %>
                                          <% if(detail.qa.length> 0){ %>
                                            <div class="serverConBox">
                                              <div class="serverTitleBox" style="margin-bottom:25px" id='qa0'>
                                                <h2 class="serverCon">
                                                  <%- detail.qa[0].title || '常见问题解答' %>
                                                </h2>
                                              </div>
                                              <% detail.qa.forEach(function(item, index){%>
                                                <% if(item.anchor_show){ %>
                                                  <div class="questionBox" style="clear:both;overflow:hidden;">
                                                    <% }else{ %>
                                                      <div class="questionBox" style="clear:both;overflow:hidden;">
                                                        <% } %>
                                                          <div class="question-word1">
                                                            <%- item.question %>
                                                          </div>
                                                          <div class="question-word2 rtfc"
                                                            style="font-size: 14px;line-height: 2">
                                                            <%- item.answer.replace(/\/static/g,locals.static) %>
                                                          </div>
                                                          <div class="question-icon-wen"></div>
                                                          <div class="question-icon"></div>
                                                      </div>
                                                      <% }) %>
                                                  </div>
                                                  <% } %>
                                                    <% } %>
                                                      <% } %>
                                                        <% } %>
                                            </div>
        </div>
        <% if(tagAdpt.length> 0){ %>
        <div style="background-color: #f8f8f8;position: relative;height: 334px;">
            <div class="recoment">
              <div class="recoment-word1">更多服务推荐</div>
              <% if(tagAdpt.length> 4){ %><span onclick="_random()">
                  <div class="recoment-word2">换一批</div>
                  <div class="recoment-icon"></div>
                </span>
                <% } %>
                  <ul class="recomentUl" id="recList">
                    <% for(var [i,item] of tagAdpt.entries()){ %>
                      <% if(i<4){ %>
                        <li class="recomentLi" style="margin-right: 20px;">
                          <a href="/sku/<%- item.alias || 'article' %>/<%- item.id %>" target="_blank">
                            <div class="recomentLi-img">
                              <img src="<%- item.thumb_img.replace(/\/static/g,locals.static) %>" alt="<%- item.img_alt %>">
                              <% if(item.is_buy==1){ %><span class="buyicon"></span>
                                <% } %>
                            </div>
                            <div class="recomentLi-word">
                              <%- item.name %>
                            </div>
                            <div class="recomentLi-word2">
                              <%- item.sub_title %>
                            </div>
                          </a>
                        </li>
                        <% } %>
                          <% } %>
                  </ul>
            </div>
      </div>
      <% } %>
  
          
        <div style="position: relative;height: 100px;width: 100%">
          <div class="home-ads">
            <div class="wrap">
              <div class="bottom-item">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#home-bottom-1"></use>
                </svg>
                服务齐全，一站全包
              </div>
              <div class="bottom-item">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#home-bottom-2"></use>
                </svg>
                快捷受理，高效省时
              </div>
              <div class="bottom-item">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#home-bottom-3"></use>
                </svg>
                专业服务，国际权威
              </div>
              <div class="bottom-item">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#home-bottom-4"></use>
                </svg>
                官方自营，价格透明
              </div>
            </div>
          </div>
        </div>
         
        <!--下载课表需要填写的信息窗口-->
        <div class="down-load-pop">
          <div class="layout"></div>
          <div class='popBox'>
            <div class="title">
              <b>资源下载</b>
              <p>填写信息免费下载</p>
            </div>
            <div class="content">
              <i class="close">X</i>
              <input type="text" name='customer' id='customer2' placeholder="您的姓名" />
              <input type="text" name='phone' id='phone2' maxlength="11" placeholder="手机号码" />
              <input type="text" name='email' id='email2' placeholder="电子邮箱" />
              <div style="margin-top: 10px;">
                <label><input type="checkbox" name="" id="check">我接受</label><a
                  href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs"
                  style="color:#FE660B;text-decoration:none;" target="_blank">《隐私政策》</a>
              </div>
              <button id='submit2'>提交并下载</button>
            </div>
          </div>
        </div>

        <!--第二版下载课表需要填写的信息窗口-->
        <div class="down-load-pop2">
          <div class="layout"></div>
          <div class='popBox'>
            <div class="title">
              <b>资源下载</b>
              <p>填写信息免费下载</p>
            </div>
            <div class="content">
              <i class="close">X</i>
              <div style="display: flex;flex-wrap: wrap;justify-content: space-between;">
                <input type="text" name='customer' id='customer3' placeholder="您的姓名" />
                <input type="text" name='phone' id='phone3' maxlength="11" placeholder="手机号码" />
                <input type="text" name='email' id='email3' placeholder="电子邮箱" />
                <input type="text" name='email' id='company3' placeholder="企业名称" />
                <textarea id="textarea3" placeholder="请简述您下载本资源最希望解决的问题" style="overflow-y: auto"></textarea>
              </div>
              <div style="text-align: left">
                <div style="font-size: 14px;margin-top:10px;">贵公司是否已与SGS有合作</div>
                <div style="font-size: 14px;margin-top:10px;display: flex">
                  <input type="radio" name="isCoop" value="1">
                  <span>是，IATF 16949认证</span>
                </div>
                <div style="font-size: 14px;margin-top:10px;display: flex">
                  <input type="radio" name="isCoop" value="2">
                  <span>否</span>
                  <input type="text" name='coopName' id='coopName' placeholder="请输入您企业的IATF 16949认证机构" />
                </div>
              </div>
              <div style="margin-top: 20px;">
                <label><input type="checkbox" name="" id="check3">我接受</label><a
                  href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs"
                  style="color:#FE660B;text-decoration:none;" target="_blank">《隐私政策》</a>
              </div>
              <button id='submit3'>提交并下载</button>
            </div>
          </div>
        </div>
        <% include ./components/login.html %>
      </div>
        <script src="<%- locals.static %>/js/FileSaver.js"></script>
        <% if(locals.env != 'prod'){ %>
        <script src="<%- locals.static %>/plugin/v2.test.js"></script>
        <% }else{ %>
        <script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js" type="text/javascript"></script>
        <% } %>
        <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
        <script src="<%- locals.static %>/js/md5.js"></script>
        <script src="/static/svg-sprite.js"></script>
        <script uac_busitype="<%- uac_busiType || '' %>" uac_busisubtype="<%- uac_busiSubType || '' %>"
          uac_busicode="<%- uac_busiCode || '' %>"></script>
        <script>
          var newVue = new Vue({
            name: 'sku_detail',
            el: '#sku_detail_new',
            mixins: [quoteMixins],
            data: function () {
              var validatorUserPhone = function (rule, value, callback) {
                var reg = /^1[2|3|4|5|6|7|8|9][0-9]\d{8}$/
                if (!value) {
                  return callback(new Error('*请输入手机号'));
                } else if (!reg.test(value)) {
                  return callback(new Error('*请输入正确的手机号码'));
                } else {
                  return callback()
                }
              }
              var validatorEmail = function (rule, value, callback) {
                var reg =   /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/
                if (!value) {
                  return callback(new Error('*请输入邮箱地址'));
                } else if (!reg.test(value)) {
                  return callback(new Error('*请输入正确的邮箱地址'));
                } else {
                  return callback()
                }
              }
              return {
                form: {
                  type: '业务咨询',
                  trade: '',
                  tradeName: '其他',
                  tradeIndex: '',
                  serviceIndex: '',
                  service: '',
                  serviceName: '其他',
                  company: '',
                  provice: '',
                  content: '',
                  frontTitle: '<%- detail.name %>',
                  customer: '<%- userInfo.userName %>',
                  email: '<%- userInfo.userEmail %>',
                  phone: '<%- userInfo.userPhone %>',
                  imageCode: '',
                  frontUrl: window.location.href,
                  destCountry: '',
                  verifyCode: '',
                  busiCode: '<%- detail.id %>'
                },
                showModal: false,
                seconds: 59,
                timer: null,
                countdownTime: 5,
                countdownTimer: null,
                isLogin: <%- isLogin %>,
                disablePhone: <%- disablePhone %>,
                disableMail: <%- disableMail %>,
                loading: false,
                isSuccess: false,
                approve: false,
                tab: 'phone',
                provice: [],
                host: '<%- host %>',
                pid: 'pid.mall',
                pcode: 'Z0zCnRE3IaY9Kzem',
                rules: {
                  provice: [{ required: true, message: '*请选择所在城市' }],
                  content: [{ required: true, message: '*请输入您的咨询内容' }],
                  customer: [{ required: true, message: '*请输入您的称呼' }],
                  // email: [{ validator: validatorEmail }],
                  // phone: [{ validator: validatorUserPhone }],
                },
                type: 0,
                dialogVisibleReg: false,
                contact_approve_show: true,
                showLoginModal: false,
                closeBtn: false,
                domain: '<%- domain %>',
                apihost: '<%- apihost %>',
                loginSource: '',
                isRedirect: 1,
                parameter: {
                  regMode: 'sku-<%- detail.id %>',
                  regSource: 'mall',
                  regFrom: window.location.href,
                  busiCode: '<%- detail.id %>'
                },
                caseLoading: false,
              }
            },
            methods: {
              headerRegister: function () {
                this.showLoginModal = true
                this.closeBtn = true
                this.loginSource = 'header'
              },
              handleCloseLogin: function () {
                this.showLoginModal = false
              },
              successCallback: function (type) {
                var that = this
                that.showLoginModal = false
                that.caseLoading = true
                location.reload()
              },
              handleChange: function () { },
              handleJumpStep1: function () {
                window.open('<%- memberUrl %>/questionnaire/step1')
              },
            },
            mounted: function () {
            }
          });

          // ga('ec:addProduct', {
          //   'id': '<%- id %>',
          //   'name': '<%- detail.name %>',
          //   'category': '',
          //   'brand': '',
          //   'variant': ''
          // });
          // ga('ec:setAction', 'detail');
          // ga('send', 'pageview');

          $('.xgzl-ul li a').mouseenter(function (e) {
            const width = $(e.target)[0].getBoundingClientRect().width
            if ($(e.target).css('position') === 'static') {
              $(e.target).parent().css({
                'width': width,
                background: '#ca4300'
              }).children(':after').css('width', width)
              $(e.target).css({
                'background': 'none',
                'position': 'absolute'
              })
            }
          })
          function clickAnchor(id) {
            var top = $(id).offset().top;
            $("html,body").stop().animate({ scrollTop: top - 150 }, 500);//有滚动效果到达指定位置
            //$("html,body").scrollTop(top);//直接到达指定位置
            //$(window).scrollTop(top);//直接到达指定位置
          }
          function _random() {
            $.post('/getSkuTagApi', { id:<%- id %>, pageRow: 4 }, function (r) {
              if (r.resultCode == 0) {
                var list = r.data && r.data.items;
                var html = '';
                $.each(list, function (i, item) {
                  if (i < 4) {
                    html += '<li class="recomentLi"><a href="/sku/' + item.alias + '/' + item.id + '" target="_blank"><div class="recomentLi-img"><img src="' + item.thumbImg.replace(/\/static/g, '<%- locals.static %>') + '" alt=""></div><div class="recomentLi-word">' + item.name + '</div><div class="recomentLi-word2">' + item.subTitle + '</div></a></li>';
                  }
                });
                $('#recList').html(html);
              }
            })
          }

          function getEl(url) {
            var newWin = window.open();
            $.post('/getCode', { _csrf: '<%- csrf %>' }, function (res) {
              if (res.resultCode == 0) {
                var token = res.data.token;
                var newurl = '<%- tokenUrl %>/interface/getSgsTokenIn?token=' + token + '&notifyUrl=' + url;
                newWin.location.href = newurl;
              }
            })
          }
          var pw = $('html,body').width(),
            ph = $('html,body').height();

          $('.down-load-pop .layout').css({
            width: pw,
            height: ph
          })
          $('.down-load-pop2 .layout').css({
            width: pw,
            height: ph
          })
          var downloadUrl,
            downloadType,
            cookieKey = location.pathname
          $('.download-btn').on('click', function (e) {
            var _target = $(e.target)
            downloadUrl = _target.attr('data-url')
            downloadType = _target.attr('data-type')
            var download = localStorage.getItem(cookieKey)
            if (!download) {
              $('.down-load-pop').show();
              $('.down-load-pop .popBox').animate({
                width: 349,
                height: 400,
                opacity: 1
              }, 200, function () {
                $('.down-load-pop .content').show();
              })
            } else if (JSON.parse(download).isSend) {
              // window.location.href = downloadUrl
              window.open(downloadUrl)
            }
          })
          $('.down-load-pop .close').on('click', function () {
            $('.down-load-pop .content').hide();
            $('.down-load-pop .popBox').animate({
              width: 0,
              height: 0,
              opacity: 0
            }, 200, function () {
              $('.down-load-pop').hide();
            })
          });
          $("#submit2").on("click", function () {
            var data = {
              type: downloadType,
              trade: '其他',
              tradeName: '其他',
              service: '其他',
              serviceName: '其他',
              content: '页面文件下载',
              customer: $('#customer2').val().trim(),
              phone: $('#phone2').val().trim(),
              email: $('#email2').val().trim(), // 不能为空，不能为非法邮箱
              provice: '其他地区',
              company: '',
              os_type: 'pc',
              _csrf: '<%- csrf %>'
            }
            if (!$('#customer2').val().trim()) {
              alert('请输入姓名！')
            } else if (!$('#phone2').val().trim()) {
              alert('请输入手机号码！')
            } else if (!/^1\d{10}$/.test($('#phone2').val().trim())) {
              alert('请输入正确的手机号码！')
            } else if (!$('#email2').val().trim()) {
              alert('请输入电子邮箱')
            } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email2').val().trim())) {
              alert('请输入正确电子邮箱')
            } else if (!$('#check').prop("checked")) {
              alert('请同意隐私政策！')
            } else {
              $.ajax({
                url: "/ticket/post",
                type: 'POST',
                data: data,
                headers: {
                  frontUrl: window.location.href
                },
                success: function (res) {
                  if (res.success) {
                    $('.down-load-pop').hide();
                    localStorage.setItem(cookieKey, JSON.stringify({ isSend: true })); // 用于校验后期下载文件是否可以直接下载
                    // window.location.href = downloadUrl
                    window.open(downloadUrl)
                  } else {
                    alert(res.data)
                  }
                },
                fail: function (e) {
                }
              })
            }
          })

          // 第二版下载交互需求
          $('.download-btn2').on('click', function (e) {
            var _target = $(e.target)
            downloadUrl = _target.attr('data-url')
            downloadType = _target.attr('data-type')
            var download = localStorage.getItem(cookieKey)
            if (!download) {
              $('.down-load-pop2').show();
              $('.down-load-pop2 .popBox').animate({
                width: 422,
                height: 531,
                opacity: 1
              }, 200, function () {
                $('.down-load-pop2 .content').show();
              })
            } else if (JSON.parse(download).isSend) {
              // window.location.href = downloadUrl
              window.open(downloadUrl)
            }
          })
          $('.down-load-pop2 .close').on('click', function () {
            $('.down-load-pop2 .content').hide();
            $('.down-load-pop2 .popBox').animate({
              width: 0,
              height: 0,
              opacity: 0
            }, 200, function () {
              $('.down-load-pop2').hide();
            })
          });
          $("#submit3").on("click", function () {
            var company = $('#company3').val().trim()
            var content = $('#textarea3').val().trim()
            var isCoop = $('input[name="isCoop"]:checked').val()
            var data = {
              type: isCoop === '1' ? 'IATF 16949' : $('#coopName').val().trim(),
              trade: '其他',
              tradeName: '其他',
              service: '其他',
              serviceName: '其他',
              content: content,
              customer: $('#customer3').val().trim(),
              phone: $('#phone3').val().trim(),
              email: $('#email3').val().trim(), // 不能为空，不能为非法邮箱
              provice: '其他地区',
              company: company,
              os_type: 'pc',
              _csrf: '<%- csrf %>'
            }
            if (!$('#customer3').val().trim()) {
              alert('请输入姓名！')
            } else if (!$('#phone3').val().trim()) {
              alert('请输入手机号码！')
            } else if (!/^1\d{10}$/.test($('#phone3').val().trim())) {
              alert('请输入正确的手机号码！')
            } else if (!$('#email3').val().trim()) {
              alert('请输入电子邮箱')
            } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email3').val().trim())) {
              alert('请输入正确电子邮箱')
            } else if (!company) {
              alert('请输入企业名称')
            } else if (!content) {
              alert('请简述您下载本资源最希望解决的问题')
            } else if (!isCoop) {
              alert('请选择贵公司是否已与SGS有合作')
            } else if (!data.type) {
              alert('请输入您企业的IATF 16949认证机构')
            } else if (!$('#check3').prop("checked")) {
              alert('请同意隐私政策！')
            } else {
              $.ajax({
                url: "/ticket/post",
                type: 'POST',
                data: data,
                headers: {
                  frontUrl: window.location.href
                },
                success: function (res) {
                  if (res.success) {
                    $('.down-load-pop2').hide();
                    localStorage.setItem(cookieKey, JSON.stringify({ isSend: true })); // 用于校验后期下载文件是否可以直接下载
                    // window.location.href = downloadUrl
                    window.open(downloadUrl)
                  } else {
                    alert(res.data)
                  }
                },
                fail: function (e) {
                }
              })
            }
          })

          // 2019年9月16日 set tm sup
          $(function () {
            var wordHtml = $('.word1').html();
            if (wordHtml === 'WELLTM性能验证服务') {
              $('.word1').html('WELL<sup style="font-size: 10px;">TM</sup>性能验证服务')
            }
          });
          // 下载资源
          var isLogin = <%- isLogin %>,
            url = '<%- url %>';
          function downloadFile(id, is_public, url) {
            verification(id, is_public, url)
          }
          function verification(id, is_public, url) {
            if (is_public == '1' || isLogin) {
              if (id !== '0') {
                getFilePath(id);
              } else {
                downloadImg(url)
              }
            } else {
              window.location.href = '<%- memberUrl %>/login?next_origin=www&next_path=' + encodeURIComponent(window.location.pathname);
            }
          }
          function getFilePath(id) {
            jQuery.post('/getFilePath', { id: id, _csrf: '<%- csrf %>' }, function (result) {
              if (result.success) {
                if (result.path.indexOf('http') < 0) {
                  downloadImg(url + result.path)
                } else {
                  downloadImg(result.path)
                }
              }
            })
          }
          function downloadImg(url) {
            if (!!window.ActiveXObject || "ActiveXObject" in window) {
              var filename = url.substr(url.lastIndexOf('/') + 1, url.length);
              var xhr = new XMLHttpRequest();
              xhr.open('GET', encodeURI(url), true);
              xhr.responseType = 'blob';
              xhr.setRequestHeader("Authorization", "Basic a2VybWl0Omtlcm1pdA==")
              xhr.onload = function () {
                if (xhr.status === 200) {
                  //将图片文件用浏览器中下载
                  saveAs(xhr.response, filename);
                  document.execCommand(xhr.response, '', filename);
                }
              }
              xhr.send();
            } else {
              var a = document.createElement('a');          // 创建一个a节点插入的document
              var event = new MouseEvent('click')           // 模拟鼠标click点击事件
              a.download = ''                  // 设置a节点的download属性值
              a.href = url;                                 // 将图片的src赋值给a节点的href
              a.dispatchEvent(event);                      // 触发鼠标点击事件
            }
          }
        </script>
        <% include footer.html %>