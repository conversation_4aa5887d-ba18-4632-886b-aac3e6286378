$(function () {
  var TIMER;//定义全局变量
  $(window).scroll(function () {
    clearTimeout(TIMER);//必须要有这句
    if ($(document).scrollTop() > 174) {
      TIMER = setTimeout(function () {
        $(".bannerInfo").addClass("abc");
        $(".abc").css('right', (document.body.scrollWidth - 1226) / 2)
        var dd = $('#helpInfo').offset().top - ($('.bannerInfo').offset().top + $('.bannerInfo').height())
        if (dd < 0) {
          $(".bannerInfo").removeClass("abc");
        }
      }, 100);
    } else {
      TIMER = setTimeout(function () {
        $(".bannerInfo").removeClass("abc");
      }, 100);
    }
  });
});