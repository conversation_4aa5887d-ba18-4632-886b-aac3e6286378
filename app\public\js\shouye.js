$(function () {
    $(".homeConCon4Li").hover(function () {
        var $this = $(this),$c = $this.children(".learnMore");


        $c.animate({opacity:1,bottom:120})
    },function () {
        var $this = $(this),$c = $this.children(".learnMore");
        $c.animate({opacity:0,bottom:40})

    })

    $(".title").each(function(){
		var $that = $(this);
		$that.click(function () {
			var $this = $(this);
			var index = $this.index();
			var width = $this.width() + 50;
			var cw = $(document).width();

			var left = $this.position().left;
			$that.parent().children(".titleBor").css("width",width).animate({left:left});

			$this.addClass("active").siblings(".title").removeClass("active");
		});
    })


	/*
    $(".homeConCon5Li").mouseover(function () {
        var index = $(this).index();
        if(index == 0){
            $(this).children(".homeConCon5Li-img").attr("src","./images/minsuxuan.png");
            $(".homeConCon5Li").eq(2).children(".homeConCon5Li-img").attr("src","./images/nengxiao.png");
            $(".homeConCon5Li").eq(3).children(".homeConCon5Li-img").attr("src","./images/anju.png");
            $(".homeConCon5Li").eq(1).children(".homeConCon5Li-img").attr("src","./images/duju.png");

        }else if(index == 1){
            $(this).children(".homeConCon5Li-img").attr("src","./images/dujuxuan.png");
            $(".homeConCon5Li").eq(0).children(".homeConCon5Li-img").attr("src","./images/minsu.png");
            $(".homeConCon5Li").eq(2).children(".homeConCon5Li-img").attr("src","./images/nengxiao.png");
            $(".homeConCon5Li").eq(3).children(".homeConCon5Li-img").attr("src","./images/anju.png");
        }else if(index == 2){
            $(this).children(".homeConCon5Li-img").attr("src","./images/nengxiaoxuan.png");
            $(".homeConCon5Li").eq(3).children(".homeConCon5Li-img").attr("src","./images/anju.png");
            $(".homeConCon5Li").eq(0).children(".homeConCon5Li-img").attr("src","./images/minsu.png");
            $(".homeConCon5Li").eq(1).children(".homeConCon5Li-img").attr("src","./images/duju.png");
        }else {
            $(this).children(".homeConCon5Li-img").attr("src","./images/anjuxuan.png");
            $(".homeConCon5Li").eq(0).children(".homeConCon5Li-img").attr("src","./images/minsu.png");
            $(".homeConCon5Li").eq(2).children(".homeConCon5Li-img").attr("src","./images/nengxiao.png");
            $(".homeConCon5Li").eq(1).children(".homeConCon5Li-img").attr("src","./images/duju.png");
        }

    })
	*/

	$(".homeConCon5Li").on('mouseover',function () {
		$(this).children('img:first').stop().fadeOut().siblings('img').stop().fadeIn();
		$(this).siblings('li').each(function(){
			$(this).children('img:first').stop().fadeIn().siblings('img').stop().fadeOut();
		});
	});
})
