<% include header.html %>
<% include ./components/header.html %>
<div class="bgBanner">
    <img src="<%- locals.static %>/images/bgbanner.png" alt="">
    <div class="bgBannerW">
        <div class="bgBannerW1">SGS 资讯中心</div>
    </div>
</div>
<div class="location">
    <ul>
        <li class="locationLi">
            <a href="/">第三方检测机构</a>
        </li>
        <li class="locationLi icon"></li>
        <li class="locationLi">
            <a href="javascrpit:void(0);">资讯中心</a>
        </li>
    </ul>
</div>
<div class="caseList" id='caseList' v-cloak>
    <div class="nlBox">
        <div class="nlSearch">
            <div class="nlSearchBox">
                <el-form :inline="true" :model="form" class="demo-form-inline">
                    <el-form-item label="资讯标题：">
                        <el-input size="mini" v-model="form.title" placeholder="请输入资讯标题..."></el-input>
                    </el-form-item>
                    <el-form-item label="资讯日期：">
                        <el-date-picker
                            size="mini"
                            placeholder="请选择资讯日期..."
                            type="daterange"
                            @change='handleChangeDate'
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            v-model="createdDateView"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="mini" type="primary" @click="handlerSearch" class='el-icon-search'></el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="nlLists">
            <ul class="nlListsUl" v-if='caseList.length'>
                <li class="nlListsLi" v-for="(item, index) of caseList" :key='index'>
                    <span class="nlListsI"></span>
                    <a :href="'/case/' + item.alias + '/detail-' + item.id + '.html'" class="nlListsA">{{ item.title }}</a>
                    <img v-if='item.isTop' style="vertical-align: middle;" src="<%- locals.static %>/images/hot.png">
                    <span class="nlListsT">{{ item.gmtPublishTime }}</span>
                </li>
            </ul>
            <div v-else>
                <p v-if="searchhint==1">加载中......</p>
                <p v-else>很抱歉，没有为您找到对应的结果，请扩大筛选范围</p>
            </div>
        </div>
        <div class="pagination" v-if='caseList.length' style="text-align: right; width: 1226px;">
            <el-pagination layout="prev, pager, next, jumper" @current-change="handleCurrentChange"
                @size-change="handleSizeChange" :current-page.sync="form.pageNum" :page-size="form.pageRow" :total="totalNum">
            </el-pagination>
        </div>
    </div>
</div>
<script>
    var newVue = new Vue({
        name: 'caseList',
        el: '#caseList',
        data: {
            createdDateView: [],
            form: {
                pageRow: 10,
                pageNum: 1,
                title: '',
                gmtPublishTime: '',
            },
            totalNum: 0,
            caseList: [],
            searchhint:1
        },
        methods: {
            handleChangeDate(date) {
                if (date && date[0] && date[1]) {
                    let start = date[0],
                        end = date[1];
                    end = end.substr(0, end.indexOf(" ")) + " 23:59:59";
                    this.form.gmtPublishTime = start + "/" + end;
                    this.createdDateView = [start, end];
                } else {
                    this.form.gmtPublishTime = "";
                }
            },
            handleCurrentChange: function (val) {
                this.form.pageNum = val
                this.qryCaseList()
            },
            handleSizeChange: function (val) {
                this.form.pageNum = val
                this.qryCaseList()
            },
            handlerSearch: function () {
                this.form.pageNum = 1
                this.qryCaseList()
            },
            qryCaseList: function () {
                var that = this
                var loading = this.$loading({
                    lock: true,
                    text: '加载中...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                    fullscreen: false
                });
                that.searchhint=1
                axios.post('/case/list', this.form,{timeout:15000})
                    .then(function (res) {
                        if (res.status === 200 && res.data.resultCode === '0') {
                            var datas = res.data.data;
                            that.totalNum = datas.totalNum;
                            that.caseList = datas.items;
                            if(datas.length==0){
                                that.searchhint=2
                            }
                        } else {
                            that.$message({
                                message: '获取数据异常，请稍后重试。',
                                type: 'warning'
                            });
                            that.searchhint=2
                        }
                        loading.close();
                    })
                    .catch(function (error) {
                        that.$message({
                            message: error,
                            type: 'warning'
                        });
                        that.searchhint=2
                        loading.close()
                    });
            }
        },
        mounted: function () {
            this.qryCaseList()
        }
    });
</script>
<% include footer.html %>
