<!-- 右侧悬浮窗按钮 -->
<% if(isChatbot){ %>
  <div class="sgs-floating-menu" style="top: 45%;">
    <% }else{ %>
      <div class="sgs-floating-menu">
        <% } %>
          <ul class="menu">
            <li class="tips phone" data-agl-cvt="2">
              <div class="menu-icon">
                <i></i>
              </div>
              <span class="icon-desc fixed_menu_hotline">400热线</span>
              <div class="menu-more-phone" style="display: none;">
                <a href="/hotline" target="_blank" style="color: white;" class="fixed_menu_tel">4000-558-581</a>
              </div>
            </li>
            <li class="tips ticket">
              <a href="/baojia">
                <div class="menu-icon">
                  <img src="<%- locals.static %>/images/fixedMenu/floating.menu.zixun.png">
                </div>
                <span class="icon-desc">业务咨询</span>
              </a>
            </li>
            <% if(!detail.isTestingCertification){ %>
              <li class="tips ticket quick">
                <a href="<%- memberUrl %>/questionnaire/step1">
                  <div class="menu-icon">
                    <img src="<%- locals.static %>/images/fixedMenu/floating.menu.baojia.png">
                  </div>
                  <span class="icon-desc">快速报价</span>
                </a>
              </li>
            <% } %>
            <% if(!isChatbot){ %>
              <li class="tips handleIMtoolMenu" data-agl-cvt="1">
                <% }else{ %>
              <li class="tips handleIMtoolMenu hide" data-agl-cvt="1">
                <% } %>
                  <div class="menu-icon">
                    <img src="<%- locals.static %>/images/fixedMenu/floating.menu.kefu.png">
                  </div>
                  <span class="icon-desc">在线客服</span>
              </li>
              <li class="tips feedback">
                <!-- <span class="icon-desc">报告真伪</span> -->
                <a href="/DocCheck" target="_blank">报告真伪</a>
              </li>
          </ul>

          <!-- 意见反馈 -->
          <!-- <div class="floating-menu-feedback">
            <div class="block-menu-feedback">
              <form name="floating-menu-feedback">
                <h3 style="font-size: 16px; color: #333; float: left; margin: 0;">意见反馈</h3>
                <div class="button-close">
                  <img src="<%- locals.static %>/images/fixedMenu/floating.menu.close.png">
                </div>
                <p style="font-size: 14px; color: #333; margin-top: 15px;">对SGS服务不满意吗？请告诉我们 <em>*</em></p>
                <div class="block-content">
                  <textarea name="" placeholder="欢迎提出您遇到的问题或宝贵建议，感谢您对SGS的支持。" id='content'></textarea>
                </div>
                <div class="err_tips block-content_tip"></div>
                <p style="font-size: 14px; color: #333; margin-top: 5px;">联系方式 <em>*</em></p>
                <div class="block-input-area">
                  <div>
                    <input type="text" class="input-phone" placeholder="姓名" maxlength="25" id='customer' />
                    <div class="err_tips phone_tip"></div>
                  </div>
                  <div style="margin-left: 10px;">
                    <input type="text" class="input-phone" placeholder="手机号码" id='phone' />
                    <div class="err_tips phone_tip"></div>
                  </div>
                  <div>
                    <input type="text" class="input-email" placeholder="邮箱" id='email' />
                    <div class="err_tips email_tip"></div>
                  </div>
                </div>
                <div class="block-form-action">
                  <button type="button" class="button-submit" id='submit-fixed-menu'>提交</button>
                </div>
              </form>
            </div>
          </div>

          <div class="modal" id='modal'>
            <div class="modal_content">
              <p><i></i><span>您的意见反馈已收到，我们将尽快处理。</span></p>
            </div>
            <div class="modal_button">
              <button id='btn_close'>关闭</button>
            </div>
          </div> -->
          <script>
            $(function () {
              var ticViewUrl = ''
              var origin = window.location.origin;
              if (origin === "https://www.sgsmall.com.cn" || origin === "https://store.sgsonline.com.cn") {
                ticViewUrl = 'https://www.sgsmall.com.cn'
              } else {
                ticViewUrl = 'https://uat.sgsmall.com.cn'
                // ticViewUrl = 'http://localhost:9511'
              }
              $(document).on("click", ".sgs-floating-menu li.phone", function (event) {
                if ($(this).find(".menu-more-phone").css("display") == "none") {
                  $(this).find(".menu-more-phone").show();
                  $(".sgs-floating-menu .floating-menu-feedback").hide();
                  // 阻止事件冒泡到DOM树上,后面的事件不再执行
                  event = event || window.event;
                  event.stopPropagation();
                } else {
                  $(this).find(".menu-more-phone").hide();
                }
              });
              // // 意见反馈
              // $(document).on("click", ".sgs-floating-menu li.feedback", function (event) {
              //   if ($(".sgs-floating-menu .floating-menu-feedback").css("display") == "none") {
              //     $(".sgs-floating-menu .floating-menu-feedback").show();
              //     $(this).closest('ul').find(".menu-more-phone").hide();
              //   } else {
              //     $(".sgs-floating-menu .floating-menu-feedback").hide();
              //   }
              // });
              // 必填输入校验
              // $(document).on('keyup', "#content", function () {
              //   if (!$("#content").val().trim()) {
              //     $('.block-content').addClass('err')
              //     $('.block-content_tip').html('*必填');
              //   } else {
              //     $('.block-content').removeClass('err')
              //     $('.block-content_tip').html('');
              //   }
              // });
              // $(document).on('keyup', "#phone", function () {
              //   if (!$("#phone").val().trim()) {
              //     $('#phone').addClass('err')
              //     $('.phone_tip').html('*必填');
              //   } else {
              //     $('#phone').removeClass('err')
              //     $('.phone_tip').html('');
              //   }
              // });
              // $(document).on('keyup', "#email", function () {
              //   if (!$("#email").val().trim()) {
              //     $('#email').addClass('err')
              //     $('.email_tip').html('*必填');
              //   } else {
              //     $('#email').removeClass('err')
              //     $('.email_tip').html('');
              //   }
              // });
              // 工单提交
              $(document).on('click', '#submit-fixed-menu', function () {
                var content = $("#content").val().trim();
                var customer = $("#customer").val().trim();
                var phone = $("#phone").val().trim();
                var email = $("#email").val().trim();
                var frontUrl = window.location.href;

                if (!content) {
                  alert('请输入您的意见和建议！')
                  $('.block-content').addClass('err')
                } else if (!customer) {
                  alert('请输入姓名！')
                  $('#phone').addClass('err')
                } else if (!phone) {
                  alert('请输入手机号码！')
                  $('#phone').addClass('err')
                } else if (!/^1\d{10}$/.test(phone)) {
                  alert('请输入正确的手机号码！')
                } else if (!email) {
                  alert('请输入邮箱')
                  $('#email').addClass('err')
                } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test(email)) {
                  alert('请输入正确邮箱！')
                } else {
                  $.ajax({
                    url: ticViewUrl + "/ticket/opinion",
                    // url:  "http://localhost:9511/ticket/opinion",
                    type: 'POST',
                    data: {
                      content: content,
                      phone: phone,
                      email: email,
                      customer: customer,
                      frontUrl: frontUrl
                    },
                    success: function (res) {
                      if (res.resultMsg === 'Successful') {
                        $("#modal").show();
                        closeFeedBack();
                      } else {
                        alert(res.resultMsg)
                      }
                    },
                    fail: function (e) {
                    }
                  })
                }
              });
              // 关闭提示
              $(document).on('click', "#btn_close", function () {
                $('#modal').hide();
                closeFeedBack();
              })
              // 隐藏表单输入和清空输入内容
              $(document).on('click', '.button-close', function () {
                closeFeedBack();
              });
              function closeFeedBack(params) {
                $('.err_tips').html('');
                $('.block-content, .input-phone, .input-email').removeClass('err');
                $('.floating-menu-feedback').hide();
                $("#content").val('');
                $("#customer").val('');
                $("#phone").val('');
                $("#email").val('');
              }

              // TIC-3577
              $(document).on('hover', '.phone', function () {
                $('.menu-more-phone').show();
              }, function () {
                $('.menu-more-phone').hide();
              })
              // $('.phone').hover(function () {
              //   $('.menu-more-phone').show();
              // }, function () {
              //   $('.menu-more-phone').hide();
              // })
              // TIC-4409

              /*
              IM 工具点击判断
                TIC-4971
                联系客服按钮事件
                当3级域名是activity或访问的pathname为oiq || oiq/start || /lab 的页面，永远触发KF5
              */
              $(document).on("click", ".handleIMtoolMenu", function () {
                $.ajax({
                  type: 'POST',
                  url: ticViewUrl + '/IMtool',
                  data: {
                    paraName: 'CHAT_SET'
                  },
                  headers: {
                    frontUrl: window.location.href
                  },
                  success: function (res) {
                    var isChatbot = false;
                    var isZhiChi = false;
                    var notLeadoo = false; // 不会触发leadoo的路由
                    debugger
                    if (window.location.pathname === "/oiq" || window.location.pathname === "/oiq/start" || window.location.pathname.indexOf('/lab') > -1) {
                      notLeadoo = true
                    }
                    const ticViewList = res.filter(v => {
                      return v.groupName === 'MALL'
                    })
                    if (window.location.host === "www.sgsmall.com.cn" || window.location.host === "uat.sgsmall.com.cn" || window.location.host === "localhost:9511") {
                      ticViewList.forEach(v => {
                        if (v.paraValue === "1") {
                          if (v.paraCode.includes("CHATBOT")) isChatbot = true
                          if (v.paraCode.includes("SOBOT")) isZhiChi = true
                        }
                      })
                    }
                    if (notLeadoo) {
                      $('#zc__sdk__sys__btn').trigger('click')
                    } else {
                      if (isZhiChi) {
                        $('#zc__sdk__sys__btn').trigger('click')
                      } else if (isChatbot) {
                        var iframeLen = $("iframe").length;
                        for (var i = 0; i < iframeLen; i++) {
                          if ($("iframe")[i]) {
                            $($("iframe")[i]).contents().find('.ld-launch-btn').trigger('click');
                          }
                        }
                      }
                    }
                  },
                })
              });


              // bbc 和 member永远显示KF5按钮
              if (window.location.host === "member.sgsonline.com.cn" || window.location.host === "memberuat.sgsonline.com.cn" || window.location.host === "store.sgsonline.com.cn" || window.location.host === "ticuat.sgsmall.com.cn" || window.location.pathname === "/oiq" || window.location.pathname === "/oiq/start" || window.location.pathname.indexOf('/lab') > -1) {
                setTimeout(function () {
                  $(document).find('.handleIMtoolMenu').removeClass('hide');
                  $(document).find('.handleIMtoolMenu').addClass('show');
                }, 1000);
              }
              /* 
                TIC-16834 副站的实验室页面去掉漂浮快速报价入口
              */
              if (window.location.pathname.indexOf('/lab') > -1) {
                $(document).find('.quick').addClass('hide');
              }
            })
          </script>
      </div>