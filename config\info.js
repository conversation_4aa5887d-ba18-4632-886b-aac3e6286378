'use strict';

const crypto = require('crypto');
const pid = 'cmsPid',
  pcode = 'Tic123!';
let upStr = (pid + pcode).toUpperCase();
const pstr = crypto.createHash('md5').update(upStr).digest("hex");

const siteInfo = {
  siteTitle: 'SGS ONLINE',
  solrPstr: pstr.toUpperCase(),
  solrPid: pid,
  mallPid: 'pid.mall',
  mallPcode: 'Z0zCnRE3IaY9Kzem',
  env: {
    prodGray: {
      url: 'https://gray.sgsmall.com.cn', // prod
      storeUrl: 'https://store.sgsonline.com.cn', // prod
      memberUrl: 'https://membergray.sgsonline.com.cn', // prod
      solrUrl: 'https://gate.sgsonline.com.cn/solrServer/business/api.v1.solr/content/qry', // prod
      ticServiceUrl: 'http://10.205.236.190:8090/', // prod
      ticMall: 'https://gate.sgsonline.com.cn', // prod
      version: 'gray',
      indUrl: 'https://oiqindgray.sgsonline.com.cn',
      portalUrl: 'https://portalgray.sgsonline.com.cn',
      ENV_API: 'https://mallmdogray.sgsonline.com.cn',
      domain: '.sgsonline.com.cn',
      env: 'prodGray',
    },
    prod: {
      url: 'https://www.sgsmall.com.cn', // prod
      storeUrl: 'https://store.sgsonline.com.cn', // prod
      memberUrl: 'https://member.sgsonline.com.cn', // prod
      solrUrl: 'https://gate.sgsonline.com.cn/solrServer/business/api.v1.solr/content/qry', // prod
      ticServiceUrl: 'http://10.205.236.190:8090/', // prod
      ticMall: 'https://gate.sgsonline.com.cn', // prod
      indUrl: 'https://oiqind.sgsonline.com.cn',
      portalUrl: 'https://portal.sgsonline.com.cn',
      ENV_API: 'https://mallmdo.sgsonline.com.cn',
      domain: '.sgsonline.com.cn',
      env: 'prod',
    },
    UAT: {
      url: 'https://uat.sgsmall.com.cn', // uat
      storeUrl: 'https://ticuat.sgsonline.com.cn', // uat
      memberUrl: 'https://memberuat.sgsonline.com.cn', // uat
      solrUrl: 'https://gateuat.sgsonline.com.cn/solrServer/business/api.v1.solr/content/qry', // uat
      ticServiceUrl: 'http://**************:8090/', // UAT
      ticMall: 'https://gateuat.sgsonline.com.cn', // UAT
      indUrl: 'https://oiqinduat.sgsonline.com.cn',
      portalUrl: 'https://portaluat.sgsonline.com.cn',
      ENV_API: 'https://mallmdouat.sgsonline.com.cn',
      domain: '.sgsonline.com.cn',
      env: 'uat',
    },
    gray: {
      url: 'https://uatgray.sgsmall.com.cn', // gray
      storeUrl: 'https://ticuatgray.sgsonline.com.cn', // gray
      memberUrl: 'https://memberuatgray.sgsonline.com.cn', // gray
      solrUrl: 'https://gateuat.sgsonline.com.cn/solrServer/business/api.v1.solr/content/qry', // gray
      ticServiceUrl: 'http://**************:8090/', // gray
      ticMall: 'https://gateuat.sgsonline.com.cn', // gray
      version: 'gray',
      indUrl: 'https://oiqinduatgray.sgsonline.com.cn',
      portalUrl: 'https://portaluatgray.sgsonline.com.cn',
      ENV_API: 'https://mallmdouatgray.sgsonline.com.cn',
      domain: '.sgsonline.com.cn',
      env: 'uatGray',
    },
    dev: {
      url: 'https://dev.sgsmall.com.cn', // dev
      storeUrl: 'https://ticdev.sgsonline.com.cn', // dev
      memberUrl: 'https://memberdev.sgsonline.com.cn', // dev
      solrUrl: 'https://gatedev.sgsonline.com.cn/solrServer/business/api.v1.solr/content/qry', // dev
      ticServiceUrl: 'http://**************:8090/', // dev
      ticMall: 'https://gatedev.sgsonline.com.cn', // dev
      indUrl: 'https://oiqinddev.sgsonline.com.cn',
      portalUrl: 'https://portaldev.sgsonline.com.cn',
      ENV_API: 'https://mallmdodev.sgsonline.com.cn',
      domain: '.sgsonline.com.cn',
      env: 'dev',
    },
    test: {
      url: 'https://test.sgsmall.com.cn', // test
      storeUrl: 'https://tictest.sgsonline.com.cn', // test
      memberUrl: 'https://membertest.sgsonline.com.cn', // test
      solrUrl: 'https://gatetest.sgsonline.com.cn/solrServer/business/api.v1.solr/content/qry', // test
      ticServiceUrl: 'http://**************:8090/', // test
      ticMall: 'https://gatetest.sgsonline.com.cn', // test
      indUrl: 'https://oiqindtest.sgsonline.com.cn',
      portalUrl: 'https://portaltest.sgsonline.com.cn',
      ENV_API: 'https://mallmdotest.sgsonline.com.cn',
      domain: '.sgsonline.com.cn',
      env: 'test',
    },
    local: {
      url: 'http://localhost:9501', // uat
      storeUrl: 'https://ticuat.sgsonline.com.cn', // uat
      memberUrl: 'https://memberuat.sgsonline.com.cn', // uat
      solrUrl: 'https://gateuat.sgsonline.com.cn/solrServer/business/api.v1.solr/content/qry', // uat
      ticServiceUrl: 'http://**************:8090/', // UAT
      ticMall: 'https://gatetest.sgsonline.com.cn', // UAT
      // ticMall: 'http://*************:6800', // UAT
      indUrl: 'https://oiqinduat.sgsonline.com.cn',
      portalUrl: 'https://portaluat.sgsonline.com.cn',
      ENV_API: 'https://mallmdouat.sgsonline.com.cn',
      domain: '',
      env: 'local',
    }
  }
}

module.exports = {
  siteInfo: siteInfo
}
