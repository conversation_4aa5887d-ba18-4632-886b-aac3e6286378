<% include ../header.html %>
<% include ./../components/header.html %>
<link rel="stylesheet" href="<%- locals.static %>/css/promotion/gaoFenZi.css">
<div class="gaoFenZi">
  <div class="banner">
    <div class="wrap">
      <!--<img class="phone" src="<%- locals.static %>/images/promotion/gaoFenZi/phone2.png" alt="">
  <br>-->
      <span class="serviceBtn handleIMtool">详细咨询客服 ></span>
    </div>
  </div>
  <div class="banner-bottom">
    <div class="link-content">
      <a class="link-text handleIMtool">掌握原料成分</a>
      <a class="link-text handleIMtool">剖析产品配方</a>
      <a class="link-text handleIMtool">解决产品失效问题</a>
      <a class="link-text handleIMtool">符合买家质检需求</a>
      <a class="link-text handleIMtool">助力企业入驻电商平台</a>
    </div>
  </div>
  <div class="bg-white clearfix">
    <div class="wrap">
      <div class="title">
        <h2>服务行业</h2>
        <p>专业材料化学领域，世界500强合作企业</p>
        <span></span>
      </div>
      <div class="service" id="service">
        <div class="tab">
          <ul></ul>
        </div>
      </div>
    </div>
  </div>
  <div class="service-card bg-gray clearfix">
    <ul class="wrap"></ul>
  </div>
  <div class="bg-gray clearfix">
    <div class="wrap">
      <div class="title">
        <h2>行业案例</h2>
        <span></span>
      </div>
      <div class="case clearfix" id='case'>
        <div class="tab">
          <ul>
            <li class="active"><span>回料成分鉴定</span></li>
            <li><span>产品对比</span></li>
            <li><span>异物分析</span></li>
            <li><span>油品分析</span></li>
            <li><span>液体分析</span></li>
            <li><span>天然乳胶鉴定</span></li>
          </ul>
        </div>
        <div class="card">
          <div class="box">
            <div class="item">
              <div class="left">
                <p>
                  回料成分鉴定
                </p>
                <img src="<%- locals.static %>/images/promotion/gaoFenZi/case/2.jpg" />
              </div>
              <div class="right">
                <dl>
                  <dt>背景介绍</dt>
                  <dd>客户想从国外购买一批聚乙烯塑料粒子回料，价格比较高，但是供应商承诺回料中成分材质是单一聚乙烯材质，不会掺杂其他材质。客户委托我们SGS上海高分子分析实验室检测该回料全部成分含量</dd>
                </dl>
                <dl>
                  <dt>结果</dt>
                  <dd>经过对样品的分离提纯，结合FTIR, PGC-MS, EDX,
                    DSC及TGA对成分进行分析，结果显示聚乙烯含量88%，聚丙烯含量3%，不是纯的聚乙烯回料，客户看了报告之后最终没有选择这家供应商，避免了上当受骗。</dd>
                </dl>
                <span class="serviceBtn handleIMtool">咨询客服</span>
              </div>
            </div>
            <div class="item">
              <div class="left">
                <p>
                  产品对比
                </p>
                <img src="<%- locals.static %>/images/promotion/gaoFenZi/case/3.jpg" />
              </div>
              <div class="right">
                <dl>
                  <dt>背景介绍</dt>
                  <dd>
                    客户作为经销商，长期供应一款知名品牌胶带A给某大公司作为原材料使用，但近期却遭到了品质投诉，说此批胶带品质不符合要求，怀疑客户使用仿造产品，以次充好。客户经内部审核并与知名品牌沟通后，发现产品确实有差异。但由于他们确实没有造假，因此怀疑供应商供应环节出现问题，把外观极其相似的C产品作为A来供应。为验证是否确实如此，客户提供了知名品牌胶带A，此批不合格胶带B，疑似胶带C，3种样品，委托SGS上海高分子实验室进行鉴定。
                  </dd>
                </dl>
                <dl>
                  <dt>结果</dt>
                  <dd>经过对3个样品分别进行分离提纯，结合FTIR, PGC-MS, EDX,
                    DSC及TGA对产品进行全成分定量分析，结果显示B、C两个样品的成分确实一致，并与A有所不同。进一步经过物理手段进行背胶表面刮擦，发现A样品刮擦后是透明材质，B/C刮擦后是黑色材质。由此确定确实是供应商供货错误导致，帮客户洗刷了仿造产品的嫌疑，稳住了他们的大客户。
                  </dd>
                </dl>
                <span class="serviceBtn handleIMtool">咨询客服</span>
              </div>
            </div>
            <div class="item">
              <div class="left">
                <p>
                  异物分析
                </p>
                <img src="<%- locals.static %>/images/promotion/gaoFenZi/case/4.jpg" />
              </div>
              <div class="right">
                <dl>
                  <dt>背景介绍</dt>
                  <dd>
                    客户有一批注射器，供货以后被投诉针头部位含有杂质，目测显示有很多细小颗粒。因为医药企业对无尘问题要求严苛，客户担心如果杂质是灰尘问题会非常严重，因此特意委托SGS上海高分子实验室，对异物进行鉴别。
                  </dd>
                </dl>
                <dl>
                  <dt>结果</dt>
                  <dd>经过显微红外结合EDX对样品进行分析，结果显示杂质主要是PE，不是灰尘。而与客户交流后，客户表示这可能是生产过程的模具中携带出来的，查明了异物来源，为改善提供了强有力的依据。
                  </dd>
                </dl>
                <span class="serviceBtn handleIMtool">咨询客服</span>
              </div>
            </div>
            <div class="item">
              <div class="left">
                <p>
                  油品分析
                </p>
                <img src="<%- locals.static %>/images/promotion/gaoFenZi/case/5.jpg" />
              </div>
              <div class="right">
                <dl>
                  <dt>背景介绍</dt>
                  <dd>客户是一家贸易公司，订购了一批山茶油卖给终端客户，因为需要对产品进行宣传和推广，需要实际掌握山茶油的成分，
                    从成分含量方面向消费者宣传山茶油的功效，委托SGS上海高分子成分实验室进行山茶油全部成分定性和定量分析。</dd>
                </dl>
                <dl>
                  <dt>结果</dt>
                  <dd>通过GCFID、GC-MS、ICP-MS等分析手段，对样品进行分析，将山茶油的主要成分和微量元素都进行了测定，很好地符合了客户的需求。
                  </dd>
                </dl>
                <span class="serviceBtn handleIMtool">咨询客服</span>
              </div>
            </div>
            <div class="item">
              <div class="left">
                <p>
                  液体分析
                </p>
                <img src="<%- locals.static %>/images/promotion/gaoFenZi/case/6.jpg" />
              </div>
              <div class="right">
                <dl>
                  <dt>背景介绍</dt>
                  <dd>客户从国外购买了一批液体脱模剂，产品已经到国内港口了，但被海关扣留。因为产品成分不明确，需要提供权威第三方出具的产品成分说明，客户委托SGS上海高分子成分实验室对产品进行成分分析。
                  </dd>
                </dl>
                <dl>
                  <dt>结果</dt>
                  <dd>通过FTIR, NMR, GC-MS, EDX. MS, ICP等分析手段对样品进行鉴定，给出了完整的成分信息。最终报告提交给了我海关，对货物进行放行，避免了不必要的滞留损失。
                  </dd>
                </dl>
                <span class="serviceBtn handleIMtool">咨询客服</span>
              </div>
            </div>
            <div class="item">
              <div class="left">
                <p>
                  天然乳胶鉴定
                </p>
                <img src="<%- locals.static %>/images/promotion/gaoFenZi/case/1.jpg" />
              </div>
              <div class="right">
                <dl>
                  <dt>背景介绍</dt>
                  <dd>
                    客户公司为给员工提供福利，从泰国团购了一批天然乳胶枕和天然乳胶床垫作为礼品，计划分发给公司员工。为了保障员工真正享受到福利，客户希望找专业权威机构检测产品是否为纯的天然乳胶制品，故委托SGS
                    上海高分子分析实验室检测，出具产品的成分分析报告。</dd>
                </dl>
                <dl>
                  <dt>结果</dt>
                  <dd>经过对样品进行分离提纯，结合FTIR, PGC-MS,
                    EDX及TGA等对成分进行测试，并用UV-Vis对水溶性蛋白进行测定，结果显示天然乳胶含量31%，合成胶占比39%，其他填料助剂占比30%。由此判定该产品不是纯的天然乳胶制品，同时添加有大量的填料降低成本。
                  </dd>
                </dl>
                <span class="serviceBtn handleIMtool">咨询客服</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--<div class="bg-gray clearfix">
<div class="title">
  <h2>应用领域</h2>
  <span></span>
</div>
<div class="wrap territory" id='territory'>
  <ul></ul>
</div>
</div>-->
  <div class="bg-test clearfix">
    <div class="title">
      <h2>测试项目</h2>
      <p>我们通过专业设备、分析手段和微观图谱来辅助您解决技术问题</p>
      <span></span>
    </div>
    <div class="wrap test" id='testItem'>
      <table cellspacing="0" cellpadding='0'>
        <tbody></tbody>
      </table>
      <span class="serviceBtn handleIMtool">点击咨询以上测试项目</span>
      <!--<img class="phone" src="<%- locals.static %>/images/promotion/gaoFenZi/phone.png" alt="">-->
    </div>
  </div>
  <div class="bg-white clearfix">
    <div class="wrap">
      <div class="title">
        <h2>测试现场</h2>
        <p>SGS工业部材料实验室现场实拍，50余台精密仪器满足所有测试需求</p>
        <span></span>
      </div>
      <!--<div class="service" id="service">
    <div class="tab">
      <ul></ul>
    </div>
  </div>-->
      <div class="wrap testSite" id="wrap">
        <div class="slide" id="slide">
          <ul>
            <!--六张图片-->
            <li><img src="<%- locals.static %>/images/promotion/gaoFenZi/testSite/1.jpg" alt="" /></li>
            <li><img src="<%- locals.static %>/images/promotion/gaoFenZi/testSite/2.jpg" alt="" /></li>
            <li><img src="<%- locals.static %>/images/promotion/gaoFenZi/testSite/3.jpg" alt="" /></li>
            <li><img src="<%- locals.static %>/images/promotion/gaoFenZi/testSite/4.jpg" alt="" /></li>
            <li><img src="<%- locals.static %>/images/promotion/gaoFenZi/testSite/5.jpg" alt="" /></li>
            <li><img src="<%- locals.static %>/images/promotion/gaoFenZi/testSite/6.jpg" alt="" /></li>
          </ul>
          <!--左右切换按钮-->
          <div class="arrow" id="arrow">
            <a href="javascrpit:void(0);" class="prev"></a>
            <a href="javascrpit:void(0);" class="next"></a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="bg-white clearfix">
    <div class="wrap">
      <div class="title">
        <h2>关键优势</h2>
        <p>20年专业经验与服务大数据,用数据证实SGS的技术实力</p>
        <span></span>
      </div>
      <div class="wrap advantage" id="advantage">
        <ul>
          <li>
            <h3><em data-max='50'>0</em>+</h3>
            <span>仪器设备</span>
            <p>实验室拥有成分分析类和检测仪器设备50多台</p>
          </li>
          <li>
            <h3><em data-max='100'>0</em>+</h3>
            <span>技术人员</span>
            <p>实验室拥有100多名资深技术人员，大多来自一线研发机构和生产厂家</p>
          </li>
          <li>
            <h3><em data-max='10000'>0</em>+</h3>
            <span>累计客户数</span>
            <p>累计服务客户10000多家，与多家世界500强企业合作</p>
          </li>
          <li>
            <h3><em data-max='100'>0</em>万+</h3>
            <span>图谱配方</span>
            <p>实验室拥有100万+图谱配方能应对各类材料和产品的成分检测分析服务</p>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="bg-white clearfix certificate-content">
    <div class="title">
      <h2>
        实验室拥有
        <img src="<%- locals.static %>/images/promotion/gaoFenZi/certificate/CMA.png" alt="">
        <img src="<%- locals.static %>/images/promotion/gaoFenZi/certificate/CNAS.png" alt="">
        等多项资质认可与认证
      </h2>
      <!--      <span></span>-->
    </div>
    <div class="certificate wrap" id="certificate">
      <!--      <ul></ul>-->
      <img src="<%- locals.static %>/images/promotion/gaoFenZi/certificate/certificate.jpg" alt="">
    </div>
  </div>
  <div class="bg-white clearfix">
    <div class="wrap">
      <div class="title">
        <h2>服务流程</h2>
        <p>规范、高效、专业，成果更有保障！</p>
        <span></span>
      </div>
      <div class="wrap step">
        <div class="step1 step-item">
          <h3 class="step-title"><span>1.</span>业务咨询</h3>
          <div class="step-form" id='gaoFenZi'>
            <% include ../components/registionClause.html %>
            <input type="text" name='customer' id='customer' v-model="form.customer" placeholder="姓名" maxlength='50' />
            <input type="text" name='phone' id='phone' v-model="form.phone" maxlength="11" placeholder="手机号码" />
            <input type="text" name='email' id='email' v-model="form.email" placeholder="电子邮箱" maxlength='80' />
            <textarea placeholder="请简述您的需求， 工程师2小时内与您联系（工作日：9:00-17:00）" name='content' type="textarea"
              class="input-big" id='content' v-model="form.content"></textarea>
            <div class="form-item">
              <el-checkbox v-model="approve">
                <template v-if='!isLogin'>提交后自动注册会员，获取后续信息。</template>
                我已阅读并同意
                <template v-if='!isLogin'><a @click.stop='dialogVisibleReg = true'>注册条款</a>及</template>
                <a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" target="_blank">隐私政策</a>
              </el-checkbox>
            </div>
            <button class="serviceBtn" @click='handleSubmit'>提交留言</button>
            <span id='submit2' class="more handleIMtool" style="margin: 0 0 0 90px;">快速咨询入口</span>
            <% include ./../pages/quote/modal.html %>
          </div>
        </div>
        <div class="step2 step-item">
          <h3 class="step-title"><span>2.</span>评估提案</h3>
          <div class="step-content">
            <p>SGS销售联系客户</p>
            <p>评估并提出方案</p>
            <p>（约2工作日）</p>
          </div>
          <div class="next-icon">
            <img src="<%- locals.static %>/images/promotion/gaoFenZi/step/step-next.png" alt="">
          </div>
          <div class="step-icon">
            <img src="<%- locals.static %>/images/promotion/gaoFenZi/step/icon2-a.png" alt="">
          </div>
        </div>
        <div class="step3 step-item">
          <h3 class="step-title"><span>3.</span>分析测试</h3>
          <div class="step-content">
            <p>签订合同后实施方案</p>
            <p>分析测试</p>
            <p>（约5-7工作日）</p>
          </div>
          <div class="next-icon">
            <img src="<%- locals.static %>/images/promotion/gaoFenZi/step/step-next.png" alt="">
          </div>
          <div class="step-icon">
            <img src="<%- locals.static %>/images/promotion/gaoFenZi/step/icon3-a.png" alt="">
          </div>
        </div>
        <div class="step4 step-item">
          <h3 class="step-title"><span>4.</span>出具报告</h3>
          <div class="step-content">
            <p>出具测试报告</p>
            <p>并提供售后服务</p>
          </div>
          <div class="next-icon">
            <img src="<%- locals.static %>/images/promotion/gaoFenZi/step/step-next.png" alt="">
          </div>
          <div class="step-icon">
            <img src="<%- locals.static %>/images/promotion/gaoFenZi/step/icon4-a.png" alt="">
          </div>
        </div>
      </div>
      <!--<div class="step clearfix" id="step">
    <div class="item">
      <i class="icon1"></i>
      <em>需求咨询</em>
      <p>
        客户选择测试服务<br />
        咨询客服了解详情
      </p>
    </div>
    <div class="dot">
      <span></span>
      <span></span>
      <span></span>
      <span></span>
    </div>
    <div class="item">
      <i class="icon2"></i>
      <em>评估提案</em>
      <p>
        SGS销售联系客户<br />
        评估并提出方案
      </p>
    </div>
    <div class="dot">
      <span></span>
      <span></span>
      <span></span>
      <span></span>
    </div>
    <div class="item">
      <i class='icon3'></i>
      <em>分析测试</em>
      <p>
        签订合同后实方案<br />
        分析测试
      </p>
    </div>
    <div class="dot">
      <span></span>
      <span></span>
      <span></span>
      <span></span>
    </div>
    <div class="item">
      <i class='icon4'></i>
      <em>出具报告</em>
      <p>
        出具测试报告<br />
        并提供售后服务
      </p>
    </div>
  </div>-->
    </div>
  </div>

  <!--<div class="bg-white clearfix">
<div class="title">
  <h2>实验室设备仪器</h2>
  <span></span>
</div>
<div class="wrap product" id='product'>
  <div class="productBox"></div>
  <div class="productBtn">
    <span class="active"></span>
    <span></span>
    <span></span>
  </div>
</div>
</div>-->
  <div class="bg-gray clearfix">
    <div class="title">
      <h2>更多服务推荐</h2>
      <span></span>
    </div>
    <div class="wrap moreService" id='moreService'>
      <ul class="clearfix"></ul>
      <a href="/industry/Industrial/service" target="_blank" class="more">更多服务</a>
    </div>
  </div>
  <!--<div class="bg-write">
<div class="wrap">
  <div class="title">
    <h2>留言咨询</h2>
    <span></span>
  </div>
  <div class="wrap consult">
    <div class="clearfix">
      <label>
        &lt;!&ndash; <span>姓名<em>*</em></span> &ndash;&gt;
        <input type="text" name='customer' id='customer' placeholder="姓名" />
      </label>
      <label>
        &lt;!&ndash; <span>手机号码<em>*</em></span> &ndash;&gt;
        <input type="text" name='phone' id='phone' maxlength="11" placeholder="手机号码" />
      </label>
      <label>
        &lt;!&ndash; <span>电子邮箱<em>*</em></span> &ndash;&gt;
        <input type="text" name='email' id='email' placeholder="电子邮箱" />
      </label>
      &lt;!&ndash; <label>
        <span>城市<em>*</em></span>
        <input type="text" name='city' id='city' placeholder="城市" />
      </label> &ndash;&gt;
      <label>
        &lt;!&ndash; <span>需求<em>*</em></span> &ndash;&gt;
        <input placeholder="请简述您的检测需求" name='content' type="text" class="input-big" id='content' />
      </label>
      <button id='submit'>提交</button>
    </div>
    <p>
      工程师2小时内与您联系(工作日:9:00-17:00)
    </p>
  </div>
</div>
</div>-->
  <!-- <div class="bg-black clearfix messageBox">
<div class="wrap">
  <label>
    <span>姓名<em>*</em></span>
    <input type="text" id='customer' />
  </label>
  <label>
    <span>手机号码<em>*</em></span>
    <input type="text" id='phone' maxlength="11" />
  </label>
  <label>
    <span>需求<em>*</em></span>
    <input placeholder="请简述您的检测需求" type="text" class="input-b" id='content' />
  </label>
  <button id='submit'>提交</button>
  <label class="content">
    <em class="con1">工程师<i>2</i>小时内与您联系</em>
    <em class="con2">(工作日:9:00-17:00)</em>
  </label>
</div>
</div> -->
</div>
<div class="pop" id='submitPop'>
  <div class="layout"></div>
  <div class="popBox">
    <h2 class="tit">
      提示
    </h2>
    <div class="cont">
      您的业务咨询已提交，我们会尽快与您取得联系！
    </div>
    <div class="btn">
      <span>确定</span>
    </div>
  </div>
</div>
<div class="pop" id='img'>
  <div class="layout"></div>
  <div class="imgBox">
    <img src="<%- locals.static %>/images/promotion/gaoFenZi/pic1.jpg" />
  </div>
  <div class="close" id='close'>X</div>
</div>
<script src="<%- locals.static %>/js/promotion/jquery.imgscroll.min.js"></script>
<% if(locals.env != 'prod'){ %>
<script src="<%- locals.static %>/plugin/v2.test.js"></script>
<% }else{ %>
<script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js" type="text/javascript"></script>
<% } %>
<script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>

<script>
  var newVue = new Vue({
    name: 'gaoFenZi',
    el: '#gaoFenZi',
    mixins: [quoteMixins],
    data: {
      form: {
        type: '高分子成分分析',
        trade: 98,
        tradeName: '工业制造与材料',
        service: 7,
        serviceName: '测试与分析',
        content: '',
        customer: '<%- userInfo.userName %>',
        email: '<%- userInfo.userEmail %>',
        phone: '<%- userInfo.userPhone %>',
        provice: '浙江省',
        company: 'sgs',
        os_type: 'pc',
        frontUrl: window.location.href,
        _csrf: '<%- csrf %>',
        imageCode: '',
        verifyCode: '',
      },
      showModal: false,
      seconds: 59,
      timer: null,
      loading: false,
      host: '<%- host %>',
      isLogin: <%- isLogin %>,
      pid: 'pid.mall',
      pcode: 'Z0zCnRE3IaY9Kzem',
      pageInfo: {
        title: '验证手机号',
        sendModel: '已发送到手机号：+86',
        prepend: '手机验证码'
      },
      approve: false,
      dialogVisibleReg: false,
    },
    methods: {
      handleClose: function () {
        this.showModal = false
        $("#submit").prop('disabled', false);
        this.form.verifyCode = ''
      },
      sing: function (param, timestamp) {
        var pmd5 = md5((this.pid + this.pcode).toUpperCase());
        var str = JSON.stringify(param) + pmd5.toUpperCase();
        return md5(str + timestamp);
      },
      handleSubmit: function () {
        var that = this
        var data = {
          type: '高分子成分分析',
          trade: '98',
          tradeName: '工业制造与材料',
          service: 7,
          serviceName: '测试与分析',
          content: $('#content').val(),
          customer: $('#customer').val().trim(),
          phone: $('#phone').val().trim(),
          // city: $('#city').val().trim(),
          email: $('#email').val().trim(), // 不能为空，不能为非法邮箱
          provice: '浙江省',
          company: 'sgs',
          os_type: 'pc',
          frontUrl: window.location.href,
          _csrf: '<%- csrf %>'
        }
        that.form.content = data.content
        that.form.customer = data.customer
        that.form.phone = data.phone
        that.form.email = data.email
        if (!$('#customer').val().trim()) {
          alert('请输入姓名！')
        } else if (!$('#phone').val().trim()) {
          alert('请输入手机号码！')
        } else if (!/^1\d{10}$/.test($('#phone').val().trim())) {
          alert('请输入正确的手机号码！')
        } else if (!$('#email').val().trim()) {
          alert('请输入电子邮箱')
        } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email').val().trim())) {
          alert('请输入正确电子邮箱')
        } else if (!$('#content').val()) {
          alert('请输入需求信息！')
        } else if (!this.approve) {
          alert('请同意隐私政策！')
        } else {
          if (!this.isLogin) {
            $("#submit1").prop('disabled', true);
            this.form.phone = $('#phone').val()
            this.showModal = true
          } else {
            this.handleModalSubmit({})
          }
        }
      },
      handleModalSubmit: function (loginRef) {
        this.form.verifyCode = loginRef.verifyCode
        var that = this
        var param = that.form
        var timestamp = new Date().valueOf();
        var url = '/submitTicket';
        param.verifyMode = this.tab
        loginRef.loading = true
        $.ajax({
          type: 'POST',
          url: url,
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: this.pid,
            sign: that.sing(param, timestamp),
            timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            that.loading = false
            loginRef.loading = false
            if (res.resultCode === '0') {
              Cookies.set("SSO_TOKEN", res.data.token, { domain: ".sgsmall.com.cn", expires: 7 });
              that.showModal = false
              $('#submitPop').show();
              $("#submit1").prop('disabled', false);
            } else if(res.resultCode === '9978') {
              Cookies.remove('SSO_TOKEN', { domain: '.sgsmall.com.cn' })
              that.isLogin = false
              that.showModal = true
            } else {
              that.$message.error(res.resultMsg)
            }
          },
          fail: function (data) {
            loginRef.loading = false
            that.loading = false
            that.$message.error(res.resultMsg)
          },
          complete: function (complete) {
          }
        })
      },
    },
    mounted: function () {
      if (this.isLogin) {
        if ($("#phone").val()) $("#phone").attr('disabled', true)
        $("#phone").val(this.form.phone)
        $("#email").val(this.form.email)
        $("#customer").val(this.form.customer)
      }
    }
  });
  $(function () {
    // advantage 关键优势区域数字动画
    var advantageFirst = true
    function advantageAnimate() {
      advantageFirst = false
      var box1 = $("#advantage li em").eq(0);
      var min1 = 0;
      var max1 = box1.data('max');
      var box2 = $("#advantage li em").eq(1);
      var min2 = 0;
      var max2 = box2.data('max');
      var box3 = $("#advantage li em").eq(2);
      var min3 = 0;
      var max3 = box3.data('max');
      var box4 = $("#advantage li em").eq(3);
      var min4 = 0;
      var max4 = box4.data('max');
      setInterval(function () {
        if (min1 < max1) {
          min1 = min1 + 1
          box1.html(min1)
        }
      }, 10)

      setInterval(function () {
        if (min2 < max2) {
          min2 = min2 + 2
          box2.html(min2)
        }
      }, 10)
      setInterval(function () {
        if (min3 < max3) {
          min3 = min3 + 200
          box3.html(min3)
        }
      }, 10)
      setInterval(function () {
        if (min4 < max4) {
          min4 = min4 + 2
          box4.html(min4)
        }
      }, 10)
    }
    $('.pop .layout').css({
      width: $('html,body').width(),
      height: $('html,body').height()
    })
    function changePopImg(time) {
      return setTimeout(function () {
        $('.pop-im').show();
      }, time)
    }
    // 去掉弹窗效果
    // var popImgTimer = changePopImg(15*1000)
    $('.pop-im img').on('click', function () {
      $('#kf5-support-btn').trigger('click');
    });
    $('.pop-im .close').on('click', function () {
      $('.pop-im').hide();
      // if (!judgeFormScrollHeight()) {
      //   popImgTimer = changePopImg(20*1000)
      // }
    });
    function judgeFormScrollHeight() { // 判断表单是否在可视区域
      var formHeight = 435; // 表单区域高度
      var clientHeight = $(window).height();
      var scrollTop = $(window).scrollTop()
      var formScrollHeight = {
        min: 4920 - (clientHeight - formHeight),
        max: 4920 + formHeight
      }
      return scrollTop >= formScrollHeight.min && scrollTop <= formScrollHeight.max
    }
    $(window).scroll(function () {
      // $('.detailNav').removeClass('fix');
      var scrollTop = $(window).scrollTop()
      if (scrollTop >= 3000) {
        if (advantageFirst) {
          advantageAnimate()
        }
      }
      // if (judgeFormScrollHeight() && popImgTimer) {
      //   clearTimeout(popImgTimer)
      //   popImgTimer = null
      // } else {
      //   if ($('.pop-im').css('display') === 'none' && !popImgTimer) {
      //     // popImgTimer = changePopImg(20*1000)
      //   }
      // }
    })
    $('.pop .btn').on('click', function () {
      $('.pop').hide();
      $('#customer').val('');
      $('#phone').val('');
      // $('#city').val('');
      $('#email').val('');
      $('#content').val('');
    });
    // $('.banner-bottom .link-text').on('click', function () {
    //   $('#kf5-support-btn').trigger('click');
    // })
    // 行业服务
    var services = [
      {
        title: '塑料',
        img: '<%- locals.static %>/images/promotion/gaoFenZi/service/pic1.jpg',
        itemTitle: '检测材质',
        items: [
          {
            item: '通用塑料',
            content: 'PE、PP、EEA、EVA、PVC等'
          },
          {
            item: '通用工程塑料',
            content: 'PS、HIPS、ABS、AAS、ACS、MBS、AS、BS、PMMA等'
          },
          {
            item: '结构工程塑料',
            content: 'PA、POM、NORYL、PC、PET、PBT等'
          },
          {
            item: '耐高温工程塑料',
            content: 'PPO、PPS、PSF、PAR等'
          },
          {
            item: '塑料合金',
            content: 'PC-ABS、PC-PRB、PC-PMMA等'
          },
          {
            item: '热塑性弹性体',
            content: 'TPR、TPU、TPE等'
          }
        ],
        qaTitle: '我们能为您解决哪些问题？',
        qaItem: '耐热、耐低温性能差、耐试剂性能差;塑化时间长、泡孔大，泡孔不均匀、易析出;摩擦性不够、耐热性阻燃性不够;加工困难、着色性差、应力变形等;想了解树脂是哪类树脂，助剂是哪类助剂;需要检测塑料物理性能，热学、老化等性能;对塑料原料和制品成分分析，掌握材料成分'
      },
      {
        title: '橡胶',
        img: '<%- locals.static %>/images/promotion/gaoFenZi/service/pic2.jpg',
        itemTitle: '检测材质',
        items: [
          {
            item: '合成橡胶',
            content: '丁苯橡胶SBR、丁基橡胶IIR、顺丁橡胶BR、异戊橡胶IR、乙丙橡胶EPM\EPDM、氯丁橡胶CR等'
          },
          {
            item: '特种橡胶',
            content: '丁腈橡胶NBR、氯化丁腈橡胶HNBR、氟橡胶EPM、聚氨酯橡胶AU\EU、硅橡胶Q等'
          },
          {
            item: '结构工程塑料',
            content: 'PA、POM、NORYL、PC、PET、PBT等'
          }
        ],
        qaTitle: '我们能为您解决哪些问题？',
        qaItem: '抗冷热变化能力弱，受到冷热冲击后容易产生裂缝，防潮能力差;硫化速度慢、使用寿命短;密封性/回弹性不好、密封失效、易喷霜龟裂、导电率不够等;改善配方中特定有效成分，提升产品专项或者综合性能，开拓研发思路、缩短研发周期'
      },
      {
        title: '天然乳胶',
        img: '<%- locals.static %>/images/promotion/gaoFenZi/service/pic3.jpg',
        itemTitle: '检测材质',
        items: [
          {
            item: '',
            content: '纯天然乳胶'
          },
          {
            item: '',
            content: '合成天然乳胶'
          },
          {
            item: '',
            content: '竹炭天然乳胶'
          },
          {
            item: '',
            content: '负离子天然乳胶'
          },
          {
            item: '',
            content: '其他类型天然乳胶制品'
          }
        ],
        qaTitle: '我们能为您解决哪些问题？',
        qaItem: '异味很大，让人产生不愉快的味道;回弹性、抗压缩性能不好;想了解产品的配方，确定是否是纯天然乳胶;项了解产品是否有释放负离子功效，是否具有抗菌、防螨性能;项了解产品是否有吸附甲醛、甲苯有害气体等功效'
      },
      {
        title: '胶粘剂',
        img: '<%- locals.static %>/images/promotion/gaoFenZi/service/pic4.jpg',
        itemTitle: '检测材质',
        items: [
          {
            item: '环氧树脂粘合剂',
            content: '密封胶、电子胶黏剂、建筑胶、防腐胶黏剂、锚固剂、结构胶'
          },
          {
            item: '聚氨酯粘合剂',
            content: '多异氰酸酯粘合剂、端异氰酸酯基聚氨酯预聚体粘合剂、端羟基聚氨酯粘合剂、聚氨酯树脂粘合剂'
          },
          {
            item: '丙烯酸酯粘合剂',
            content: '氰基丙烯酸酯胶、改性丙烯酸酯胶、紫外光固化胶、502胶、AB胶等'
          },
          {
            item: '其他粘合剂',
            content: 'UV 粘合剂、有机硅粘合剂、聚酰亚胺粘合剂、呋喃树脂粘合剂等'
          }
        ],
        qaTitle: '我们能为您解决哪些问题？',
        qaItem: '粘接强度低、柔韧性不好、固化速度、附着力差、耐黄变性能差附着力;柔韧性差、流挂性、触变性差，使用寿命短;附着力差，脆性、耐黄变性能、流平性差;耐水性差，涂布干燥速度慢，环保不达标;抗冷热变化能力弱，防潮能力差、防霉性能差、抗菌性能差	'
      },
      {
        title: '涂料',
        img: '<%- locals.static %>/images/promotion/gaoFenZi/service/pic5.jpg',
        itemTitle: '检测材质',
        items: [
          {
            item: '丙烯酸酯涂料',
            content: '水性涂料、高固体组分涂料、溶剂型涂料、粉末涂料'
          },
          {
            item: '聚氨酯涂料',
            content: '湿固化的聚氨酯涂料、封闭型异氰酸酯烘干涂料、催化固化聚氨酯涂料、羟基固化性双组份聚氨酯涂料、水性聚氨酯涂料、高固体分聚氨酯涂料'
          },
          {
            item: '环氧树脂涂料',
            content: '粉末涂料、环氧地坪漆'
          },
          {
            item: '其他涂料',
            content: '氨基树脂涂料、不饱和聚酯漆、有机硅涂料、功能性涂料表面涂装材料'
          }
        ],
        qaTitle: '我们能为您解决哪些问题？',
        qaItem: '附着力、平滑性、弹性、强度性不好，耐水性、干燥速度差;耐溶剂性、光泽度、涂膜硬度、流平性、柔软性及爽滑性差;固化速度、柔韧性、粘结性、耐黄变性能差;湿布擦洗后有痕迹，耐久性不好，易泛黄;防火性、阻燃性、抑烟性，色彩度不好'
      },
      {
        title: '油墨',
        img: '<%- locals.static %>/images/promotion/gaoFenZi/service/pic6.jpg',
        itemTitle: '检测材质',
        items: [
          {
            item: '丝印油墨类常见产品',
            content: '玻璃保护油墨、导电油墨、印刷版油墨、橡胶油墨、蚀刻油墨、其他'
          },
          {
            item: '凹印油墨',
            content: '凹版表印油墨、凸版表印油墨、玻璃油墨、平板油墨、UV油墨、其他'
          },
          {
            item: ' 喷涂油墨',
            content: ' 导电油墨、印花墨水、喷码墨水、其他'
          },
          {
            item: ' 笔用墨水',
            content: '其他'
          }
        ],
        qaTitle: '我们能为您解决哪些问题？',
        qaItem: '耐酸碱性不好，脱落不够彻底，分散均一性差;印网有很大的残留，不彻底，分散性差;分散性差、颜料的种类及粒径;附着力差;溶剂残留高、气味很大、异味、耐热性差;不能耐高温蒸煮、印刷刮不干净，油墨增粘'
      },
      {
        title: '油',
        img: '<%- locals.static %>/images/promotion/gaoFenZi/service/pic7.jpg',
        itemTitle: '检测材质',
        items: [
          {
            item: '特种机械润滑油',
            content: '齿轮油、抗磨耐压油、真空泵油、白油、润滑脂、导热油、变压器油等'
          },
          {
            item: '金属加工油',
            content: '乳化切削液、油性切削液、水性研磨液、铜拉丝油、防锈油、电火花加工油等'
          },
          {
            item: '润滑油',
            content: '合成润滑脂、合成空气压缩机油、合成齿轮油、食品级润滑脂、硅脂、高级硅油等'
          },
          {
            item: '防锈油',
            content: '气相防锈油、气相防锈剂、润滑防锈油、硬膜防锈油、脱水防锈油、快干防锈油等'
          },
          {
            item: '食用油',
            content: '食用油、成品油、合成食品油等'
          }
        ],
        qaTitle: '我们能为您解决哪些问题？',
        qaItem: '想了解产品的有效成分配比;想了解产品是否对制件有腐蚀作用;润滑性不够、抗乳化性较差、剪切安定性较差;清洁度不好、寿命短;酸值较大、溶胀制件'
      },
      {
        title: '精细化学品',
        img: '<%- locals.static %>/images/promotion/gaoFenZi/service/pic8.jpg',
        itemTitle: '检测材质',
        items: [
          {
            content: '医药、农药、染料、颜料、信息技术用化学品（包括光感材料、磁记录材料等）、化学试剂和高纯物质、食品添加剂、饲料添加剂、催化剂、助剂、表面活性剂、香料等。',
            item: ''
          }
        ],
        qaTitle: '我们能为您解决哪些问题？',
        qaItem: '想了解某种药物、农药的成分及含量;想了解某种香料、食品添加剂的成分及含量;纺织助剂脱脂不干净、乳化、去污、增白效;果差，渗透、润湿性能不好;染料附着力、平滑性、弹性、强度性不好;耐水性、干燥速度差等;助剂在配方中的配伍性问题，成本高等'
      },
      {
        title: '无机物',
        img: '<%- locals.static %>/images/promotion/gaoFenZi/service/pic9.jpg',
        itemTitle: '检测材质',
        items: [
          {
            item: '单质',
            content: '金属单质（钠、钙、铁、铜、锌、镍、镁、铝等）、非金属单质（碳、硅、磷、硫、氢气、二氧化碳等）'
          },
          {
            item: '无机化合物',
            content: '氧化物（三氧化硫、五氧化二磷、一氧化碳、氧化铁、氧化镁、氧化硫等）、无机酸（硫酸、盐酸、硝酸等）、碱（氢氧化钙、氢氧化钠等）、无机盐（氯化钠、碳酸钙、硫酸钠等）'
          },
          {
            item: '助剂未知物',
            content: '各化工行业助剂未知物分析'
          }
        ],
        qaTitle: '我们能为您解决哪些问题？',
        qaItem: '产品需要改进性能，以提高市场占有率;检测产品质量是否合格;需要了解某些物质的组成成分与含量;想研发更优的产品，帮您解决出现的技术问  题或周期问题;产品出现失效、异物等问题'
      },
      {
        title: '未知物',
        img: '<%- locals.static %>/images/promotion/gaoFenZi/service/pic10.jpg',
        itemTitle: '检测材质',
        items: [
          {
            item: '化学未知物',
            content: '清洗剂、化妆品、胶粘剂、油墨、涂料等'
          },
          {
            item: '材料未知物',
            content: '医药包材、PVC管材、密封件、建材等'
          },
          {
            item: '助剂未知物',
            content: '可提供各化工行业助剂未知物分析'
          }
        ],
        qaTitle: '我们能为您解决哪些问题？',
        qaItem: '产品里出现了不明物不知道怎么办;得到一个产品不知道什么;有种原材料不知道什么;怀疑未知异物导致产品失效，想明确原因;想确定未知物（油渍、颗粒、粉末等）的成  分和来源'
      },
      {
        title: '其他',
        img: '<%- locals.static %>/images/promotion/gaoFenZi/service/pic11.jpg',
        itemTitle: '检测材质',
        items: [
          {
            item: '',
            content: '各类高分子材料'
          },
          {
            item: '',
            content: '建筑助剂、纺织印染助剂'
          },
          {
            item: '',
            content: '表面处理、电镀添加剂'
          },
          {
            item: '',
            content: '脱模剂、造纸助剂'
          },
          {
            item: '',
            content: '表面活性剂'
          },
          {
            item: '',
            content: '生物医药'
          },
          {
            item: '',
            content: '清洗剂'
          }
        ],
        qaTitle: '我们能为您解决哪些问题？',
        qaItem: '需要了解不明物质（粉体、颗粒等）是什么;产品表面出现斑点、异物、析出物等，需要  寻找根源，解决问题;想了解产品纯度，材质是什么;需要了解某些物质的成分组成、元素、离子等含量;研发实力不足，或研发进度较慢'
      }
    ];
    var service = $("#service");
    var serviceTabDom = '';
    for (var i = 0; i < services.length; i++) {
      serviceTabDom += '<li>' +
        '<i class="icon' + (i + 1) + '"></i>' +
        '<span>' + services[i].title + '</span>' +
        '</li>';
    }
    service.find('.tab ul').append(serviceTabDom);
    service.find('.tab li').eq(0).addClass("active");
    service.find('.tab li').mousemove(function () {
      var index = $(this).index()
      $(this).addClass('active').siblings().removeClass('active')
      $('.service-card ul').find('li').eq(index).show().siblings().hide();
    });
    var serviceCardDom = '';
    for (var i = 0; i < services.length; i++) {
      serviceCardDom += '<li>' +
        '<div class="left">' +
        '<p>' + services[i].title + '</p>' +
        '<img src="' + services[i].img + '" />' +
        '</div>' +
        '<div class="middle">' +
        '<p>' + services[i].itemTitle + '</p>' +
        '<dl>';
      for (var j = 0; j < services[i].items.length; j++) {
        serviceCardDom += '<dd>';
        if (services[i].items[j].item) {
          serviceCardDom += '<span>' + services[i].items[j].item + '：</span>';
        }
        serviceCardDom += '<em>' + services[i].items[j].content + '</em>' +
          '</dd>';
      }
      if (i === services.length - 1) {
        serviceCardDom += '<span class="serviceBtn handleIMtool">点击咨询更多材质</span>';
      }
      serviceCardDom += '</dl>' +
        '</div>' +
        '<div class="right">' +
        '<p>' + services[i].qaTitle + '</p><div class="items">';
      var qaItems = services[i].qaItem.split(';');
      for (var k = 0; k < qaItems.length; k++) {
        serviceCardDom += '<i>·</i><span>' + qaItems[k] + '</span><br />';
      }
      serviceCardDom += '</div>';
      if (i === services.length - 1) {
        serviceCardDom += '<span class="serviceBtn handleIMtool">点击咨询更多问题</span>';
      } else {
        serviceCardDom += '<span class="serviceBtn handleIMtool">咨询客服</span>';
      }
      serviceCardDom += '</div>' +
        '</li>';
    }
    $('.service-card ul').append(serviceCardDom)
    $('.service-card ul').find('li').eq(0).show();
    // $('.service-card').find('.serviceBtn').on('click', function () {
    //   $('#kf5-support-btn').trigger('click');
    // });
    // onclick="$('#kf5-support-btn').click();"
    // 应用领域
    /*var territory = $('#territory ul');
    var territoryDom = '';
    var territorys = [
      {
        title: '原料质控',
        des: '确定供应商供货是否符合材料指标要求；筛选优质供应商或供货渠道；对高分子原材料和制品成分进行分析，掌握材料成分'
      },
      {
        title: '配方分析',
        des: '掌握高分子材料或者产品配方；模仿生产高分子热门产品；改善配方中特定有效成分，提升产品专项或者综合性能，开拓研发思路、缩短研发周期'
      },
      {
        title: '性能改进',
        des: '对比分析目标样品和自身产品的差异，提升改进产品性能，降低原料成本；产品性能不达标，针对样品进行分析，配方改进，提高产品性能。'
      },
      {
        title: '工业诊断',
        des: '产品不合格时，可通过成分分析，确定问题是由于配方比例不准确，还是生产过程中混入了一些杂质，了解产品生产过程中的失效、异物、析出等问题，最终制定解决方案。'
      },
      {
        title: '买家要求',
        des: '根据客户提供的国标、美标、ISO标准等，利用大型仪器检测，出具报告，解决企业产品在研发、生产、商检等过程中的检测需求。'
      },
      {
        title: '商城入驻、环评、竞标、销售，出口等',
        des: '检测产品中是否含有有毒有害、致病致癌等物质；测试报告加盖CNAS/CMA等资质章，可用于入驻淘宝、天猫、京东等商城；可参与环评、竞标、投标、销售，出口等。'
      }
    ];
    for (var i = 0; i < territorys.length; i++) {
      territoryDom += '<li>' +
        '<h2>' + territorys[i].title + '</h2>' +
        '<p>' + territorys[i].des + '</p>' +
        '<div>' +
        '<span class="serviceBtn">了解详情</span>' +
        '</div>' +
        '</li>';
    }
    territory.append(territoryDom);
    territory.find('.serviceBtn').on('click', function () {
      $('#kf5-support-btn').trigger('click');
    });*/
    // 测试项目
    var testItem = $('#testItem tbody');
    var testItemDom = '';
    var testItems = [
      {
        title: '性能检测',
        link: '/sku/product/141',
        detail: '理化性能、物理性能、化学性能、光学性能、环保性能、热学性能、可靠性能、电学性能、阻燃性能等'
      },
      {
        title: '元素离子检测',
        link: '/sku/product/193',
        detail: '金属元素、非金属元素、重金属、贵金属、离子、元素、稀土元素、放射性元素、营养元素硒等'
      },
      {
        title: '理化性能检测',
        link: '',
        detail: '纯度、浓度、硬度、水分、灰分、PH值、比热容、导热系数、粘度、闪点、导热率、粘度、细度、外观等'
      },
      {
        title: '物理性能检测',
        link: '/sku/product/287',
        detail: '尺寸、密度、硬度、穿刺性能、水蒸气透过率、耐磨、蠕变、拉伸心梗、弯曲性能、冲击性能、压缩性能、剪切、摩擦、剥离强度等'
      },
      {
        title: '可靠性能检测',
        link: '/sku/product/283',
        detail: '湿热循环、高低温循环、 碎石冲击、 震动试验、跌落试验、防尘防水试验、电学性能、快速变试验、冷热冲击试验、冰水冲击、落球冲击试验、防护等级、滚筒跌落、按键疲劳、插拔疲劳、寿命测试、降解试验等'
      },
      {
        title: '老化性能检测',
        link: '/sku/product/116',
        detail: '盐雾试验(中性盐雾、酸性盐雾、交变盐雾及铜离子加速盐雾)、热空气老化、紫外老化、氙灯老化、臭氧老化、碳弧灯老化、户外曝露试验、高温老化、低温老化、 SO2/SO3老化试验、H2S/SO2老化试验、耐试剂等'
      },
      {
        title: '热学性能检测',
        link: '',
        detail: '熔融指数、热变形温度、维卡软化点、玻璃化转变温度、熔点、热失重温度、低温脆化温度、线性热膨胀系数、导热性能等'
      },
      {
        title: '有害物质检测',
        link: '/sku/product/216',
        detail: '微生物、甲醛、重金属、ROHS、REACH检测、三聚氰胺、PAHs、增塑剂、卤素、石棉、VOC、TVOC等'
      },
      {
        title: '阻燃防火测试',
        link: '/sku/product/289',
        detail: '阻燃性能、防火性能、极限氧指数、垂直燃烧、水平燃烧、45°燃烧、CTI/PTI、灼热丝测试等'
      }
    ];
    for (var i = 0; i < testItems.length; i++) {
      testItemDom += '<tr>';
      if (testItems[i].link) {
        testItemDom += '<th><a target="_blank" href="' + testItems[i].link + '">' + testItems[i].title + '</a></th>';
        testItemDom += '<td><a target="_blank" href="' + testItems[i].link + '">' + testItems[i].detail + '</a></td></tr>';
      } else {
        testItemDom += '<th>' + testItems[i].title + '</th>';
        testItemDom += '<td>' + testItems[i].detail + '</td></tr>';
      }
    }
    testItem.append(testItemDom)
    // $('#testItem').find('.serviceBtn').on('click', function () {
    //   $('#kf5-support-btn').trigger('click');
    // });
    // 证书
    /*var certificate = $('#certificate ul');
    var certificates = [
      '<%- locals.static %>/images/promotion/gaoFenZi/certificate/1.jpg',
      '<%- locals.static %>/images/promotion/gaoFenZi/certificate/2.jpg',
      '<%- locals.static %>/images/promotion/gaoFenZi/certificate/3.jpg',
      '<%- locals.static %>/images/promotion/gaoFenZi/certificate/4.jpg',
      '<%- locals.static %>/images/promotion/gaoFenZi/certificate/5.jpg',
      '<%- locals.static %>/images/promotion/gaoFenZi/certificate/6.jpg',
      '<%- locals.static %>/images/promotion/gaoFenZi/certificate/7.jpg',
      '<%- locals.static %>/images/promotion/gaoFenZi/certificate/8.jpg',
      '<%- locals.static %>/images/promotion/gaoFenZi/certificate/9.jpg'
    ];
    var certificateDom = '';
    for (var i = 0; i < certificates.length; i++) {
      certificateDom += '<li>' +
        '<img src="' + certificates[i] + '" />' +
        '</li>';
    }
    certificate.append(certificateDom)
    imgScroll.rolling({
      name: 'certificate',
      width: '163px',
      height: '230px',
      direction: 'left',
      speed: 10,
      addcss: true
    });
    certificate.find('img').on("click", function () {
      var src = $(this).attr('src')
      $("#img").find('img').attr('src', src);
      $("#img").show();
      setTimeout(function () {
        certificate.find('img').eq(0).trigger('mouseenter')
      }, 200);
    });
    $("#close").on('click', function () {
      $("#img").hide();
      certificate.find('img').eq(0).trigger('mouseleave')
    });
    //  服务流程
    $('#step .item').hover(function () {
      $(this).addClass('acitve')
    }, function () {
      $(this).removeClass('acitve')
    })*/
    // 案例集锦
    var caseFlag = 0;
    $("#case .tab li").hover(function () {
      var index = $(this).index()
      $("#case .box").animate({
        top: -index * 468
      }, 400)
      $(this).addClass('active').siblings().removeClass('active');
      // $('#case .card .item').eq(index).show().siblings().hide();
    }, function () { });
    // 自动轮播动画
    var caseInterval = setInterval(function () {
      if (caseFlag < 2) {
        caseFlag++
      } else {
        caseFlag = 0
      }
      $("#case .tab li").eq(caseFlag).trigger('mouseenter').trigger('mouseleave');
    }, 1000);
    $("#case").hover(function () {
      clearInterval(caseInterval)
    }, function () {
      caseInterval = setInterval(function () {
        if (caseFlag < 2) {
          caseFlag++
        } else {
          caseFlag = 0
        }
        $("#case .tab li").eq(caseFlag).trigger('mouseenter').trigger('mouseleave');
      }, 5000);
    });
    // $('#case').find('.serviceBtn').on('click', function () {
    //   $('#kf5-support-btn').trigger('click');
    // });
    // 实验室设备仪器
    /*var product = $('#product .productBox');
    var productDom = '';
    var products = [
      [
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/1.jpg',
          text: '傅里叶变换红外光谱仪（FTIR)'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/2.jpg',
          text: '显微红外光谱仪(FTIR-Micro)'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/3.jpg',
          text: '紫外-可见光分光光度计(UV-Vis)'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/4.jpg',
          text: '能量色散型X射线荧光光谱仪(EDX)'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/5.jpg',
          text: '电感耦合等离子光谱仪(ICP-OES)'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/6.jpg',
          text: '裂解气相质谱联用仪(PyGCMS)'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/7.jpg',
          text: '马弗炉(Muffle)'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/8.jpg',
          text: '差热扫描量热仪(DSC)'
        }
      ], [
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/9.jpg',
          text: '热重分析仪(TGA)'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/10.jpg',
          text: '动态热机械分析仪(DMA)'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/11.jpg',
          text: '热机械分析仪(TMA)'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/12.jpg',
          text: '落锤冲击试验机'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/13.jpg',
          text: '核磁共振波谱仪（NMR）'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/14.jpg',
          text: '凝胶渗透色谱仪（GPC）'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/15.jpg',
          text: '摆锤冲击仪'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/16.jpg',
          text: '电子万能试验机'
        }
      ], [
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/17.jpg',
          text: '气体透过率测试系统'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/18.jpg',
          text: '熔体流动速率仪'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/19.jpg',
          text: '水平垂直燃烧试验箱'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/20.jpg',
          text: '热风老化箱'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/21.jpg',
          text: '接触角测试仪'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/22.jpg',
          text: '毛细管粘度计'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/23.jpg',
          text: 'ATLAS-Ci5000氙灯老化试验箱'
        },
        {
          img: '<%- locals.static %>/images/promotion/gaoFenZi/product/24.jpg',
          text: '盐雾试验箱'
        }
      ],
    ];
    for (var i = 0; i < products.length; i++) {
      productDom += '<div>' +
        '<ul>';
      for (var j = 0; j < products[i].length; j++) {
        productDom += '<li>' +
          '<img src="' + products[i][j].img + '" />' +
          '<span>' + products[i][j].text + '</span>' +
          '</li>';
      }
      productDom += '</ul>' +
        '</div>';
    }
    product.append(productDom);
    $('#product .productBtn span').mouseenter(function () {
      var index = $(this).index();
      $(this).addClass('active').siblings().removeClass('active')
      product.animate({
        left: -1226 * index
      }, 500)
    });*/
    // 自动轮播动画
    var productFlag = 0
    var productInterval = setInterval(function () {
      if (productFlag < 2) {
        productFlag++
      } else {
        productFlag = 0
      }
      $('#product .productBtn span').eq(productFlag).trigger('mouseenter').trigger('mouseleave');
    }, 1000);
    $("#product").hover(function () {
      clearInterval(productInterval)
    }, function () {
      productInterval = setInterval(function () {
        if (productFlag < 2) {
          productFlag++
        } else {
          productFlag = 0
        }
        $('#product .productBtn span').eq(productFlag).trigger('mouseenter').trigger('mouseleave');
      }, 5000);
    });
    // 更多服务推荐
    var moreService = $('#moreService ul');
    var moreServiceDom = '';
    var moreServices = [
      {
        img: '<%- locals.static %>/images/promotion/gaoFenZi/moreService/1.jpg',
        title: '高分子材料测试服务',
        des: '塑料、橡胶、涂料、胶黏剂等材料测试',
        link: '/sku/product/141'
      },
      {
        img: '<%- locals.static %>/images/promotion/gaoFenZi/moreService/2.jpg',
        title: '燃烧测试',
        des: '塑料、橡胶、涂料、胶黏剂的燃烧测试',
        link: '/sku/product/289'
      },
      {
        img: '<%- locals.static %>/images/promotion/gaoFenZi/moreService/3.jpg',
        title: '高分子材料失效分析',
        des: '高效客观地识别失效原因，改善产品质量',
        link: '/sku/product/103'
      },
      {
        img: '<%- locals.static %>/images/promotion/gaoFenZi/moreService/4.jpg',
        title: '高分子材料物理性能测试',
        des: '机械性能、电学性能、光学性能测试',
        link: '/sku/product/287'
      }
    ];
    for (var i = 0; i < moreServices.length; i++) {
      moreServiceDom += '<li>' +
        '<a href="' + moreServices[i].link + '" target="_blank">' +
        '<img src="' + moreServices[i].img + '" />' +
        '<h2>' + moreServices[i].title + '</h2>' +
        '<p>' + moreServices[i].des + '</p>' +
        '</a>' +
        '</li>';
    }
    moreService.append(moreServiceDom)
    // 提交表单
    $(".messageBox input").on('focus', function () {
      $(this).addClass('focus')
    })
    $(".messageBox input").on('blur', function () {
      $(this).removeClass('focus')
    })
    $("#submit1").on("click", function () {
      var data = {
        type: '高分子成分分析',
        trade: '98',
        tradeName: '工业制造与材料',
        service: 7,
        serviceName: '测试与分析',
        content: $('#content').val(),
        customer: $('#customer').val().trim(),
        phone: $('#phone').val().trim(),
        // city: $('#city').val().trim(),
        email: $('#email').val().trim(), // 不能为空，不能为非法邮箱
        provice: '浙江省',
        company: 'sgs',
        os_type: 'pc',
        frontUrl: window.location.href,
        _csrf: '<%- csrf %>'
      }
      if (!$('#customer').val().trim()) {
        alert('请输入姓名！')
      } else if (!$('#phone').val().trim()) {
        alert('请输入手机号码！')
      } else if (!/^1\d{10}$/.test($('#phone').val().trim())) {
        alert('请输入正确的手机号码！')
      } else if (!$('#email').val().trim()) {
        alert('请输入电子邮箱')
      } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email').val().trim())) {
        alert('请输入正确电子邮箱')
      } else if (!$('#content').val()) {
        alert('请输入需求信息！')
      } else if (!$('#check').prop("checked")) {
        alert('请同意隐私政策！')
      } else {
        $("#submit1").prop('disabled', true);
        $.ajax({
          url: "/ticket/post",
          type: 'POST',
          data: data,
          headers: {
            frontUrl: window.location.href
          },
          success: function (res) {
            if (res.success) {
              $('#submitPop').show();
              $("#submit1").prop('disabled', false);
            } else {
              alert(res.data)
              $("#submit1").prop('disabled', false);
            }
          },
          fail: function (e) {
          }
        })
      }
    })
    $('#submit .btn').on('click', function () {
      $('#submit').hide();
      $('#customer').val('');
      $('#phone').val('');
      $('#content').val('');
    });
    // 测试现场旋转木马轮播
    //arr数组存放图片样式的JSON
    var arr = [
      {   //  1
        width: 300,
        top: 70,
        left: 150,
        opacity: 20,
        zIndex: 2
      },
      {  // 2
        width: 400,
        top: 120,
        left: 100,
        opacity: 80,
        zIndex: 3
      },
      {   // 3
        width: 600,
        top: 100,
        left: 300,
        opacity: 100,
        zIndex: 4
      },
      {  // 4
        width: 400,
        top: 120,
        left: 700,
        opacity: 80,
        zIndex: 3
      },
      {   //5
        width: 300,
        top: 70,
        left: 750,
        opacity: 20,
        zIndex: 2
      },
      {   //6
        width: 200,
        top: 100,
        left: 400,
        opacity: 0,
        zIndex: 2
      }
    ];
    //获取元素
    var slide = document.getElementById("slide");
    var liArr = slide.getElementsByTagName("li");
    var arrow = slide.children[1];
    var btn = arrow.children;
    //创建一个flag变量，开闭原则，为了再点击按钮完成所有样式之后才可以继续点击
    var flag = true;

    //鼠标移入时左右按钮显示，离开隐藏
    slide.onmouseenter = function () {
      animate(arrow, { "opacity": 100 });
    };
    slide.onmouseleave = function () {
      animate(arrow, { "opacity": 0 });
    };

    //页面加载样式
    /*	for(var i=0;i<liArr.length;i++){
            //利用数组中的JSON语句，给图片设置样式
            animate(liArr[i],{
                "width":arr[i].width,
                "top":arr[i].top,
                "left":arr[i].left,
                "opacity":arr[i].opacity,
                "zIndex":arr[i].zIndex
            });
        }*/
    //可以使用move函数直接给图片设置样式
    move();

    //左右切换图片
    //点击左侧按钮，将第一个样式删除放到数组的最后位置
    //点击右侧按钮，将最后一个样式删除放在数组的最前面
    //使用for...in为按钮绑定事件，或者单独绑定
    for (var k in btn) {
      btn[k].onclick = function () {
        //判断按钮是哪一个，
        //如果按钮的className是prev，为move传递一个布尔值为true的参数，表示他是左侧；
        //反之传递false，表示他是右侧按钮
        if (this.className === "prev") {
          if (flag) {
            //点击之后将flag设置为false，这样在这个样式完全显示之前点击按钮没有变化
            flag = false;
            move(true);
          }
        } else {
          if (flag) {
            flag = false;
            move(false);
          }
        }
      };
    }
    //创建函数
    function move(bool) {
      if (bool === true || bool === false) {
        if (bool) {
          //上一张，将第一个样式删除放到数组的最后位置
          arr.push(arr.shift());
        } else {
          //下一张，将最后一个样式删除放在数组的最前面
          arr.unshift(arr.pop());
        }
      }
      //之后再次给图片设置样式
      for (var i = 0; i < liArr.length; i++) {
        //利用数组中的JSON语句，给图片设置样式
        animate(liArr[i], arr[i], function () {
          flag = true;
        });
        /*animate(liArr[i],{
            "width":arr[i].width,
            "top":arr[i].top,
            "left":arr[i].left,
            "opacity":arr[i].opacity,
            "zIndex":arr[i].zIndex
        },function(){
            //在这里将flag设置为true
            flag = true;
        });*/
      }
    };

    function getStyle(ele, attr) {
      if (window.getComputedStyle) {
        return window.getComputedStyle(ele, null)[attr];
      }
      return ele.currentStyle[attr];
    }

    //参数变为3个
    //参数变为3个
    function animate(ele, json, fn) {
      //先清定时器
      clearInterval(ele.timer);
      ele.timer = setInterval(function () {
        //开闭原则
        var bool = true;


        //遍历属性和值，分别单独处理json
        //attr == k(键)    target == json[k](值)
        for (var k in json) {
          //四部
          var leader;
          //判断如果属性为opacity的时候特殊获取值
          if (k === "opacity") {
            leader = getStyle(ele, k) * 100 || 1;
          } else {
            leader = parseInt(getStyle(ele, k)) || 0;
          }

          //1.获取步长
          var step = (json[k] - leader) / 10;
          //2.二次加工步长
          step = step > 0 ? Math.ceil(step) : Math.floor(step);
          leader = leader + step;
          //3.赋值
          //特殊情况特殊赋值
          if (k === "opacity") {
            ele.style[k] = leader / 100;
            //兼容IE678
            ele.style.filter = "alpha(opacity=" + leader + ")";
            //如果是层级，一次行赋值成功，不需要缓动赋值
            //为什么？需求！
          } else if (k === "zIndex") {
            ele.style.zIndex = json[k];
          } else {
            ele.style[k] = leader + "px";
          }
          //4.清除定时器
          //判断: 目标值和当前值的差大于步长，就不能跳出循环
          //不考虑小数的情况：目标位置和当前位置不相等，就不能清除清除定时器。
          if (json[k] !== leader) {
            bool = false;
          }
        }

        //console.log(1);
        //只有所有的属性都到了指定位置，bool值才不会变成false；
        if (bool) {
          clearInterval(ele.timer);
          //所有程序执行完毕了，现在可以执行回调函数了
          //只有传递了回调函数，才能执行
          if (fn) {
            fn();
          }
        }
      }, 25);
    };
    function autoMove() {
      return setInterval(function () { move(false) }, 5000)
    }
    var autoTimer = autoMove()
    $('.testSite').mouseenter(function () { clearInterval(autoTimer) }).mouseleave(function () { autoTimer = autoMove() })
    // $('.banner').find('.serviceBtn').on('click', function () {
    //   $('#kf5-support-btn').trigger('click');
    // });
    // $('.step').find('#submit2').on('click', function () {
    //   $('#kf5-support-btn').trigger('click');
    // });
  });
</script>
<% include ../footer.html %>
