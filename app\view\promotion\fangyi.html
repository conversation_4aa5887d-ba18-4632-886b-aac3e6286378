<% include ../header.html %>
<% include ./../components/header.html %>
<link rel="stylesheet" href="<%- locals.static %>/css/promotion/fangyi.css">
<div class="fangyi" id='fangyi' v-cloak>
  <div class="banner">
    <div class="wrap">
      <p>
        医疗、防护、消杀产品
        <br />
        专业<i>测试</i>与出口<i>认证</i>服务
      </p>
      <p>
        【产品范围】
        <br />
        熔喷布 / 口罩 / 防护服 / 手套 / 护目镜 / 体温计 / 试剂盒 / 洗手液 / 消毒剂 / 呼吸机
      </p>
      <label>
        <button @click='handlerKF5'>咨询工程师<em>></em></button>
        <span>
          4000-558-581
        </span>
      </label>
      <div class='lange'>
        <a href='/promotion/fangyi' class='active'>中文</a>
        <a href='/promotion/PPE-service'>EN</a>
      </div>
    </div>
  </div>
  <div class="banner-bottom">
    <div class="wrap">
      <a class="link-text" href='/baojia' target='_blank'><i></i><span>内销/出口产品测试</span></a>
      <a class="link-text" href='/baojia' target='_blank'><i></i><span>欧盟CE认证</span></a>
      <a class="link-text" href='/baojia' target='_blank'><i></i><span>产品检验</span></a>
      <a class="link-text" href='/baojia' target='_blank'><i></i><span>助力产品顺利出口，进入全球市场</span><i
          style="float: right;"></i></a>
    </div>
  </div>
  <div class="wrap">
    <div class="title">
      <h2>测试与认证</h2>
      <p>医疗/个人防护/消杀产品测试与认证服务，更多防疫产品请咨询客服</p>
      <span></span>
    </div>
  </div>
  <div class="wrap">
    <ul class="service-tab clearfix">
      <li v-for='(item, index) of services' :key='index' :class="{active: index === serviceCurr}"
        @mouseover="handlerService(index)">
        <div></div>
        <img :src='"./../../public/images/promotion/fangyi/icon" + (item.index+1) + ".png"' />
        <span>{{ item.title }}</span>
        <i></i>
      </li>
    </ul>
  </div>
  <div class="service-content" v-if='index === serviceCurr' v-for='(item, index) of services' :key='index'>
    <div class="wrap">
      <div class="service-content-tab">
        <ul>
          <li v-for='(tab, tabIndex) of item.tabs' :key='tabIndex' :class="{active: tabIndex === tabCurr}"
            @mouseover="handlerTab(tabIndex)">
            <span>{{tab.title}}</span>
            <i v-if='tabIndex === 1'></i>
          </li>
        </ul>
      </div>
      <div class="service-content-con clearfix" v-for='(tab, tabIndex) of item.tabs' :key='tabIndex'
        v-if='tabIndex === tabCurr'>
        <div class="clearfix">
          <div class="item item1">
            <span>{{item.title}}</span>
            <img :src='"./../../public/images/promotion/fangyi/pic" + (item.index+1) + ".png"' />
          </div>
          <div class="item item2">
            <span>测试产品</span>
            <ul>
              <li v-for='(product, proIndex) of tab.products' :key='proIndex'>
                {{product}}
              </li>
            </ul>
          </div>
          <div class="item item3"
            v-if='tab.standard.length && !(tab.items.seviceList && tab.items.seviceList.length)'>
            <span>主要测试项目</span>
            <p v-if='tab.items.describe'>
              {{tab.items.describe}}
            </p>
            <dl v-if='tab.items.seviceName'>
              <dd>
                认证服务: <em>{{tab.items.seviceName}}</em>
              </dd>
            </dl>
          </div>
          <div class="item item3" v-if='tab.items.seviceList && tab.items.seviceList.length'>
            <span>认证服务</span>
            <ul>
              <li v-for='(service, serviceIndex) of tab.items.seviceList' :key='serviceIndex'>
                {{service}}
              </li>
            </ul>
          </div>
          <div class="item item4" v-if='tab.standard.length'>
            <span>测试标准</span>
            <ul>
              <li v-for='(standard, standardIndex) of tab.standard' :key='standardIndex'>
                {{standard}}
              </li>
            </ul>
          </div>
          <div class="item item5" v-if='!tab.standard.length'>
            <span>认证服务</span>
            <em>{{tab.items.seviceName}}</em>
            <p>{{tab.items.describe}}</p>
          </div>
        </div>
        <div class="service-content-bottom">
          <p v-if='tab.items.html' v-html='tab.items.html'></p>
          <button @click='handlerKF5'>咨询工程师<em>></em></button>
        </div>
      </div>
    </div>
  </div>
  <div class="wrap">
    <div class="title">
      <h2>产品检验</h2>
      <p>SGS将在供应链所有环节提供产品质量控制和质量检验服务，帮助客户及时、保质保量地交付防疫产品。</p>
      <span></span>
    </div>
    <div class="inspection">
      <div>
        <a class="text" href='/static/upload/2020/05/27/sgs防疫物资检验申请指南.docx'>《SGS防疫物资检验申请指南》</a>
      </div>
      <div>
        <a class="button" href="/static/upload/2020/05/27/sgs防疫物资检验申请指南.docx">下载</a>
      </div>
      <dl>
        <dt>我们的服务</dt>
        <dd>生产初期检验（验货）</dd>
        <dd>监装服务</dd>
        <dd>生产中期检验（验货）</dd>
        <dd>抽样服务</dd>
        <dd>现场生产控制（线检）</dd>
        <dd>物流仓库检验（验货）</dd>
        <dd>生产终期检验（验货）</dd>
        <dd>定制检验（验货）</dd>
      </dl>
    </div>
  </div>
  <div class="wrap">
    <div class="title">
      <h2>关键优势</h2>
      <p>20年专业经验与服务大数据，用数据证实SGS的技术实力</p>
      <span></span>
    </div>
    <div class="wrap advantage" id="advantage">
      <ul>
        <li>
          <h3><em>CMA/CNAS</em></h3>
          <span>资质</span>
          <p>SGS是国内最早获得CMA和CNAS认可的国际性检测机构，报告结果公正权威，具有法律效力</p>
        </li>
        <li>
          <h3><em>PPE指定</em></h3>
          <span>测试机构</span>
          <p>SGS可以做PPE产品的测试和认证，且CE认证Notify Body是SGS Fimko，OY代码为0598</p>
        </li>
        <li>
          <h3><em>1000+</em></h3>
          <span>测试项目</span>
          <p>SGS在中国设立了50多个分支机构和100多间专业实验室，服务能力全面覆盖防疫类物资</p>
        </li>
        <li>
          <h3><em>20000+</em></h3>
          <span>行业客户</span>
          <p>累计服务客户20000多家，与多家世界500强企业合作</p>
        </li>
      </ul>
    </div>
  </div>
  <div class="serviceDetail">
    <div class="wrap">
      <div class="title">
        <h2>服务详情</h2>
        <span></span>
      </div>
      <div class="serverCon clearfix">
        <div class="showsel show">
          <ul>
            <li class="serverConLi" v-for='(item, index) of serviceDetail' :key="index">
              <a :href="item.link" target="_blank">
                <img alt="机械产品认证" :src="item.img">
                <span class="serverConLiWorBac">
                  <span class="serverConLiWord1">{{item.title}}</span>
                  <span class="serverConLiWord2">{{item.des}}</span>
                </span>
                <i v-if='item.isBuy'></i>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="wrap">
    <div class="title">
      <h2>相关法规解读</h2>
      <span></span>
    </div>
    <div class="cooperationDemo clearfix" style="display: block;">
      <ul>
        <li v-for='(item, index) of news' :key='index'>
          <i class="new"></i>
          <a :href="item.link" target="_blank">{{item.title}}</a>
          <time>{{item.time}}</time>
          <span v-if='item.hot'></span>
        </li>
      </ul>
      <p>
        <a href='/information?type=1010' target="_blank">更多法规动态</a>
      </p>
    </div>
  </div>
  <div class="consult">
    <% include ../components/registionClause.html %>
    <div class="wrap">
      <div class="title">
        <h2>留言咨询</h2>
        <span></span>
      </div>
      <div class="clearfix">
        <label>
          <input type="text" name="customer" id="customer" placeholder="姓名" v-model='form.customer' maxlength='50'>
        </label>
        <label>
          <input type="text" name="phone" id="phone" maxlength="11" placeholder="手机号码" v-model='form.phone'>
        </label>
        <label>
          <input type="text" name="email" id="email" placeholder="电子邮箱" v-model='form.email' maxlength='80'>
        </label>
        <label>
          <input placeholder="简述您的需求" name="content" type="text" class="input-big" id="content"
            v-model='form.content'>
        </label>
        <button id="submit" @click='handlerSubmit'>提交</button>
      </div>
      <div class="form-item-approve">
        <el-checkbox v-model="approve">
          <template v-if='!isLogin'>提交后自动注册会员，获取后续信息。</template>
          我已阅读并同意
          <template v-if='!isLogin'><a @click.stop='dialogVisibleReg = true'>注册条款</a>及</template>
          <a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" target="_blank">隐私政策</a>
        </el-checkbox>
      </div>
    </div>
  </div>
  <% include ./../pages/quote/modal.html %>
</div>


<% if(locals.env != 'prod'){ %>
<script src="<%- locals.static %>/plugin/v2.test.js"></script>
<% }else{ %>
<script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js" type="text/javascript"></script>
<% } %>
<script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
<script>
  var newVue = new Vue({
    name: 'fangyi',
    el: '#fangyi',
    mixins: [quoteMixins],
    data: {
      pid: 'pid.mall',
      pcode: 'Z0zCnRE3IaY9Kzem',
      services: [{
        title: '口罩',
        index: 0,
        tabs: [
          {
            title: '国内销售 ',
            products: [
              '医用防护口罩',
              '医用外科口罩',
              '一次性使用医用口罩',
              '日常防护型口罩',
              'N95口罩',
              '儿童口罩',
              '熔喷布'
            ],
            items: {
              describe: '外观、鼻夹、口罩带、气流阻力、呼气阻力、吸气阻力、合成血液穿透、表面抗湿性、阻燃性能 、皮肤刺激性、密合性、微生物指标、环氧乙烷残留量、细菌过滤效率、颗粒过滤效率、压力差、标识、可分解致癌芳香胺染料',
              seviceName: ''
            },
            standard: [
              'GB 19083-2010 医用防护口罩技术要求',
              'YY 0469-2011 医用外科口罩',
              'YY/T 0969-2013 一次性使用医用口罩',
              'GB/T 32610-2016 日常防护型口罩技术规范',
              'GB 2626-2006 呼吸防护用品 自吸过滤式防颗粒物呼吸器',
              'GB/T 38880-2020 儿童口罩技术规范'
            ]
          },
          {
            title: '出口欧洲 ',
            products: [
              '一次性使用医用口罩',
              '医用外科口罩',
              '医用防护口罩',
            ],
            items: {
              describe: '外观、材料、包装要求、可拆卸部件、阻燃测试、头带、视野、呼气阀、颗粒物过滤效率、呼吸阻力、总内漏、二氧化碳浓度、实际性能、容尘性能、皮肤刺激性、细菌过滤效率，抗合成血液渗透性、微生物清洁度、生物相容性等 ',
              seviceName: '口罩CE认证',
              html: '为响应欧盟关于COVID-19威胁下的符合性评定和市场监督程序的建议(Commission recommendation (EU) 2020/403），助力全球抗疫，<br />' +
                '<span>SGS特别开启CE认证应急通道，优先考虑并迅速开展各类防疫物资的符合性评估工作，</span> <br />' +
                '也可以为您提供专业的服务方案（ISO 13485 / MDSAP / CE / Technical File Review Report），更多信息请咨询工程师。'
            },
            standard: [
              'EN 149：2001+A1:2009 防颗粒吸入的过滤式呼吸器——要求，测试，标识',
              'EN14683 - 2019 医用口罩 要求和实验方法 '
            ]
          },
          {
            title: '出口美国 ',
            products: [
              '外科口罩',
              '带有抗菌/抗病毒介质的外科口罩',
              '儿科口罩',
              '防护口罩',
            ],
            items: {
              describe: '细菌过滤效率、通气压差、亚微粒过滤、合成血液穿透、可燃性等',
              seviceName: ''
            },
            standard: [
              'ASTM F2100-2019 医用口罩材料性能规范 ',
            ]
          },
        ]
      },
      {
        title: '防护服',
        index: 1,
        tabs: [
          {
            title: '国内销售 ',
            products: [
              '一次性医用防护服',
              '防化服',
              '隔离衣',
              '手术衣',
            ],
            items: {
              describe: '外观，结构，号型规格，抗渗水性、透湿量、抗合成血液穿透性、表面抗湿性，过滤效率，断裂强力、断裂伸长率，阻燃性能，抗静电性，静电衰减性能，微生物指标（细菌菌落总数，大肠杆菌，绿脓杆菌，金黄色葡萄球菌，溶血性链球菌，真菌菌落总数），无菌测试（仅考核明示“无菌”或“灭菌”的防护服），环氧乙烷残留量（仅考核声称或明示环氧乙烷灭菌的防护服） ，皮肤刺激性等',
              seviceName: ''
            },
            standard: [
              'GB 19082-2009 医用一次性防护服技术要求',
              'GB/T 29511-2013 防护服装 固体颗粒物化学防护服'
            ]
          },
          {
            title: '出口欧洲 ',
            products: [
              '一次性医用防护服',
              '防化服',
              '隔离衣',
              '手术衣',
            ],
            items: {
              describe: '抗污染液穿透性能等级，抗感染物穿透性，抗污染液体气溶胶穿透性能等级，抗污染固体颗粒穿透性能等级，性能要求，抗拉强度，抗穿刺性，撕裂强度，服装材料接缝断裂强度，阻燃性能等',
              seviceName: '防护服CE认证',
              html: '为响应欧盟关于COVID-19威胁下的符合性评定和市场监督程序的建议(Commission recommendation (EU) 2020/403），助力全球抗疫，<br />' +
                '<span>SGS特别开启CE认证应急通道，优先考虑并迅速开展各类防疫物资的符合性评估工作，</span> <br />' +
                '也可以为您提供专业的服务方案（ISO 13485 / MDSAP / CE / Technical File Review Report），更多信息请咨询工程师。'
            },
            standard: [
              'EN 14126:2003 防护服-防传病毒防护服的性能要求和试验方法',
              'EN ISO 13982-1 防固体颗粒用防护服',
              'EN 14325:2018 化学防护服',
              'EN 14605:2005+A1:2009 液态化学物质防护服装',
            ]
          },
          {
            title: '出口美国 ',
            products: [
              '非手术防护服',
              '手术用防护服',
            ],
            items: {
              describe: '冲击渗水性测试，耐静水压测试，合成液穿透测试，病毒穿透测试等',
              seviceName: ''
            },
            standard: [
              'ASTM F2407:2006 (R2013) 医疗保健设施中使用的外科手术服的标准规范',
              'ASTM F3352: 2019医疗保健设施中使用的隔离衣的标准规范',
              'ANSI / AAMI PB70: 2012 医疗保健设施中使用的防护服和防护布的防液性能和分类 ',
            ]
          },
        ]
      },
      {
        title: '护目镜 面罩',
        index: 2,
        tabs: [
          {
            title: '国内销售 ',
            products: [
              '医用护目镜	',
              '一次性护目镜	',
              '一次性医用隔离面罩	',
            ],
            items: {
              describe: '5.6   光学性能、5.13  化学雾滴防护性能、5.14  粉尘防护性能、.15  刺激性气体防护性能等 宣称“无菌”/“灭菌”，环氧乙烷残留量（仅考核声称或明示环氧乙烷灭菌的防护服）',
              seviceName: ''
            },
            standard: [
              'GB 14866-2006 个人用眼护具技术要求'
            ]
          },
          {
            title: '出口欧洲 ',
            products: [
              '医用护目镜',
              '一次性护目镜',
              '一次性医用隔离面罩',
            ],
            items: {
              describe: '7.1    基础防护性能、7.2.4  防液体滴落及液体飞溅性能、7.2.5  大粒尘埃防护性能、7.2.6  细粒尘埃及气体防护性能',
              html: '为响应欧盟关于COVID-19威胁下的符合性评定和市场监督程序的建议(Commission recommendation (EU) 2020/403），助力全球抗疫，<br />' +
                '<span>SGS特别开启CE认证应急通道，优先考虑并迅速开展各类防疫物资的符合性评估工作，</span> <br />' +
                '也可以为您提供专业的服务方案（ISO 13485 / MDSAP / CE / Technical File Review Report），更多信息请咨询工程师。'
            },
            standard: [
              'EN 166:2001 个人眼睛防护设备',
            ]
          },
          {
            title: '出口美国 ',
            products: [
              '医用护目镜',
              '一次性护目镜',
              '一次性医用隔离面罩',
            ],
            items: {
              describe: '5.1 Optical requirement 光学性能、5.2 Physical requirement 物理性能、8.1 Droplet and Splash Hazard  液滴防护性能、8.2 Dust Hazard 粉尘防护性能、8.3 Fine Dust Hazard 极细粉尘防护性能等',
              seviceName: ''
            },
            standard: [
              'ANSI/ISEA Z87.1-2015 职业和教育的个人眼部和脸部防护设备',
            ]
          },

        ]
      },
      {
        title: '防护手套',
        index: 3,
        tabs: [
          {
            title: '国内销售 ',
            products: [
              '防护手套',
              '医用手套',
              '一次性灭菌手套',
            ],
            items: {
              describe: '尺寸，不透水性；断裂力，断裂延伸，抗穿刺性，包装，粉末含量；铅、镉、六价铬，多环芳香烃，有机锡，领苯二甲酸盐，短链氯化石蜡，高关注物，偶氮染料，富马酸二甲酯，环氧乙烷；蛋白含量，无菌性，热源性，皮肤致敏等',
              seviceName: ''
            },
            standard: [
              'GB 10213 一次性使用医用橡胶检查手套',
              'GB 24786 一次性使用聚氯乙烯医用检查手套',
              'GB 24787 一次性使用非灭菌橡胶外科手套',
              'GB 7543 一次性使用灭菌橡胶外科手套',
              'GB/T 21869医用手套表面残余粉末的测定'
            ]
          },
          {
            title: '出口欧洲 ',
            products: [
              '防护手套',
              '医用手套',
              '一次性灭菌手套',
            ],
            items: {
              describe: '尺寸，不透水性；断裂力，断裂延伸，抗穿刺性，包装，粉末含量；铅、镉、六价铬，多环芳香烃，有机锡，领苯二甲酸盐，短链氯化石蜡，高关注物，偶氮染料，富马酸二甲酯，环氧乙烷；蛋白含量，无菌性，热源性，皮肤致敏等',
              // seviceName: '医用手套CE认证',
              html: '为响应欧盟关于COVID-19威胁下的符合性评定和市场监督程序的建议(Commission recommendation (EU) 2020/403），助力全球抗疫，<br />' +
                '<span>SGS特别开启CE认证应急通道，优先考虑并迅速开展各类防疫物资的符合性评估工作，</span> <br />' +
                '也可以为您提供专业的服务方案（ISO 13485 / MDSAP / CE / Technical File Review Report），更多信息请咨询工程师。'
            },
            standard: [
              'EN 455-1 一次性医用手套 无破洞要求及测试',
              'EN 455-2 一次性医用手套 物理属性要求及测试',
              'EN 455-3 一次性医用手套 生物学评价要求及测试',
            ]
          },
          {
            title: '出口美国 ',
            products: [
              '防护手套',
              '医用手套',
              '一次性灭菌手套',
            ],
            items: {
              describe: '尺寸，不透水性；断裂力，断裂延伸，抗穿刺性，包装，粉末含量；铅、镉、六价铬，多环芳香烃，有机锡，领苯二甲酸盐，短链氯化石蜡，高关注物，偶氮染料，富马酸二甲酯，环氧乙烷；蛋白含量，无菌性，热源性，皮肤致敏等',
              seviceName: ''
            },
            standard: [
              'ASTM D 3577 橡胶医用手套',
              'ASTM D 3578 乳胶检查手套',
              'ASTM D 6124 医用手套残余粉末',
              'ASTM D 5151 医用手套孔洞检测',
              'ASTM D 5250 医用聚氯乙烯手套',
              'ASTM D 6319 医用丁腈检查手套',
              'ASTM D 3772 天然橡胶指套',
            ]
          },

        ]
      },
      {
        title: '体温计',
        index: 4,
        tabs: [
          {
            title: '国内销售 ',
            products: [
              '电子体温计',
              '额温枪',
              '耳温枪',
              '体温筛检仪',
            ],
            items: {
              describe: '量测/显示温度范围、准确度、解析度、精确性及再现性、显示之数字大小、自我校正、量测所需时间、大能量发散、生物相容性、操作及存储环境、电池状况、器材结构、标签标示、其他安全性测试项目 ',
              seviceName: ''
            },
            standard: [
              'GB9706.1-2007 医用电气设备安全通用要求',
              'GB/T 21416-2008 医用电子体温计',
              'YY0505-2012 医用电气设备电磁兼容并列要求',
              'GB/T14710-2009 医用电气设备环境要求',
              'GB/T 21417.1-2008 医用红外体温计 ',
              'GB/T 19146-2010 红外人体表面温度快速筛检仪',
              'GB/T18268.1-2010 测量用电设备电磁兼容通用要求',
            ]
          },
          {
            title: '出口欧洲 ',
            products: [
              '电子体温计',
              '红外测温仪',
              '额温枪',
              '耳温枪',
              '体温筛检仪',
            ],
            items: {
              describe: '量测/显示温度范围、准确度、解析度、精确性及再现性、显示之数字大小、自我校正、量测所需时间、大能量发散、生物相容性、操作及存储环境、电池状况、器材结构、标签标示、其他安全性测试项目',
              seviceName: '体温计CE认证',
              html: '为响应欧盟关于COVID-19威胁下的符合性评定和市场监督程序的建议(Commission recommendation (EU) 2020/403），助力全球抗疫，<br />' +
                '<span>SGS特别开启CE认证应急通道，优先考虑并迅速开展各类防疫物资的符合性评估工作，</span><br />' +
                '也可以为您提供专业的服务方案（ISO 13485 / MDSAP / CE / Technical File Review Report），更多信息请咨询工程师。'
            },
            standard: [
              'IEC 60601-1:2005+A1:2012 医用电气设备安全通用要求',
              'IEC60601-1-6:2010+A1:2013/IEC62366-1:2015 可用性',
              'IEC60601-1-11:2015 家用标准',
              'ISO80601-2-56:2017+A1:2018 体温计专用标准',
              'IEC60601-1-2:2014  医用电气设备电磁兼容并列要求',
              'IEC 60601-1-8:1006+A1:2012 医用报警并列标准',
              'IEC60601-1-2:2014  医用电气设备电磁兼容并列要求',
            ]
          },
          {
            title: '出口美国 ',
            products: [
              '电子体温计',
              '红外测温仪',
              '额温枪',
              '耳温枪',
              '体温筛检仪',
            ],
            items: {
              describe: '量测/显示温度范围、准确度、解析度、精确性及再现性、显示之数字大小、自我校正、量测所需时间、大能量发散、生物相容性、操作及存储环境、电池状况、器材结构、标签标示、其他安全性测试项目',
              seviceName: ''
            },
            standard: [
              'IEC 60601-1:2005+A1:2012 医用电气设备安全通用要求',
              'IEC60601-1-6:2010+A1:2013/IEC62366-1:2015 可用性',
              'IEC60601-1-11:2015 家用标准',
              'ISO80601-2-56:2017+A1:2018 体温计专用标准',
              'IEC60601-1-2:2014  医用电气设备电磁兼容并列要求',
              'ASTM E1965-98 (Reapproved 2016) FDA体温计专标',
              'IEC 60601-1-8:1006+A1:2012 医用报警并列标准',
            ]
          },
        ]
      },
      // {
      //   title: '检测试剂盒',
      // index: 5,
      //   tabs: [{
      //     title: '出口国外 ',
      //     products: [
      //       '病毒检测试剂盒',
      //       '体外诊断试剂',
      //     ],
      //     items: {
      //       describe: '为响应欧盟关于COVID-19威胁下的符合性评定和市场监督程序的建议(Commission recommendation (EU) 2020/403），助力全球抗疫，SGS特别开启CE认证应急通道，优先考虑并迅速开展各类防疫物资的符合性评估工作，也可以为您提供专业的服务方案（ISO 13485 / MDSAP / CE / Technical File Review Report），更多信息请咨询工程师。',
      //       seviceName: '试剂盒CE认证'
      //     },
      //     standard: []
      //   }]
      // },
      {
        title: '洗手液',
        index: 6,
        tabs: [{
          title: '国内销售 ',
          products: [
            '抗/抑菌洗手液',
            '抗/抑菌免洗洗手液',
          ],
          items: {
            describe: '气味、外观、稳定性试验、pH值测定、总活性物含量、有效成分含量测定、铅/砷/汞的测定、甲醛测定、甲醇测定、微生物指标测定、杀菌率/抑菌率测定等',
            seviceName: ''
          },
          standard: [
            '《消毒技术规范》2002年版',
            '《消毒产品卫生安全评价规定》2014年版',
            'WS 628-2018 消毒产品卫生安全评价技术要求',
            'GB 27950-2011 手消毒剂卫生要求',
            'GB 27951-2011 皮肤消毒剂卫生要求'
          ]
        }]
      },
      {
        title: '消毒剂',
        index: 7,
        tabs: [{
          title: '国内销售 ',
          products: [
            '电子醇类消毒剂',
            '含氯消毒剂',
            '二氧化氯消毒剂',
            '84消毒液',
            '过氧乙酸消毒剂',
            '其他消毒产品',
          ],
          items: {
            describe: '有效成分含量测定、pH值测定、稳定性试验、微生物杀灭效果测定、模拟现场试验或现场试验、毒理学安全性检测、铅/砷/汞的测定、金属腐蚀性试验等',
            seviceName: ''
          },
          standard: [
            '《消毒技术规范》2002年版',
            '《消毒产品卫生安全评价规定》2014年版',
            'WS 628-2018 消毒产品卫生安全评价技术要求',
            'GB 27950-2011 手消毒剂卫生要求',
            'GB 27951-2011 皮肤消毒剂卫生要求'
          ]
        }]
      },
      {
        title: '呼吸机',
        index: 8,
        tabs: [{
          title: '出口国外 ',
          products: [
            '呼吸机',
            '无需排单，即刻测试！',
          ],
          items: {
            describe: '',
            seviceList: [
              '国际CB认证',
              '北美NRTL认证',
              '欧盟CE认证',
              '美国FDA注册',
              '巴西INMETRO认证'
            ],
            seviceName: '',
            html: '作为医疗器械及体外诊断行业测试认证的领先服务提供商，<br />' +
              '<span>SGS特别开启呼吸机测试服务的快捷通道。</span><br />' +
              '品质加固，速度先行，与各国共克时艰，为生命保驾护航，更多信息请咨询工程师。'
          },
          standard: [
            'IEC/EN 60601-1',
            'IEC/EN 60601-1-2',
            'IEC/EN 60601-1-6',
            'IEC/EN 60601-1-8',
            'EN ISO 80601-2-12'
          ]
        }]
      }
      ],
      serviceCurr: 0,
      tabCurr: 0,
      news: [
        {
          title: 'MDD推迟一年，SGS教你如何维持证书有效性',
          link: '/case/article/detail-291.html',
          time: '2020-05-06'
        },
        {
          title: '口罩全球适用标准，正版电子版（含中英文翻译）',
          link: '/case/article/detail-211.html',
          time: '2020-03-25',
          hot: true
        },
        {
          title: '欧盟官方提示：出口抗疫物资不能忽视适用法规要求',
          link: '/case/article/detail-289.html',
          time: '2020-05-06'
        },
        {
          title: '海关官方 | 防护服出口通关申报指南',
          link: '/case/article/detail-221.html',
          time: '2020-03-30'
        },
        {
          title: '专业解读丨英国脱欧对当前医疗器械制造商都有哪些影响？',
          link: '/case/article/detail-257.html',
          time: '2020-03-06'
        },
        {
          title: '海关官方 | 口罩出口通关申报超清晰指南',
          link: '/case/article/detail-218.html',
          time: '2020-03-30'
        },
        {
          title: '官方发布《IRIS认证: COVID 19-通知》及要点解读',
          link: '/case/article/detail-250.html',
          time: '2020-04-08'
        },
        {
          title: '呼吸机测试服务开启快捷通道，无需排单，即刻测试！',
          link: '/case/article/detail-244.html',
          time: '2020-04-03'
        }
      ],
      serviceDetail: [{
        title: '防护口罩检测套餐',
        link: '/sku/product/901046',
        des: '医用、防护型口罩基础测试套餐，CMA资质报告',
        img: '/static/upload/2020/02/14/heidnNHY.jpg',
        isBuy: true
      },
      {
        title: '医用一次性防护服检测服务',
        link: '/sku/product/901050',
        des: '抗疫战斗服的质量卫士',
        img: '/static/upload/2020/02/20/WxqLgNvW.jpg',
        isBuy: false
      },
      {
        title: '医用防护手套测试服务',
        link: '/sku/product/901052',
        des: '用专业的力量，守护身边的她',
        img: '/static/upload/2020/02/21/HisdJfXR.jpg',
        isBuy: false
      },
      {
        title: '医用护目镜及面罩测试	',
        link: '/sku/product/901068',
        des: '防疫物资检测，用专业开展全球驰援',
        img: '/static/upload/2020/04/01/ACTcQFLP.jpg',
        isBuy: false
      },
      {
        title: '熔喷布测试套餐',
        link: '/sku/product/901079',
        des: '提供从熔喷布到口罩的测试、检验与认证服务',
        img: '/static/upload/2020/04/22/DYgxBbqY.jpg',
        isBuy: true
      },
      {
        title: '口罩原材料PP熔喷料测试',
        link: '/sku/product/901078',
        des: '聚丙烯（PP）熔喷料测试',
        img: '/static/upload/2020/04/20/cbmCwiuI.jpg',
        isBuy: false
      },
      {
        title: '电子体温计测试',
        link: '/sku/product/901057',
        des: '额温枪、耳温枪及电子体温计合规测试，CMA资质报告',
        img: '/static/upload/2020/03/02/lxMrPMWg.jpg',
        isBuy: false
      },
      {
        title: '疫情防护用品检验和品控服务',
        link: '/sku/product/901047',
        des: '口罩、防护服、防护镜、防护手套等疫情防护用品检验及品控服务-控制产品质量',
        img: '/static/upload/2020/02/19/HUBqxosJ.jpg',
        isBuy: false
      }
      ],
      form: {
        type: '服务询价',
        trade: '',
        tradeName: '其他',
        service: '',
        serviceName: '其他',
        content: '',
        customer: '<%- userInfo.userName %>',
        email: '<%- userInfo.userEmail %>',
        phone: '<%- userInfo.userPhone %>',
        provice: '浙江省',
        company: 'sgs',
        os_type: 'pc',
        frontUrl: window.location.href,
        _csrf: '<%- csrf %>',
        imageCode: '',
        verifyCode: '',
      },
      showModal: false,
      seconds: 59,
      timer: null,
      loading: false,
      host: '<%- host %>',
      isLogin: <%- isLogin %>,
      pageInfo: {
        title: '验证手机号',
        sendModel: '已发送到手机号：+86',
        prepend: '手机验证码'
      },
      approve: false,
      dialogVisibleReg: false,
    },
    methods: {
      handleClose: function () {
        $("#submit").prop('disabled', false);
        this.showModal = false
        this.form.verifyCode = ''
      },
      sing: function (param, timestamp) {
        var pmd5 = md5((this.pid + this.pcode).toUpperCase());
        var str = JSON.stringify(param) + pmd5.toUpperCase();
        return md5(str + timestamp);
      },
      handleModalSubmit: function (loginRef) {
        this.form.verifyCode = loginRef.verifyCode
        var that = this
        var param = that.form
        var timestamp = new Date().valueOf();
        var url = '/submitTicket';
        loginRef.loading = true
        param.verifyMode = this.tab
        $.ajax({
          type: 'POST',
          url: url,
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: this.pid,
            sign: that.sing(param, timestamp),
            timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            loginRef.loading = false
            that.loading = false
            if (res.resultCode === '0') {
              Cookies.set("SSO_TOKEN", res.data.token, { domain: ".sgsmall.com.cn", expires: 7 });
              that.showModal = false
              that.$message({
                message: '您的业务咨询已提交，我们会尽快与您取得联系！',
                type: 'success'
              });
              $('#submit').prop('disabled', false);
            } else if(res.resultCode === '9978') {
              Cookies.remove('SSO_TOKEN', { domain: '.sgsmall.com.cn' })
              that.isLogin = false
              that.showModal = true
            } else {
              that.$message.error(res.resultMsg)
            }
          },
          fail: function (data) {
            loginRef.loading = false
            that.loading = false
            that.$message.error(res.resultMsg)
          },
          complete: function (complete) {
          }
        })
      },
      handlerKF5: function () {
        // $('#kf5-support-btn').trigger('click');
        window.open('/baojia')
      },
      handlerService: function (index) {
        this.serviceCurr = index
        this.tabCurr = 0
      },
      handlerTab: function (index) {
        this.tabCurr = index
      },
      handlerSubmit: function () {
        if (!this.form.customer) {
          this.$message({
            message: '请输入姓名！',
            type: 'warning'
          });
        } else if (!this.form.phone) {
          this.$message({
            message: '请输入手机号码！',
            type: 'warning'
          });
        } else if (!/^1\d{10}$/.test(this.form.phone)) {
          this.$message({
            message: '请输入正确的手机号码！',
            type: 'warning'
          });
        } else if (!this.form.email) {
          this.$message({
            message: '请输入电子邮箱',
            type: 'warning'
          });
        } else if (!
            /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/
            .test(this.form.email)) {
          this.$message({
            message: '请输入正确电子邮箱',
            type: 'warning'
          });
        } else if (!this.form.content) {
          this.$message({
            message: '请输入需求信息！',
            type: 'warning'
          });
        } else if (!this.approve) {
          alert('请同意隐私政策！')
        } else {
          if (!this.isLogin) {
            $('#submit').prop('disabled', true);
            this.showModal = true
          } else {
            this.handleModalSubmit({})
          }
        }
      },
    },
    mounted: function () {
      if (this.isLogin && $("#phone").val()) $("#phone").attr('disabled', true)
    }
  });
</script>

<% include ../footer.html %>