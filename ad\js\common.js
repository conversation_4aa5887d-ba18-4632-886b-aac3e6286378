$(function () {
  _userInfo();
  fixPlaceholder();
  $(".navSearchIcon").on("click",
    function () {
      var v = $("#keyWord").val().trim();
      v = encodeURIComponent(v);
      if (!v || v == "") {
        return
      };
      window.open("https://www.sgsmall.com.cn/search?q=" + v)
    });

  updateCartNumber("#minicart_ .up-cat-number");
  var $_drag = $(".drag");
  var dragH;
  for (var i = 0; i < $_drag.length; i++) {
    dragH = $_drag.eq(i).height();
    $(".navBor").eq(i).css("height", dragH)
  };
  var $_serverConLi = $(".show>.serverConLi");
  for (var i = 0; i < $_serverConLi.length; i++) {
    if (i % 2 == 1) {
      $_serverConLi.eq(i).addClass("serverRight0")
    }
  };
  var $_solutionConLi = $(".solutionConLi");
  for (var i = 0; i < $_solutionConLi.length; i++) {
    if (i % 2 == 1) {
      $_solutionConLi.eq(i).addClass("serverRight0")
    }
  };
  var $_recomentLi = $(".recomentLi");
  for (var i = 0; i < $_recomentLi.length; i++) {
    if (i == 2 || i == 5) {
      $_recomentLi.eq(i).addClass("serverRight0")
    }
  };
  if ($(".detailBox").length > 0) {
    boxFix();
    boxMove()
  };
  $(window).resize(function () {
    var banner = null;
    if ($(".detailBox").length > 0) {
      boxFix()
    }
  });
  banner(".bannerUl>li", "active", ".point>li", ".bannerLeft", ".bannerRight");

  function banner(a, b, c, d, e) {
    var width = $(a).width();
    index = 0;
    len = $(a).length - 1;

    function teb(index) {
      $(c).eq(index).addClass(b).siblings("").removeClass(b);
      $(".bannerUl").animate({
        marginLeft: -width * index
      })
    };
    $(c).click(function () {
      index = $(this).index();
      teb(index)
    });
    $(d).click(function () {
      index--;
      if (index < 0) {
        index = len
      };
      teb(index)
    });
    $(e).click(function () {
      index++;
      if (index > len) {
        index = 0
      };
      teb(index)
    });

    function timeRun() {
      time = setInterval(function () {
        index++;
        if (index > len) {
          index = 0
        };
        teb(index)
      },
        5000)
    };
    timeRun()
  };
  var $_bor = $(".bor");
  $(".server .serverLi").click(function () {
    $(this).addClass("serverLiActive").siblings(".serverLi").removeClass("serverLiActive");
    var $_con = $(this).parents(".serverHead").siblings(".serverCon").children();
    var index = $(this).index();
    $_con.eq(index).addClass("show").siblings().removeClass("show");
    var $_serverConLi = $(".show>.serverConLi");
    for (var i = 0; i < $_serverConLi.length; i++) {
      if (i == 3 || i == 7) {
        $_serverConLi.eq(i).addClass("serverRight0")
      }
    };
    var w = $(this).outerWidth();
    var $obj = $(this).parent();
    var left = 0;
    for (var i = 0; i < index; i++) {
      left += $(">li", $obj).eq(i).outerWidth()
    };
    $_bor.animate({
      width: w,
      left: left
    })
  });
  var wloc = window.location.hash.replace("#/", "");
  $(".serverHead").each(function () {
    var hss = false;
    $("ul>li", this).each(function (i, item) {
      var hid = $(item).children("a").data("role");
      if (wloc == hid) {
        $(item).trigger("click");
        hss = true
      }
    });
    if (!hss) {
      $("ul>li:first", this).trigger("click")
    }
  });
  $(".pagesLi").click(function () {
    $(this).addClass("pagesLiActive").siblings(".pagesLi").removeClass("pagesLiActive")
  });
  var $_nav = $(".nav");
  var sc = $(document);
  $(window).scroll(function () {
    // 路由 oiq 取消吸顶动作
    if (window.location.pathname.indexOf('oiq') > -1) {
      if (sc.scrollTop() >= 38) {
        $_nav.removeClass("fix")
      } else {
        $_nav.addClass("fix")
      }
    }
  });
  var out;
  $(".navUl>li").hover(function () {
    clearTimeout(out);
    var $this = $(this),
      $c = $(this).children("ul");
    out = setTimeout(function () {
      $c.stop().fadeIn(300)
    },
      100)
  },
    function () {
      clearTimeout(out);
      var $this = $(this),
        $c = $(this).children("ul");
      $c.stop().fadeOut(300)
    })

  function projectClick(id, name, href, listName) {
    document.location = href;
    // ga('ec:addProduct', {
    //   'id': id,
    //   'name': name,
    //   'category': '',
    //   'brand': '',
    //   'variant': '',
    //   'position': 1
    // });
    // ga('ec:setAction', 'click', {
    //   list: listName
    // });
    //
    // // Send click with an event, then send user to product page.
    // ga('send', 'event', 'UX', 'click', 'Results', {
    //   hitCallback: function () {
    //     document.location = href;
    //   }
    // });
  }

  function navProjectClick(e, listName) {
    var href = $(e.target).attr('href')
    if (href) {
      var arr = href.split('/')
      var id = arr[arr.length - 1]
      var name = $(e.target).text().trim()
      projectClick(id, name, href, listName)
    }
  }

  $('#service a').on('click', function (e) {
    e.preventDefault()
    navProjectClick(e, 'our service')
  })
  $('#industry a').on('click', function (e) {
    e.preventDefault()
    navProjectClick(e, 'industry solutions')
  })
  $('#serviceNav dd a, #serviceNav .main-list2 a').on('click', function (e) {
    e.preventDefault()
    navProjectClick(e, 'service nav')
  })
  $('#tradeHome dd a').on('click', function (e) {
    e.preventDefault()
    navProjectClick(e, 'solution nav')
  })

  // setCartNumber();
  getSourceForOIQ();
  // asyncRenderNavication();
});
function navList(list, listName) {
  // var l = 1
  // for (var i = 0; i < list.length; i++) {
  //   for (var j = 0; j < list[i].firstList.length; j++) {
  //     for (var k = 0; k < list[i].firstList[j].secondList.length; k++) {
  //       var serviceItem = list[i].firstList[j].secondList[k]
  //       if (serviceItem.thirdLink) {
  //         var arr = serviceItem.thirdLink.split('/')
  //         ga('ec:addImpression', {
  //           'id': arr[arr.length - 1],
  //           'name': serviceItem.thirdTitle,
  //           'category': '',
  //           'brand': '',
  //           'variant': '',
  //           'list': listName,
  //           'position': l
  //         });
  //         ga('send', 'pageview');
  //         l++
  //       }
  //     }
  //   }
  // }
  // ga('send', 'pageview');
}

function boxFix() {
  var $_deban = $(".detailBox");
  if ($_deban.length == 0) {
    return
  };
  var offset = $_deban.offset();
  var $_ban = $(".bannerInfo");
  var $_description3 = $(".fixed-description3")
  $_ban.css("position", "fixed");
  if (parseFloat($_description3.css('height')) > 0) {
    $_description3.css('bottom', -30 - parseFloat($_description3.css('height')) + 'px')
  }
  var sc = $(document);
  var left = offset.left + 825 + 56;
  var top = offset.top;
  $_ban.css("left", left).css("top", top);
  var fo = $("#helpInfo").offset().top,
    fh = $("#helpInfo").outerHeight(),
    bh = document.documentElement.clientHeight || document.body.clientHeight;
  var ph = $("body").height();
  var sh = $_ban.outerHeight();
  $(window).scroll(function () {
    if (sc.scrollTop() >= 110 + 127) {
      $_ban.addClass("ngsFix");
      if (sc.scrollTop() + 100 + sh >= fo - 100 - 50) {
        $_ban.css("top", bh - fh - $_ban.outerHeight() - 100)
        $_ban.css('display', 'none')
      } else {
        $_ban.css("top", 100)
        $_ban.css('display', 'block')
      }
    } else {
      $_ban.removeClass("ngsFix");
      $_ban.css("top", top - sc.scrollTop())
      $_ban.css('display', 'block')
    }
  })
};

function boxMove() {
  var $_nav = $(".nav");
  var sc = $(document);
  $(window).scroll(function () {
    if (sc.scrollTop() >= 110) {
      $_nav.addClass("fix")
    } else {
      $_nav.removeClass("fix")
    }
  })
};

function _userInfo() {
  $("#topbar_hotphone").html('4000-558-581')
};

function _search(e) {
  e.preventDefault;
  var keycode = e.keyCode;
  if (keycode == 13) {
    // $(".navSearchIcon").trigger("click")
  }
};

function updateCartNumber(content) {
  content = $(content || ".up-cat-number");
  var type = $(content).attr("data-type");
  var number;
  type = (!type) ? 1 : type;
  if (type == 1) {
    number = Cookies.get("CARTNUMBER")
  } else {
    if (type == 2) {
      number = Cookies.get("CARTVARIETY")
    }
  };
  $(content).text(number || 0)
};

function goTop() {
  $("html,body").stop().animate({
    scrollTop: 0
  })
};
var url = document.location.href,
  title = "SGS Online";

function addFavorite() {
  if (window.sidebar && window.sidebar.addPanel) {
    window.sidebar.addPanel(title, url, "")
  } else {
    if (window.external && ("AddFavorite" in window.external)) {
      window.external.AddFavorite(url, title)
    } else {
      if (window.opera && window.print) {
        this.title = title;
        return true
      } else {
        alert("请使用 " + (navigator.userAgent.toLowerCase().indexOf("mac") != -1 ? "Command" : "CTRL") + " + D 加入收藏")
      }
    }
  }
};
var $pobj = null;

/*旧版悬浮交互*/
function PhoneShow(obj) {
  // 根据域名来显示不同的客服电话
  var phoneNum = '4000-558-581';
  $pobj = $(obj);
  var $sobj = $pobj.children("span.sphone");
  $sobj.find('#phoneNum').html(phoneNum)
  if ($sobj.css("width") == "0px" || $sobj.css("display") == "none") {
    $sobj.css({
      right: '60px',
      width: '195px',
      display: 'block'
    });
    $('.tipsss>ul').hide();
    record()
  } else {
    $sobj.css({
      right: '-200px',
      width: '0px'
    });
  }
};

function composedPath(el) {
  var path = [];
  while (el) {
    path.push(el);
    if (el.tag_name === "HTML") {
      path.push(document);
      path.push(window);
      return path
    };
    el = el.parentElement
  }
};

/*旧版悬浮交互*/
document.addEventListener("click",
  function (event) {
    var event = event || window.event;
    var path = event.path || (event.composedPath && event.composedPath()) || composedPath(event.target);
    if ($pobj && path.indexOf($pobj[0]) < 0) {
      // $pobj.children("span.sphone").css("display", "none")
      $pobj.children("span.sphone").css({
        right: '-200px',
        width: '0px'
      });
    };
    if ($pobj && path.indexOf($pobj[0]) < 0) {
      $pobj.children("ul.zns").css("display", "none")
    }
  });

function phoneDialog() {
  var pw = $('html,body').height(),
    ph = $('html,body').height();
  // 根据域名来显示不同的客服电话
  var phoneNum = '4000-558-581';
  $('.pshadeBox').height(ph).css('display', 'block');
  $('.ptip_box').find('#phoneNum').html(phoneNum)
  $('.ptip_box').css('display', 'block');
  record();
};

function complaintDialog() {
  var pw = $("html,body").height(),
    ph = $("html,body").height();
  $(".pshadeBox1").height(ph).css("display", "block");
  $(".ptip_box1").css("display", "block");
  record()
};

function closePD() {
  $(".pshadeBox, .ptip_box, .pshadeBox1, .ptip_box1").fadeOut()
};

function record() {
  var url = encodeURIComponent(window.location.href);
  $.get("/clickrecord/?url=" + url)
};

/*旧版悬浮框交互*/
function ssShow(obj) {
  $pobj = $(obj);
  var $sobj = $pobj.children("ul");
  $sobj.css('top', -$('>li', $sobj).length * 33 / 2 + 28);
  if ($sobj.css("display") == "none") {
    $sobj.css("display", "block");
    $('.tips.phone>span.sphone').hide()
  } else {
    $sobj.css("display", "none")
  }
};

function fixPlaceholder() {
  $(".navSearchBox [placeholder]").focus(function () {
    var input = $(this);
    if (input.val() == input.attr("placeholder")) {
      input.val("");
      input.removeClass("placeholder")
    }
  }).blur(function () {
    var input = $(this);
    if (input.val() == "" || input.val() == input.attr("placeholder")) {
      input.addClass("placeholder");
      input.val(input.attr("placeholder"))
    }
  }).blur().parents("form").submit(function () {
    $(this).find("[placeholder]").each(function () {
      var input = $(this);
      if (input.val() == input.attr("placeholder")) {
        input.val("")
      }
    })
  })
}

function isPhoneNum(num) {
  if (!/^1[3|4|5|6|7|8|9]\d{9}$/.test(num)) {
    return false
  } else {
    return true
  }
}

function countDown(that, timeLength) {
  that.addClass("disabled")
  that.prop('disabled', true)
  var btnText = ''
  var timmer = setInterval(function () {
    if (timeLength > 1) {
      timeLength--
      btnText = timeLength + '秒后重发'
    } else {
      that.removeClass("disabled")
      that.prop('disabled', false)
      btnText = '获取验证码'
      clearInterval(timmer)
    }
    that.html(btnText)
  }, 1000);
}

function goOrderList() {
  var storeListUrl = STORE_URL + '/myorder.html'
  var oiqListUrl = MEMBER_URL + '/order/list'
  if (!Cookies.get('SSO_TOKEN')) {
    location.href = MEMBER_URL + '/login?next_origin=store&next_path=/myorder.html&formClick=1'
    return
  }
  $.ajax({
    type: 'POST',
    url: '/getOrderListUrl',
    data: {},
    success: function (res) {
      location.href = res.isStoreList ? storeListUrl : oiqListUrl
    },
    fail: function (data) {
      location.href = storeListUrl
    },
    complete: function (complete) {
    }
  })
}

// 获取并设置购物车数量
// function setCartNumber() {
//   var carNum = Cookies.get("CARTNUMBER");
//   if (Number(carNum) > 99) {
//     carNum = '999+'
//   }
//   $(".cartNumberClass").html(carNum);
// }

// 跳转到OIQ答题页面，带入来源的标识位 source
function jumpOIQAddtionSource(url, separator) {
  var source = window.localStorage.getItem('oiqSource');
  window.open(url + separator + decodeURI(source), '_blank')
}

// 登出
$('#logout').on("click", function () {
  axios.post('/logout', {})
    .then(function (res) {
      if (res.status === 200 && res.data.status) {
        Cookies.remove('SSO_TOKEN', { domain: '.sgsmall.com.cn' })
        Cookies.remove('CARTNUMBER', { domain: '.sgsmall.com.cn' })
        window.location.reload()
      }
    })
    .catch(function (error) { });
})

// 注册 params: member的地址
function signup(memberUrl) {
  window.location.href = memberUrl + '/signup?next_origin=www&next_path=' + encodeURIComponent(window.location.pathname)
}

/*
  TIC-4971
  联系客服按钮事件
  当3级域名是activity或访问的pathname为oiq || oiq/start的页面，永远触发KF5
*/
// var IS_LEADOO = true
// $(".handleIMtool").on('click', function () {
//   debugger
//   // if (IS_LEADOO && (window.location.pathname !== "/oiq" || window.location.pathname !== "/oiq/start")) {
//   //   var iframeLen = $("iframe").length;
//   //   for(var i = 0; i < iframeLen; i++) {
//   //     if ($("iframe")[i]) {
//   //       $($("iframe")[i]).contents().find('.ld-launch-btn').trigger('click');
//   //     }
//   //   }
//   // } else {
//   //   $('#kf5-support-btn').trigger('click')
//   // }
//   // $('#kf5-support-btn').trigger('click')
// });

// 全站获取source并记录到localstorage
function getSourceForOIQ() {
  var searchs = window.location.search.split('?');
  var source = ''
  if (searchs && searchs.length > 1) {
    searchs.forEach(function (v1, index) {
      if (v1.indexOf('source') > -1) {
        source = v1;
      }
    })
    window.localStorage.setItem('oiqSource', source)
  }
}

/*
  TIC-5198
  异步渲染首页导航
  异步渲染内页导航
*/
function asyncRenderNavication() {
  $.ajax({
    type: 'POST',
    url: 'https://www.sgsmall.com.cn/getNavication',
    data: {},
    success: function (res) {
      createInnerNavication(res)
      createHomeNavivation(res)
    },
    fail: function (data) {
    },
    complete: function (complete) {
    }
  })
}

// 创建内页导航菜单
function createInnerNavication(res) {
  var serviceID = $("#serviceNav"),
    tradeID = $("#tradeNav"),
    serviceNavication = res.serviceNavication,
    tradeNavication = res.tradeNavication;
  //　服务菜单
  if (serviceNavication.length) {
    renderNavicationInner(serviceNavication, serviceID)
  }
  // 行业菜单
  if (tradeNavication.length) {
    renderNavicationInner(tradeNavication, tradeID)
  }
}

function renderNavicationInner(data, boxId) {
  var mainDom = '<div class="main">',
    sideDom = '<ul class="side">';
  data.forEach(function (v1, index) {
    if (!index) {
      sideDom += '<li class="active"><a href="/industry/' + v1.alias + '">' + v1.name + '</a></li>';
    } else {
      sideDom += '<li><a href="/industry/' + v1.alias + '">' + v1.name + '</a></li>';
    }
  })
  sideDom += '</ul>';
  data.forEach(function (v1, index) {
    if (!index) {
      mainDom += '<div class="list clearfix active">';
    } else {
      mainDom += '<div class="list clearfix">';
    }
    if (v1.lstSub && v1.lstSub.length >= 5) {
      mainDom += '<div class="second"><ul>';
      v1.lstSub.forEach(function (v2, index2) {
        if (!index2) {
          mainDom += '<li style="display: list-item;" class="active"><a href="javascript:void(0);" data-id="' + v2.id + '" data-link="/' + v1.alias + '/' + v2.alias + '/' + v2.id + '">' + v2.name + '</a></li>';
        } else {
          mainDom += '<li><a href="javascript:void(0);" data-id="' + v2.id + '" data-link="/' + v1.alias + '/' + v2.alias + '/' + v2.id + '">' + v2.name + '</a></li>';
        }
      })
      mainDom += '</ul></div>';
      mainDom += '<div class="secondList">';
      mainDom += '<div class="clearfix">';
      mainDom += '<ul class="secondListLi">';
      v1.lstSub.forEach(function (v2, index2) {
        // 3级菜单start
        if (!index2) {
          mainDom += '<li style="display: list-item;">';
        } else {
          mainDom += '<li>';
        }
        mainDom += '<div class="main-list2">';
        v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
          if (v3.showType === 1) {
            if (v3.linkUrl === "") {
              mainDom += '<p class="cleafix">' + v3.showName + '</p>';
            } else {
              mainDom += '<a class="cleafix" href="' + v3.linkUrl + '">' + v3.showName + '</a>';
            }
          }
          if (v3.showType === 2) {
            mainDom += '<span><a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a></span>';
          }
        })
        mainDom += '</div>';
        // 3级菜单end
        // 广告start
        mainDom += '<div class="ads clearfix"><div class="moreService"><a href="/service/testing/service" class="moreServiceA">查看更多服务<i></i></a></div>';
        mainDom += '<ul>';
        v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
          if (v3.showType === 3) {
            mainDom += '<li>' +
              '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
              '<img src="' + v3.imgUrl + '">' +
              '</a>' +
              '<div class="info">' +
              '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
              '<h3 title="' + v3.showName + '">' + v3.showName + '</h3>' +
              '<p title="' + v3.showSummary + '">' + v3.showSummary + '</p>' +
              '</a>' +
              '<button class="imBtn">在线咨询</button>' +
              '</div>' +
              '</li>';
          }
        })
        mainDom += '</ul></div>';
        // 广告end
        mainDom += '</li>';
      })
      mainDom += '</ul>';
      mainDom += '</div>';
      mainDom += '</div>';
    } else {
      mainDom += '<div class="main-list">';
      v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
        mainDom += '<dl class="clearfix">';
        // v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
        // 2级start
        mainDom += '<dt>' +
          '<span>·</span>' +
          '<a href="javascript:void(0);" data-id="' + v2.id + '" data-link="/' + v1.alias + '/' + v2.alias + '/' + v2.id + '">' + v2.name + '</a>' +
          '</dt>';
        // 2级end
        // 3级start
        v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
          if (v3.showType === 2) {
            mainDom += '<dd>' +
              '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
            if (v3.isHot) mainDom += '<i></i>'; // 是否热门
            mainDom += '</dd>';
          }
        })
        // 3级end
        // })
        mainDom += '</dl>';
      })
      // 广告start
      mainDom += '<div class="ads">';
      mainDom += '<div class="moreService">' +
        '<a href="/sku/product-certification/service">查看更多服务<i></i></a>' +
        '</div>';
      mainDom += '<ul>';
      v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
        v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
          if (v3.showType === 3) {
            mainDom += '<li>' +
              '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
              '<img src="' + v3.imgUrl + '">' +
              '</a>' +
              '<div class="info">' +
              '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
              '<h3 title="' + v3.showName + '">' + v3.showName + '</h3>' +
              '<p title="' + v3.showSummary + '">' + v3.showSummary + '</p>' +
              '</a>';
            if (v3.isBuy) {
              mainDom += '<em data-id="' + v2.id + '" data-link="' + v3.linkUrl + '">立即订购</em>';
            } else {
              mainDom += '<button class="imBtn">在线咨询</button>';
            }
            mainDom += '</div>' +
              '</li>';
          }
        })
      })
      mainDom += '</ul>';
      mainDom += '</div>';
      // 广告end
      mainDom += '</div>';
    }
    mainDom += '</div>';
  })
  mainDom += '</div>';
  boxId.html(sideDom + mainDom)
}

// 创建首页导航
function createHomeNavivation(res) {
  var serviceID = $(".n_category #service"),
    tradeID = $(".n_category #industry"),
    serviceNavication = res.serviceNavication,
    tradeNavication = res.tradeNavication;
  if (serviceNavication.length) {
    renderNavicationHome(serviceNavication, serviceID)
  }
  if (tradeNavication.length) {
    renderNavicationHome(tradeNavication, tradeID)
  }
}

function renderNavicationHome(data, boxId) {
  var mainDom = '<div class="main">',
    sideDom = '<div class="side"><ul>';
  data.forEach(function (v1, index) {
    sideDom += '<li><a href="/industry/' + v1.alias + '">' + v1.name + '</a><span></span></li>';
  })
  sideDom += '</ul></div>';
  data.forEach(function (v1, index) {
    mainDom += '<div class="list clearfix ">';
    if (v1.lstSub && v1.lstSub.length >= 5) {
      mainDom += '<div class="second"><ul>';
      v1.lstSub.forEach(function (v2, index2) {
        if (!index2) {
          mainDom += '<li style="display: list-item;" class="active"><a href="javascript:void(0);" data-id="' + v2.id + '" data-link="/' + v1.alias + '/' + v2.alias + '/' + v2.id + '">' + v2.name + '</a></li>';
        } else {
          mainDom += '<li><a href="javascript:void(0);" data-id="' + v2.id + '" data-link="/' + v1.alias + '/' + v2.alias + '/' + v2.id + '">' + v2.name + '</a></li>';
        }
      })
      mainDom += '</ul></div>';
      mainDom += '<div class="secondList">';
      mainDom += '<div class="clearfix">';
      mainDom += '<ul class="secondListLi">';
      v1.lstSub.forEach(function (v2, index2) {
        // 3级菜单start
        mainDom += '<li>';
        mainDom += '<div class="main-list2">';
        mainDom += '<label>';
        v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
          if (v3.showType === 1) {
            if (v3.linkUrl === "") {
              mainDom += '<p class="cleafix" title="' + v3.showName + '">' + v3.showName + '</p>';
            } else {
              mainDom += '<a class="cleafix" href="' + v3.linkUrl + '" title="' + v3.showName + '">' + v3.showName + '</a>';
            }
          }
          if (v3.showType === 2) {
            mainDom += '<span><a href="' + v3.linkUrl + '" title="' + v3.showName + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a></span>';
          }
          // 最后一条之后添加分隔符
          if (index3 === v2.lstNavi.length - 1) {
            mainDom += '<div class="dashedLine"></div>';
          }
        })
        mainDom += '</label>';
        mainDom += '</div>';
        // 3级菜单end
        // 广告start
        mainDom += '<div class="ads clearfix">';
        mainDom += '<ul class="clearfix">';
        v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
          if (v3.showType === 3) {
            mainDom += '<li>' +
              '<a href="' + v3.linkUrl + '" style="background-image:url(' + v3.imgUrl + ');" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '"></a>' +
              '<div class="info">' +
              '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
              '<h3 title="' + v3.showName + '">' + v3.showName + '</h3>' +
              '<p title="' + v3.showSummary + '">' + v3.showSummary + '</p>' +
              '</a>' +
              '</div>' +
              '</li>';
          }
        })
        mainDom += '</ul><div class="moreService"><a href="/service/testing/service" class="moreServiceA">查看更多服务<i></i></a></div></div>';
        // 广告end
        mainDom += '</li>';
      })
      mainDom += '</ul>';
      mainDom += '</div>';
      mainDom += '</div>';
    } else {
      mainDom += '<div class="main-list">';
      v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
        mainDom += '<dl class="clearfix">';
        // 2级start
        mainDom += '<dt>' +
          '<a href="javascript:void(0);" data-id="' + v2.id + '" data-link="/' + v1.alias + '/' + v2.alias + '/' + v2.id + '">' + v2.name + '</a>' +
          '</dt>';
        // 2级end
        v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
          if (v3.showType === 2) {
            // 3级start
            mainDom += '<dd>';
            if (v3.isHot) {
              // 是否热门
              mainDom += '<a class="hot" href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
            } else {
              mainDom += '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
            }
            mainDom += '</dd>';
            // 3级end
          }
        })
        mainDom += '</dl>';

      })
      mainDom += '</div>';
      mainDom += '<div class="ads">';
      // 广告start
      mainDom += '<ul>';
      v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
        v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
          if (v3.showType === 3) {
            mainDom += '<li>' +
              '<a href="' + v3.linkUrl + '" style="background-image:url(' + v3.imgUrl + ');" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
              '</a>' +
              '<div class="info">' +
              '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
              '<h3 title="' + v3.showName + '">' + v3.showName + '</h3>' +
              '<p title="' + v3.showSummary + '">' + v3.showSummary + '</p>' +
              '</a>' +
              '</div>' +
              '</li>';
          }
        })
      })
      mainDom += '</ul>';
      // 广告end
      mainDom += '<div class="moreService">' +
        '<a href="/sku/product-certification/service">查看更多服务<i></i></a>' +
        '</div>';
      mainDom += '</div>';
    }
    mainDom += '</div>';
  })
  mainDom += '</div>';
  boxId.html(sideDom + mainDom)
}
