html,
body {
  margin: 0;
  padding: 0;
}
body {
  min-width: 1226px;
  background: #fff;
  font-family: <PERSON><PERSON>, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
a {
  text-decoration: none;
}
a img {
  border: 0;
}
ul,
li {
  list-style: none;
}
dd,
input,
ul,
li,
p,
h2,
div {
  padding: 0;
  margin: 0;
}
em,
i {
  font-style: normal;
}
button:focus {
  border: 0;
  outline: 0;
}
[v-cloak] {
  display: none;
}
input::-webkit-input-placeholder {
  color: #ACACAC !important;
}
input::placeholder {
  color: #ACACAC !important;
}
input:-ms-input-placeholder {
  color: #ACACAC !important;
}
input:-moz-placeholder {
  color: #ACACAC !important;
}
input::-webkit-input-placeholder {
  color: #ACACAC !important;
}
input::-ms-clear,
input::-ms-reveal {
  display: none;
}
.wrap-lg {
  min-width: 1226px;
  max-width: 1226px;
  box-sizing: border-box;
  margin: 0 auto;
}
.wrap-lg > * {
  box-sizing: border-box;
}
.clearfix:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.sgs {
  font-size: 12px;
  line-height: 38px;
  color: #626262;
  float: left;
}
.sgs a {
  color: #626262;
}
.headBox {
  width: 100%;
  height: 38px;
  background: #e3e3e3;
  line-height: 38px;
}
.head {
  width: 1226px;
  height: 38px;
  background: #e3e3e3;
  margin: 0 auto;
  font-size: 12px;
}
.head a:hover {
  color: #ca4300;
}
.headUl {
  width: auto;
  height: 36px;
  float: right;
  margin: 0;
  padding: 0;
}
.headLi {
  float: left;
  line-height: 36px;
  color: #626262;
  font-size: 12px;
  list-style: none;
  padding: 0 10px;
}
.headLi i {
  padding-right: 14px;
}
.headLi a {
  color: #626262;
  cursor: pointer;
}
.headLi a:hover,
.headLi a.mainColor {
  color: #ca4300;
}
.headLi .pro_icon {
  width: 40px;
  height: 17px;
  display: inline-block;
  vertical-align: middle;
  margin-left: 5px;
  cursor: pointer;
}
.headLi span:hover {
  color: #ca4300;
}
.headLi.first {
  width: 118px;
  height: 100%;
  margin-right: 0;
  background: #424242;
}
.headLi em {
  color: #ca4300;
  padding-left: 5px;
  font-weight: bold;
  font-size: 16px;
  vertical-align: middle;
}
.headLi-first-icon {
  width: 17px;
  height: 16px;
  margin-top: 10px;
  margin-left: 18px;
  display: block;
  float: left;
  background: url(../images/shop.png) no-repeat 100% 100%;
}
.headLi-first-word {
  width: auto;
  height: 100%;
  line-height: 39px;
  margin-right: 14px;
  display: block;
  float: right;
  font-size: 12px;
  color: #b0b0b0;
}
.n_topBar {
  background: #fff;
  height: 80px;
  line-height: 80px;
}
.n_topBar .logo {
  display: block;
  float: left;
}
.n_topBar .logo img {
  height: 80px;
  float: left;
}
.n_topBar .logo span {
  display: block;
  height: 22px;
  line-height: 22px;
  float: left;
  width: 198px;
  background: url('../../public/images/newPage/logo-des.png') no-repeat 100% 100%;
  margin-left: 15px;
  margin-top: 29px;
}
.n_topBar .menus {
  float: right;
  margin-top: 31px;
  height: 17px;
}
.n_topBar .menus span,
.n_topBar .menus a,
.n_topBar .menus em,
.n_topBar .menus label {
  color: #878787;
  font-size: 14px;
  font-style: normal;
  height: 17px;
  line-height: 17px;
  display: block;
  float: left;
}
.n_topBar .menus em {
  padding: 0 8px;
}
.n_topBar .menus em i {
  padding-left: 5px;
}
.n_topBar .menus .blod {
  color: #ca4300;
  cursor: pointer;
  font-weight: 700;
}
.n_topBar .menus .segmenttaion {
  color: #e5e5e5;
}
.n_topBar .menus .carBox {
  position: relative;
}
.n_topBar .menus .carBox em {
  float: left;
  padding: 100% 100% 0 10px;
}
.n_topBar .menus .carBox em #carNum {
  color: #ca4300;
}
.n_topBar .menus .carBox img {
  float: left;
}
.n_topBar .menus .car {
  width: 20px;
  height: 17px;
}
.n_searchModule {
  height: 130px;
  background: url('../../public/images/newPage/search_bg.jpg') 50% 50% no-repeat;
  width: 100%;
}
.n_searchModule .input {
  width: 624px;
  height: 40px;
  line-height: 40px;
  margin-left: 300px;
  position: relative;
  padding-top: 43px;
}
.n_searchModule .input i {
  position: absolute;
  top: 51px;
  left: 12px;
  display: block;
  width: 23px;
  height: 23px;
  background: url('../../public/images/newPage/search_icon.png') no-repeat 100% 100%;
}
.n_searchModule input {
  background: #fff;
  border: 0;
  outline: 0;
  color: #b3b3b3;
  font-size: 12px;
  height: 40px;
  line-height: 40px;
  width: 440px;
  float: left;
  padding-left: 48px;
  box-shadow: -100% 100% 0 green, 100% 100% 0 blue, 10px 10px 13px rgba(0, 0, 0, 0.3), 100% 100% 0 yellow;
}
.n_searchModule button,
.n_searchModule a {
  width: 109px;
  background: #ca4300;
  color: #fff;
  text-align: center;
  font-size: 18px;
  height: 40px;
  line-height: 40px;
  border: 0;
  outline: 0;
  float: left;
  cursor: pointer;
  box-shadow: -100% 100% 0 green, 100% 100% 0 blue, 10px 10px 13px rgba(0, 0, 0, 0.3), 100% 100% 0 yellow;
}
.n_searchModule button:hover,
.n_searchModule a:hover {
  background: #cc5500;
  transform: 0.5s;
}
.n_searchModule .linkageWord {
  position: absolute;
  top: 83px;
  left: 300px;
  border: 1px solid #ca4300;
  width: 595px;
  z-index: 2;
  background: #fff;
  display: none;
}
.n_searchModule .linkageWord li {
  height: 35px;
  line-height: 35px;
  overflow: hidden;
  list-style: none;
  padding-left: 15px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 383px;
}
.n_searchModule .linkageWord li:hover {
  background: #f5f5f5;
}
.n_searchModule .hotWord {
  width: 580px;
  margin-left: 300px;
  padding-top: 8px;
  height: 20px;
  overflow: hidden;
}
.n_searchModule .hotWord em,
.n_searchModule .hotWord span {
  color: #e8e8e8;
  font-size: 12px;
  padding-left: 12px;
  height: 20px;
  line-height: 20px;
  font-style: normal;
}
.n_searchModule .hotWord span {
  cursor: pointer;
}
.n_searchModule .hotWord span.hot {
  color: #ca4300;
}
.navBox {
  width: 1226px;
  margin: 0 auto;
  background: white;
  position: relative;
  height: 100px;
}
.nav {
  width: 100%;
  height: 0;
  margin: 0 auto;
  background: #ffffff;
}
.nav.fix {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2000;
  height: 100px;
  box-shadow: 100% 100% 10px rgba(0, 0, 0, 0.1);
}
.navwrap {
  height: 100px;
  background: #fff;
}
.navUl {
  float: left;
  width: auto;
  height: 100%;
  margin-left: 50px;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0;
}
.navLi {
  width: auto;
  height: 100px;
  line-height: 100px;
  font-size: 16px;
  color: #333333;
  float: left;
  position: relative;
  cursor: pointer;
  text-align: center;
  margin: 0 25px;
  list-style: none;
}
.navLi a {
  color: #333333;
}
.navLi a:hover {
  color: #ca4300;
  font-weight: bold;
}
.navLi.s:hover {
  color: #ca4300;
  font-weight: bold;
}
.navLi.t {
  margin-right: 0;
}
.navLi.t:hover {
  color: #ca4300;
  font-weight: bold;
}
.navBor {
  position: absolute;
  width: 1px;
  background: #eeeeee;
  height: auto;
  top: 10px;
  left: 200px;
}
.navImg {
  width: 113px;
  height: 53px;
  margin-top: 24px;
  float: left;
  padding-bottom: 20px;
}
.navImg img {
  width: 100%;
  height: 100%;
}
.drag {
  width: 402px;
  height: auto;
  padding: 10px 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  position: absolute;
  z-index: 9999;
  top: 97px;
  left: -170px;
  border-right: 1px solid rgba(0, 0, 0, 0.15);
  border-left: 1px solid rgba(0, 0, 0, 0.15);
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  display: none;
  border-top: 3px solid #ca4300;
  background: white;
}
.drag .dragLi {
  color: #333333;
  font-weight: normal;
}
.dragLi {
  width: 200px;
  height: 35px;
  line-height: 35px;
  font-size: 14px;
  float: left;
  text-align: center;
  list-style: none;
}
.dragAngel {
  width: 0;
  height: 0;
  position: absolute;
  overflow: hidden;
  font-size: 0;
  line-height: 0px;
  border-width: 0px 10px 11px;
  border-style: solid solid solid solid;
  border-left-color: transparent;
  border-right-color: transparent;
  border-top-color: transparent;
  border-bottom-color: #ca4300;
  display: none;
  left: 44px;
  top: 89px;
}
.serverConBox ul,
.newsBox ul {
  list-style-type: decimal;
}
.serverConBox ul li,
.newsBox ul li {
  list-style-type: disc;
  list-style-position: inside;
  padding-left: 1em;
}
.serverConBox ul li > p,
.newsBox ul li > p {
  display: inline;
}
.serverConBox ol,
.newsBox ol {
  list-style-type: decimal;
}
.serverConBox ol li,
.newsBox ol li {
  list-style-type: inherit;
  list-style-position: inside;
  padding-left: 1em;
}
.serverConBox ol li > p,
.newsBox ol li > p {
  display: inline;
}
.n_wrap {
  width: 1226px;
  margin: 0 auto;
}
.nav_first--li {
  position: relative;
}
.nav_first--li .nav_second {
  background: #333333;
  position: absolute;
  top: 84px;
  left: -17px;
  z-index: 2;
  width: 150px;
  display: none;
  border-top: 4px solid #ca4300;
}
.nav_first--li .nav_second dd {
  height: 35px;
  line-height: 35px;
  text-align: center;
}
.nav_first--li .nav_second dd a {
  color: #fff;
  display: inline-block;
  height: 35px;
  line-height: 35px;
  width: 100%;
}
.nav_first--li .nav_second dd a:hover {
  background: #fff;
  color: #ca4300;
  font-weight: bold;
}
.information {
  margin-top: 33px;
}
.files {
  margin-top: 20px;
}
.download_login {
  background: #ffeedf;
  height: 40px;
  line-height: 40px;
  text-align: center;
  position: relative;
}
.download_login span {
  color: #6a6a6a;
  font-size: 12px;
}
.download_login a {
  color: #ca4300;
  font-size: 14px;
  padding-left: 20px;
  font-weight: bold;
}
.download_login i {
  position: absolute;
  right: 30px;
  top: 15px;
  background: url('../images/200304/download/close.png') no-repeat 100% 100%;
  width: 12px;
  height: 12px;
}
.download_tab {
  width: 850px;
  height: 62px;
  line-height: 62px;
  background: #f8f8f8;
}
.download_tab ul {
  display: flex;
  flex-wrap: wrap;
  margin-left: 40px;
}
.download_tab ul li {
  position: relative;
  padding: 0 25px;
  cursor: pointer;
}
.download_tab ul li:hover::after {
  transform: scaleX(1);
}
.download_tab ul li:hover {
  color: #ca4300;
}
.download_tab ul li::after {
  position: absolute;
  content: "";
  top: 59px;
  left: 0;
  width: 100%;
  height: 3px;
  background: #ca4300;
  transform: scaleX(0);
  transition: 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}
.download_tab ul li.active::after {
  transform: scaleX(1);
}
.download_tab ul li i {
  display: none;
  width: 0;
  height: 0;
  position: absolute;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  border-width: 10px;
  border-style: solid dashed dashed dashed;
  border-color: transparent transparent #f8f8f8 transparent;
  top: 60px;
  left: 50%;
  transform: translateX(-50%);
}
.download_tab ul li.active i {
  display: block;
}
.download_filter {
  width: 810px;
  background: #f8f8f8;
  margin-top: 18px;
  padding: 0 20px 20px 20px;
  float: left;
}
.download_filter .download_filter_tit {
  color: #878787;
  font-size: 14px;
  width: 80px;
  text-align-last: justify;
  text-align: justify;
  flex: 1;
  padding-left: 20px;
}
.download_filter .download_filter_action {
  border-bottom: 1px dashed #b5b5b5;
  float: left;
  width: 100%;
  padding: 13px 0;
}
.download_filter .download_filter_action span,
.download_filter .download_filter_action em {
  display: inline-block;
  color: #878787;
  font-size: 14px;
  height: 14px;
  line-height: 14px;
}
.download_filter .download_filter_action span {
  float: left;
  margin-left: 15px;
  background: url(../images/200304/download/right.png) no-repeat 60px 2px;
  padding-right: 15px;
}
.download_filter .download_filter_action em {
  float: right;
  margin-right: 15px;
  background: url(../images/200304/download/bottom.png) no-repeat 60px 4px;
  padding-right: 15px;
  cursor: pointer;
}
.download_filter .download_filter_action em.active {
  color: #ca4300;
  background: url(../images/200304/download/top.png) no-repeat 60px 4px;
}
.download_filter .download_filster_checkboxGroup {
  width: 695px;
  display: flex;
  flex-wrap: wrap;
  margin-left: 10px;
  padding-right: 15px;
}
.download_filter .download_filster_checkboxGroup label {
  margin-right: 10px;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.download_filter .download_filster_checkboxGroup label span {
  font-size: 12px;
  color: #333;
  padding-left: 3px;
}
.download_filter .download_filster_checkboxGroup label i {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('../images/200304/checbox.png') no-repeat 100% 100%;
}
.download_filter .download_filster_checkboxGroup label.active i {
  background: url('../images/200304/checbox_active.png') no-repeat 100% 100%;
}
.download_filter .download_filster_industry,
.download_filter .download_filster_service {
  display: flex;
  border-bottom: 1px #d2d2d2 dashed;
  padding: 20px 0 5px 0;
  clear: both;
}
.download_filter .download_filster_keyword {
  display: flex;
  align-items: center;
  justify-content: start;
  width: 405px;
  float: left;
  padding: 20px 0 15px 0;
}
.download_filter .download_filster_keyword div {
  display: flex;
  align-items: center;
  width: 305px;
  margin-left: 10px;
}
.download_filter .download_filster_keyword input {
  width: 218px;
  height: 26px;
  line-height: 26px;
  background: #fff;
  border: 1px solid #b5b5b5;
  padding-left: 10px;
  font-size: 12px;
}
.download_filter .download_filster_keyword input.active {
  border: 1px solid #ca4300;
  color: #333;
}
.download_filter .download_filster_keyword button {
  width: 75px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  letter-spacing: 2px;
  color: #fff;
  font-size: 14px;
  background: #ca4300;
  border: 0;
  cursor: pointer;
  outline: 0;
}
.download_filter .download_filster_date {
  display: flex;
  align-items: center;
  justify-content: start;
  width: 355px;
  float: left;
  padding: 20px 20px 15px 30px;
}
.download_filter .download_filster_date input {
  width: 262px;
  height: 26px;
  line-height: 26px;
  background: #fff;
  border: 1px solid #b5b5b5;
  font-size: 12px;
  text-align: center;
}
.download_filter .download_filster_date input.active {
  border: 1px solid #ca4300;
  color: #333;
}
.download_filter .download_filter_result {
  clear: both;
  padding: 20px 0 0 0;
  justify-content: start;
  align-items: center;
  display: none;
  border-top: 1px #d2d2d2 dashed;
}
.download_filter .download_filter_result .download_filter_result--items {
  width: 720px;
  display: flex;
  flex-wrap: wrap;
}
.download_filter .download_filter_result .download_filter_result--items label {
  border: 1px solid #b5b5b5;
  height: 26px;
  line-height: 26px;
  color: #333;
  font-size: 12px;
  display: inline-block;
  margin: 0 10px 5px 0;
}
.download_filter .download_filter_result .download_filter_result--items label span {
  padding-left: 10px;
}
.download_filter .download_filter_result .download_filter_result--items label i {
  padding: 0 10px;
  cursor: pointer;
}
.download_filter .download_filter_result .download_filter_result--items em {
  font-size: 12px;
  color: #ca4300;
  height: 28px;
  line-height: 28px;
  display: inline-block;
  margin-left: 15px;
  cursor: pointer;
}
.download_list {
  margin: 10px 0 50px 0;
  float: left;
  width: 100%;
}
.download_list li {
  border-bottom: 1px solid #ececec;
  height: 149px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.download_list li:hover h2 a {
  color: #ca4300;
}
.download_list li:hover p {
  color: #333;
}
.download_list .download_list_content {
  height: 95px;
  display: flex;
  flex-direction: column;
  flex: 1;
}
.download_list .download_list_content.hasImg h2 a {
  max-width: 539px;
}
.download_list .download_list_content.hasImg label em {
  max-width: 486px;
}
.download_list .download_list_content h2 {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
}
.download_list .download_list_content h2 a {
  color: #333;
  font-size: 18px;
  font-weight: normal;
  max-width: 754px;
  display: inline-block;
  height: 22px;
  line-height: 22px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.download_list .download_list_content h2 .original {
  width: 31px;
  height: 18px;
  background: #EEEEEE;
  border-radius: 3px;
  font-size: 11px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #959595;
  line-height: 18px;
  margin-left: 10px;
  text-align: center;
}
.download_list .download_list_content h2 .vip {
  width: 31px;
  height: 18px;
  background: #FFEAC9;
  border-radius: 3px;
  font-size: 11px;
  font-family: Arial;
  font-weight: 400;
  color: #8E6C32;
  line-height: 18px;
  margin-left: 9px;
  text-align: center;
}
.download_list .download_list_content h2 i {
  font-size: 12px;
  height: 18px;
  line-height: 18px;
  font-weight: normal;
  margin-left: 10px;
  max-width: 74px;
  padding: 0 5px;
  text-align: center;
  background: #f7f7f7;
  color: #959595;
  border: 1px solid #e5e5e5;
  border-radius: 10px;
}
.download_list .download_list_content p {
  color: #959595;
  font-size: 12px;
  line-height: 20px;
  height: 40px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.download_list .download_list_content label {
  padding-top: 10px;
  height: 12px;
  line-height: 12px;
}
.download_list .download_list_content label span {
  color: #959595;
  font-size: 12px;
  padding-right: 18px;
  display: block;
  height: 12px;
  line-height: 12px;
  float: left;
  border-right: 1px solid #c2c2c2;
  padding-left: 18px;
}
.download_list .download_list_content label span:first-child {
  padding-left: 0;
}
.download_list .download_list_content label span:last-child {
  border-right: 0;
  padding-right: 0;
}
.download_list .download_list_content label em {
  display: block;
  max-width: 652px;
  height: 12px;
  line-height: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  float: left;
  padding-right: 18px;
  border-right: 1px solid #c2c2c2;
}
.download_list .download_list_content label em span:first-child {
  padding-left: 18px;
  border-left: 1px solid #c2c2c2;
}
.download_list .download_list_content label i {
  display: block;
  width: 10px;
  height: 16px;
  line-height: 16px;
  float: left;
  color: #c2c2cc;
}
.download_list .img {
  width: 150px;
  height: 95px;
  margin-left: 20px;
  background-size: cover;
}
.download_list h3 {
  text-align: center;
  font-weight: normal;
  height: 100px;
  line-height: 100px;
}
.files .files_list {
  margin: 10px 0 50px 0;
  float: left;
  width: 100%;
}
.files .files_list li {
  height: 44px;
  line-height: 44px;
  width: 100%;
  border-bottom: 1px solid #ececec;
  padding: 25px 0;
}
.files .files_list li .files_list_checkbox {
  width: 18px;
  height: 18px;
  display: block;
  background: url('../images/200304/download/checkbox_static.png') no-repeat 100% 100%;
  float: left;
  margin-top: 15px;
}
.files .files_list li .files_list_checkbox.active {
  background: url('../images/200304/download/checkbox_active.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_checkbox.disabled {
  background: url('../images/200304/download/checkbox_disabled.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_icon {
  display: block;
  width: 44px;
  height: 44px;
  float: left;
  margin-left: 10px;
}
.files .files_list li .files_list_icon.code {
  background: url('../images/200304/download/code.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_icon.excel {
  background: url('../images/200304/download/excel.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_icon.image {
  background: url('../images/200304/download/image.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_icon.link {
  background: url('../images/200304/download/link.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_icon.pdf {
  background: url('../images/200304/download/pdf.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_icon.ppt {
  background: url('../images/200304/download/ppt.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_icon.tar {
  background: url('../images/200304/download/tar.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_icon.text {
  background: url('../images/200304/download/text.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_icon.video {
  background: url('../images/200304/download/video.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_icon.word {
  background: url('../images/200304/download/word.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_icon.other {
  background: url('../images/200304/download/other.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_con {
  float: left;
  margin-left: 15px;
  height: 44px;
  line-height: 44px;
}
.files .files_list li .files_list_con .files_list_con_tit {
  height: 18px;
  line-height: 18px;
}
.files .files_list li .files_list_con .files_list_con_tit em {
  color: #333;
  font-size: 18px;
  font-weight: normal;
  max-width: 700px;
  display: block;
  height: 22px;
  line-height: 22px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  float: left;
}
.files .files_list li .files_list_con .files_list_con_tit i {
  font-size: 12px;
  height: 18px;
  line-height: 18px;
  font-weight: normal;
  margin-left: 10px;
  max-width: 74px;
  padding: 2px 5px;
  text-align: center;
  background: #f7f7f7;
  color: #959595;
  border-radius: 10px;
}
.files .files_list li .files_list_con .files_list_con_tit i.vip {
  width: 31px;
  height: 18px;
  background: #FFEAC9;
  border-radius: 3px;
  font-size: 11px;
  font-family: Arial;
  font-weight: 400;
  color: #8E6C32;
  line-height: 18px;
  margin-left: 9px;
  text-align: center;
}
.files .files_list li .files_list_con .files_list_con_tit span {
  font-size: 12px;
  height: 18px;
  line-height: 18px;
  font-weight: normal;
  margin-left: 10px;
  max-width: 74px;
  padding: 0 5px;
  text-align: center;
  background: #f7f7f7;
  color: #959595;
  border: 1px solid #e5e5e5;
  border-radius: 10px;
  float: left;
  overflow: hidden;
}
.files .files_list li .files_list_con .files_list_con_tag {
  padding-top: 14px;
  height: 12px;
  line-height: 12px;
}
.files .files_list li .files_list_con .files_list_con_tag span {
  color: #959595;
  font-size: 12px;
  padding-right: 18px;
  display: block;
  height: 12px;
  line-height: 12px;
  float: left;
  border-right: 1px solid #959595;
  padding-left: 18px;
}
.files .files_list li .files_list_con .files_list_con_tag span:first-child {
  padding-left: 0;
}
.files .files_list li .files_list_con .files_list_con_tag span:last-child {
  border-right: 0;
  padding-right: 0;
}
.files .files_list li .files_list_con .files_list_con_tag em {
  display: block;
  max-width: 420px;
  height: 12px;
  line-height: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  float: left;
}
.files .files_list li .files_list_con .files_list_con_tag em span:first-child {
  padding-left: 18px;
  border-left: 1px solid #c2c2c2;
}
.files .files_list li .files_list_con .files_list_con_tag i {
  display: block;
  width: 10px;
  height: 16px;
  line-height: 16px;
  float: left;
  color: #c2c2cc;
}
.files .files_list li .files_list_con_btn {
  float: right;
}
.files .files_list li .files_list_con_btn label {
  display: block;
  height: 16px;
  line-height: 16px;
}
.files .files_list li .files_list_con_btn label #share,
.files .files_list li .files_list_con_btn label #download {
  cursor: pointer;
}
.files .files_list li .files_list_con_btn label:last-child {
  margin-top: 13px;
}
.files .files_list li .files_list_con_btn label i {
  display: inline-block;
  width: 10px;
  height: 10px;
}
.files .files_list li .files_list_con_btn label i.share {
  background: url('../images/200304/download/share_static.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_con_btn label i.download {
  background: url('../images/200304/download/download_static.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_con_btn label span,
.files .files_list li .files_list_con_btn label a {
  color: #333;
  font-size: 12px;
}
.files .files_list li .files_list_con_btn label:hover i.share {
  background: url('../images/200304/download/share_active.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_con_btn label:hover i.download {
  background: url('../images/200304/download/download_active.png') no-repeat 100% 100%;
}
.files .files_list li .files_list_con_btn label:hover span,
.files .files_list li .files_list_con_btn label:hover a {
  color: #ca4300;
}
.files .files_list h3 {
  text-align: center;
  height: 400px;
  line-height: 400px;
  font-weight: normal;
}
.files .files_list .files_list_option {
  margin-top: 20px;
}
.files .files_list .files_list_option label {
  margin-right: 20px;
  cursor: pointer;
  height: 18px;
  line-height: 18px;
  float: left;
}
.files .files_list .files_list_option label:hover em,
.files .files_list .files_list_option label:hover span,
.files .files_list .files_list_option label:hover a {
  color: #ca4300;
}
.files .files_list .files_list_option label:hover i.files_list_option-download {
  background: url(../images/200304/download/download_active.png) no-repeat 100% 100%;
}
.files .files_list .files_list_option label:hover i.files_list_option-share {
  background: url(../images/200304/download/share_active.png) no-repeat 100% 100%;
}
.files .files_list .files_list_option label i {
  margin-right: 5px;
  display: block;
  float: left;
}
.files .files_list .files_list_option label i.files_list_option-all {
  width: 18px;
  height: 18px;
  background: url(../images/200304/download/checkbox_static.png) no-repeat 100% 100%;
}
.files .files_list .files_list_option label i.files_list_option-download,
.files .files_list .files_list_option label i.files_list_option-share {
  width: 10px;
  height: 10px;
  margin-top: 4px;
}
.files .files_list .files_list_option label i.files_list_option-download {
  background: url(../images/200304/download/download_static.png) no-repeat 100% 100%;
}
.files .files_list .files_list_option label i.files_list_option-share {
  background: url(../images/200304/download/share_static.png) no-repeat 100% 100%;
}
.files .files_list .files_list_option label em {
  font-size: 14px;
  color: #333;
  display: block;
  height: 18px;
  line-height: 18px;
  float: left;
}
.files .files_list .files_list_option label span,
.files .files_list .files_list_option label a {
  font-size: 12px;
  color: #333;
  float: left;
  display: block;
}
.files .files_list .files_list_option label.active .files_list_option-all {
  background: url(../images/200304/download/checkbox_active.png) no-repeat 100% 100%;
}
.n_position_box {
  position: relative;
  height: 412px;
  margin-bottom: 60px;
}
.homeCon8-body,
.homeCon9-body {
  padding-bottom: 10px;
}
.homeCon8-tab,
.homeCon9-tab {
  text-align: center;
  padding-bottom: 50px;
}
.homeCon8-tab span,
.homeCon9-tab span {
  border-radius: 50%;
  width: 8px;
  height: 8px;
  border: 1px solid #ca4300;
  display: inline-block;
  margin: 0 3px;
  cursor: pointer;
}
.homeCon8-tab span.active,
.homeCon9-tab span.active {
  background: #ca4300;
}
.homeCon9-tab {
  padding-bottom: 0 !important;
}
.homeCon9 {
  padding-bottom: 50px;
}
.homeCon9 .homeCon-head {
  height: 65px;
  line-height: 65px;
}
.homeCon9 .homeCon-head a.icon,
.homeCon9 .homeCon-head span {
  background: url('../images/home/<USER>/title_car.png') no-repeat left center;
  padding-left: 50px;
}
.homeCon9 .onlineOrder_msg {
  height: 20px;
  line-height: 20px;
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-bottom: 10px;
}
.homeCon9 .onlineOrder_msg ul {
  position: absolute;
  top: 0;
  left: 63px;
}
.homeCon9 .onlineOrder_msg li {
  height: 20px;
}
.homeCon9 .onlineOrder_msg span,
.homeCon9 .onlineOrder_msg a {
  font-size: 12px;
  padding-right: 15px;
  color: #6F6F6F;
}
.homeCon9 .onlineOrder_msg a:hover {
  color: #ca4300;
}
.homeCon9 .onlineOrder_msg .title {
  color: #ACACAC;
}
.homeCon9 .onlineOrder_msg .price {
  color: #CA4300;
}
.homeCon10 .homeCon-head a,
.homeCon10 .homeCon-head span {
  background: url('../images/home/<USER>/title_oiq.png') no-repeat left center;
  padding-left: 50px;
}
.homeCon10 .oiq-wrap {
  padding-bottom: 50px;
}
.homeCon10 .oiq-wrap .oiq-bg {
  float: left;
  width: 687px;
  height: 320px;
  position: relative;
  background-size: cover;
  cursor: pointer;
}
.homeCon10 .oiq-wrap .oiq-bg a {
  position: absolute;
  bottom: 44px;
  left: 45px;
  width: 140px;
  height: 38px;
  line-height: 38px;
  background: rgba(0, 0, 0, 0);
  border: 1px solid #FFFFFF;
  border-radius: 19px;
  text-align: center;
  text-transform: uppercase;
  overflow: hidden;
  z-index: 5;
}
.homeCon10 .oiq-wrap .oiq-bg a span {
  font-size: 16px;
  color: #FFFFFF;
  display: block;
  float: left;
  margin-left: 27px;
}
.homeCon10 .oiq-wrap .oiq-bg a i {
  display: block;
  width: 16px;
  height: 13px;
  background: url('../images/home/<USER>/oiq_btn.png') no-repeat 100% 100%;
  float: left;
  margin-left: 10px;
  margin-top: 12px;
}
.homeCon10 .oiq-wrap .oiq-bg a:hover {
  background: #ca4300;
  color: #fff;
  border: 1px solid #ca4300;
}
.homeCon10 .oiq-wrap .oiq-bg a:hover i {
  background: url('../images/home/<USER>/oiq_btn_activity.png') no-repeat 100% 100%;
}
.homeCon10 .oiq-wrap .oiq-bg a:before,
.homeCon10 .oiq-wrap .oiq-bg a:after {
  z-index: -1;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}
.homeCon10 .oiq-wrap .oiq-bg a:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 80px;
  background: #ca4300;
}
.homeCon10 .oiq-wrap .oiq-bg a:hover:before {
  width: 140px;
}
.homeCon10 .oiq-wrap .oiq-bg a:active {
  background: #ca4300;
}
.homeCon10 .oiq-wrap .oiq-plan {
  width: 524px;
  height: 320px;
  background: #FFFFFF;
  border: 3px solid #E5E5E5;
  box-sizing: border-box;
  float: right;
  padding: 40px;
}
.homeCon10 .oiq-wrap .oiq-plan h3 {
  height: 22px;
  line-height: 22px;
  font-size: 22px;
  font-weight: bold;
  color: #333333;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_msg {
  height: 40px;
  width: 100%;
  overflow: hidden;
  margin-top: 10px;
  position: relative;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_msg ul {
  position: absolute;
  top: 0;
  left: 0;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_msg ul li {
  height: 40px;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_msg ul li .time {
  font-size: 12px;
  font-weight: 400;
  color: #878787;
  line-height: 20px;
  height: 20px;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_msg ul li div * {
  font-size: 12px;
  padding-right: 3px;
  height: 20px;
  line-height: 20px;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_msg ul li b {
  color: #333;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_msg ul li b.lastCategory {
  max-width: 123px;
  overflow: hidden;
  height: 14px;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_msg ul li span {
  color: #878787;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq-search {
  margin-top: 20px;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq-search a {
  display: block;
  width: 424px;
  height: 45px;
  position: relative;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq-search a:hover p {
  border: 1px solid #ca4300;
  background: #fff;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq-search p {
  width: 424px;
  height: 45px;
  line-height: 45px;
  background: #F6F6F6;
  border-radius: 4px;
  font-size: 14px;
  color: #999;
  outline: 0;
  border: 1px solid #fff;
  padding-left: 12px;
  cursor: pointer;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq-search i {
  width: 53px;
  height: 45px;
  border-radius: 4px;
  background: url('../images/home/<USER>/oiq_search.png') no-repeat center center;
  display: block;
  position: absolute;
  top: 0;
  right: -10px;
  z-index: 2;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq-category {
  margin-top: 20px;
  width: 100%;
  height: 20px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq-category a,
.homeCon10 .oiq-wrap .oiq-plan .oiq-category span {
  color: #ca4300;
  font-size: 14px;
  padding-right: 3px;
  height: 20px;
  line-height: 20px;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_start {
  margin-top: 20px;
  width: 140px;
  text-align: center;
  height: 42px;
  line-height: 42px;
  background: #ca4300;
  font-size: 16px;
  color: #fff;
  display: block;
  position: relative;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_start .oiq_start_text {
  color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  text-align: center;
  font-size: 14px;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_start .oiq_start_layout {
  width: 0;
  height: 100%;
  background: #cc5500;
  position: absolute;
  top: 0;
  left: 0;
}
.homeCon10 .oiq-wrap .oiq-plan .oiq_start:hover .oiq_start_layout {
  width: 100%;
  transition: 0.4s;
}
.n_nav {
  background: #404040;
  height: 39px;
  width: 100%;
}
.n_nav ul {
  width: 1226px;
  margin: 0 auto;
  height: 39px;
  padding: 0;
}
.n_nav li {
  float: left;
  height: 39px;
  line-height: 39px;
  text-align: center;
  position: relative;
  width: 146px;
}
.n_nav li:hover::after {
  transform: scaleX(1);
}
.n_nav li::after {
  position: absolute;
  content: "";
  top: 36px;
  left: 0;
  width: 100%;
  height: 3px;
  background: #ca4300;
  transform: scaleX(0);
  transition: 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}
.n_nav li.active::after {
  transform: scaleX(1);
}
.n_nav li a {
  color: #fff;
  font-size: 15px;
  width: 145px;
  display: block;
  float: left;
  display: flex;
  justify-content: center;
  align-items: center;
}
.n_nav li em {
  display: inline-block;
  padding-right: 10px;
}
.n_nav li i {
  display: inline-block;
  width: 10px;
  height: 6px;
  background: url('../images/home/<USER>/arrow-down-static.png') no-repeat 100% 100%;
}
.n_nav li i.n_nav_hot {
  display: inline-block;
  width: 21px;
  height: 12px;
  background: url('../images/home/<USER>/nav_hot.png') no-repeat 100% 100%;
  position: absolute;
  top: 6px;
  right: 30px;
}
.n_nav li .hot {
  position: absolute;
  width: 25px;
  height: 18px;
  right: 35px;
  top: 4px;
  background: url('../images/home/<USER>') no-repeat 100% 100%;
}
.n_nav li .new {
  position: absolute;
  width: 20px;
  height: 15px;
  right: 20px;
  top: 4px;
  background: url('../images/home/<USER>') no-repeat 100% 100%;
}
.n_nav li span {
  float: left;
  background: rgba(191, 191, 191, 0.2);
  height: 15px;
  width: 1px;
  margin-top: 12px;
}
.n_nav li:hover:nth-child(3) i,
.n_nav li:hover:nth-child(4) i {
  background: url('../images/home/<USER>/arrow-up-static.png') no-repeat 100% 100%;
}
.n_nav li:hover:nth-child(2) i,
.n_nav li:hover:nth-child(1) i {
  background: url('../images/home/<USER>/arrow-down-active.png') no-repeat 100% 100%;
}
.n_nav li.active {
  background: #ca4300;
}
.n_nav li.active a,
.n_nav li.active a:hover {
  color: #fff;
}
.n_nav li.active span {
  background: #ca4300;
}
.n_nav li.active i {
  background: url('../images/home/<USER>/arrow-up-static.png') no-repeat 100% 100%;
}
.n_nav li.active:hover i {
  background: url('../images/home/<USER>/arrow-up-static.png') no-repeat 100% 100%;
}
.n_nav dl {
  background: #404040;
  width: 146px;
  position: absolute;
  top: 39px;
  border-top: 1px solid #ca4300;
  z-index: 2;
  display: none;
}
.n_nav dl a {
  width: 146px;
  color: #fff;
}
.n_nav dl a:hover {
  color: #ca4300;
  background: #fff;
  font-weight: bold;
}
.n_banner {
  height: 412px;
  width: 736px;
  position: absolute;
  left: 245px;
  top: 0;
}
.n_banner .banner,
.n_banner .swiper-wrapper,
.n_banner .swiper-container {
  height: 412px;
}
.n_banner .swiper-button-prev,
.n_banner .swiper-button-next {
  width: 50px;
  height: 50px;
  top: 181px;
  position: absolute;
}
.n_banner .swiper-button-prev {
  background: url('../../public/images/newPage/prev.png') 100% 100% no-repeat;
  left: 10px;
}
.n_banner .swiper-button-next {
  background: url('../../public/images/newPage/next.png') 100% 100% no-repeat;
  right: 10px;
}
.n_banner .bannerTitle {
  top: 100px;
}
.n_banner .bannerMore {
  left: 293px;
  bottom: 57px;
}
.n_banner .bannermoreword {
  bottom: 90px;
}
.n_banner .swiper-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 99;
  width: 100%;
  text-align: center;
  height: 35px;
}
.n_banner .swiper-pagination {
  display: flex;
  width: 100%;
}
.n_banner .swiper-pagination-bullet {
  flex: 1;
  height: 35px;
  line-height: 35px;
  border-radius: 0;
  margin-right: 1px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  opacity: 1;
}
.n_banner .swiper-pagination-bullet:last-child {
  margin-right: 0;
}
.n_banner .swiper-pagination-bullet:hover {
  color: #fff;
}
.n_banner .swiper-pagination-bullet-active {
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
}
.n_banner .swiper-button-next.swiper-button-disabled,
.n_banner .swiper-button-prev.swiper-button-disabled {
  opacity: 0;
}
.home_side {
  height: 412px;
  width: 245px;
  position: absolute;
  right: 0;
  top: 0;
  background: #fff;
}
.home_side .welcome_info {
  width: 200px;
  float: left;
  padding: 15px 20px 10px 20px;
}
.home_side .welcome_info .welcome_info_portrait {
  width: 55px;
  height: 55px;
  background: url('../images/home/<USER>/portrait.png') no-repeat 100% 100%;
  float: left;
}
.home_side .welcome_info .welcome_info_tips {
  float: left;
  white-space: nowrap;
}
.home_side .welcome_info .welcome_info_tips p {
  color: #333;
  font-size: 16px;
  margin-bottom: 5px;
  margin-top: 4px;
  max-width: 112px;
  height: 23px;
  line-height: 23px;
  background: url('../images/home/<USER>/home_side_portrait.png') no-repeat left center;
  font-weight: bold;
  padding-left: 30px;
  overflow: hidden;
  display: inline-block;
}
.home_side .welcome_info .welcome_info_tips em {
  color: #929299;
  font-size: 12px;
  background: url('../images/home/<USER>/icon-1.png') no-repeat 0 2px;
  padding-left: 25px;
  height: 17px;
  line-height: 17px;
  display: block;
}
.home_side .login_info {
  clear: both;
  width: 200px;
  padding: 5px 20px;
  height: 78px;
}
.home_side .login_info a {
  width: 100%;
  height: 35px;
  line-height: 35px;
  display: block;
  text-align: center;
  background: #ca4300;
  position: relative;
  border-radius: 3px;
}
.home_side .login_info a.login_info_static {
  margin-top: 9px;
  background: #f8f8f8;
  border: 1px solid #bfbfbf;
  color: #333;
  font-size: 14px;
  box-sizing: border-box;
}
.home_side .login_info a.login_info_active:hover .login_info_active_layout {
  width: 100%;
  transition: 0.4s;
}
.home_side .login_info a.login_info_static:hover .login_info_static_layout {
  width: 100%;
  transition: 0.4s;
}
.home_side .login_info a .login_info_active_text {
  color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  text-align: center;
  font-size: 14px;
}
.home_side .login_info a .login_info_static_text {
  color: #333;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  text-align: center;
}
.home_side .login_info a .login_info_active_layout {
  width: 0;
  height: 100%;
  background: #cc5500;
  position: absolute;
  top: 0;
  left: 0;
}
.home_side .login_info a .login_info_static_layout {
  width: 0;
  height: 100%;
  background: #efefef;
  position: absolute;
  top: 0;
  left: 0;
}
.home_side .static_items {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #E5E5E5;
  padding: 15px 0;
}
.home_side .static_items span {
  font-size: 12px;
  color: #878787;
  display: inline-block;
  padding-left: 12px;
  background: url('../images/home/<USER>/checked.png') no-repeat left center;
}
.home_side .account_info {
  clear: both;
  width: 204px;
  height: 87px;
  line-height: 87px;
  border-bottom: 1px solid #e5e5e5;
  margin: 0 auto;
}
.home_side .account_info a,
.home_side .account_info label {
  display: block;
  width: 33%;
  text-align: center;
  float: left;
  cursor: pointer;
}
.home_side .account_info em,
.home_side .account_info span {
  display: block;
  text-align: center;
  height: 30px;
  line-height: 30px;
}
.home_side .account_info em {
  font-size: 24px;
  color: #ca4300;
  padding-top: 15px;
}
.home_side .account_info span {
  font-size: 14px;
  color: #333;
}
.home_side .shortcut_menu {
  clear: both;
  padding: 12px 20px 8px 20px;
  width: 204px;
  float: left;
  height: 70px;
}
.home_side .shortcut_menu a {
  float: left;
  margin-right: 21px;
  width: 53px;
  text-align: center;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.home_side .shortcut_menu a:last-child {
  margin-right: 0;
}
.home_side .shortcut_menu a i {
  display: inline-block;
  width: 42px;
  height: 39px;
}
.home_side .shortcut_menu a i.shortcut_menu_report {
  background: url('../images/home/<USER>/report_static.svg') center center no-repeat;
}
.home_side .shortcut_menu a i.shortcut_menu_step {
  background: url('../images/home/<USER>/car_static.png') center center no-repeat;
}
.home_side .shortcut_menu a i.shortcut_menu_inquiry {
  background: url('../images/home/<USER>/inquiry_static.png') center center no-repeat;
}
.home_side .shortcut_menu a span {
  color: #333;
  font-size: 13px;
  padding-top: 5px;
}
.home_side .shortcut_menu a label {
  width: 11px;
  height: 12px;
  background: url('../images/home/<USER>/home_side_hot.png') no-repeat;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
}
.home_side .shortcut_menu a:hover span {
  color: #ca4300;
}
.home_side .shortcut_menu a:hover .shortcut_menu_report {
  background: url('../images/home/<USER>/report_active.svg') center center no-repeat;
}
.home_side .shortcut_menu a:hover .shortcut_menu_step {
  background: url('../images/home/<USER>/car_active.png') center center no-repeat;
}
.home_side .shortcut_menu a:hover .shortcut_menu_inquiry {
  background: url('../images/home/<USER>/inquiry_active.png') center center no-repeat;
}
.home_side .informaition_info {
  clear: both;
  width: 204px;
  padding: 0 20px;
  height: 131px;
}
.home_side .informaition_info .informaition_info_tit {
  border-top: 1px solid #e5e5e5;
  width: 100%;
  padding: 16px 0 10px 0;
  float: left;
  clear: both;
  height: 17px;
  line-height: 17px;
}
.home_side .informaition_info .informaition_info_tit span {
  border-bottom: 1px solid #fff;
  color: #333;
  font-size: 12px;
  float: left;
  margin-right: 15px;
  cursor: pointer;
}
.home_side .informaition_info .informaition_info_tit span.active {
  color: #ca4300;
  border-bottom: 1px solid #ca4300;
}
.home_side .informaition_info .informaition_info_tit a {
  float: right;
  color: #969696;
  font-size: 12px;
  border-bottom: 1px solid #fff;
  margin: 0;
}
.home_side .informaition_info .informaition_info_tit a:hover {
  color: #ca4300;
  border-bottom: 1px solid #ca4300;
}
.home_side .informaition_info .informaition_info_list {
  height: 100px;
  overflow: hidden;
  width: 209px;
  margin-left: -5px;
  position: relative;
}
.home_side .informaition_info .informaition_info_list a {
  background: url('../images/home/<USER>/dot.png') 0 center no-repeat;
  padding-left: 7px;
  max-width: 200px;
  margin-left: 5px;
}
.home_side .informaition_info .informaition_info_list ul {
  position: absolute;
}
.home_side .informaition_info .informaition_info_list li {
  width: 205px;
  overflow: hidden;
  height: 20px;
  line-height: 20px;
}
.home_side .informaition_info .informaition_info_list li a,
.home_side .informaition_info .informaition_info_list li span {
  height: 20px;
  line-height: 20px;
  display: block;
  float: left;
}
.home_side .informaition_info .informaition_info_list li a {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.home_side .informaition_info .informaition_info_list li a:hover {
  color: #ca4300;
}
.home_side .informaition_info .informaition_info_list li span {
  color: #ca4300;
  font-size: 12px;
}
.home_side .informaition_info .informaition_info_list li a {
  color: #929292;
  font-size: 12px;
}
.homeCon-head {
  height: 86px;
  line-height: 86px;
  border-top: 2px solid #dcdcdc;
}
.homeCon-head ul {
  float: right;
  margin-right: 10px;
  padding-top: 27px;
  position: relative;
}
.homeCon-head ul li {
  float: left;
  height: 30px;
  line-height: 30px;
  color: #333;
  cursor: pointer;
  width: 94px;
  text-align: center;
  font-size: 16px;
}
.homeCon-head ul li.active {
  background: #ca4300;
  color: #fff;
}
.homeCon-head ul li:hover {
  color: #fff;
}
.homeCon-head ul li.homeCon-head-bg {
  background: #ca4300;
  height: 30px;
  width: 84px;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -1;
}
.homeCon-head span,
.homeCon-head a {
  float: left;
  font-size: 18px;
  color: #333;
}
.homeCon-head a.more {
  float: right;
  color: #8b8b8b;
  font-size: 16px;
  display: none;
  padding-top: 34px;
  width: 50px;
}
.homeCon-head a.more.active {
  display: block;
}
.homeCon-head a.more:hover i {
  background: url('../images/newPage/more-a.png') no-repeat 100% 100%;
}
.homeCon-head a.more em {
  font-style: normal;
  display: block;
  height: 16px;
  line-height: 16px;
  float: left;
}
.homeCon-head a.more i {
  float: left;
  display: block;
  width: 16px;
  height: 16px;
  background: url('../images/newPage/more.png') no-repeat 100% 100%;
}
.homeCon-head a:hover {
  color: #ca4300;
}
.homeCon6 {
  padding-bottom: 70px;
}
.homeCon6-body .homeCon6-item {
  float: left;
  width: 395px;
  margin-right: 20px;
}
.homeCon6-body .homeCon6-item:last-child {
  margin-right: 0;
}
.homeCon6-item-bg {
  height: 200px;
  background: #ddd;
  width: 100%;
  text-align: center;
}
.homeCon6-item-bg a {
  display: block;
  width: 100%;
  height: 100%;
}
.homeCon6-item-bg span {
  color: #fff;
  font-size: 24px;
  display: block;
  height: 25px;
  line-height: 25px;
  text-align: center;
  padding-top: 85px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
.homeCon6-item-bg i {
  display: inline-block;
  height: 3px;
  width: 80px;
  background: #fff;
}
.homeCon6-item-list {
  position: relative;
  height: 175px;
  background: #fff;
}
.homeCon6-item-line {
  position: absolute;
  z-index: 1;
  width: 345px;
  padding: 35px 25px 0 25px;
}
.homeCon6-item-line div {
  width: 100%;
  height: 30px;
  line-height: 30px;
  border-bottom: 1px dashed #ddd;
}
.homeCon6-item-list ul {
  padding: 38px 25px 0 25px;
  position: absolute;
  z-index: 2;
  width: 345px;
}
.homeCon6-item-list li {
  float: left;
  padding-right: 20px;
  height: 30px;
  line-height: 30px;
}
.homeCon6-item-list.homeCon6-li li {
  width: 33.33%;
  padding-right: 0;
}
.homeCon6-item-list.homeCon6-li a.more {
  right: 57px;
}
.homeCon6-item-list li a {
  color: #666;
  font-size: 14px;
  padding-left: 10px;
  display: block;
  width: 100%;
  height: 30px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.homeCon6-item-list li a:hover {
  color: #ca4300;
}
.homeCon6-item-list .more {
  color: #fff;
  background: #ca4300;
  display: inline-block;
  width: 72px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  position: absolute;
  right: 40px;
  top: 100px;
  font-size: 13px;
  border-radius: 22px;
  z-index: 2;
}
.homeCon6-item-list .more:hover {
  background: #cc5500;
  transform: 0.5s;
}
.homeCon7 .homeCon7-body {
  height: 400px;
  padding-bottom: 70px;
}
.homeCon7 .homeCon7-body li {
  float: left;
  width: 395px;
  height: 400px;
  margin-right: 20px;
  position: relative;
}
.homeCon7 .homeCon7-body li:last-child {
  margin-right: 0;
}
.homeCon7 .homeCon7-body li.active dl {
  display: block;
}
.homeCon7 .homeCon7-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 395px;
  height: 400px;
  z-index: 1;
  background: #ddd;
  background-size: cover;
}
.homeCon7 .homeCon7-mark {
  background: #000;
  position: absolute;
  top: 0;
  left: 0;
  width: 395px;
  height: 400px;
  z-index: 2;
  opacity: 0;
}
.homeCon7 .homeCon7-con {
  position: absolute;
  top: 0;
  left: 0;
  width: 335px;
  height: 340px;
  z-index: 3;
  padding: 30px;
}
.homeCon7 .homeCon7-con:hover dl a {
  transform: translateY(0px);
  transition: all 0.8s;
  opacity: 1;
}
.homeCon7 .homeCon7-con h2 {
  color: #fff;
  padding: 5px 0;
  font-size: 24px;
}
.homeCon7 .homeCon7-con p {
  height: 48px;
  line-height: 24px;
  color: #fff;
  font-size: 15px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  width: 100%;
}
.homeCon7 .homeCon7-con dl {
  position: absolute;
  top: 230px;
  left: 30px;
  bottom: 30px;
  transition: all 0.25s;
  overflow: hidden;
}
.homeCon7 .homeCon7-con dl dd {
  height: 30px;
  line-height: 30px;
  _overflow: hidden;
  overflow: visible;
}
.homeCon7 .homeCon7-con dl dd a {
  font-size: 14px;
  text-decoration: none;
  color: #eee;
  display: block;
  transform: translateY(100px);
  opacity: 0;
}
.homeCon7 .homeCon7-con a {
  color: #fff;
  display: block;
  height: 30px;
  line-height: 30px;
  width: 335px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.homeCon7 .homeCon7-con a:hover {
  color: #ca4300;
}
.homeCon8-item,
.homeCon9-item {
  display: none;
}
.homeCon8-item.active,
.homeCon9-item.active {
  display: block;
}
.homeCon8-body .side {
  float: left;
  width: 305px;
  height: 406px;
  border-right: 2px solid #f9f9f9;
  padding: 20px 25px;
  box-sizing: border-box;
  position: relative;
}
.homeCon8-body .side *:before,
.homeCon8-body .side *:after {
  z-index: -1;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}
.homeCon8-body .side img {
  position: absolute;
  z-index: 1;
  width: 305px;
  height: 406px;
  top: 0;
  left: 0;
}
.homeCon8-body .side a {
  text-transform: uppercase;
  overflow: hidden;
  z-index: 5;
  width: 112px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  position: absolute;
  bottom: 50px;
  left: 95px;
  border: 1px solid #fff;
  color: #fff;
  font-size: 16px;
  font-style: normal;
  z-index: 2;
}
.homeCon8-body .side a:hover {
  border: 1px solid #ca4300;
}
.homeCon8-body .side a:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 80px;
  background: #ca4300;
}
.homeCon8-body .side a:hover:before {
  width: 112px;
}
.homeCon8-body .side a:active {
  background: #ca4300;
}
.homeCon8-body .list {
  float: right;
  width: 919px;
  background: #fff;
}
.homeCon8-body .list h2 {
  font-size: 16px;
  font-weight: normal;
  color: #333;
  width: 255px;
}
.homeCon8-body .list h2 span {
  display: block;
  float: left;
  height: 20px;
  line-height: 20px;
  max-width: 255px;
  overflow: hidden;
  font-size: 16px;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.homeCon8-body .list h2 span.active {
  max-width: 203px;
}
.homeCon8-body .list h2 i {
  margin-left: 5px;
  display: block;
  float: left;
  width: 47px;
  height: 19px;
  background: url('../images/newPage/isBuy.png') no-repeat 100% 100%;
  display: none;
}
.homeCon8-body .list p {
  font-size: 12px;
  color: #b4b4b4;
  width: 255px;
  height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-top: 5px;
}
.homeCon8-body .list img {
  width: 255px;
  height: 100px;
  background: #ddd;
  display: block;
  margin-top: 11px;
  overflow: hidden;
}
.homeCon8-body .list li {
  float: left;
  width: 255px;
  border-right: 2px solid #f9f9f9;
  padding: 23px 25px;
  border-bottom: 2px solid #f9f9f9;
}
.homeCon8-body .list li:nth-child(3n) {
  border-right: 0;
}
.homeCon8-body .list li:nth-child(n+4) {
  border-bottom: 0;
}
.homeCon8-body .list li:hover h2 span {
  color: #ca4300;
}
.homeCon8-body .list li:hover p {
  color: #333;
}
.homeCon9-body .hot {
  width: 30px;
  text-align: center;
  height: 18px;
  line-height: 18px;
  background: #ca4300;
  display: block;
  color: #fff;
  font-size: 10px;
  border-radius: 3px;
}
.homeCon9-body .side {
  float: left;
  width: 305px;
  height: 406px;
  border-right: 2px solid #f9f9f9;
  padding: 20px 19px;
  box-sizing: border-box;
  position: relative;
  background: #fff;
}
.homeCon9-body .side h3 {
  font-size: 16px;
  color: #333333;
}
.homeCon9-body .side li {
  height: 100px;
  width: 266px;
  position: relative;
  background: #F8F8F8;
  margin-top: 16px;
  padding: 19px 0 0 26px;
  box-sizing: border-box;
  background-size: cover;
}
.homeCon9-body .side li:nth-child(2) label {
  color: #FF6600;
}
.homeCon9-body .side li:nth-child(3) label {
  color: #FF9000;
}
.homeCon9-body .side li label {
  font-style: italic;
  font-size: 20px;
  font-weight: bold;
  color: #ca4300;
  position: absolute;
  top: 17px;
  left: 5px;
}
.homeCon9-body .side li h2 {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 190px;
  height: 20px;
  line-height: 20px;
}
.homeCon9-body .side li .sub-title {
  font-size: 12px;
  color: #878787;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 190px;
  height: 20px;
  line-height: 20px;
  margin: 5px 0 9px 0;
}
.homeCon9-body .side li .num span {
  font-size: 12px;
  color: #CA4300;
  padding-right: 12px;
}
.homeCon9-body .side li .num span i {
  font-size: 14px;
  padding-right: 5px;
}
.homeCon9-body .side li .num em {
  font-size: 12px;
  color: #B0B0B0;
}
.homeCon9-body .list {
  float: right;
  width: 919px;
  background: #fff;
}
.homeCon9-body .list h2 {
  font-size: 16px;
  font-weight: normal;
  color: #333;
  width: 255px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-wrap: break-word;
  white-space: normal;
  word-break: break-all;
}
.homeCon9-body .list h2 span {
  display: block;
  float: left;
  height: 20px;
  line-height: 20px;
  max-width: 255px;
  overflow: hidden;
  font-size: 16px;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.homeCon9-body .list h2 span.active {
  max-width: 203px;
}
.homeCon9-body .list h2 i {
  margin-left: 5px;
  display: block;
  float: left;
  width: 47px;
  height: 19px;
  background: url('../images/newPage/isBuy.png') no-repeat 100% 100%;
}
.homeCon9-body .list li {
  float: left;
  width: 255px;
  border-right: 2px solid #f9f9f9;
  padding: 23px 25px 20px 25px;
  border-bottom: 2px solid #f9f9f9;
  height: 158px;
}
.homeCon9-body .list li:nth-child(3n) {
  border-right: 0;
}
.homeCon9-body .list li:nth-child(n+4) {
  border-bottom: 0;
}
.homeCon9-body .list li h2 {
  padding: 8px 0 5px 0;
  height: 16px;
  line-height: 16px;
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: flex;
  justify-content: space-between;
}
.homeCon9-body .list li h2 span {
  display: inline-block;
  text-align: left;
  font-size: 16px;
  color: #333333;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 190px;
  height: 20px;
}
.homeCon9-body .list li h2 em {
  display: inline-block;
  text-align: right;
  font-size: 13px;
  color: #B0B0B0;
}
.homeCon9-body .list li h3 {
  font-size: 12px;
  color: #B4B4B4;
  line-height: 24px;
  height: 24px;
  font-weight: 400;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: flex;
  justify-content: space-between;
}
.homeCon9-body .list li h3 span {
  display: inline-block;
  text-align: left;
  font-size: 12px;
  color: #878787;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 190px;
  height: 20px;
}
.homeCon9-body .list li h3 em {
  display: inline-block;
  text-align: right;
  font-size: 12px;
  color: #ca4300;
}
.homeCon9-body .list li h3 em i {
  font-size: 14px;
  padding-right: 5px;
}
.homeCon9-body .list li .img {
  position: relative;
}
.homeCon9-body .list li .img label {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 2;
}
.homeCon9-body .list li .img img {
  width: 255px;
  height: 100px;
  display: block;
  margin-top: 11px;
  overflow: hidden;
  background: #999;
}
.homeCon9-body .list li .img p {
  position: absolute;
  z-index: 2;
  bottom: 10px;
  right: 10px;
  color: #fff;
  font-family: Arial;
}
.homeCon9-body .list li .img p b {
  font-size: 15px;
  padding: 0 3px;
}
.homeCon9-body .list li .img p i,
.homeCon9-body .list li .img p span {
  font-size: 12px;
}
.n_position_box_inner .main {
  display: block;
}
.n_position_box_inner .n_category {
  z-index: 2;
  background: #fff;
}
.helpinfo {
  width: 100%;
  min-width: 1226px;
  padding: 61px 0 30px;
  box-sizing: border-box;
  border-bottom: 1px solid #282828;
  background: #111111;
  margin: 0 auto;
  line-height: 1;
}
.helpinfo:after {
  content: "\0020";
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  overflow: hidden;
}
.helpinfo .helpinfo-item {
  float: left;
  width: 165px;
}
.helpinfo .helpinfo-item .hotlineText,
.helpinfo .helpinfo-item .highlight {
  color: #ca4300;
}
.helpinfo .helpinfo-item.helpinfo-email {
  width: 224px;
  padding-right: 20px;
}
.helpinfo .helpinfo-item li {
  margin-bottom: 17px;
  color: #aaa;
  font-size: 16px;
}
.helpinfo .helpinfo-title {
  color: #fff;
  font-size: 16px;
  margin: 0;
  margin-bottom: 30px;
}
.helpinfo ul a,
.helpinfo ul span,
.helpinfo ul em,
.helpinfo ul label {
  color: #858585;
  font-size: 14px;
}
.helpinfo ul a:hover {
  color: #ca4300;
}
.helpinfo ul label {
  display: inline-block;
  position: relative;
  top: -21px;
}
.helpinfo ul em {
  display: inline-block;
  line-height: 22px;
  width: 157px;
  height: 44px;
}
.helpinfo .QRcode {
  float: right;
  border-left: 1px solid #333;
  width: 268px;
  padding-left: 52px;
}
.helpinfo .QRcode .QRcode_img {
  float: left;
  margin-top: 18px;
  margin-right: 35px;
}
.helpinfo .QRcode .QRcode_img.m-r {
  margin-right: 0;
}
.helpinfo .QRcode .QRcode_img img {
  width: 114px;
  height: 114px;
}
.helpinfo .QRcode .QRcode_img span {
  padding-top: 18px;
  width: 114px;
  text-align: center;
  font-size: 14px;
  color: #858585;
  display: block;
}
.helpinfo .helpinfo_seo {
  padding-top: 35px;
  font-size: 14px;
}
.helpinfo .helpinfo_seo * {
  color: #858585;
}
.helpinfo .helpinfo_seo a:hover {
  color: #ca4300;
}
.footer {
  background: #111111;
  padding: 23px 0 57px 0;
}
.footer a,
.footer span {
  font-size: 14px;
  color: #464646;
}
.footer a {
  padding-left: 34px;
}
.footer a img {
  height: 13px;
}
.footer a:focus,
.footer a:active,
.footer a:visited,
.footer a:focus-within,
.footer a:focus-visible,
.footer a:target {
  color: #464646;
}
.footer a:hover,
.footer a:hover span {
  color: #ca4300;
}
#categoryDetail .swiper-bottom {
  position: absolute;
  bottom: 0;
  left: 45.5%;
}
#categoryDetail .swiper-pagination {
  position: relative;
  display: inline-block;
  top: -5px;
}
#categoryDetail .swiper-pagination .swiper-pagination-bullet {
  display: inline-block;
  width: 10px;
  height: 10px;
  border: 1px solid #ca4300;
  border-radius: 50%;
  background: transparent;
  opacity: 1;
  margin: 0 4px;
}
#categoryDetail .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: #ca4300;
}
#categoryDetail .swiper-button-prev {
  position: relative;
  width: 12px;
  height: 24px;
  overflow: hidden;
  right: auto;
  top: auto;
  left: auto;
  display: inline-block;
  background-size: auto 60%;
  margin-top: 0;
}
#categoryDetail .swiper-button-next {
  position: relative;
  width: 12px;
  height: 24px;
  overflow: hidden;
  right: auto;
  top: auto;
  left: auto;
  display: inline-block;
  background-size: auto 60%;
  margin-top: 0;
}
.cooperation {
  width: 100%;
  height: auto;
  background: #fafafa;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  padding-bottom: 20px;
}
.cooperation .pages {
  bottom: 25px;
}
.cooperationBox {
  width: 1226px;
  margin: 0 auto;
  position: relative;
}
.cooperationDemo {
  margin-top: 25px;
  display: none;
}
.cooperationDemo ul {
  max-height: 140px;
  height: auto;
  overflow: hidden;
}
.cooperationDemo ul li {
  float: left;
  width: 590px;
  margin-right: 46px;
  border-bottom: 1px dashed #d8d8d8;
}
.cooperationDemo ul li:nth-child(2n) {
  margin-right: 0;
}
.cooperationDemo ul li i,
.cooperationDemo ul li a,
.cooperationDemo ul li time {
  display: block;
  height: 34px;
  line-height: 34px;
  float: left;
}
.cooperationDemo ul li i {
  width: 25px;
  background: url('../images/category/v2/dot.png') no-repeat center center;
}
.cooperationDemo ul li i.new {
  background: url('../images/category/v2/new.png') no-repeat center left;
}
.cooperationDemo ul li a {
  width: 480px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 16px;
  color: #333;
  padding-left: 5px;
}
.cooperationDemo ul li a:hover {
  color: #ca4300;
}
.cooperationDemo ul li time {
  width: 65px;
  color: #c2c2c2;
  font-size: 12px;
  float: right;
}
.cooperationDemo .cooperationDemo_slide {
  text-align: center;
  margin-top: 20px;
}
.cooperationDemo .cooperationDemo_slide span {
  width: 28px;
  height: 28px;
  background: url('../images/category/v2/arrow-down.png') no-repeat 100% 100%;
  display: inline-block;
  cursor: pointer;
}
.cooperationDemo .cooperationDemo_slide span.active {
  background: url('../images/category/v2/arrow-up.png') no-repeat 100% 100%;
}
.resources {
  width: 1226px;
  height: auto;
  margin: 0 auto;
  padding-bottom: 10px;
}
.resources .serverBody {
  display: none;
}
.resources .serverBody ul {
  max-height: 140px;
  height: auto;
  overflow: hidden;
}
.resources .serverBody ul li {
  width: 408px;
  float: left;
}
.resources .serverBody ul li a {
  padding-left: 25px;
  width: 365px;
  height: 35px;
  line-height: 35px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: block;
  color: #333;
  font-size: 16px;
}
.resources .serverBody ul li a:hover {
  color: #ca4300;
}
.resources .serverBody ul li a.excel {
  background: url('../images/category/v2/file-excel.png') center left no-repeat;
}
.resources .serverBody ul li a.pdf {
  background: url('../images/category/v2/file-pdf.png') center left no-repeat;
}
.resources .serverBody ul li a.url {
  background: url('../images/category/v2/file-url.png') center left no-repeat;
}
.resources .serverBody ul li a.video {
  background: url('../images/category/v2/file-video.png') center left no-repeat;
}
.resources .serverBody ul li a.word {
  background: url('../images/category/v2/file-word.png') center left no-repeat;
}
.resources .serverBody ul li a.zip {
  background: url('../images/category/v2/file-zip.png') center left no-repeat;
}
.resources .serverBody ul li a.img {
  background: url('../images/category/v2/file-img.png') center left no-repeat;
}
.resources .serverBody ul li a.ppt {
  background: url('../images/category/v2/file-ppt.png') center left no-repeat;
}
.resources .serverBody ul li a.other {
  background: url('../images/category/v2/file-other.png') center left no-repeat;
}
.resources .serverBody .serverBody_slide {
  text-align: center;
  margin-top: 20px;
}
.resources .serverBody .serverBody_slide span {
  width: 28px;
  height: 28px;
  background: url('../images/category/v2/arrow-down.png') no-repeat 100% 100%;
  display: inline-block;
  cursor: pointer;
}
.resources .serverBody .serverBody_slide span.active {
  background: url('../images/category/v2/arrow-up.png') no-repeat 100% 100%;
}
#case .serverHead .serverLi,
#files .serverHead .serverLi {
  padding: 0 18px;
  min-width: 100px;
}
#case .serverHead .serverLi a,
#files .serverHead .serverLi a {
  position: absolute;
  right: 0;
  display: none;
  top: 0;
}
#case .serverHead .serverLi.demo a,
#files .serverHead .serverLi.demo a {
  display: block;
}
.serverLi {
  width: auto;
  height: 74px;
  line-height: 80px;
  font-size: 16px;
  color: #333333;
  float: left;
  cursor: pointer;
  list-style: none;
  text-align: center;
  transition: border-bottom-color 0.4s;
}
.serverLi a {
  color: #333;
  padding: 0 18px;
  display: inline-block;
  min-width: 100px;
}
.serverLi.solu {
  border-bottom: 3px solid #ca4300;
  font-style: normal;
  font-stretch: normal;
  letter-spacing: normal;
  color: #333333;
  font-weight: bold;
}
.serverLi.demo {
  border-bottom: 3px solid #ca4300;
  font-style: normal;
  font-stretch: normal;
  letter-spacing: normal;
  color: #333333;
  font-weight: bold;
}
.serverLi.demo a {
  display: inline-block;
  font-weight: normal;
  color: #ca4300;
  font-size: 16px;
  padding-right: 10px;
}
.serverLi.demo a:hover {
  font-weight: bold;
}
.serverLi.demo a i {
  font-size: 0;
  line-height: 0;
  border-width: 5px;
  border-color: #ca4300;
  border-right-width: 0;
  border-style: dashed;
  border-left-style: solid;
  border-top-color: transparent;
  border-bottom-color: transparent;
  position: absolute;
  right: 0;
  top: 35px;
}
.serverLi.reletive {
  border-bottom: 3px solid #ca4300;
  font-style: normal;
  font-stretch: normal;
  letter-spacing: normal;
  color: #333333;
  font-weight: bold;
}
.serverLiActive {
  border-bottom: 3px solid #ca4300;
  font-style: normal;
  font-stretch: normal;
  letter-spacing: normal;
  color: #ca4300;
  font-weight: bold;
}
.serverLiActive a {
  color: #ca4300;
}
.search_list li {
  border-bottom: 1px solid #eeeeee;
  padding: 10px 0;
  clear: both;
}
.search_list li h2 {
  font-weight: normal;
  margin: 10px 0;
}
.search_list li h2 a {
  font-size: 16px;
  color: #000;
  margin-bottom: 20px;
  cursor: pointer;
}
.search_list li h2 a:hover {
  color: #ca4300;
}
.search_list li h2 span {
  font-size: 12px;
  color: #eee;
  display: inline-block;
  height: 14px;
  line-height: 14px;
  margin-left: 5px;
  background: #888;
  padding: 3px;
}
.search_list li h2 span.active {
  background: #ca4300;
  color: #fff;
}
.search_list li p {
  color: #999;
  font-size: 14px;
}
.search_list li .search_list_down a {
  font-size: 14px;
  color: #000;
  padding-left: 25px;
  background: url('../images/down.png') center left no-repeat;
  height: 35px;
  line-height: 35px;
  display: inline-block;
}
.search_list .search_empty_list {
  text-align: center;
  height: 400px;
  line-height: 400px;
}
.ticket .messageCon {
  background: #fafafa;
  margin-top: 21px;
  margin-bottom: 20px;
  position: relative;
}
.ticket .messageCon .messageConT {
  padding: 10px 0;
  font-size: 32px;
  text-align: center;
  font-weight: 400;
}
.ticket .messageCon .messageConS {
  text-align: center;
  font-size: 16px;
  color: #000000;
  padding-bottom: 20px;
}
.ticket .messageCon .messageCon_switch {
  position: absolute;
  right: 20px;
  top: 20px;
}
.ticket .messageCon .messageCon_switch a {
  display: inline-block;
  width: 35px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  font-size: 14px;
  color: #666;
}
.ticket .messageCon .messageCon_switch a.active {
  color: #fff;
  background: #ca4300;
}
.ticket .ticket_accept {
  padding-bottom: 100px;
}
.ticket .ticket_accept .accept {
  height: 36px;
}
.ticket .ticket_accept .acceptIcon {
  margin-left: 135px;
}
.ticket .ticket_accept .submit {
  width: 216px;
  margin-left: 310px;
}
.ticket textarea {
  font-family: Arial;
}
.yoursInfo {
  height: auto;
}
.yoursInfo .yoursT {
  height: 44px;
  background: #fafafa;
  text-align: center;
  line-height: 44px;
  margin-bottom: 44px;
  font-size: 16px;
}
.yoursInfo .yoursInfoC {
  width: 905px;
}
.yoursInfo .yoursInfoC .yoursInfoCC {
  float: left;
  width: 400px;
  margin-right: 50px;
  display: block;
  position: relative;
}
.yoursInfo .yoursInfoC .yoursInfoCC .selectC {
  width: 400px;
  padding: 5px 20px;
  box-sizing: border-box;
  outline: none;
  color: #000000;
  font-size: 14px;
}
.yoursInfo .yoursInfoC .yoursInfoCC .selectC1 {
  width: 400px;
  padding: 5px 20px;
  box-sizing: border-box;
  outline: none;
  color: #000000;
}
.yoursInfo .yoursInfoC .yoursInfoCC button {
  cursor: pointer;
  position: absolute;
  top: 38px;
  right: 2px;
  z-index: 2;
  height: 37px;
  line-height: 38px;
  border: 0;
  background: #ca4300;
  border-radius: 0 5px 5px 0;
  color: #fff;
  font-size: 14px;
  width: 100px;
}
.yoursInfo .yoursInfoC .yoursInfoCC button.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.bannerInfo {
  width: 345px;
  background: #FFFFFF;
  box-shadow: 0px 7px 10px 0px rgba(0, 0, 0, 0.12);
  padding-bottom: 5px;
  z-index: 99;
}
.bannerInfo .el-form-item {
  margin-bottom: 12px !important;
}
.bannerInfo .el-form-item__label {
  line-height: 20px;
  padding: 0 12px 7px 0;
}
.bannerInfo .el-form-item__error {
  padding-top: 1px;
}
.bannerInfo .el-input {
  height: 32px;
  line-height: 32px;
}
.bannerInfo .el-input-group__prepend {
  border: 0;
  background: #F2F2F2;
}
.bannerInfo .side_line {
  width: 100%;
  height: 4px;
  background: #FF6600;
}
.bannerInfo .side_line.step {
  width: 317px;
}
.bannerInfo .side_wrap {
  padding: 0 28px 0 27px;
}
.bannerInfo .side_wrap h2 {
  font-size: 18px;
  color: #333;
  padding-top: 22px;
}
.bannerInfo .side_wrap p.des {
  color: #f60;
  font-size: 14px;
  padding: 10px 0 15px 0;
}
.bannerInfo .side_wrap input {
  background: #F2F2F2;
  height: 32px;
  border: none;
  font-family: Roboto, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.bannerInfo .side_wrap textarea {
  background: #F2F2F2;
  border: none;
  font-family: Roboto, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.bannerInfo .side_wrap .side_tab {
  height: 25px;
  margin-top: 10px;
  margin-bottom: 0 !important;
}
.bannerInfo .side_wrap .side_tab span {
  display: inline-block;
  width: 75px;
  height: 26px;
  text-align: center;
  line-height: 20px;
  font-size: 14px;
  color: #A6A6A6;
  background: #fff;
  border-radius: 4px 4px 0px 0px;
  cursor: pointer;
}
.bannerInfo .side_wrap .side_tab span.active {
  color: #333;
  background: #F2F2F2;
}
.bannerInfo .side_wrap .side_tips {
  padding: 0 !important;
  margin: 0 !important;
}
.bannerInfo .side_wrap .side_tips * {
  padding: 0;
  margin: 0;
  color: #f49000;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
}
.bannerInfo .side_wrap a.button {
  margin-top: 6px;
  color: #fff;
  font-size: 18px;
  width: 290px;
  line-height: 42px;
  height: 42px;
  text-align: center;
  background: #ca4300;
  border-radius: 3px;
  display: block;
  cursor: pointer;
}
.bannerInfo .side_wrap a.button:hover {
  background: #f49000;
}
.bannerInfo .side_wrap .side_protocol {
  margin-bottom: 0 !important;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 10px;
}
.bannerInfo .side_wrap .side_protocol .el-checkbox {
  display: flex;
  align-items: center;
}
.bannerInfo .side_wrap .side_protocol .el-checkbox__input {
  position: relative;
}
.bannerInfo .side_wrap .side_protocol .el-checkbox__label {
  font-size: 12px;
}
.bannerInfo .side_wrap .side_protocol a {
  color: #ca4300;
}
.bannerInfo .side_wrap .side_protocol a:hover {
  color: #f49000;
}
.bannerInfo .side_wrap .side_protocol .is-checked .el-checkbox__label {
  color: #606266;
}
.bannerInfo .side_success {
  padding: 0 28px 20px 27px;
  text-align: center;
}
.bannerInfo .side_success .side_success_email {
  width: 147px;
  height: 100px;
  padding-top: 32px;
}
.bannerInfo .side_success .side_success_title {
  font-size: 22px;
  font-weight: bold;
  color: #333333;
  padding-top: 19px;
}
.bannerInfo .side_success .side_success_des {
  font-size: 14px;
  color: #333333;
  padding-top: 23px;
  padding-bottom: 14px;
}
.bannerInfo .side_success .side_success_ERcode {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.bannerInfo .side_success .side_success_ERcode img {
  width: 78px;
  height: 80px;
}
.bannerInfo .side_success .side_success_ERcode p {
  width: 185px;
  font-size: 14px;
  color: #878787;
  line-height: 24px;
}
.bannerInfo .side_success .side_success_ERcode p i {
  color: #f60;
}
.bannerInfo .side_success .side_success_userinfo {
  background: #F2F2F2;
  width: 300px;
  min-height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #000;
  margin-bottom: 20px;
  word-wrap: break-word;
  white-space: normal;
  word-break: break-all;
}
.bannerInfo .side_success .side_success_userinfo a {
  color: #ca4300;
}
.bannerInfo .side_success .side_success_tips {
  color: #878787;
  font-size: 14px;
  line-height: 24px;
  text-align: center;
}
.bannerInfo .side_success .side_success_tips a {
  color: #ca4300;
  text-decoration: underline;
}
.bannerInfo .side_success .side_success_countdown {
  font-size: 14px;
  color: #999;
  margin-top: 10px;
}
.bannerInfo .side_success .side_success_countdown i {
  color: #ca4300;
}
.bannerInfo .side_step {
  padding: 0 28px 0 27px;
}
.bannerInfo .side_step h2 {
  width: 155px;
  font-size: 16px;
  font-weight: bold;
  font-style: italic;
  color: #333333;
  background: url('../images/sku/arrow.png') no-repeat 0 center;
  padding-left: 20px;
  margin-top: 13px;
}
.bannerInfo .side_step a.button {
  margin-top: 23px;
  color: #fff;
  font-size: 18px;
  width: 290px;
  line-height: 42px;
  height: 42px;
  text-align: center;
  background: #ca4300;
  border-radius: 3px;
  display: block;
  cursor: pointer;
}
.bannerInfo .side_step a.button:hover {
  background: #f49000;
}
.bannerInfo .side_step .side_step_gif {
  margin-top: 33px;
  width: 287px;
  height: 137px;
  border: 1px solid #D2D2D2;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.bannerInfo .side_step .side_step_gif span {
  position: absolute;
  width: 49px;
  height: 11px;
  font-size: 12px;
  color: #ddd;
  display: inline-block;
  top: -9px;
  left: 30px;
  background: #fff;
  padding: 0 3px;
}
.bannerInfo .side_step img {
  cursor: pointer;
}
.bannerInfo .side_step a.more {
  padding-top: 13px;
  font-size: 12px;
  color: #000000;
  opacity: 0.5;
  display: block;
  text-align: right;
  padding-bottom: 20px;
}
.bannerInfo .side_step a.more:hover {
  color: #ca4300;
}
.abc {
  position: fixed;
  top: 0;
}
.detailbannerBox {
  width: 100%;
  height: 320px;
  margin-bottom: 10px;
}
.detailbannerBox .detailbanner {
  width: 858px;
  height: 290px;
  float: left;
  overflow: hidden;
}
.detailbannerBox .detailbanner img {
  display: block;
  width: 858px;
  height: 290px;
  background: #ccc;
}
.detailbannerContent {
  width: 100%;
  margin-bottom: 10px;
  min-height: 110px;
}
.nlSearchBox {
  width: 1226px;
  margin: 0 auto;
  padding-top: 13px;
}
.nlLists {
  width: 1226px;
  height: auto;
  margin: 0 auto;
}
.nlLists p {
  text-align: center;
  height: 400px;
  line-height: 400px;
}
.nlListsUl {
  width: 100%;
  height: auto;
  margin-bottom: 28px;
  margin-top: 0;
  padding: 0;
  overflow: hidden;
}
.nlListsLi {
  width: 100%;
  height: 45px;
  border-bottom: 1px solid #e5e5e5;
  list-style: none;
}
.nlListsLi img {
  width: 25px;
}
.nlListsI {
  width: 13px;
  height: 17px;
  display: inline-block;
  background: url(../images/textI.png);
  background-position: 0 50%;
}
.nlListsA {
  width: auto;
  height: 100%;
  line-height: 45px;
  color: #000000;
}
.nlListsA:hover {
  color: #ca4300;
  text-decoration: underline;
}
.nlListsT {
  width: auto;
  height: 100%;
  line-height: 45px;
  display: block;
  float: right;
  color: #999999;
}
.listBox {
  width: 100%;
  position: relative;
  margin-bottom: 80px;
  margin-top: 20px;
}
.listBox .listUl {
  width: 100%;
  margin: 0;
  padding: 0;
}
.listBox .listUl .listLi {
  width: 100%;
  height: 142px;
  margin-bottom: 20px;
  list-style: none;
  position: relative;
}
.listBox .listUl .listLi .list-img {
  width: 284px;
  height: 142px;
  float: left;
  background: #ddd;
}
.listBox .listUl .listLi .list-img img {
  width: 284px;
  height: 142px;
}
.listBox p {
  text-align: center;
  height: 400px;
  line-height: 400px;
}
.sitemap {
  background-color: #f8f8f8;
  padding: 25px;
  width: 796px;
}
.sitemap dl {
  padding: 0;
  margin: 0;
}
.sitemap dt {
  font-size: 16px;
  color: #333;
}
.sitemap ul {
  padding: 15px 0 20px 0;
  display: flex;
  flex-wrap: wrap;
}
.sitemap li {
  width: 33.33%;
  padding: 5px 0;
}
.sitemap a {
  color: #ca4300;
  font-size: 14px;
}
.sitemap a:hover {
  text-decoration: underline;
}
/*详情相关资料*/
.xgzl-ul {
  list-style-type: none;
}
.xgzl-ul li {
  list-style-type: none !important;
  height: 34px;
  border-radius: 4px;
  background: #ccc;
  padding: 0 !important;
  line-height: 34px;
  margin-right: 10px;
  float: left;
  margin-bottom: 10px;
  position: relative;
}
.xgzl-ul li a {
  color: #fff;
  display: inline-block;
  padding: 0 14px;
  background: #ca4300;
  border-radius: 4px;
  white-space: nowrap;
  z-index: 2;
}
.xgzl-ul li .xgzl-icon {
  margin-left: 5px;
  display: inline-block;
  width: 12px;
  height: 14px;
  background: url(../images/xiazai.png) no-repeat 100% 100%;
  background-size: contain;
  position: relative;
  top: 3px;
}
.xgzl-ul li:after {
  content: "";
  width: 0;
  height: 100%;
  background: #cc5500;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 4px;
  transition: 0.4s;
}
.xgzl-ul li:hover:after {
  width: 100%;
}
/*详情合作案例*/
.hzal-ul {
  list-style-type: none;
}
.hzal-ul li {
  list-style-type: none !important;
  padding: 0 !important;
  border-bottom: 1px dashed #e9e9e9;
  line-height: 50px;
}
.hzal-ul li .hzal-icon {
  margin: 0 10px;
  display: inline-block;
  width: 19px;
  height: 19px;
  background: url(../images/al.png) no-repeat 100% 100%;
  background-size: contain;
  position: relative;
  top: 5px;
}
.hzal-ul li .hzal-title:hover {
  text-decoration: underline;
}
.hzal-ul li a {
  color: #333;
}
.hzal-ul li a:hover {
  color: #ca4300;
}
.hzal-ul li a:hover .hzal-icon {
  background: url(../images/al-active.png) no-repeat 100% 100%;
}
.fixed-description3 {
  position: absolute;
  left: 0;
  width: 100%;
}
.fixed-description3 * {
  padding: 0;
  margin: 0;
}
.contact_v2 {
  background: #f8f8f8;
  padding-top: 15px;
}
.contact_v2 .contact_main {
  width: 600px;
  margin: 0 auto;
  background: #fff;
  padding: 0 36px;
}
.contact_v2 .contact_main input,
.contact_v2 .contact_main textarea {
  background: #F2F2F2;
  border: 0;
}
.contact_v2 .contact_main textarea {
  font-size: 14px;
  font-family: 'Arial';
}
.contact_v2 .contact_main textarea[class='el-textarea__inner']::-webkit-input-placeholder,
.contact_v2 .contact_main input[class='el-input__inner']::-webkit-input-placeholder {
  color: #575757 !important;
}
.contact_v2 .contact_main textarea[class='el-textarea__inner']:-moz-placeholder,
.contact_v2 .contact_main input[class='el-input__inner']::-webkit-input-placeholder {
  color: #575757 !important;
}
.contact_v2 .contact_main textarea[class='el-textarea__inner']::-moz-placeholder,
.contact_v2 .contact_main input[class='el-input__inner']::-webkit-input-placeholder {
  color: #575757 !important;
}
.contact_v2 .contact_main textarea[class='el-textarea__inner']:-ms-input-placeholder,
.contact_v2 .contact_main input[class='el-input__inner']::-webkit-input-placeholder {
  color: #575757 !important;
  font-weight: 400 !important;
}
.contact_v2 .contact_main textarea[class='el-textarea__inner']::-ms-input-placeholder,
.contact_v2 .contact_main input[class='el-input__inner']::-webkit-input-placeholder {
  color: #575757 !important;
}
.contact_v2 .contact_main textarea[class='el-textarea__inner']::placeholder,
.contact_v2 .contact_main input[class='el-input__inner']::-webkit-input-placeholder {
  color: #575757 !important;
}
.contact_v2 .contact_main .contact_title {
  font-size: 22px;
  text-align: center;
}
.contact_v2 .contact_main .contact_des {
  color: #FF6600;
  font-size: 16px;
  text-align: center;
  padding-top: 15px;
}
.contact_v2 .contact_main .contact_form {
  padding: 30px 0;
}
.contact_v2 .contact_main .contact_form input,
.contact_v2 .contact_main .contact_form select,
.contact_v2 .contact_main .contact_form textarea {
  color: #333;
}
.contact_v2 .contact_main .contact_form .el-form-item__label {
  color: #333;
}
.contact_v2 .contact_main .contact_form .el-select .el-icon-arrow-up:before {
  content: "\e78f";
  color: #333333;
}
.contact_v2 .contact_main .contact_form .el-form-item__error {
  top: 0;
  left: 630px;
  width: 200px;
}
.contact_v2 .contact_main .contact_form .jump_dialog .el-dialog__header {
  display: none;
}
.contact_v2 .contact_main .contact_form .jump_dialog .el-dialog {
  width: 678px;
  background: transparent;
  box-shadow: none;
}
.contact_v2 .contact_main .contact_form .jump_dialog .el-dialog__body {
  padding: 0;
  position: relative;
}
.contact_v2 .contact_main .contact_form .jump_dialog .jump_img {
  width: 100%;
}
.contact_v2 .contact_main .contact_form .jump_dialog .close-btn {
  position: absolute;
  top: 54px;
  right: 57px;
  cursor: pointer;
}
.contact_v2 .contact_main .contact_form .jump_dialog .jump-btn {
  width: 303px;
  height: 48px;
  border-radius: 5px;
  font-size: 18px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #FFFFFF;
  position: absolute;
  top: 378px;
  left: 186px;
}
.contact_v2 .contact_main .contact_form .contact_form_content {
  display: flex;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-input__inner {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  border-color: #F2F2F2;
  background: #F2F2F2;
  padding: 0 13px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-textarea .el-textarea__inner {
  border-radius: 5px;
  background: #F2F2F2;
  border-color: #F2F2F2;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 1.5;
  padding: 13px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .contact_form_left {
  width: 779px;
  box-sizing: border-box;
  border-right: 1px solid #F2F2F2;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding-right: 40px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .contact_form_left .el-form-item {
  margin-right: 0;
}
.contact_v2 .contact_main .contact_form .contact_form_content .contact_form_left .el-form-item:not(:last-child) {
  margin-bottom: 25px !important;
}
.contact_v2 .contact_main .contact_form .contact_form_content .contact_form_left .el-form-item.is-hidden {
  visibility: hidden;
}
.contact_v2 .contact_main .contact_form .contact_form_content .contact_form_left .el-select {
  width: 260px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .contact_form_left .el-textarea .el-textarea__inner {
  width: 649px;
  height: 199px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .contact_form_left .el-input {
  width: 260px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .contact_form_right {
  flex: 1;
  padding-left: 27px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .contact_form_right .el-form-item {
  display: block;
  margin-right: 0;
  margin-bottom: 25px !important;
}
.contact_v2 .contact_main .contact_form .contact_form_content .contact_form_right .el-form-item .el-input__inner {
  width: 260px;
  height: 40px;
  background: #F2F2F2;
  border-color: #F2F2F2;
  border-radius: 5px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .contact_form_right .el-button {
  width: 261px;
  height: 40px;
  background: #ca4300;
  border-radius: 0;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.code-item .el-input.el-input-group.el-input-group--append {
  border-radius: 5px;
  overflow: hidden;
  border: 1px solid transparent;
  display: flex;
  width: 258px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.code-item .el-input__inner {
  width: 130px !important;
  border: none;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.code-item .el-input-group__append {
  width: 116px;
  background: #F2F2F2;
  border: none;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  border-radius: 0;
  padding: 0;
  text-align: center;
  position: relative;
  cursor: pointer;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.code-item .el-input-group__append:before {
  content: "";
  position: absolute;
  width: 1px;
  height: 18px;
  background: #333333;
  left: 0;
  top: 11px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.code-item .el-input-group__append .sendCaptcha {
  padding: 0 12px;
  line-height: 38px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.code-item .el-form-item__content {
  width: 260px;
  text-align: center;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.code-item .code-item-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.code-item img {
  width: 90px;
  height: 40px;
  cursor: pointer;
  display: block;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.code-item i {
  width: 30px;
  height: 18px;
  background: url(../images/refresh.png) no-repeat 100% 100%;
  display: flex;
  margin-left: 10px;
  cursor: pointer;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form--inline .el-form-item__label {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 40px;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.is-error .el-form-item__label {
  color: #FF0000;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.is-error .el-input__inner,
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.is-error .el-textarea__inner {
  border-color: #FF0000;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.is-error .el-form-item__error {
  color: #FF0000;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.is-error.code-item .el-input.el-input-group.el-input-group--append {
  border: 1px solid #FF0000;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.is-error.code-item .el-input__inner,
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.is-error.code-item .el-input-group__append {
  border: none;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
  display: none;
}
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:after,
.contact_v2 .contact_main .contact_form .contact_form_content .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:after {
  content: '*';
  color: #FF0000;
}
.contact_v2 .contact_main .contact_form .hotline span {
  display: block;
  float: left;
}
.contact_v2 .contact_main .contact_form .hotline span:first-child {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  height: 18px;
  line-height: 18px;
  border-left: 4px solid #ca4300;
  padding-left: 14px;
}
.contact_v2 .contact_main .contact_form .hotline span:last-child {
  height: 19px;
  font-size: 26px;
  font-weight: bold;
  color: #ca4300;
  line-height: 19px;
  font-family: Microsoft YaHei;
  background: url('../images/quote_tel.png') no-repeat 100% 100%;
  margin-left: 10px;
  padding-left: 32px;
}
.contact_v2 .contact_main .contact_form .workcycle {
  margin-top: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #878787;
}
.contact_v2 .contact_main #contact.contact_form {
  display: flex;
}
.contact_v2 .contact_main #contact.contact_form .contact-img {
  width: 185px;
  height: 244px;
  margin-right: 76px;
  flex-shrink: 0;
}
.contact_v2 .contact_main #contact.contact_form .contact_form_content {
  display: block;
}
.contact_v2 .contact_main #contact.contact_form .contact_form_content .el-form-item {
  margin-bottom: 24px !important;
}
.contact_v2 .contact_main #contact.contact_form .contact_form_content .el-form-item.margin-right {
  margin-right: 60px;
}
.contact_v2 .contact_main #contact.contact_form .contact_form_content .el-form-item .el-input,
.contact_v2 .contact_main #contact.contact_form .contact_form_content .el-form-item .el-select {
  width: 320px;
}
.contact_v2 .contact_main #contact.contact_form .contact_form_content .el-form-item .el-textarea .el-textarea__inner {
  width: 792px;
  height: 80px;
}
.contact_v2 .contact_main #contact.contact_form .contact_form_content .code-item .el-input.el-input-group.el-input-group--append {
  width: 318px;
}
.contact_v2 .contact_main #contact.contact_form .contact_form_content .code-item .el-input__inner {
  width: 201px !important;
}
.contact_v2 .contact_main #contact.contact_form .contact_form_content .contact-btn {
  width: 150px;
  height: 40px;
  background: #888888;
  border-radius: 0;
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 16px;
}
.contact_v2 .contact_main .contact_hotline {
  padding: 45px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.contact_v2 .contact_main .contact_hotline img {
  width: 185px;
  height: 237px;
}
.contact_v2 .contact_main .contact_hotline .red-line {
  padding-left: 15px;
  position: relative;
  line-height: 30px;
  font-size: 16px;
  color: #333;
}
.contact_v2 .contact_main .contact_hotline .red-line:before {
  content: "";
  width: 4px;
  height: 15px;
  background: #ca4300;
  position: absolute;
  top: 7px;
  left: 0;
}
.contact_v2 .contact_main .contact_hotline .contact-center {
  width: 305px;
  margin-right: 69px;
}
.contact_v2 .contact_main .contact_hotline .contact-right {
  flex: 1;
}
.contact_v2 .contact_main .contact_locatin {
  padding: 45px 0;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.contact_v2 .contact_main .contact_locatin img {
  width: 185px;
  height: 237px;
}
.contact_v2 .contact_main .contact_locatin .red-line {
  padding-left: 15px;
  position: relative;
  line-height: 30px;
  font-size: 18px;
  color: #333;
  font-weight: bold;
}
.contact_v2 .contact_main .contact_locatin .red-line:before {
  content: "";
  width: 4px;
  height: 15px;
  background: #ca4300;
  position: absolute;
  top: 7px;
  left: 0;
}
.contact_v2 .contact_main .contact_locatin .contact-center {
  padding-top: 21px;
  margin-right: 117px;
}
.contact_v2 .contact_main .contact_locatin .contact-right {
  padding-top: 21px;
}
.contact_v2 .contact_main .contact_approve {
  margin: 0 0 20px 0;
  display: flex;
  justify-content: center;
}
.contact_v2 .contact_main .contact_approve a {
  color: #ca4300;
}
.contact_v2 .contact_main .contact_approve a:hover {
  color: #f49000;
}
.contact_v2 .contact_main .contact_submit {
  text-align: center;
}
.contact_v2 .contact_main .contact_submit button {
  width: 200px;
  height: 42px;
  background: #ca4300;
  border: 0;
  font-size: 16px;
}
.contact_v2 .contact_main .contact_submit button:hover {
  background: #f49000;
}
.contact_v2 .contact_main .contact_type {
  position: relative;
  background: #f2f2f2;
}
.contact_v2 .contact_main .contact_type .contact_radios {
  position: absolute;
  left: 15px;
  top: 0;
  z-index: 2;
}
.contact_v2 .contact_main .contact_type .contact_radios span {
  color: #575757;
}
.contact_v2 .contact_main .contact_type .contact_radios i {
  color: #ca4300;
}
.contact_v2 .contact_main .contact_type .contact_type_swtich {
  position: absolute;
  left: 140px;
  top: 5px;
  z-index: 2;
  width: 125px;
  height: 30px;
  line-height: 30px;
  background: #fff;
  border-radius: 30px;
  box-shadow: 3px 3px 3px #ddd inset;
}
.contact_v2 .contact_main .contact_type .contact_type_swtich em {
  position: absolute;
  z-index: 3;
  display: inline-block;
  width: 62px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #575757;
  font-size: 15px;
  top: 0;
  cursor: pointer;
}
.contact_v2 .contact_main .contact_type .contact_type_swtich em:first-child {
  left: 0;
}
.contact_v2 .contact_main .contact_type .contact_type_swtich em:last-child {
  right: 0;
}
.contact_v2 .contact_main .contact_type .contact_type_swtich em.active {
  background: #ca4300;
  color: #fff;
  border-radius: 35px;
}
.contact_v2 .contact_main .contact_type .contact_type_swtich em label {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #f49000;
  position: absolute;
  top: 5px;
  right: 7px;
  z-index: 4;
  display: none;
}
.contact_v2 .contact_main .contact_type .contact_type_swtich em.on label {
  display: block;
}
.contact_v2 .contact_main .contact_type .contact_type_line {
  width: 1px;
  height: 25px;
  position: absolute;
  background: #ca4300;
  z-index: 2;
  left: 300px;
  top: 8px;
}
.contact_v2 .contact_main .contact_type input {
  position: absolute;
  width: 300px;
  right: 3px;
  bottom: -8px;
  z-index: 2;
  border-left: 1px solid #ff6600;
  border-radius: 0;
  height: 25px;
  line-height: 25px;
}
.contact_v2 .contact_main .contact_email_form,
.contact_v2 .contact_main .contact_phone_form {
  position: relative;
  top: -22px;
  margin-bottom: 0;
}
.contact_v2 .contact_main .contact_email_form input,
.contact_v2 .contact_main .contact_phone_form input {
  padding-left: 315px;
}
.contact_v2 .contact_main .contact_tips {
  padding: 0 !important;
  margin: 0 !important;
  position: relative;
  top: -20px;
}
.contact_v2 .contact_main .contact_tips * {
  padding: 0;
  margin: 0;
  color: #f49000;
  height: 20px;
  line-height: 20px;
  text-align: center;
}
.contact_v2 .contact_main1 {
  margin: 0 auto;
  width: 1154px;
  background: #fff;
  padding: 0 36px;
}
.contact_v2 .contact_main1 .contact_tab {
  height: 72px;
  line-height: 72px;
  border-bottom: 3px #F2F2F2 solid;
  width: 100%;
}
.contact_v2 .contact_main1 .contact_tab li {
  float: right;
  width: 100px;
  text-align: center;
  height: 72px;
  line-height: 72px;
  font-size: 20px;
}
.contact_v2 .contact_main1 .contact_tab li.activity {
  border-bottom: 3px solid #ca4300;
}
.contact_v2 .contact_main1 .contact_tab li.activity a {
  color: #333333;
}
.contact_v2 .contact_main1 .contact_tab li:first-child {
  width: 112px;
  float: left;
}
.contact_v2 .contact_main1 .contact_tab li:first-child a {
  font-size: 20px;
}
.contact_v2 .contact_main1 .contact_tab li a {
  font-size: 14px;
  color: #878787;
}
.contact_v2 .contact_main1 .contact_tab li a:hover {
  color: #333333;
}
.contact_v2 .contact_main1 .contact_des {
  height: 18px;
  line-height: 18px;
  border-left: 4px solid #fe6602;
  padding-left: 14px;
  margin-top: 25px;
  font-size: 16px;
  color: #333333;
}
.contact_v2 .contact_main1 .contact_form {
  padding: 40px 0;
}
.contact_v2 .contact_main1 .contact_form ::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #878787;
}
.contact_v2 .contact_main1 .contact_form ::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #878787;
}
.contact_v2 .contact_main1 .contact_form :-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #878787;
}
.contact_v2 .contact_main1 .contact_form .el-form-item__label {
  color: #333;
}
.contact_v2 .contact_main1 .contact_form .el-select .el-icon-arrow-up:before {
  content: "\e78f";
  color: #333333;
}
.contact_v2 .contact_main1 .contact_form .jump_dialog .el-dialog__header {
  display: none;
}
.contact_v2 .contact_main1 .contact_form .jump_dialog .el-dialog {
  width: 678px;
  background: transparent;
  box-shadow: none;
}
.contact_v2 .contact_main1 .contact_form .jump_dialog .el-dialog__body {
  padding: 0;
  position: relative;
}
.contact_v2 .contact_main1 .contact_form .jump_dialog .jump_img {
  width: 100%;
}
.contact_v2 .contact_main1 .contact_form .jump_dialog .close-btn {
  position: absolute;
  top: 54px;
  right: 57px;
  cursor: pointer;
}
.contact_v2 .contact_main1 .contact_form .jump_dialog .jump-btn {
  width: 303px;
  height: 48px;
  border-radius: 5px;
  font-size: 18px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #FFFFFF;
  position: absolute;
  top: 378px;
  left: 186px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content {
  display: flex;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-input__inner {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  border-color: #F2F2F2;
  background: #F2F2F2;
  padding: 0 13px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-textarea .el-textarea__inner {
  border-radius: 5px;
  background: #F2F2F2;
  border-color: #F2F2F2;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 1.5;
  padding: 13px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .contact_form_left {
  width: 779px;
  box-sizing: border-box;
  border-right: 1px solid #F2F2F2;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding-right: 40px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .contact_form_left .el-form-item {
  margin-right: 0;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .contact_form_left .el-form-item:not(:last-child) {
  margin-bottom: 25px !important;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .contact_form_left .el-form-item.is-hidden {
  visibility: hidden;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .contact_form_left .el-select {
  width: 260px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .contact_form_left .el-textarea .el-textarea__inner {
  width: 649px;
  height: 199px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .contact_form_left .el-input {
  width: 260px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .contact_form_right {
  flex: 1;
  padding-left: 27px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .contact_form_right .el-form-item {
  display: block;
  margin-right: 0;
  margin-bottom: 25px !important;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .contact_form_right .el-form-item .el-input__inner {
  width: 260px;
  height: 40px;
  background: #F2F2F2;
  border-color: #F2F2F2;
  border-radius: 5px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .contact_form_right .el-button {
  width: 261px;
  height: 40px;
  background: #FE6602;
  border-radius: 0;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.code-item .el-input.el-input-group.el-input-group--append {
  border-radius: 5px;
  overflow: hidden;
  border: 1px solid transparent;
  display: flex;
  width: 258px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.code-item .el-input__inner {
  width: 142px !important;
  border-radius: 0;
  border: none;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.code-item .el-input-group__append {
  width: 116px;
  background: #F2F2F2;
  border: none;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  border-radius: 0;
  padding: 0;
  text-align: center;
  position: relative;
  cursor: pointer;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.code-item .el-input-group__append:before {
  content: "";
  position: absolute;
  width: 1px;
  height: 18px;
  background: #333333;
  left: 0;
  top: 11px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.code-item .el-input-group__append .sendCaptcha {
  padding: 0 12px;
  line-height: 38px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form--inline .el-form-item__label {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 40px;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.is-error .el-form-item__label {
  color: #FF0000;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.is-error .el-input__inner,
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.is-error .el-textarea__inner {
  border-color: #FF0000;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.is-error .el-form-item__error {
  color: #FF0000;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.is-error.code-item .el-input.el-input-group.el-input-group--append {
  border: 1px solid #FF0000;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.is-error.code-item .el-input__inner,
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.is-error.code-item .el-input-group__append {
  border: none;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
  display: none;
}
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:after,
.contact_v2 .contact_main1 .contact_form .contact_form_content .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:after {
  content: '*';
  color: #FF0000;
}
.contact_v2 .contact_main1 .contact_form .hotline span {
  display: block;
  float: left;
}
.contact_v2 .contact_main1 .contact_form .hotline span:first-child {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  height: 18px;
  line-height: 18px;
  border-left: 4px solid #ca4300;
  padding-left: 14px;
}
.contact_v2 .contact_main1 .contact_form .hotline span:last-child {
  height: 19px;
  font-size: 26px;
  font-weight: bold;
  color: #ca4300;
  line-height: 19px;
  font-family: Microsoft YaHei;
  background: url('../images/quote_tel.png') no-repeat 100% 100%;
  margin-left: 10px;
  padding-left: 32px;
}
.contact_v2 .contact_main1 .contact_form .workcycle {
  margin-top: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #878787;
}
.contact_v2 .contact_main1 #contact.contact_form {
  display: flex;
}
.contact_v2 .contact_main1 #contact.contact_form .contact-img {
  width: 185px;
  height: 244px;
  margin-right: 76px;
  flex-shrink: 0;
}
.contact_v2 .contact_main1 #contact.contact_form .contact_form_content {
  display: block;
}
.contact_v2 .contact_main1 #contact.contact_form .contact_form_content .el-form-item {
  margin-bottom: 24px !important;
}
.contact_v2 .contact_main1 #contact.contact_form .contact_form_content .el-form-item.margin-right {
  margin-right: 60px;
}
.contact_v2 .contact_main1 #contact.contact_form .contact_form_content .el-form-item .el-input,
.contact_v2 .contact_main1 #contact.contact_form .contact_form_content .el-form-item .el-select {
  width: 320px;
}
.contact_v2 .contact_main1 #contact.contact_form .contact_form_content .el-form-item .el-textarea .el-textarea__inner {
  width: 792px;
  height: 80px;
}
.contact_v2 .contact_main1 #contact.contact_form .contact_form_content .code-item .el-input.el-input-group.el-input-group--append {
  width: 318px;
}
.contact_v2 .contact_main1 #contact.contact_form .contact_form_content .code-item .el-input__inner {
  width: 201px !important;
}
.contact_v2 .contact_main1 #contact.contact_form .contact_form_content .contact-btn {
  width: 150px;
  height: 40px;
  background: #888888;
  border-radius: 0;
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 16px;
}
.contact_v2 .contact_main1 .contact_hotline {
  padding: 45px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.contact_v2 .contact_main1 .contact_hotline img {
  width: 185px;
  height: 237px;
}
.contact_v2 .contact_main1 .contact_hotline .red-line {
  padding-left: 15px;
  position: relative;
  line-height: 30px;
  font-size: 16px;
  color: #333;
}
.contact_v2 .contact_main1 .contact_hotline .red-line:before {
  content: "";
  width: 4px;
  height: 15px;
  background: #FE6602;
  position: absolute;
  top: 7px;
  left: 0;
}
.contact_v2 .contact_main1 .contact_hotline .contact-center {
  width: 305px;
  margin-right: 69px;
}
.contact_v2 .contact_main1 .contact_hotline .contact-right {
  flex: 1;
}
.contact_v2 .contact_main1 .contact_locatin {
  padding: 45px 0;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.contact_v2 .contact_main1 .contact_locatin img {
  width: 185px;
  height: 237px;
}
.contact_v2 .contact_main1 .contact_locatin .red-line {
  padding-left: 15px;
  position: relative;
  line-height: 30px;
  font-size: 18px;
  color: #333;
  font-weight: bold;
}
.contact_v2 .contact_main1 .contact_locatin .red-line:before {
  content: "";
  width: 4px;
  height: 15px;
  background: #FE6602;
  position: absolute;
  top: 7px;
  left: 0;
}
.contact_v2 .contact_main1 .contact_locatin .contact-center {
  padding-top: 21px;
  margin-right: 117px;
}
.contact_v2 .contact_main1 .contact_locatin .contact-right {
  padding-top: 21px;
}
.contact_v2 .contact_ads {
  background: url('../images/newPage/ads.jpg') no-repeat 50% 50% #e8e8e8;
  height: 100px;
}
.contact_v2 .product-notice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 5px;
  background: #f2f2f2;
  border-radius: 3px;
}
.contact_v2 .product-notice .product-notice-text {
  width: 444px;
  max-height: 44px;
  font-size: 14px;
  color: #333333;
  line-height: 22px;
  overflow: hidden;
}
.food_safe_banner {
  height: 300px;
  background: url('../../public/images/foodSafeguard/bg.jpg') no-repeat center 0;
  font-size: 36px;
  line-height: 300px;
  text-align: center;
  color: #fff;
}
.food_safe_tab {
  width: 1226px;
  margin: 0 auto;
  padding-top: 50px;
}
.food_safe_tab li {
  background: #F2F2F2;
  height: 80px;
  line-height: 80px;
  width: 408px;
  float: left;
  text-align: center;
  font-size: 16px;
  color: #333;
  border-bottom: 3px solid #878787;
  cursor: pointer;
}
.food_safe_tab li.active {
  background: #fff;
  color: #ca4300;
  font-weight: bold;
  border-bottom: 3px solid #ca4300;
}
.food_safe_tab li:hover {
  color: #ca4300;
  font-weight: bold;
  border-bottom: 3px solid #ca4300;
}
.food_safe_wrap {
  width: 1226px;
  margin: 0 auto;
  padding-top: 65px;
  padding-bottom: 80px;
}
.food_safe_wrap .food_safe_search {
  text-align: center;
}
.food_safe_wrap .food_safe_search .el-cascader {
  float: left;
  margin-left: 220px;
  width: 630px;
  height: 44px;
  line-height: 44px;
}
.food_safe_wrap .food_safe_search .food_safe_search_btn {
  width: 153px;
  height: 44px;
  border-radius: 0px 5px 5px 0px;
  background: #ca4300;
  float: left;
}
.food_safe_wrap .food_safe_search .el-cascader,
.food_safe_wrap .food_safe_search .el-input__inner {
  height: 44px;
  line-height: 44px;
  border-radius: 5px 0 0 5px;
}
.food_safe_wrap .food_safe_link {
  text-align: center;
  color: #878787;
  font-size: 16px;
  margin: 33px auto;
}
.food_safe_wrap .food_safe_link a {
  color: #ca4300;
}
.food_safe_wrap .food_safe_tips {
  text-align: center;
  width: 780px;
  border: 1px solid #EBEEF5;
  border-bottom: 0;
  margin-left: 223px;
  background: #fff;
  height: 40px;
  line-height: 40px;
}
.food_safe_wrap .food_safe_table {
  text-align: center;
}
.food_safe_wrap .el-table {
  width: 780px;
  margin-left: 223px;
}
.modal {
  position: absolute;
  right: 66px;
  width: 423px;
  height: 180px;
  background: #FFFFFF;
  border: 1px solid #BFBFBF;
  top: 50%;
  transform: translate(0, -50%);
  text-align: center;
  z-index: 9;
  display: none;
}
.modal .modal_content {
  padding-top: 47px;
}
.modal i {
  background: url('../images/fixedMenu/success.png') no-repeat 100% 100%;
  display: block;
  float: left;
  width: 35px;
  height: 35px;
  margin-left: 55px;
}
.modal span {
  height: 35px;
  line-height: 35px;
  display: block;
  float: left;
  padding-left: 18px;
}
.modal p {
  font-size: 14px;
  color: #333333;
  line-height: 35px;
  height: 35px;
}
.modal .modal_button {
  margin-top: 21px;
}
.modal button {
  width: 60px;
  height: 32px;
  line-height: 32px;
  background: #F2F2F2;
  border: 1px solid #BFBFBF;
  border-radius: 3px;
  cursor: pointer;
}
.modal button:hover {
  background: #ca4300;
  border: 1px solid #ca4300;
  color: #fff;
}
.quote_modal .el-dialog {
  border-radius: 20px;
}
.quote_modal .el-dialog__header {
  text-align: center;
  color: #333;
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px solid #E5E5E5;
}
.quote_modal .el-dialog__footer {
  text-align: center;
}
.quote_modal input {
  border: 1px solid #ddd !important;
}
.quote_modal .quote_modal_phone {
  font-size: 14px;
  color: #878787;
  padding-bottom: 20px;
  text-align: center;
}
.quote_modal .quote_modal_countdown {
  margin-top: 34px;
  font-size: 14px;
  color: #878787;
  text-align: center;
}
.quote_modal .quote_modal_countdown span {
  color: #ca4300;
}
.success {
  padding: 30px 0 70px 0;
  text-align: center;
}
.success .succes_infomation {
  width: 540px;
  border-bottom: 1px solid #ddd;
  margin: 0 auto;
}
.success .succes_infomation img {
  width: 101px;
  height: 67px;
}
.success .succes_infomation h2 {
  font-size: 22px;
  font-weight: 400;
  color: #333;
  margin: 22px 0 20px 0;
}
.success .succes_infomation p {
  font-size: 14px;
  color: #333;
  padding: 5px 0;
}
.success .succes_infomation p:last-child {
  padding-bottom: 25px;
}
.success .succes_infomation a {
  color: #ca4300;
}
.success .succes_infomation a:hover {
  color: #f49000;
  text-decoration: underline;
}
.success .succes_infomation .countdown {
  color: #999;
}
.success .succes_infomation .countdown i {
  color: #ca4300;
}
.success .succes_infomation .succes_infomation_btn {
  background: #ca4300;
  color: #fff;
  font-size: 14px;
  height: 36px;
  line-height: 36px;
  width: 120px;
  text-align: center;
  border-radius: 3px;
  display: inline-block;
}
.success .succes_infomation .succes_infomation_btn:hover {
  background: #f49000;
}
.success .success_WXcode {
  padding-top: 25px;
}
.success .success_WXcode p {
  font-size: 14px;
  color: #878787;
}
.success .success_WXcode p span {
  color: #ca4300;
}
.success .success_WXcode .success_WXcode_item {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.success .success_WXcode .success_WXcode_item img {
  width: 101px;
  height: 103px;
  margin-right: 25px;
}
.success .success_WXcode .success_WXcode_item ul {
  margin-top: 20px;
}
.success .success_WXcode .success_WXcode_item ul li {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #878787;
  line-height: 24px;
  text-align: left;
}
.success .success_WXcode .success_WXcode_item ul li i {
  height: 4px;
  width: 4px;
  display: inline-block;
  background: #ca4300;
  margin-right: 5px;
}
.register-terms .el-dialog__header {
  display: none;
}
.register-terms .el-dialog__body {
  padding: 0;
}
.w_popup_container {
  padding-top: 20px;
}
.popup_container {
  background: #fff;
  border-radius: 4px;
  border: 4px solid rgba(0, 0, 0, 0.2);
}
.popup_container {
  width: 850px;
  height: 600px;
}
.online_rule_title {
  text-align: center;
  margin-bottom: 20px;
}
.online_rule_title p {
  color: #333;
  font-size: 18px;
  margin-bottom: 10px;
  margin-top: 0;
  line-height: 1;
}
.online_rule_content {
  width: 800px;
  height: 466px;
  border-radius: 2px;
  background-color: #f8f8f8;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 20px;
  padding-right: 0px;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  overflow: auto;
  text-align: left;
}
.popup_container .online_rule_content .title {
  clear: both;
  height: 30px;
  font-size: 16px;
  font-weight: bold;
  padding-left: 32px;
  padding-top: 16px;
}
.online_rule_content ul li {
  clear: both;
  padding-top: 4px;
  line-height: 18px;
}
.online_rule_content ul li div.left {
  float: left;
  height: auto;
  width: 32px;
}
.online_rule_content ul li div.right {
  float: left;
  width: 720px;
}
.online_rule_submit {
  margin-top: 10px;
  text-align: center;
}
.online_rule_submit_button {
  display: inline-block;
  width: 195px;
  height: 45px;
  text-align: center;
  line-height: 45px;
  color: #999;
  font-size: 16px;
  cursor: pointer;
}
.online_rule_submit_button.active {
  color: #fff;
  background: #ca4300;
}
.inner_quote_form {
  width: 420px;
  height: 500px;
  background: rgba(0, 0, 0, 0.4);
  position: absolute;
  top: 0;
  right: 0;
}
.inner_quote_form input {
  height: 32px;
  border: none;
  font-family: Roboto, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.inner_quote_form textarea {
  border: none;
  font-family: Roboto, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.inner_quote_form .des {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  text-align: center;
  padding: 35px 0 20px 0;
}
.inner_quote_form .des b {
  color: #f49000;
}
.inner_quote_form .side_tab {
  margin-bottom: 5px !important;
}
.inner_quote_form .side_tab span {
  display: inline-block;
  width: 75px;
  height: 13px;
  line-height: 13px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  color: #B4B4B4;
}
.inner_quote_form .side_tab span:last-child {
  border-left: 1px solid #ca4300;
}
.inner_quote_form .side_tab span.active {
  color: #fff;
}
.inner_quote_form .side_tab .el-form-item__content {
  height: 20px;
  line-height: 20px;
}
.inner_quote_form .side_tab_lable {
  margin: 0 !important;
}
.inner_quote_form .side_tab_lable .el-form-item__label {
  display: none;
}
.inner_quote_form .side_tips {
  color: #B4B4B4;
}
.inner_quote_form .side_tips .el-form-item__content {
  height: 20px;
  line-height: 20px;
}
.inner_quote_form .button {
  width: 180px;
  text-align: center;
  height: 36px;
  line-height: 36px;
  background: #ca4300;
  box-shadow: 2px 2px 22px 0px rgba(0, 0, 0, 0.15);
  border-radius: 3px;
  font-size: 16px;
  color: #fff;
  display: block;
  margin-left: 75px;
}
.inner_quote_form .button:hover {
  background: #f49000;
}
.inner_quote_form .side_protocol {
  margin-bottom: 0 !important;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 10px;
}
.inner_quote_form .side_protocol .el-checkbox {
  display: flex;
  align-items: center;
}
.inner_quote_form .side_protocol .el-checkbox__input {
  position: relative;
}
.inner_quote_form .side_protocol .el-checkbox__label {
  font-size: 12px;
  color: #fff;
}
.inner_quote_form .side_protocol a {
  color: #ca4300;
}
.inner_quote_form .side_protocol a:hover {
  color: #f49000;
}
.inner_quote_form .side_protocol .is-checked .el-checkbox__label {
  color: #fff;
}
.inner_quote_form .el-form-item__content {
  line-height: 0;
}
.inner_quote_form .el-form {
  width: 335px;
  margin-left: 43px;
}
.inner_quote_form .el-form-item {
  margin-bottom: 15px;
}
.inner_quote_form .el-form-item__label {
  color: #fff;
  height: 20px;
  line-height: 20px;
}
.inner_quote_form .el-form-item__error {
  color: #f00;
}
.inner_quote_form .el-input__inner {
  height: 34px;
  line-height: 34px;
}
.inner_quote_success {
  width: 420px;
  height: 500px;
  background: rgba(0, 0, 0, 0.4);
  position: absolute;
  top: 0;
  right: 0;
  color: #fff;
  text-align: center;
}
.inner_quote_success .side_success_email {
  padding-top: 65px;
}
.inner_quote_success .side_success_title {
  font-size: 22px;
  font-weight: bold;
  color: #fff;
  padding-top: 19px;
}
.inner_quote_success .side_success_des {
  font-size: 14px;
  color: #fff;
  padding-top: 23px;
  padding-bottom: 14px;
}
.inner_quote_success .side_success_ERcode {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 280px;
  margin-left: 69px;
  margin-top: 30px;
}
.inner_quote_success .side_success_ERcode img {
  width: 78px;
  height: 80px;
}
.inner_quote_success .side_success_ERcode p {
  width: 185px;
  font-size: 14px;
  color: #fff;
  line-height: 24px;
}
.inner_quote_success .side_success_ERcode p i {
  color: #f60;
}
.inner_quote_success .side_success_userinfo {
  width: 300px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #fff;
  margin-bottom: 20px;
}
.inner_quote_success .side_success_userinfo em {
  color: #ca4300;
  word-wrap: break-word;
  white-space: normal;
  word-break: break-all;
}
.inner_quote_success .side_success_tips {
  color: #fff;
  font-size: 14px;
  line-height: 24px;
  text-align: center;
}
.inner_quote_success .side_success_tips a {
  color: #f49000;
  text-decoration: underline;
}
.inner_quote_success .side_success_countdown {
  font-size: 14px;
  color: #fff;
  margin-top: 30px;
}
.inner_quote_success .side_success_countdown i {
  color: #f49000;
}
/* 检测认证中心留言表单改版 */
.inner_quote_form .inner_quote_form-item {
  display: flex;
  background: #fff;
  width: 342px;
  height: 44px;
  background: #F5F5F5;
  border-radius: 4px;
  align-items: center;
}
.inner_quote_form .inner_quote_form-item .icon {
  display: block;
  width: 14px;
  height: 14px;
  margin: 0 7px 0 11px;
}
.inner_quote_form .inner_quote_form-item.content {
  margin-bottom: 0;
}
.inner_quote_form .inner_quote_form-item.content .icon {
  background: url('../images/promotion/jiance/form/content.svg') no-repeat;
}
.inner_quote_form .inner_quote_form-item.customer .icon {
  background: url('../images/promotion/jiance/form/customer.svg') no-repeat;
}
.inner_quote_form .inner_quote_form-item.phone {
  margin-bottom: 20px;
}
.inner_quote_form .inner_quote_form-item.phone p {
  color: #FEA202;
  left: 95px;
  position: absolute;
  top: 55px;
  font-size: 12px;
}
.inner_quote_form .inner_quote_form-item.phone .icon {
  background: url('../images/promotion/jiance/form/phone.svg') no-repeat;
}
.inner_quote_form .inner_quote_form-item.provice .icon {
  width: 16px;
  height: 16px;
  background: url('../images/promotion/jiance/form/provice.svg') no-repeat;
}
.inner_quote_form .inner_quote_form-item .label {
  width: 50px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  color: #333;
}
.inner_quote_form .inner_quote_form-item .input-box {
  width: 250px;
}
.inner_quote_form .inner_quote_form_customer .el-textarea__inner,
.inner_quote_form .inner_quote_form_customer .el-input__inner {
  background: #f5f5f5;
}
.inner_quote_form .inner_quote_form_customer .el-form-item {
  margin-bottom: 25px;
}
.inner_quote_form .inner_quote_form_customer .el-form-item__error {
  margin-left: 95px;
}
.inner_quote_form .inner_quote_form_customer .side_protocol a {
  color: #FF8F1F;
}
/* 检测认证中心留言表单改版 */
.order-line-search-result {
  width: 100%;
  border-bottom: 20px #fff solid;
}
.order-line-search-result > div {
  background: #fff;
  margin: 23px;
  padding-bottom: 20px;
  position: relative;
}
.order-line-search-result .icon {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 82px;
  height: 34px;
  background: url('../images/buyicon.png') no-repeat;
  z-index: 1;
}
.order-line-search-result .img {
  width: 280px;
  height: 130px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  background-size: cover;
}
.order-line-search-result .num {
  padding: 10px 15px 5px 15px;
  font-size: 15px;
  color: #333;
  display: flex;
}
.order-line-search-result .num span {
  color: #ca4300;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 141px;
  display: inline-block;
}
.order-line-search-result .description {
  padding: 5px 15px;
  color: #878787;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.order-line-search-result .description i {
  display: inline-block;
  height: 4px;
  width: 4px;
  border-radius: 50%;
  background: #A7A7A7;
}
.order-line-search-result .price {
  padding: 5px 15px;
  height: 14px;
  color: #ca4300;
}
.order-line-search-result a {
  display: block;
  height: 42px;
  background: #ca4300;
  border-radius: 4px;
  line-height: 42px;
  color: #fff;
  font-size: 16px;
  text-align: center;
  margin: 15px 15px 0 15px;
}
.order-line-search-result a:hover {
  background: #f49000;
}
.el-loading-spinner .el-loading-text,
.el-loading-spinner i {
  color: #ca4300 !important;
}
.pagination {
  width: 1186px;
  margin: 20px auto;
  text-align: left;
  clear: both;
}
.pagination .el-pager li {
  background: #f2f2f2;
  margin-left: 5px;
  font-weight: normal;
}
.pagination .el-pager li.active,
.pagination .el-pager li:hover {
  background: #ca4300;
  color: #fff;
}
.pagination .el-pagination__jump .el-input__inner:focus {
  border: 1px solid #DCDFE6;
  outline: 1;
}
.pagination .serverpages {
  width: auto;
  height: auto;
  margin: 0;
  padding: 20px 0;
  overflow: hidden;
  float: right;
}
.pagination .serverpage {
  width: 40px;
  height: 30px;
  background: #f2f2f2;
  margin-right: 5px;
  float: left;
  text-align: center;
  line-height: 30px;
  font-size: 12px;
  border: 1px solid #f2f2f2;
  cursor: pointer;
  list-style: none;
}
.pagination .serverpage a {
  color: #666;
  display: block;
}
.pagination .serverpage:hover {
  background: white;
  border: 1px solid #ca4300;
  color: #000000;
}
.pagination .serverpage.pageActive {
  background: #ca4300;
  color: white;
  border: 1px solid #ca4300;
}
.pagination .serverpage.pageActive a {
  color: #fff;
}
.pagination .serverpage.pageActive:hover {
  background: #ca4300;
  border: 1px solid #ca4300;
  color: white;
}
.pagination .pageActive {
  background: #ca4300;
  color: white;
  border: 1px solid #ca4300;
}
.pagination .pageActive a {
  color: #fff;
}
.docCheck-title {
  font-size: 30px;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 30px;
  padding-top: 49px;
}
.docCheck-sub-title {
  font-size: 16px;
  color: #CADCE8;
  line-height: 16px;
  margin-top: 16px;
  display: flex;
}
.docCheck-sub-title span:not(:last-child) {
  border-right: 1px solid #8DA6B6;
  padding-right: 16px;
}
.docCheck-sub-title span:not(:first-child) {
  padding-left: 16px;
}
.docCheck-wrap * {
  box-sizing: border-box;
}
.docCheck-wrap .docCheck-content {
  display: flex;
  justify-content: space-between;
}
.docCheck-wrap .docCheck-left {
  width: 880px;
}
.docCheck-wrap .docCheck-right {
  width: 309px;
  height: 472px;
  background: #FAFAFA;
  border: 1px solid #ECECEC;
  box-shadow: 1px 5px 15px 0px rgba(0, 0, 0, 0.1);
  padding: 0 28px;
  margin-top: 13px;
}
.docCheck-wrap .docCheck-right .title {
  font-size: 16px;
  color: #333333;
  border-bottom: 1px solid #DEDEDE;
  font-weight: bold;
  line-height: 49px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.docCheck-wrap .docCheck-right .title .more-link {
  font-size: 14px;
  color: #CA4300;
  line-height: 14px;
  margin-left: 22px;
}
.docCheck-wrap .docCheck-right ul {
  font-size: 14px;
  color: #333333;
  line-height: 32px;
  margin-top: 20px;
  margin-bottom: 20px;
}
.docCheck-wrap .docCheck-right ul a {
  color: #333;
  cursor: pointer;
}
.docCheck-wrap .docCheck-right ul a:hover {
  color: #CA4300;
}
.docCheck-wrap .docCheck-right ul li {
  position: relative;
  padding-left: 20px;
}
.docCheck-wrap .docCheck-right ul li:before {
  content: "";
  width: 6px;
  height: 6px;
  background: #CA4300;
  position: absolute;
  left: 0;
  top: 13px;
}
.docCheck-wrap .docCheck-right .other-page {
  padding: 17px 0;
  border-top: 1px solid #DEDEDE;
  display: flex;
  align-items: center;
}
.docCheck-wrap .docCheck-right .other-page img {
  width: 11px;
  height: 20px;
  margin-right: 11px;
}
.docCheck-wrap .docCheck-right .other-page a {
  font-size: 14px;
  color: #333333;
  line-height: 14px;
}
.docCheck-wrap .docCheck-right .contact-text {
  border-top: 1px solid #DEDEDE;
  font-size: 14px;
  color: #878787;
  line-height: 14px;
  padding-top: 20px;
  padding-left: 20px;
  position: relative;
}
.docCheck-wrap .docCheck-right .contact-text img {
  position: absolute;
  top: 20px;
  left: 0;
}
.docCheck-wrap .docCheck-right .contact-text p {
  display: flex;
}
.docCheck-wrap .docCheck-right .contact-text p span {
  flex-shrink: 0;
}
.docCheck-wrap .docCheck-detail-text {
  width: 880px;
  height: 135px;
  background: rgba(255, 102, 0, 0.08);
  padding: 23px 43px 0 34px;
  font-size: 16px;
  color: #333333;
  line-height: 30px;
  margin-top: 18px;
}
.docCheck-wrap .docCheck-detail-text p {
  display: flex;
  align-items: flex-start;
}
.docCheck-wrap .docCheck-detail-text .label {
  color: #1C1C1C;
  font-weight: bold;
  flex-shrink: 0;
  position: relative;
  padding-left: 25px;
}
.docCheck-wrap .docCheck-detail-text .label .icon-label {
  color: #CA4300;
  font-size: 24px;
  position: absolute;
  top: 0;
  left: 0;
}
.docCheck-wrap .lang-tab {
  margin-top: 13px;
  display: flex;
  justify-content: flex-end;
}
.docCheck-wrap .lang-tab a {
  width: 40px;
  height: 22px;
  border: 1px solid #878787;
  border-radius: 3px;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  color: #878787;
  margin-left: 6px;
}
.docCheck-wrap .lang-tab a.is-active {
  border-color: #CA4300;
  background: #CA4300;
  color: #FFFFFF;
}
.docCheck-wrap .form-content {
  width: 880px;
  margin: auto;
  margin-top: 17px;
}
.docCheck-wrap .form-content .form-tips {
  color: #F56C6C;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
  display: none;
  white-space: nowrap;
}
.docCheck-wrap .form-content .tips-item.is-success .form-tips {
  display: block;
}
.docCheck-wrap .form-content .phone-item .el-input {
  width: 320px;
  border: 1px solid #D2D2D2;
  border-radius: 4px;
  background: #F5F7FA;
}
.docCheck-wrap .form-content .phone-item .el-input-group__prepend {
  padding-left: 13px;
  padding-right: 0;
  font-size: 16px;
  color: #333333;
  line-height: 24px;
  border: none;
}
.docCheck-wrap .form-content .phone-item .el-input-group__prepend i {
  color: #A2A2A2;
}
.docCheck-wrap .form-content .phone-item .phone-prepend {
  padding-right: 13px;
  border-right: 1px solid #D2D2D2;
}
.docCheck-wrap .form-content .phone-item .el-input__inner {
  border: none;
  width: 243px;
}
.docCheck-wrap .form-content .el-form-item__label {
  font-size: 16px;
  color: #333333;
  line-height: 48px;
  white-space: nowrap;
}
.docCheck-wrap .form-content .el-form-item {
  margin-bottom: 36px;
}
.docCheck-wrap .form-content .el-form-item.is-required .el-form-item__label:before {
  display: none;
}
.docCheck-wrap .form-content .el-form-item.is-required .el-form-item__label:after {
  content: "*";
  color: #FF0000;
  margin-left: 4px;
  position: relative;
  top: 4px;
}
.docCheck-wrap .form-content .el-input__inner {
  width: 320px;
  height: 48px;
  background: #F6F9FC;
  border: 1px solid #D2D2D2;
  border-radius: 4px;
}
.docCheck-wrap .form-content .file-upload-item {
  font-size: 14px;
  color: #878787;
}
.docCheck-wrap .form-content .file-upload-item .el-form-item__content {
  height: 48px;
  background: #F6F9FC;
  border: 1px solid #D2D2D2;
  border-radius: 4px;
  position: relative;
  padding-left: 130px;
  line-height: 46px;
  display: flex;
}
.docCheck-wrap .form-content .file-upload-item .el-form-item__content .upload-btn {
  width: 118px;
  height: 48px;
  background: #3C515B;
  border-radius: 4px;
  font-size: 16px;
  color: #FFFFFF;
  position: absolute;
  top: -1px;
  left: -1px;
}
.docCheck-wrap .form-content .file-upload-item .el-form-item__content .upload-btn:hover,
.docCheck-wrap .form-content .file-upload-item .el-form-item__content .upload-btn:focus {
  border-color: #D2D2D2;
}
.docCheck-wrap .form-content .file-upload-item .el-form-item__content .file-name {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.docCheck-wrap .form-content .upload-tips {
  padding-left: 102px;
  font-size: 14px;
  color: #878787;
  line-height: 24px;
  margin-bottom: 30px;
}
.docCheck-wrap .form-content .upload-tips .check-box {
  display: flex;
}
.docCheck-wrap .form-content .upload-tips label {
  padding-right: 10px;
}
.docCheck-wrap .form-content .upload-tips span,
.docCheck-wrap .form-content .upload-tips a {
  color: #CA4300;
  cursor: pointer;
}
.docCheck-wrap .form-content .isCheckedTips {
  color: #F56C6C;
  font-size: 12px;
}
.docCheck-wrap .form-content .check-content {
  padding-left: 102px;
}
.docCheck-wrap .form-content .check-content .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #333;
}
.docCheck-wrap .form-content .submit-btn {
  margin: 0;
  margin-left: 97px;
}
.docCheck-wrap .submit-btn {
  width: 220px;
  height: 60px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
  margin: 68px auto 0;
  display: block;
}
.docCheck-wrap .result {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-top: 13px;
  min-height: 455px;
}
.docCheck-wrap .result .submit-btn {
  margin: 55px auto;
}
.docCheck-wrap .result .pdf-result {
  width: 880px;
}
.docCheck-wrap .result .tips {
  background: rgba(255, 102, 0, 0.08);
  padding: 21px 37px 36px 26px;
}
.docCheck-wrap .result .tips .title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  line-height: 1;
  margin-bottom: 21px;
}
.docCheck-wrap .result .tips .result-title {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 400;
  color: #333333;
  line-height: 1;
  font-weight: bold;
}
.docCheck-wrap .result .tips .result-title img {
  margin-right: 13px;
}
.docCheck-wrap .result .tips p {
  text-align: center;
  font-size: 16px;
  color: #333333;
  line-height: 30px;
}
.docCheck-wrap .result .tips .red-color {
  color: #CA4300;
}
.docCheck-wrap .result .tips .sub-tips {
  display: flex;
  padding-top: 17px;
  padding-left: 103px;
  margin-top: 16px;
  font-size: 14px;
  color: #999999;
  line-height: 30px;
  border-top: 1px solid #FFDFC9;
}
.docCheck-wrap .result .tips .sub-tips .sub-btn {
  cursor: pointer;
  color: #333;
}
.docCheck-wrap .result .pdf-tips {
  font-size: 16px;
  color: #333333;
  margin: 24px 0;
}
.docCheck-wrap .result .invalidated-tips {
  width: 880px;
  height: 230px;
  background: #FFFFFF;
  border: 1px solid #C5C5C5;
  padding: 19px 24px;
  font-size: 16px;
  color: #333333;
  line-height: 24px;
}
.docCheck-wrap .docCheck-dialog .dialog-tips {
  width: 437px;
  background: #F5F5F5;
  display: flex;
  padding: 16px 24px 15px 29px;
  font-size: 12px;
  color: #999999;
  line-height: 24px;
  margin: 29px auto 23px;
  text-align: left;
  word-break: break-word;
}
.docCheck-wrap .docCheck-dialog .el-dialog {
  border-radius: 12px;
}
.docCheck-wrap .docCheck-dialog .el-dialog__header {
  display: none;
}
.docCheck-wrap .docCheck-dialog .el-dialog__body {
  padding: 48px 0 37px;
  font-size: 16px;
  color: #333333;
  line-height: 30px;
  text-align: center;
  position: relative;
}
.docCheck-wrap .docCheck-dialog .el-dialog__body img {
  position: absolute;
  top: 22px;
  right: 23px;
  cursor: pointer;
}
.docCheck-wrap .docCheck-dialog .dialog-footer {
  text-align: center;
}
.docCheck-wrap .docCheck-dialog .el-button {
  width: 80px;
  height: 38px;
  border-radius: 4px;
}
.form-item-approve {
  padding: 18px 0!important;
  text-align: center;
  display: flex;
  justify-content: center;
}
.form-item-approve label:first-child {
  margin: 0!important;
}
.form-item-approve .el-checkbox__label {
  font-size: 12px;
  color: #878787 !important;
  margin-left: 5px;
}
.form-item-approve .el-checkbox__label a {
  color: #CA4300;
}
