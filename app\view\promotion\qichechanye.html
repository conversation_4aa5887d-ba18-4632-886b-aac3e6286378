<% include ../header.html %>
<% include ./../components/header.html %>
<link rel="stylesheet" href="<%- locals.static %>/css/promotion/qichechanye.css">
<link rel="stylesheet" href="<%- locals.static %>/css/promotion/qichechanye_new.css">
<div class="car_all">
  <!-- banner -->
  <div class="banner" style="background: url(<%- locals.static %>/images/promotion/qichechanye/new_images/banner_bg.jpg) center no-repeat;">
    <div class="txt1">SGS 汽车行业一站式解决方案</div>
    <div class="txt2"><img src="<%- locals.static %>/images/promotion/qichechanye/new_images/banner_icon1.svg" />审核<img src="<%- locals.static %>/images/promotion/qichechanye/new_images/banner_icon3.svg" />认证<img
            src="<%- locals.static %>/images/promotion/qichechanye/new_images/banner_icon2.svg" />培训</div>
    <div class="txt3">
      <span>IATF 16949</span>
      <span>ISO/SAE 21434</span>
      <span>TISAX®</span>
      <span>VDA 6.3/6.5</span>
      <span>ISO 26262</span>
    </div>
    <div class="txt4">SGS携手组织成为汽车行业新形势下的行业之星</div>
    <div class="btn handleIMtool" >立即咨询</div>
  </div>
  <!-- 热门服务 -->
  <div class="all_title">
    <span>热门服务</span><b></b>
    <font>SGS致力于向汽车行业提供贯穿全价值链的质量保障服务，从零部件品质到整车安全，覆盖汽车全产业链各个细分领域；</font>
    <font>SGS是少数几家被国内汽车主机厂都认可的第三方认证机构之一，拥有100多名IATF 16949审核员；同时，SGS拥有的客户数量也领先同侪！</font>
  </div>

  <div class="content1">
    <a class="more" href="/service/certification" target="_blank">查看更多 →</a>
    <div class="line1">
      <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c1_1.jpg" />
      <div class="txt_in">
        <div class="txt1"><b>IATF 16949</b>国际汽车行业质量管理体系</div>
        <div class="txt2">一场变革之旅，SGS对IATF 16949的审核将通过支持您提高产品质量、减少浪费和防止缺陷来帮助您的组织脱颖而出。<a
                href="/sku/IATF16949/227" target="_blank">查看详情>></a></div>
      </div>
      <div class="btn handleIMtool">获取报价</div>
    </div>

    <div class="line1">
      <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c1_2.jpg" />
      <div class="txt_in">
        <div class="txt1"><b>ISO/SAE 21434</b>道路车辆网络安全标准</div>
        <div class="txt2">借助于我们的培训和认证、产品/网络测试和认证、评估及咨询服务，让您组织、员工、产品和服务在实现目标的同时，拥有抵御网络风险与威胁的能力。<a
                href="/sku/product/901505" target="_blank">查看详情>></a></div>
      </div>
      <div class="btn handleIMtool">获取报价</div>
    </div>

    <div class="line1">
      <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c1_3.jpg" />
      <div class="txt_in">
        <div class="txt1"><b>TISAX<span>®</span></b>值得信赖的信息安全评估交换</div>
        <div class="txt2">
          TISAX®确保汽车制造商、服务提供商和供应商之间的信息安全水平一致。它通过确保制造过程中的完整性和可用性来帮助保护数据。专用平台可在行业内交换信息安全评估结果。<a
                href="/sku/product/901327" target="_blank">查看详情>></a></div>
      </div>
      <div class="btn handleIMtool">获取报价</div>
    </div>
    <div class="line1">
      <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c1_4.jpg" />
      <div class="txt_in">
        <div class="txt1"><b>VDA6.3</b>过程审核</div>
        <div class="txt2">以专业对过程/产品进行分析，客观评价产品、制造过程和质量管理体系的风险。帮助企业实现持续改进。<a href="/sku/product/234" target="_blank">查看详情>></a></div>
      </div>
      <div class="btn handleIMtool">获取报价</div>
    </div>

  </div>

  <!-- 相关培训课程 -->
  <div class="all_title">
    <span>相关培训课程</span><b></b>
  </div>
  <div class="content2">
    <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c2.jpg" />
    <div class="list">
      <div class="list_in">
        <a target="_blank" href="/sku/product/580">IATF 16949:2016汽车行业质量管理体系内审员培训课程</a>
        <span>HOT</span>
      </div>

      <div class="list_in">
        <a target="_blank" href="/sku/product/290">IATF 16949:2016五大核心工具培训课程</a>
        <span>HOT</span>
      </div>

      <div class="list_in">
        <a target="_blank"  href="/sku/product/586">VDA 6.3 & VDA 6.5过程审核(2023版)&产品审核(2016版)培训课程</a>
      </div>

      <div class="list_in">
        <a target="_blank"  href="/sku/product/763">ISO 26262汽车功能安全—AFSP培训课程</a>
        <span>HOT</span>
      </div>

      <div class="list_in">
        <a target="_blank"  href="/sku/product/772">汽车行业产品安全代表（PSB）培训课程</a>
      </div>

      <div class="list_in">
        <a target="_blank"  href="/sku/fmea/990">FMEA 潜在失效模式分析新版培训课程</a>
      </div>

      <div class="list_in">
        <a target="_blank"  href="/sku/product/588">国际材料数据系统（IMDS 13.0）培训</a>
      </div>

      <div class="list_in">
        <a target="_blank"  href="/sku/product/581">CQI-8 分层审核 (Layered Process Audit)培训课程</a>
      </div>

      <div class="list_in">
        <a target="_blank"  href="/sku/product/868">CQI-9 热处理系统评估培训课程</a>
      </div>
      <a class="more" target="_blank"  href="/service/training">查看更多 →</a>
    </div>
  </div>

  <!-- 相关培训课程 -->
  <div class="all_title">
    <span>汽车行业综合解决方案</span><b></b>
    <font>质量、环境、能源、安全是汽车行业的主要挑战，SGS可以为汽车行业提供一站式全面解决方案</font>
  </div>
  <div class="content3">
    <div class="content3_in">
      <div class="title">
        <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c3_1.svg" />质量解决方案
      </div>
      <div class="txt">
        <div class="txt_in"><a target="_blank" href="/sku/IATF16949/227">IATF 16949 国际汽车行业质量管理体系</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/635">VDA 6.5 产品审核</a></div>
        <div class="txt_in"><a target="_blank" href="/promotion/iso9001">ISO 9001 质量管理体系</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/901700">应对欧盟新电池法一站式解决方案</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/234">VDA 6.3 过程审核</a></div>
      </div>
    </div>
    <div class="content3_in">
      <div class="title">
        <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c3_2.svg" />安全解决方案
      </div>
      <div class="txt">
        <div class="txt_in"><a target="_blank" href="/sku/product/901327">TISAX 汽车可信信息安全评估交换</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/iso27001/447">ISO/IEC 27001 信息安全管理体系</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/901505">ISO/IEC 21434 道路车辆网络安全工程</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/901089">ISO/IEC 27701 隐私信息管理体系</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/185">ISO 26262 汽车功能安全认证</a></div>
      </div>
    </div>
    <div class="content3_in">
      <div class="title">
        <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c3_3.svg" />能源、环境解决方案
      </div>
      <div class="txt">
        <div class="txt_in"><a target="_blank" href="/sku/product/225">ISO 14001 环境管理体系</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/901521">ISO 14021 自我环境声明</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/226">ISO 45001 职业健康管理体系</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/901540">ISO 14025/EPD 环境产品声明验证</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/245">ISO 50001/ GB/T23331 能源管理体系</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/901530">低碳解决方案</a></div>
      </div>
    </div>
    <div class="content3_in">
      <div class="title">
        <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c3_4.svg" />社会责任解决方案
      </div>
      <div class="txt">
        <div class="txt_in"><a target="_blank" href="/sku/product/443">ISO 22301 业务连续性管理体系</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/901542">ESG 认证</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/901541">ISO 20400 可持续采购</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/710">ESG 综合解决方案</a></div>
        <div class="txt_in"><a target="_blank" href="/sku/product/720">ESG 报告编制和审验</a></div>
      </div>
    </div>

    <div class="xxx">
      <span></span>
      <span></span>
      <span></span>
    </div>
    <div class="yyy">
      <span></span>
      <span></span>
    </div>
    <a class="more" href="/service/certification" target="_blank">查看更多 →</a>
  </div>

  <!-- 汽车OEM经销商网络管理服务 -->
  <div class="all_title">
    <span>汽车OEM经销商网络管理服务</span><b></b>
    <font>在业务快速发展的今天，企业的整车销售与售后服务管理应该随着企业的快速发展而同步发展，所以建立规范的销售与售后服务的工作流程尤为重要。</font>
    <font>我们为您提供全面的OEM人力资源外包、经销商培训，让您在激烈的市场竞争中脱颖而出！</font>
  </div>
  <div class="content4">
    <div class="card">
      <div class="tips">新经销商建店</div>
      <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c4_1.jpg" />
      <div class="txt"><span>经销商标准审计</span><span>经销商标准本地化调研与咨询</span><span>5S培训及审核</span></div>
      <div class="btn handleIMtool">咨询客服</div>
    </div>

    <div class="card">
      <div class="tips">整车/二手车销售</div>
      <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c4_2.jpg" />
      <div class="txt"><span>电动汽车业务、库存管理</span><span>激励政策审计、票据凭证核查</span><span>二手车现场核查</span></div>
      <div class="btn handleIMtool">咨询客服</div>
    </div>

    <div class="card">
      <div class="tips">售后服务</div>
      <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c4_3.jpg" />
      <div class="txt"><span>原厂零配件外销辅导</span><span>保修业务管理</span><span>经销商售后业务优化辅导</span></div>
      <div class="btn handleIMtool">咨询客服</div>
    </div>
  </div>

  <!-- 合作案例 -->
  <div class="all_title">
    <span>合作案例</span><b></b>
  </div>
  <div class="content5">
    <div class="content5_in" onclick="window.open('/case/article/detail-2431.html','_blank')">
      <div class="pic"><img src="<%- locals.static %>/images/promotion/qichechanye/new_images/1.png" /></div>
      <b>畅行智驾</b>
      <span>TISAX 审核</span>
    </div>

    <div class="content5_in" onclick="window.open('/case/article/detail-1465.html','_blank')">
      <div class="pic"><img src="<%- locals.static %>/images/promotion/qichechanye/new_images/2.png" /></div>
      <b>天科合达</b>
      <span>IATF 16949 认证</span>
    </div>

    <div class="content5_in" onclick="window.open('/case/article/detail-2422.html','_blank')">
      <div class="pic"><img src="<%- locals.static %>/images/promotion/qichechanye/new_images/3.png" /></div>
      <b>吉利汽车</b>
      <span>ISO/SAE 21434 认证</span>
      <span>ISO 26262 认证</span>
    </div>

    <div class="content5_in" onclick="window.open('/case/article/detail-1953.html','_blank')">
      <div class="pic"><img src="<%- locals.static %>/images/promotion/qichechanye/new_images/4.png" /></div>
      <b>哪吒汽车</b>
      <span>ISO 26262 认证</span>
    </div>

    <div class="content5_in" onclick="window.open('/case/article/detail-1842.html','_blank')">
      <div class="pic"><img src="<%- locals.static %>/images/promotion/qichechanye/new_images/5.png" /></div>
      <b>春风动力</b>
      <span>ISO 26262 认证</span>
    </div>

    <div class="content5_in" onclick="window.open('/case/article/detail-1814.html','_blank')">
      <div class="pic"><img src="<%- locals.static %>/images/promotion/qichechanye/new_images/6.png" /></div>
      <b>路特斯科技</b>
      <span>ISO 26262 认证</span>
    </div>



  </div>


  <!-- SGS在汽车行业进行审核、认证以及培训的优势 -->
  <div class="all_title">
    <span>SGS在汽车行业进行审核、认证以及培训的优势</span><b></b>
  </div>
  <div class="content6">
    <div class="card">
      <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c6_1.jpg" />
      <div class="title">
        <b>一站式服务</b>
      </div>
      SGS是国际公认的专业第三方测试、检验和认证机构，SGS可以提供汽车行业检验、测试、认证及培训一站式综合解决方案。
    </div>

    <div class="card">
      <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c6_2.jpg" />
      <div class="title">
        <b>IATF认可</b><span>认证资质</span>
      </div>
      SGS是IATF所认可的41家机构中，第一批获得认可的认证机构之一。<br />
      在全球网络安全领域，SGS也是首批获得 ISO/SAE 21434 道路车辆网络安全认证服务资质的机构之一。
    </div>

    <div class="card">
      <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c6_3.jpg" />
      <div class="title">
        <b>200+</b><span>审核员</span>
      </div>
      SGS拥有先进的技术力量，技术人员来自汽车主机厂和一级供应商的审核员团队，具有二方/三方多元化项目管理的能力和经历，本着客观、公正的审核态度和以风险为导向的思维逻辑进行审核。
    </div>

    <div class="card">
      <img src="<%- locals.static %>/images/promotion/qichechanye/new_images/c6_4.jpg" />
      <div class="title">
        <b>4000+</b><span>客户数量</span>
      </div>
      在中国大陆，SGS拥有数量庞大的ISO/TS 16949及IATF 16949：2016的认证客户，达到4000家以上，SGS也是众多知名主机厂指定认证审核机构。
    </div>
  </div>

  <!-- 留言咨询 -->
  <div class="content7" id='iso9001' v-cloak>
    <div class="all_title">
      <span>留言咨询</span><b></b>
    </div>
    <div class="action">
      <% include ../components/registionClause.html %>
      <input type="text" placeholder="姓名" name='customer1' id='customer1' maxlength='50'/>
      <input type="text" placeholder="手机号码" name='phone1' id='phone1' maxlength="11"/>
      <input type="text" placeholder="电子邮箱" name='email1' id='email1' maxlength='80'/>
      <select style="background:#fff url(<%- locals.static %>/images/promotion/qichechanye/new_images/select.svg) no-repeat center; background-position: 120px center;" id='service1'>
        <option value='-1'>选择服务类型</option>
        <option value='24'>审核与评估</option>
        <option value='32'>培训服务</option>
        <option value='28'>体系认证</option>
      </select>
      <input type="text" placeholder="请简述您的需求" name='content1' id='content1' maxlength="1000"/>
      <div class="btn" id='submit1' @click='handleSubmit'>提 交</div>
      <% include ./../pages/quote/modal.html %>
    </div>
    <div class="form-item-approve">
      <el-checkbox v-model="approve">
        <template v-if='!isLogin'>提交后自动注册会员，获取后续信息。</template>
        我已阅读并同意
        <template v-if='!isLogin'><a @click.stop='dialogVisibleReg = true'>注册条款</a>及</template>
        <a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" target="_blank">隐私政策</a>
      </el-checkbox>
    </div>
  </div>



</div>

<div class="pop" id='submitPop'>
  <div class="layout"></div>
  <div class="popBox">
    <h2 class="tit">
      提示
    </h2>
    <div class="cont">
      您的业务咨询已提交，我们会尽快与您取得联系！
    </div>
    <div class="btn">
      <span>确定</span>
    </div>
  </div>
</div>

<!--下载课表需要填写的信息窗口-->
<div class="down-load-pop">
  <div class="layout"></div>
  <div class='popBox'>
    <div class="title">
      <b>资源下载</b>
      <p>填写信息免费下载</p>
    </div>
    <div class="content">
      <i class="close">X</i>
      <input type="text" name='customer' id='customer2' placeholder="您的姓名" />
      <input type="text" name='phone' id='phone2' maxlength="11" placeholder="手机号码" />
      <input type="text" name='email' id='email2' placeholder="电子邮箱" />
      <div style="margin-top: 10px;">
        <label><input type="checkbox" name="" id="check">我接受</label><a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" style="color:#FE660B;text-decoration:none;" target="_blank">《隐私政策》</a>
      </div>
      <button id='submit2'>提交并下载</button>
    </div>
  </div>
</div>

<!--第二版下载课表需要填写的信息窗口-->
<div class="down-load-pop2">
  <div class="layout"></div>
  <div class='popBox'>
    <div class="title">
      <b>资源下载</b>
      <p>填写信息免费下载</p>
    </div>
    <div class="content">
      <i class="close">X</i>
      <div style="display: flex;flex-wrap: wrap;justify-content: space-between;">
        <input type="text" name='customer' id='customer3' placeholder="您的姓名" />
        <input type="text" name='phone' id='phone3' maxlength="11" placeholder="手机号码" />
        <input type="text" name='email' id='email3' placeholder="电子邮箱" />
        <input type="text" name='email' id='company3' placeholder="企业名称" />
        <textarea id="textarea3" placeholder="请简述您下载本资源最希望解决的问题" style="overflow-y: auto"></textarea>
      </div>
      <div style="text-align: left">
        <div style="font-size: 14px;margin-top:10px;">贵公司是否已与SGS有合作</div>
        <div style="font-size: 14px;margin-top:10px;display: flex">
          <input type="radio" name="isCoop" value="1">
          <span>是，IATF 16949认证</span>
        </div>
        <div style="font-size: 14px;margin-top:10px;display: flex">
          <input type="radio" name="isCoop" value="2">
          <span>否</span>
          <input type="text" name='coopName' id='coopName' placeholder="请输入您企业的IATF 16949认证机构" />
        </div>
      </div>
      <div style="margin-top: 20px;">
        <label><input type="checkbox" name="" id="check3">我接受</label><a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" style="color:#FE660B;text-decoration:none;" target="_blank">《隐私政策》</a>
      </div>
      <button id='submit3'>提交并下载</button>
    </div>
  </div>
</div>

<% if(locals.env != 'prod'){ %>
<script src="<%- locals.static %>/plugin/v2.test.js"></script>
<% }else{ %>
<script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js" type="text/javascript"></script>
<% } %>
<script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>

<script>
  var newVue = new Vue({
    name: 'iso9001',
    el: '#iso9001',
    mixins: [quoteMixins],
    data: {
      form: {
        type: 'IATF 16949',
        trade: 68,
        tradeName: '汽车与零部件',
        service: '',
        serviceName: '其他',
        content: '',
        customer: '<%- userInfo.userName %>',
        email: '<%- userInfo.userEmail %>',
        phone: '<%- userInfo.userPhone %>',
        provice: '浙江省',
        company: 'sgs',
        os_type: 'pc',
        frontUrl: window.location.href,
        _csrf: '<%- csrf %>',
        imageCode: '',
        verifyCode: '',
      },
      showModal: false,
      seconds: 59,
      timer: null,
      loading: false,
      host: '<%- host %>',
      isLogin: <%- isLogin %>,
      pid: 'pid.mall',
      pcode: 'Z0zCnRE3IaY9Kzem',
      pageInfo: {
        title: '验证手机号',
        sendModel: '已发送到手机号：+86',
        prepend: '手机验证码'
      },
      approve: false,
      dialogVisibleReg: false,
    },
    methods: {
      handleClose: function () {
        this.showModal = false
        $("#submit1").prop('disabled', false);
        this.form.verifyCode = ''
      },
      sing: function (param, timestamp) {
        var pmd5 = md5((this.pid + this.pcode).toUpperCase());
        var str = JSON.stringify(param) + pmd5.toUpperCase();
        return md5(str + timestamp);
      },
      handleSubmit: function () {
        var that = this
        var data = {
          type: 'IATF 16949',
          trade: 68,
          tradeName: '汽车与零部件',
          service: $("#service1").val(),
          serviceName: $("#service1").find("option:selected").text(),
          content: $('#content1').val(),
          customer: $('#customer1').val().trim(),
          phone: $('#phone1').val().trim(),
          email: $('#email1').val().trim(), // 不能为空，不能为非法邮箱
          provice: '浙江省',
          company: 'sgs',
          os_type: 'pc',
          frontUrl: window.location.href,
          _csrf: '<%- csrf %>'
        }
        that.form.service = data.service
        that.form.serviceName = data.serviceName
        that.form.content = data.content
        that.form.customer = data.customer
        that.form.phone = data.phone
        that.form.email = data.email
        if (!$('#customer1').val().trim()) {
          alert('请输入姓名！')
        } else if (!$('#phone1').val().trim()) {
          alert('请输入手机号码！')
        } else if (!/^1\d{10}$/.test($('#phone1').val().trim())) {
          alert('请输入正确的手机号码！')
        } else if (!$('#email1').val().trim()) {
          alert('请输入电子邮箱')
        } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email1').val().trim())) {
          alert('请输入正确电子邮箱')
        } else if (data.service == '-1') {
          alert('请选择服务类型！')
        } else if (!$('#content1').val()) {
          alert('请输入需求信息！')
        } else if (!this.approve) {
          alert('请同意隐私政策！')
        } else {
          if (!this.isLogin) {
            $("#submit1").prop('disabled', true);
            this.showModal = true
            this.form.phone = $('#phone1').val()
            this.form.service = $("#service1").val()
            this.form.serviceName = $("#service1").find("option:selected").text()
          } else {
            this.handleModalSubmit({})
          }
        }
      },
      handleModalSubmit: function (loginRef) {
        this.form.verifyCode = loginRef.verifyCode
        var that = this
        var param = that.form
        var timestamp = new Date().valueOf();
        var url = '/submitTicket';
        loginRef.loading = true
        param.verifyMode = this.tab
        $.ajax({
          type: 'POST',
          url: url,
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: this.pid,
            sign: that.sing(param, timestamp),
            timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            loginRef.loading = false
            that.loading = false
            if (res.resultCode === '0') {
              Cookies.set("SSO_TOKEN", res.data.token, { domain: ".sgsmall.com.cn", expires: 7 });
              that.showModal = false
              $('#submitPop').show();
              $("#submit1").prop('disabled', false);
            } else if(res.resultCode === '9978') {
              Cookies.remove('SSO_TOKEN', { domain: '.sgsmall.com.cn' })
              that.isLogin = false
              that.showModal = true
            } else {
              that.$message.error(res.resultMsg)
            }
          },
          fail: function (data) {
            loginRef.loading = false
            that.loading = false
            that.$message.error(res.resultMsg)
          },
          complete: function (complete) {
          }
        })
      },
    },
    mounted: function () {
      if (this.isLogin) {
        if ($("#phone1").val()) $("#phone").attr('disabled', true)
        $("#phone1").val(this.form.phone)
        $("#email1").val(this.form.email)
        $("#customer1").val(this.form.customer)
      }
    }
  });

  $(function () {
    var pw = $('html,body').width(),
      ph = $('html,body').height();
    $('.down-load-pop .layout').css({
      width: pw,
      height: ph
    })
    $('#hotService .item').hover(function () {
      $(this).addClass('activity').siblings().removeClass('activity');
    }, function () {
      $(this).removeClass("activity");
    });
    // $("#hotService button").on("click", function () {
    //   $('#kf5-support-btn').trigger('click');
    // })
    // $("#manageService button").on("click", function () {
    //   $('#kf5-support-btn').trigger('click');
    // })
    // $("#carService button").on("click", function () {
    //   $('#kf5-support-btn').trigger('click');
    // })
    //
    var trainingCourse = $('#trainingCourse');
    var trainingCourseList = $('#trainingCourse #lists');
    var trainingCourseDatas = [
      {
        text: 'IATF 16949:2016汽车行业质量管理体系内审员培训课程',
        href: '/sku/product/292',
        isHot: true
      },
      {
        text: 'IATF 16949:2016五大核心工具培训课程',
        href: '/sku/product/290',
        isHot: true
      },
      {
        text: 'VDA 6.3 & VDA 6.5过程审核&产品审核 （2016版）培训课程',
        href: '/sku/product/586',
        isHot: false
      },
      {
        text: 'ISO 26262汽车功能安全—AFSP培训课程',
        href: '/sku/product/763',
        isHot: true
      },
      {
        text: '汽车行业产品安全代表（PSB）培训课程',
        href: '/sku/product/772',
        isHot: false
      },
      {
        text: 'FMEA 潜在失效模式分析新版培训课程',
        href: '/sku/fmea/990',
        isHot: false
      },
      {
        text: '国际材料数据系统（IMDS 11.0）培训课程',
        href: '/sku/product/588',
        isHot: false
      },
      {
        text: 'CQI-8 分层审核 (Layered Process Audit)培训课程',
        href: '/sku/product/581',
        isHot: false
      },
      {
        text: 'CQI-9 热处理系统评估培训课程',
        href: '/sku/product/868',
        isHot: false
      }
    ];
    var trainingCourseDom = '';
    for (var i = 0; i < trainingCourseDatas.length; i++) {
      trainingCourseDom += '<li>' +
        '<span></span>' +
        '<a href="' + trainingCourseDatas[i].href + '" target="_blank">' + trainingCourseDatas[i].text + '</a>';
      if (trainingCourseDatas[i].isHot) {
        trainingCourseDom += '<i></i>';
      }
      trainingCourseDom += '</li>';
    }
    trainingCourseList.empty();
    trainingCourseList.append(trainingCourseDom)
    var auditServices = $('.auditServices .a')
    auditServices.hover(function () {
      $(this).addClass('active').siblings().removeClass('active')
    }, function () {
      $(this).removeClass('active')
    })
    //
    var carServiceBox = $('#carService ul');
    var carServiceDom = '';
    var carServiceDatas = [
      // todo
      // {
      //   text: '社会责任评估',
      //   href: '/industry/automotive/'
      // },
      {
        text: '汽车轻量化部件测试',
        href: '/industry/automotive/'
      },
      {
        text: '整车/零部件性能测试',
        href: '/industry/automotive/'
      },
      {
        text: '汽车电子产品测试',
        href: '/industry/automotive/'
      },
      {
        text: '汽车禁限用物质测试',
        href: '/industry/automotive/'
      },
      {
        text: '车内空气测试',
        href: '/industry/automotive/'
      },
      {
        text: '整车/零部件检验',
        href: '/industry/automotive/'
      },
      {
        text: '整车/零部件产品认证',
        href: '/industry/automotive/'
      }
    ];
    for (var i = 0; i < carServiceDatas.length; i++) {
      carServiceDom += '<li class="clearfix">' +
        '<span></span>' +
        '<a href="' + carServiceDatas[i].href + '" target="_blank">' + carServiceDatas[i].text + '</a>' +
        '<em>>></em>' +
        '</li>';
    }
    carServiceBox.empty().append(carServiceDom);
    var carServiceFirst = true
    function carServiceAnimate() {
      $('#carService .box1').animate({
        right: 0
      }, 1000)
      $('#carService .box2').animate({
        right: '465px'
      }, 1000)
    }
    // advantage
    var advantageFirst = true
    function advantageAnimate() {
      advantageFirst = false
      var box1 = $("#advantage li em").eq(0);
      var min1 = 0;
      var max1 = box1.data('max');
      var box2 = $("#advantage li em").eq(1);
      var min2 = 0;
      var max2 = box2.data('max');
      var box3 = $("#advantage li em").eq(2);
      var min3 = 0;
      var max3 = box3.data('max');
      setInterval(function () {
        if (min1 < max1) {
          min1 = min1 + 1
          box1.html(min1)
        }
      }, 1)

      setInterval(function () {
        if (min2 < max2) {
          min2 = min2 + 10
          box2.html(min2)
        }
      }, 1)
      setInterval(function () {
        if (min3 < max3) {
          min3 = min3 + 50
          box3.html(min3)
        }
      }, 1)
    }
    // moreService
    var moreServiceBox = $("#moreService ul");
    var moreServiceDom = '';
    var moreServiceDatas = [
      {
        tit: '信息安全认证',
        more: '/service/certification/#/30',
        lists: [
          {
            text: 'ISO/IEC 27001 信息安全管理',
            href: '/sku/iso27001/447'
          },
          {
            text: 'ISO 22301 业务连续性管理',
            href: '/sku/product/443'
          },
          {
            text: 'ISO/IEC  20000 信息技术管理',
            href: '/sku/product/231'
          },
          {
            text: 'ISO/IEC  27017 云安全管理',
            href: '/sku/product/519'
          },
          {
            text: 'ISO/IEC  27018 公共云个人信息管理',
            href: '/sku/product/519'
          },
          {
            text: 'GDPR通用数据保护条例',
            href: '/sku/product/1003'
          }
        ]
      },
      {
        tit: 'CQI系列课程',
        more: '/service/training/#/35',
        lists: [
          {
            text: 'CQI-8分层审核培训',
            href: '/sku/product/581'
          },
          {
            text: 'CQI-9热处理系统评估培训',
            href: '/sku/product/868'
          },
          {
            text: 'CQI-11电镀系统评估培训',
            href: '/sku/product/833'
          },
          {
            text: 'CQI-12喷漆系统评估培训',
            href: '/sku/product/834'
          },
          {
            text: 'CQI-14汽车保修管理指南',
            href: '/sku/product/835'
          },
          {
            text: 'CQI-15焊接系统评估培训',
            href: '/sku/product/836'
          }
        ]
      },
      {
        tit: '质量管理系列课程',
        more: '/service/training/#/35',
        lists: [
          {
            text: '全面质量管理（TQM）',
            href: '/sku/product/614'
          },
          {
            text: '质量成本管理',
            href: '/sku/product/618'
          },
          {
            text: '高级质量经理人管理魔方',
            href: '/sku/product/611'
          },
          {
            text: '新QC七大手法',
            href: '/sku/product/617'
          },
          {
            text: '抽样检验与品质保证',
            href: '/sku/product/767'
          },
          {
            text: '解决问题的8D原则与方法',
            href: '/sku/product/612'
          }
        ]
      },
      {
        tit: '标准体系系列课程',
        more: '/service/training/#/35',
        lists: [
          {
            text: '福特：Q1基础知识',
            href: '/sku/product/872'
          },
          {
            text: 'ISO 26262:2011汽车功能安全基础培训',
            href: '/sku/product/763'
          },
          {
            text: '汽车行业产品安全代表（PSB）',
            href: '/sku/product/772'
          },
          {
            text: 'IATF 16949（2016版）二方审核员培训',
            href: '/sku/product/859'
          },
          {
            text: 'IATF 16949（2016版）不符合项分析应对',
            href: '/sku/product/858'
          },
          {
            text: 'Automotive SPICE 3.1',
            href: '/sku/product/829'
          }
        ]
      }
    ]
    for (var i = 0; i < moreServiceDatas.length; i++) {
      moreServiceDom += '<li>' +
        '<dl>' +
        '<dt>' + moreServiceDatas[i].tit + '</dt>';
      for (var j = 0; j < moreServiceDatas[i].lists.length; j++) {
        moreServiceDom += '<dd>' +
          '<a href="' + moreServiceDatas[i].lists[j].href + '" target="_blank">' + moreServiceDatas[i].lists[j].text + '</a>' +
          '</dd>';
      }
      moreServiceDom += '</dl>' +
        '<a href="' + moreServiceDatas[i].more + '" target="_blank" class="more">MORE+</a>' +
        '</li>';
    }
    moreServiceBox.empty().append(moreServiceDom);
    // 滚动事件监听
    $(window).scroll(function () {
      $('.detailNav').removeClass('fix');
      if ($(this).scrollTop() >= 1514) {
        // 相关培训课程
        trainingCourse.find('.pic1').animate({
          top: '50px'
        }, 1000)
        trainingCourse.find('.pic2').animate({
          top: '20px'
        }, 1000)
        trainingCourse.find('.pic3').animate({
          top: '260px'
        }, 1500)
        trainingCourse.find('.pic4').animate({
          top: '207px'
        }, 1500)
      }
      if ($(this).scrollTop() >= 5240) {
        if (advantageFirst) {
          advantageAnimate()
        }
      }
      if ($(this).scrollTop() >= 4450) {
        if (carServiceAnimate) {
          carServiceAnimate()
        }
      }
    })
    // 提交表单
    $('.pop .layout').css({
      width: $('html,body').width(),
      height: $('html,body').height()
    })
    $('.pop .btn').on('click', function () {
      $('.pop').hide();
      $('#customer').val('');
      $('#customer1').val('');
      $('#phone').val('');
      $('#phone1').val('');
      $('#email').val('');
      $('#email1').val('');
      $('#content').val('');
      $('#content1').val('');
      $("#service").val('-1')
      $("#service1").val('-1')
    });
    $("#submit").on("click", function () {
      var data = {
        type: 'IATF 16949',
        trade: 68,
        tradeName: '汽车与零部件',
        service: $("#service").val(),
        serviceName: $("#service").find("option:selected").text(),
        content: $('#content').val(),
        customer: $('#customer').val().trim(),
        phone: $('#phone').val().trim(),
        email: $('#email').val().trim(), // 不能为空，不能为非法邮箱
        provice: '浙江省',
        company: 'sgs',
        os_type: 'pc',
        frontUrl: window.location.href,
        _csrf: '<%- csrf %>'
      }
      if (!$('#customer').val().trim()) {
        alert('请输入姓名！')
      } else if (!$('#phone').val().trim()) {
        alert('请输入手机号码！')
      } else if (!/^1\d{10}$/.test($('#phone').val().trim())) {
        alert('请输入正确的手机号码！')
      } else if (!$('#email').val().trim()) {
        alert('请输入电子邮箱')
      } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email').val().trim())) {
        alert('请输入正确电子邮箱！')
      } else if (data.service == '-1') {
        alert('请选择服务类型！')
      } else if (!$('#content').val()) {
        alert('请输入需求信息！')
      } else {
        $.ajax({
          url: "/ticket/post",
          type: 'POST',
          data: data,
          headers: {
            frontUrl: window.location.href
          },
          success: function (res) {
            if (res.success) {
              $('#submitPop').show();
            } else {
              alert(res.data)
            }
          },
          fail: function (e) {
          }
        })
      }
    })
    var BANNER_DOWNLOAD_URL = ''
    // 下载控制处理
    $('.download-btn').on('click', function(e) {
      var _target = $(e.target)
      BANNER_DOWNLOAD_URL = _target.attr('data-url')
      var qichechanye = localStorage.getItem('qichechanye')
      if (!qichechanye) {
        $('.down-load-pop').show();
        $('.down-load-pop .popBox').animate({
          width: 349,
          height: 400,
          opacity: 1
        }, 200, function () {
          $('.down-load-pop .content').show();
        })
      } else if (JSON.parse(qichechanye).isSend) {
        window.open(BANNER_DOWNLOAD_URL);
      }
    })
    $('.down-load-pop .close').on('click', function () {
      $('.down-load-pop .content').hide();
      $('.down-load-pop .popBox').animate({
        width: 0,
        height: 0,
        opacity: 0
      }, 200, function () {
        $('.down-load-pop').hide();
      })
    });
    $("#submit2").on("click", function () {
      var data = {
        type: 'IATF 16949',
        trade: '68',
        tradeName: '汽车与零部件',
        service: '28',
        serviceName: '体系认证',
        content: '页面文件下载',
        customer: $('#customer2').val().trim(),
        phone: $('#phone2').val().trim(),
        email: $('#email2').val().trim(), // 不能为空，不能为非法邮箱
        provice: '其他地区',
        company: '系统定义',
        os_type: 'pc',
        frontUrl: window.location.href,
        _csrf: '<%- csrf %>'
      }
      if (!$('#customer2').val().trim()) {
        alert('请输入姓名！')
      } else if (!$('#phone2').val().trim()) {
        alert('请输入手机号码！')
      } else if (!/^1\d{10}$/.test($('#phone2').val().trim())) {
        alert('请输入正确的手机号码！')
      } else if (!$('#email2').val().trim()) {
        alert('请输入电子邮箱')
      } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email2').val().trim())) {
        alert('请输入正确电子邮箱')
      } else if (!$('#check').prop("checked")) {
        alert('请同意隐私政策！')
      } else {
        $.ajax({
          url: "/ticket/post",
          type: 'POST',
          data: data,
          headers: {
            frontUrl: window.location.href
          },
          success: function (res) {
            if (res.success) {
              $('.down-load-pop').hide();
              localStorage.setItem('qichechanye', JSON.stringify({ isSend: true })); // 用于校验后期下载文件是否可以直接下载
              // window.location.href = BANNER_DOWNLOAD_URL
              window.open(BANNER_DOWNLOAD_URL);
            } else {
              alert(res.data)
            }
          },
          fail: function (e) {
          }
        })
      }
    })

    // 第二版下载交互需求
    $('.down-load-pop2 .layout').css({
        width: pw,
        height: ph
    })
    var downloadUrl
      cookieKey = 'qichechanye'
    $('.download-btn2').on('click', function(e) {
        var _target = $(e.target)
        downloadUrl = _target.attr('data-url')
        var download = localStorage.getItem('qichechanye')
        if (!download) {
            $('.down-load-pop2').show();
            $('.down-load-pop2 .popBox').animate({
                width: 422,
                height: 531,
                opacity: 1
            }, 200, function () {
                $('.down-load-pop2 .content').show();
            })
        } else if (JSON.parse(download).isSend) {
            // window.location.href = downloadUrl
            window.open(downloadUrl)
        }
    })
    $('.down-load-pop2 .close').on('click', function () {
        $('.down-load-pop2 .content').hide();
        $('.down-load-pop2 .popBox').animate({
            width: 0,
            height: 0,
            opacity: 0
        }, 200, function () {
            $('.down-load-pop2').hide();
        })
    });
    $("#submit3").on("click", function () {
        var company = $('#company3').val().trim()
        var content = $('#textarea3').val().trim()
        var isCoop = $('input[name="isCoop"]:checked').val()
        var data = {
            type: isCoop === '1' ? 'IATF 16949' : $('#coopName').val().trim(),
            trade: '',
            tradeName: '其他',
            service: '',
            serviceName: '其他',
            content: content,
            customer: $('#customer3').val().trim(),
            phone: $('#phone3').val().trim(),
            email: $('#email3').val().trim(), // 不能为空，不能为非法邮箱
            provice: '其他地区',
            company: company,
            os_type: 'pc',
            frontUrl: window.location.href,
            _csrf: '<%- csrf %>'
        }
        if (!$('#customer3').val().trim()) {
            alert('请输入姓名！')
        } else if (!$('#phone3').val().trim()) {
            alert('请输入手机号码！')
        } else if (!/^1\d{10}$/.test($('#phone3').val().trim())) {
            alert('请输入正确的手机号码！')
        } else if (!$('#email3').val().trim()) {
            alert('请输入电子邮箱')
        } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email3').val().trim())) {
            alert('请输入正确电子邮箱')
        } else if (!company) {
            alert('请输入企业名称')
        } else if (!content) {
            alert('请简述您下载本资源最希望解决的问题')
        } else if (!isCoop) {
            alert('请选择贵公司是否已与SGS有合作')
        } else if (!data.type) {
            alert('请输入您企业的IATF 16949认证机构')
        } else if (!$('#check3').prop("checked")) {
            alert('请同意隐私政策！')
        } else {
            $.ajax({
                url: "/ticket/post",
                type: 'POST',
                data: data,
                headers: {
                  frontUrl: window.location.href
                },
                success: function (res) {
                    if (res.success) {
                        $('.down-load-pop2').hide();
                        localStorage.setItem(cookieKey, JSON.stringify({ isSend: true })); // 用于校验后期下载文件是否可以直接下载
                        // window.location.href = downloadUrl
                        window.open(downloadUrl)
                    } else {
                        alert(res.data)
                    }
                },
                fail: function (e) {
                }
            })
        }
    })
  })
</script>
<% include ../footer.html %>
