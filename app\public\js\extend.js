$(function () {
  var ticViewUrl = ''
  var origin = window.location.origin;
  ticViewUrl = origin
  // ticViewUrl = 'http://localhost:9511'

  $('ul.zns').css('top', -$('>li', $('ul.zns')).length * 33 / 2 + 28);
  // 热搜词
  $("#searchBtn").on("click", function (event) {
    var value = $("#keyWord").val().trim();
    if (value) {
      if (value === '在线询价' || value === '在线报价') {
        window.open(ticViewUrl + '/oiq/start', '_blank')
      } else {
        hotKeywordClick(value)
      }
    }
  });
  var linkageWordDom = '';
  var linkageWordBox = $('.linkageWord ul')
  $('#keyWord').on('keyup', function (e) {
    var value = $(this).val().trim()
    if (value) {
      var keycode = e.keyCode;
      if (keycode == 13) {
        if (value === '在线询价' || value === '在线报价') {
          window.open(ticViewUrl + '/oiq/start', '_blank')
        } else {
          hotKeywordClick(value)
        }
      }
    }
    hotKeywordQry(value)
  })
  $("#keyWord").focus(function () {
    $('.hotKeyword').fadeOut();
    hotKeywordQry($(this).val())
  });
  $("#keyWord").blur(function () {
    if (!$('#keyWord').val()) {
      $('.hotKeyword').fadeIn();
    }
    $('.linkageWord').fadeOut();
  });
  $('.hotKeyword').on("click", 'span', function () {
    var value = $(this).html();
    $("#keyWord").val(value)
    if (value) {
      if (value === '在线询价' || value === '在线报价') {
        window.open(ticViewUrl + '/oiq/start', '_blank')
      } else {
        hotKeywordClick(value)
      }
    }
  });
  $('.linkageWord').on("click", 'li', function () {
    var value = $(this).data('keyword');
    $("#keyWord").val(value);
    $('.linkageWord').fadeOut();
    $('.hotKeyword').hide();
    if (value) {
      if (value === '在线询价' || value === '在线报价') {
        window.open(ticViewUrl + '/oiq/start', '_blank')
      } else {
        hotKeywordClick(value)
      }
    }
  });

  function hotKeywordClick(value) {
    var param = {
      keyword: value
    }
    $.ajax({
      type: 'POST',
      url: ticViewUrl + '/hotWord/click',
      data: param,
      success: function (res) {
        window.open(ticViewUrl + '/search?q=' + encodeURIComponent(value), '_blank')
      },
      fail: function (data) {

      },
      complete: function (complete) {

      }
    })
  }

  function hotKeywordQry(value) {
    var param = {
      keyword: value,
      pageNum: 1,
      pageRow: 6
    }
    $.ajax({
      type: 'POST',
      url: ticViewUrl + '/hotWord/qry',
      data: param,
      success: function (res) {
        var data = typeof res === 'string' ? JSON.parse(res) : res;
        var datas = data.data
        if (data.resultCode === '0' && datas.items.length) {
          linkageWordDom = '';
          var items = datas.items;
          for (var i = 0; i < items.length; i++) {
            if (items[i].keyword) {
              linkageWordDom += '<li data-keyword="' + items[i].keyword + '">' + items[i].title + '</li>'
            }
          }
          if (!$('.linkageWord').is(":visible")) $('.linkageWord').show();
        } else {
          $('.linkageWord').hide();
          linkageWordDom = ''
        }
        linkageWordBox.empty().append(linkageWordDom)
      },
      fail: function (data) { },
      complete: function (complete) { }
    })
  }

  /*
  TIC-5198
  异步渲染首页导航
  异步渲染内页导航
*/
  // asyncRenderNavication()
  // function asyncRenderNavication() {
  //   $.ajax({
  //     type: 'POST',
  //     url: ticViewUrl + '/getNavication',
  //     data: {},
  //     success: function (res) {
  //       createInnerNavication(res)
  //     },
  //     fail: function (data) {
  //     },
  //     complete: function (complete) {
  //     }
  //   })
  // }
  // // 创建内页导航菜单
  // function createInnerNavication(res) {
  //   var serviceID = $("#serviceNav"),
  //     tradeID = $("#tradeNav"),
  //     serviceNavication = res.serviceNavication,
  //     tradeNavication = res.tradeNavication;
  //   //　服务菜单
  //   if (serviceNavication.length) {
  //     renderNavicationInner(serviceNavication, serviceID, '/service/')
  //   }
  //   // 行业菜单
  //   if (tradeNavication.length) {
  //     renderNavicationInner(tradeNavication, tradeID, '/industry/')
  //   }
  // }

  // function renderNavicationInner(data, boxId, path) {
  //   var mainDom = '<div class="main">',
  //     sideDom = '<ul class="side">';
  //   data.forEach(function (v1, index) {
  //     if (!index) {
  //       sideDom += '<li class="active"><a data-id="'+ v1.id +'" href="'+ ticViewUrl +path + v1.alias + '">' + v1.name + '</a></li>';
  //     } else {
  //       sideDom += '<li><a href="'+ ticViewUrl +path + v1.alias + '">' + v1.name + '</a></li>';
  //     }
  //   })
  //   sideDom += '</ul>';
  //   data.forEach(function (v1, index) {
  //     if (!index) {
  //       mainDom += '<div class="list clearfix active">';
  //     } else {
  //       mainDom += '<div class="list clearfix">';
  //     }
  //     if (v1.lstSub && v1.lstSub.length >= 5) {
  //       mainDom += '<div class="second"><ul>';
  //       v1.lstSub.forEach(function (v2, index2) {
  //         if (!index2) {
  //           mainDom += '<li style="display: list-item;" class="active"><a href="javascript:void(0);" data-id="' + v2.id + '" data-link="'+ path + v1.alias + '/#/'  + v2.id + '">' + v2.name + '</a></li>';
  //         } else {
  //           mainDom += '<li><a href="javascript:void(0);" data-id="' + v2.id + '" data-link="'+ path + v1.alias + '/#/'  + v2.id + '">' + v2.name + '</a></li>';
  //         }
  //       })
  //       mainDom += '</ul></div>';
  //       mainDom += '<div class="secondList">';
  //       mainDom += '<div class="clearfix">';
  //       mainDom += '<ul class="secondListLi">';
  //       v1.lstSub.forEach(function (v2, index2) {
  //         // 3级菜单start
  //         if (!index2) {
  //           mainDom += '<li style="display: list-item;">';
  //         } else {
  //           mainDom += '<li>';
  //         }
  //         mainDom += '<div class="main-list2 clearfix">';
  //         v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
  //           if (v3.showType === 1) {
  //             mainDom += '<a class="cleafix" href="' + (v3.linkUrl === "" ? "javascript:;" : v3.linkUrl) + '">' + v3.showName + '</a>';
  //           }
  //           if (v3.showType === 2) {
  //             mainDom += '<dd>' +
  //             '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
  //             if (v3.isHot) mainDom += '<i></i>'; // 是否热门
  //             mainDom += '</dd>';
  //           }
  //         })
  //         mainDom += '</div>';
  //         // 3级菜单end
  //         // 广告start
  //         mainDom += '<div class="ads clearfix"><div class="moreService"><a href="'+ ticViewUrl +'/service/'+ v1.alias +'/service" class="moreServiceA">查看更多服务<i></i></a></div>';
  //         mainDom += '<ul>';
  //         v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
  //           if (v3.showType === 3) {
  //             mainDom += '<li>' +
  //               '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
  //               '<img src="' + v3.imgUrl + '">' +
  //               '</a>' +
  //               '<div class="info">' +
  //               '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
  //               '<h3 title="' + v3.showName + '">' + v3.showName + '</h3>' +
  //               '<p title="' + v3.showSummary + '">' + v3.showSummary + '</p>' +
  //               '</a>';
  //               if (v3.isBuy) {
  //                 mainDom += '<em data-id="' + v2.id + '" data-link="'+ v3.buyUrl + '">立即订购</em>';
  //               } else {
  //                 mainDom += '<button class="imBtn">在线咨询</button>';
  //               }
  //               mainDom += '</div>' +
  //               '</li>';
  //           }
  //         })
  //         mainDom += '</ul></div>';
  //         // 广告end
  //         mainDom += '</li>';
  //       })
  //       mainDom += '</ul>';
  //       mainDom += '</div>';
  //       mainDom += '</div>';
  //     } else {
  //       mainDom += '<div class="main-list">';
  //       v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
  //         mainDom += '<dl class="clearfix">';
  //         // v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
  //         // 2级start
  //         mainDom += '<dt>' +
  //           '<span>·</span>' +
  //           '<a href="javascript:void(0);" data-id="' + v2.id + '" data-link="'+ path + v1.alias + '/#/' + v2.id + '">' + v2.name + '</a>' +
  //           '</dt>';
  //         // 2级end
  //         // 3级start
  //         v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
  //           if (v3.showType === 2) {
  //             mainDom += '<dd>' +
  //               '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
  //             if (v3.isHot) mainDom += '<i></i>'; // 是否热门
  //             mainDom += '</dd>';
  //           }
  //         })
  //         // 3级end
  //         // })
  //         mainDom += '</dl>';
  //       })
  //       // 广告start
  //       mainDom += '<div class="ads">';
  //       mainDom += '<div class="moreService">' +
  //         '<a href="'+ ticViewUrl +'/service/'+ v1.alias +'/service">查看更多服务<i></i></a>' +
  //         '</div>';
  //       mainDom += '<ul>';
  //       v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
  //         v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
  //           if (v3.showType === 3) {
  //             mainDom += '<li>' +
  //               '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
  //               '<img src="' + v3.imgUrl + '">' +
  //               '</a>' +
  //               '<div class="info">' +
  //               '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
  //               '<h3 title="' + v3.showName + '">' + v3.showName + '</h3>' +
  //               '<p title="' + v3.showSummary + '">' + v3.showSummary + '</p>' +
  //               '</a>';
  //             if (v3.isBuy) {
  //               mainDom += '<em data-id="' + v2.id + '" data-link="'+ v3.buyUrl + '">立即订购</em>';
  //             } else {
  //               mainDom += '<button class="imBtn">在线咨询</button>';
  //             }
  //             mainDom += '</div>' +
  //               '</li>';
  //           }
  //         })
  //       })
  //       mainDom += '</ul>';
  //       mainDom += '</div>';
  //       // 广告end
  //       mainDom += '</div>';
  //     }
  //     mainDom += '</div>';
  //   })
  //   mainDom += '</div>';
  //   boxId.html(sideDom + mainDom)
  //   setTimeout(function () {
  //     asyncBuildEvent()
  //   }, 100)
  // }

  // function asyncBuildEvent() {
  //   var tradeNav = $('#tradeNav');
  //   tradeNav.on('mouseenter', '.side li', function () {
  //     var index = $(this).index()
  //     tradeNav.find('.main .list').eq(index).addClass("active").siblings().removeClass("active");
  //     $(this).addClass("active").siblings().removeClass("active")
  //   })
  //   $("#tradeHome").hover(function () {
  //     tradeNav.show();
  //     $(this).find('.dragAngel').show();
  //   }, function () {
  //     tradeNav.hide();
  //     $(this).find('.dragAngel').hide();
  //   })
  //   tradeNav.on('mouseenter', '.second li', function () {
  //     var index = $(this).index()
  //     $(this).closest('.list').find('.secondListLi > li').eq(index).show().siblings().hide();
  //     $(this).addClass("active").siblings().removeClass("active")
  //   })

  //   var serviceNav = $('#serviceNav');
  //   serviceNav.on('mouseenter', '.side li', function () {
  //     var index = $(this).index()
  //     serviceNav.find('.main .list').eq(index).addClass("active").siblings().removeClass("active");
  //     $(this).addClass("active").siblings().removeClass("active")
  //   })

  //   // B类二级菜单
  //   serviceNav.on('mouseenter', '.second li', function () {
  //     var index = $(this).index()
  //     $(this).closest('.list').find('.secondListLi > li').eq(index).show().siblings().hide();
  //     $(this).addClass("active").siblings().removeClass("active")
  //   })
  //   $("#serviceHome").hover(function () {
  //     serviceNav.show();
  //     $(this).find('.dragAngel').show();
  //   }, function () {
  //     serviceNav.hide();
  //     $(this).find('.dragAngel').hide();
  //   });
  //   // 新增二级下拉查单
  //   $('.nav_first--li').hover(function () {
  //     $(this).find('.dragAngel').show();
  //     $(this).find('.nav_second').show();
  //   }, function () {
  //     $(this).find('.dragAngel').hide();
  //     $(this).find('.nav_second').hide();
  //   });
  //   // 强制刷新页面
  //   // $('.allNav').on('click', 'dt a', function () {
  //   //   var link = $(this).data('link')
  //   //   window.location.href = ticViewUrl + link
  //   // });
  //   // $('.allNav').on('click', '.second a', function () {
  //   //   var link = $(this).data('link')
  //   //   window.location.href = ticViewUrl + link
  //   // });
  //   $('.mainNav').on('click', 'em', function () {
  //     var buyUrl = $(this).data('link');
  //     window.location.href = buyUrl
  //   });
  //   $('.mainNav').off("click").on('click', '.imBtn', function () {
  //     $.ajax({
  //       type: 'POST',
  //       url: ticViewUrl + '/IMtool',
  //       data: {
  //         paraName: 'CHAT_SET'
  //       },
  //       headers: {
  //         frontUrl: window.location.href
  //       },
  //       success: function (res) {
  //         var isChatbot = false;
  //         var isZhiChi = false;
  //         var notLeadoo = false; // 不会触发leadoo的路由
  //         debugger
  //         if (window.location.pathname === "/oiq" || window.location.pathname === "/oiq/start" || window.location.pathname.indexOf('/lab') > -1) {
  //           notLeadoo = true
  //         }
  //         const ticViewList = res.filter(v => {
  //           return v.groupName === 'MALL'
  //         })
  //         if (window.location.host === "www.sgsmall.com.cn" || window.location.host === "uat.sgsmall.com.cn" || window.location.host === "localhost:9511") {
  //           ticViewList.forEach(v => {
  //             if (v.paraValue === "1") {
  //               if (v.paraCode.includes("CHATBOT")) isChatbot = true
  //               if (v.paraCode.includes("SOBOT")) isZhiChi = true
  //             }
  //           })
  //         }
  //         if (notLeadoo) {
  //           $('#kf5-support-btn').trigger('click')
  //         } else {
  //           if (isZhiChi) {
  //             $('#zc__sdk__sys__btn').trigger('click')
  //           } else if (isChatbot) {
  //             var iframeLen = $("iframe").length;
  //             for (var i = 0; i < iframeLen; i++) {
  //               if ($("iframe")[i]) {
  //                 $($("iframe")[i]).contents().find('.ld-launch-btn').trigger('click');
  //               }
  //             }
  //           }
  //         }
  //       },
  //     })

  //     // if (window.location.host === "member.sgsonline.com.cn" ||  window.location.host === "memberuat.sgsonline.com.cn" || window.location.host === "store.sgsonline.com.cn" || window.location.host === "ticuat.sgsonline.com.cn" || window.location.pathname === "/oiq" || window.location.pathname === "/oiq/start") {
  //     //   if (IS_KF5) {
  //     //     $('#kf5-support-btn').trigger('click')
  //     //   } else if (IS_ZHICHI) {
  //     //     $('#zc__sdk__sys__btn').trigger('click')
  //     //   }
  //     // } else {
  //     //   if (IS_CHATBOT) {
  //     //     var iframeLen = $("iframe").length;
  //     //     for(var i = 0; i < iframeLen; i++) {
  //     //       if ($("iframe")[i]) {
  //     //         $($("iframe")[i]).contents().find('.ld-launch-btn').trigger('click');
  //     //       }
  //     //     }
  //     //   } else if (IS_KF5) {
  //     //     $('#kf5-support-btn').trigger('click')
  //     //   } else if (IS_ZHICHI) {
  //     //     $('#zc__sdk__sys__btn').trigger('click')
  //     //   }
  //     // }
  //   });

  //   /*
  //     TIC-5202
  //     数据模型： 站点.页面.模块.标识位.session
  //     实例化后：spm=EJHxGX9b.h.n-${id}.${collectCode}.${sessionId}&token=${token}
  //     a:EJHxGX9b 站点名称 ticView(EJHxGX9b) member(HNCO2i11) store(Y0tCOIDS)
  //     b:1YCEZc6a(home首页) M3nTPXTW(inner)内页 网站页面
  //     c:n-${id} (navication)导航的菜单服务或行业id
  //     d:${collectCode} 服务端唯一标识
  //     e:${sessionId} sessionid
  //     token: 登录后用户token
  //   */
  //   $("#serviceHome, #tradeHome").on("click", "a", function (e) {
  //     if (e && e.preventDefault) {
  //       e.preventDefault();
  //     } else {
  //       window.event.returnValue = false;
  //     }
  //     var href = $(this).attr('href')
  //     var link = $(this).data('link')
  //     var collectcode = '';
  //     var id = '';
  //     if ($(this).data('collectcode')) collectcode = $(this).data('collectcode')
  //     if ($(this).data('id')) id = '-' + $(this).data('id')
  //     if (link) {
  //       createSPM('M3nTPXTW', id, collectcode, 'sessionId')
  //       window.location.href = ticViewUrl + link
  //     } else {
  //       createSPM('M3nTPXTW', id, collectcode, 'sessionId')
  //       window.location.href = href
  //     }
  //   });
  //   function createSPM(b, c, d, e) {
  //     var website = 'EJHxGX9b';
  //     var pageCode = '';
  //     var token = '';
  //     if (origin.indexOf('member') > -1) {
  //       website = 'HNCO2i11';
  //       token = '';
  //     } else if (origin.indexOf('store') > -1) {
  //       website = 'Y0tCOIDS';
  //       token = '';
  //     } else {
  //       token = Cookies.get('SSO_TOKEN');
  //     }
  //     var spm = [website, b, 'n' + c, d, e];
  //     var query = {
  //       spm: spm.join('.'),
  //       token: token,
  //       tx: ''
  //     }
  //     $.ajax({
  //       type: 'POST',
  //       url: ticViewUrl + '/sendSPM',
  //       // url: 'http://localhost:9511/sendSPM',
  //       data: query,
  //       success: function (res) { },
  //       fail: function (data) { },
  //       complete: function (complete) { }
  //     })
  //   }
  // }
});
