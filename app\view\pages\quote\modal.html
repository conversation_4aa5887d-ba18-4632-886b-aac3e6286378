<tic-login-verify-code ref="loginVerifyCode" :env="env" :captcha-app-id="captchaAppId" v-model="showModal" :captcha-show="captchaShow" :number="tab === 'phone' ? form.phone : form.email" :data="pageInfo" :handle-submit="handleModalSubmit" :verify-params="verifyParams" />

<script>
  console.log('<%- locals.env %>')
  var quoteMixins = {
    data() {
      return {
        pageInfo: {
          title: '验证手机号',
          sendModel: '已发送到手机号：+86',
          prepend: '手机验证码'
        },
        captchaAppId: '<%- captchaAppId %>',
        captchaShow: true,
        verifyParams: {
          projectName: 'TIC_TEMPLATE',
          verifyType: 2
        },
        env: '<%- locals.env %>'.toLowerCase(),
        // env: 'test',
        tab: 'phone'
      }
    },
    mounted() {
      if (this.provice) this.qryCity()
      if (this.isLogin) this.contact_approve_show = false
    },
    methods: {
      sing: function (param, timestamp) {
        var pmd5 = md5((this.pid + this.pcode).toUpperCase());
        var str = JSON.stringify(param) + pmd5.toUpperCase();
        return md5(str + timestamp);
      },
      handleInputEmail: function () {
        // if (!this.isLogin) {
        //   if (this.form.email && !this.form.phone) {
        //     this.contact_approve_show = false
        //   } else {
        //     this.contact_approve_show = true
        //   }
        // }
      },
      handleTab: function (val) {
        this.tab = val
      },
      handleModalSubmit: function (loginRef) {
        this.form.verifyCode = loginRef.verifyCode
        var that = this
        var param = that.form
        var timestamp = new Date().valueOf();
        var url = '/submitTicket';
        loginRef.loading = true
        param.verifyMode = this.tab
        $.ajax({
          type: 'POST',
          url: url,
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: this.pid,
            sign: that.sing(param, timestamp),
            timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            loginRef.loading = false
            if (res.resultCode === '0') {

              if (res.data.isReg === 1) {
                that.type = 1
              } else if (res.data.isReg === 0) {
                if (that.isLogin) {
                  that.type = 3
                } else {
                  that.type = 2
                }
              }
              Cookies.set("SSO_TOKEN", res.data.token, { domain: ".sgsmall.com.cn", expires: 7 });
              if (that.type === 1 || that.type === 2 || that.type === 3) {
                that.countdownTimer = setInterval(function () {
                  if (that.countdownTime > 1) {
                    that.countdownTime--
                  } else {
                    clearInterval(that.countdownTimer)
                    if (that.type !== 4) {
                      window.location.href = '<%- memberUrl %>/quote/list'
                    }
                  }
                }, 1000);
              }
              that.showModal = false
              that.isSuccess = true
              this.type = 0
            } else if (res.resultCode === '9978') {
              Cookies.remove('SSO_TOKEN', { domain: '.sgsmall.com.cn' })
              Cookies.remove('SSO_TOKEN')
              that.isLogin = false
              that.showModal = true
            } else {
              that.$message.error(res.resultMsg)
            }
            that.loading = false
          },
          fail: function (data) {
            loginRef.loading = false
            that.$message.error(data.resultMsg)
          }
        })
      },
      qryCity: function () {
        var param = {}
        var timestamp = new Date().valueOf();
        var that = this
        $.ajax({
          type: 'POST',
          url: this.host + '/ticCenter/business/api.v1.center/org/qryCity',
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: this.pid,
            sign: this.sing(param, timestamp),
            timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            if (res.resultCode === '0') {
              for (var i = 0; i < res.data.length; i++) {
                res.data[i].citys = null
              }
              that.provice = res.data || []
            } else {
              alert(res.resultMsg)
            }
          },
          fail: function (data) {
            alert(res.resultMsg)
          },
          complete: function (complete) {
          }
        })
      },
      // 验证手机和邮箱
      verifyPhoneAndMail: function () {
        var regMail =   /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/;
        var regPhone = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;
        this.captchaShow = this.tab === 'phone'
        if (this.tab === 'phone') {
          if (!this.form.phone) {
            this.$message.error('请输入手机号')
            return
          }
          if (!regPhone.test(this.form.phone)) {
            this.$message.error('请输入正确的手机号')
            return
          }
          this.pageInfo = {
            title: '验证手机号',
            sendModel: '已发送到手机号：+86',
            prepend: '手机验证码'
          }
          return true
        } else {
          if (!this.form.email) {
            this.$message.error('请输入邮箱地址')
            return
          }
          if (!regMail.test(this.form.email)) {
            this.$message.error('请输入正确的邮箱地址')
            return
          }
          this.pageInfo = {
            title: '验证邮箱',
            sendModel: '已发送到邮箱',
            prepend: '邮箱验证码'
          }
          return true
        }
      },
      handleSubmit: function () {
        const that = this
        if (!that.approve) {
          this.$message.error('请先阅读隐私政策并选择是否同意')
          return
        }
        this.$refs.form.validate(function (valid) {
          that.form.company = ''
          if (valid) {
            if (that.verifyPhoneAndMail()) {
              if (!that.isLogin) {
                Cookies.remove('SSO_TOKEN', { domain: '.sgsmall.com.cn' })
                Cookies.remove('SSO_TOKEN')
                that.showModal = true
              } else {
                that.handleModalSubmit({})
              }
            }
          } else {
            that.loading = false
          }
        })
      },
    }
  }
</script>