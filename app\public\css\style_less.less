@import './mixin.less';

// reset base styel
html,
body {
  margin: 0;
  padding: 0;
}

body {
  min-width: 1226px;
  background: #fff;
  font-family: <PERSON><PERSON>, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}

a {
  text-decoration: none;
}


a img {
  border: 0
}

ul,
li {
  list-style: none;
}

dd,
input,
ul,
li,
p,
h2,
div {
  padding: 0;
  margin: 0;
}

em,
i {
  font-style: normal;
}

button:focus {
  border: 0;
  outline: 0;
}

[v-cloak] {
  display: none;
}

input::-webkit-input-placeholder {
  color: #ACACAC !important;
}

input::placeholder {
  color: #ACACAC !important;
}

input:-ms-input-placeholder {
  color: #ACACAC !important;
}

input:-moz-placeholder {
  color: #ACACAC !important;
}

input::-webkit-input-placeholder {
  color: #ACACAC !important;
}

input::-ms-clear,
input::-ms-reveal {
  display: none;
}

// reset base styel
// common style

.wrap-lg {
  min-width: 1226px;
  max-width: 1226px;
  box-sizing: border-box;
  margin: 0 auto;

  &>* {
    box-sizing: border-box;
  }
}

.clearfix {
  // *+height: 1%;

  &:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
}

// common style

.sgs {
  font-size: 12px;
  line-height: 38px;
  //color: rgb(176, 176, 176);
  color: #626262;
  float: left;

  a {
    //color: rgb(176, 176, 176);
    color: #626262;
  }
}

// header style
.headBox {
  width: 100%;
  height: 38px;
  //background: rgb(51, 51, 51);
  background: #e3e3e3;
  line-height: 38px;
}

.head {
  width: 1226px;
  height: 38px;
  //background: rgb(51, 51, 51);
  background: #e3e3e3;
  margin: 0 auto;
  font-size: 12px;

  a {
    &:hover {
      color: @mainColor;
    }
  }
}

.headUl {
  width: auto;
  height: 36px;
  float: right;
  margin: 0;
  padding: 0;
}

.headLi {
  float: left;
  line-height: 36px;
  //color: rgb(176, 176, 176);
  color: #626262;
  font-size: 12px;
  list-style: none;
  padding: 0 10px;

  i {
    padding-right: 14px;
  }

  a {
    //color: rgb(176, 176, 176);
    color: #626262;
    cursor: pointer;

    &:hover,
    &.mainColor {
      color: @mainColor;
    }
  }

  .pro_icon {
    width: 40px;
    height: 17px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 5px;
    cursor: pointer;
  }

  span {
    &:hover {
      color: @mainColor;
    }
  }

  &.first {
    width: 118px;
    height: 100%;
    margin-right: 0;
    background: rgb(66, 66, 66);
  }

  em {
    color: @mainColor;
    padding-left: 5px;
    font-weight: bold;
    font-size: 16px;
    vertical-align: middle;
  }
}

.headLi-first-icon {
  width: 17px;
  height: 16px;
  margin-top: 10px;
  margin-left: 18px;
  display: block;
  float: left;
  background: url(../images/shop.png) no-repeat 100% 100%;
}

.headLi-first-word {
  width: auto;
  height: 100%;
  line-height: 39px;
  margin-right: 14px;
  display: block;
  float: right;
  font-size: 12px;
  color: rgb(176, 176, 176);
}

.n_topBar {
  background: #fff;
  height: 80px;
  line-height: 80px;

  .logo {
    display: block;
    float: left;

    img {
      height: 80px;
      float: left;
    }

    span {
      display: block;
      height: 22px;
      line-height: 22px;
      float: left;
      width: 198px;
      background: url('./../../public/images/newPage/logo-des.png') no-repeat 100% 100%;
      margin-left: 15px;
      margin-top: 29px;
    }
  }

  .menus {
    float: right;
    margin-top: 31px;
    height: 17px;

    span,
    a,
    em,
    label {
      color: #878787;
      font-size: 14px;
      font-style: normal;
      height: 17px;
      line-height: 17px;
      display: block;
      float: left;
    }

    em {
      padding: 0 8px;

      i {
        padding-left: 5px;
      }
    }

    .blod {
      color: @mainColor;
      cursor: pointer;
      font-weight: 700;
    }

    .segmenttaion {
      color: #e5e5e5;
    }

    .carBox {
      position: relative;

      em {
        float: left;
        padding: 100% 100% 0 10px;

        #carNum {
          color: @mainColor;
        }
      }

      img {
        float: left;
      }
    }

    .car {
      width: 20px;
      height: 17px;
    }

  }
}

.n_searchModule {
  height: 130px;
  background: url('./../../public/images/newPage/search_bg.jpg') 50% 50% no-repeat;
  width: 100%;

  .input {
    width: 624px;
    height: 40px;
    line-height: 40px;
    margin-left: 300px;
    position: relative;
    padding-top: 43px;

    i {
      position: absolute;
      top: 51px;
      left: 12px;
      display: block;
      width: 23px;
      height: 23px;
      background: url('./../../public/images/newPage/search_icon.png') no-repeat 100% 100%;
    }

  }

  input {
    background: #fff;
    border: 0;
    outline: 0;
    color: #b3b3b3;
    font-size: 12px;
    height: 40px;
    line-height: 40px;
    width: 440px;
    float: left;
    padding-left: 48px;
    box-shadow: -100% 100% 0 green,
      100% 100% 0 blue,
      10px 10px 13px rgba(0, 0, 0, .3),
      100% 100% 0 yellow;
  }

  button,
  a {
    width: 109px;
    background: @mainColor;
    color: #fff;
    text-align: center;
    font-size: 18px;
    height: 40px;
    line-height: 40px;
    border: 0;
    outline: 0;
    float: left;
    cursor: pointer;
    box-shadow: -100% 100% 0 green,
      100% 100% 0 blue,
      10px 10px 13px rgba(0, 0, 0, .3),
      100% 100% 0 yellow;
  }

  button:hover,
  a:hover {
    background: #cc5500;
    transform: .5s;
  }

  .linkageWord {
    position: absolute;
    top: 83px;
    left: 300px;
    border: 1px solid #ca4300;
    width: 595px;
    z-index: 2;
    background: #fff;
    display: none;

    li {
      height: 35px;
      line-height: 35px;
      overflow: hidden;
      list-style: none;
      padding-left: 15px;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 383px;

      &:hover {
        background: #f5f5f5;
      }
    }
  }

  .hotWord {
    width: 580px;
    margin-left: 300px;
    padding-top: 8px;
    height: 20px;
    overflow: hidden;

    em,
    span {
      color: #e8e8e8;
      font-size: 12px;
      padding-left: 12px;
      height: 20px;
      line-height: 20px;
      font-style: normal;
    }

    span {
      cursor: pointer;

      &.hot {
        color: @mainColor;
      }
    }
  }
}

// header style

// navigation style
.navBox {
  width: 1226px;
  margin: 0 auto;
  background: white;
  position: relative;
  height: 100px;
}

.nav {
  width: 100%;
  height: 0;
  margin: 0 auto;
  background: rgb(255, 255, 255);

  &.fix {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 2000;
    height: 100px;
    box-shadow: 100% 100% 10px rgba(0, 0, 0, .1);
  }
}

.navwrap {
  height: 100px;
  background: #fff;
}


.navUl {
  float: left;
  width: auto;
  height: 100%;
  margin-left: 50px;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0;
}

.navLi {
  width: auto;
  height: 100px;
  line-height: 100px;
  font-size: 16px;
  color: rgb(51, 51, 51);
  float: left;
  position: relative;
  cursor: pointer;
  text-align: center;
  margin: 0 25px;
  list-style: none;

  a {
    color: rgb(51, 51, 51);

    &:hover {
      color: @mainColor;
      font-weight: bold;
    }
  }

  &.s:hover {
    color: #ca4300;
    font-weight: bold;
  }

  &.t {
    margin-right: 0;

    &:hover {
      color: @mainColor;
      font-weight: bold;
    }
  }
}

.navBor {
  position: absolute;
  width: 1px;
  background: #eeeeee;
  height: auto;
  top: 10px;
  left: 200px;
}

.navImg {
  width: 113px;
  height: 53px;
  margin-top: 24px;
  float: left;
  padding-bottom: 20px;

  img {
    width: 100%;
    height: 100%;
  }

}

.drag {
  width: 402px;
  height: auto;
  padding: 10px 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, .15);
  position: absolute;
  z-index: 9999;
  top: 97px;
  left: -170px;
  border-right: 1px solid rgba(0, 0, 0, .15);
  border-left: 1px solid rgba(0, 0, 0, .15);
  border-bottom: 1px solid rgba(0, 0, 0, .15);
  display: none;
  border-top: 3px solid @mainColor;
  background: white;

  .dragLi {
    color: rgb(51, 51, 51);
    font-weight: normal;
  }
}

.dragLi {
  width: 200px;
  height: 35px;
  line-height: 35px;
  font-size: 14px;
  float: left;
  text-align: center;
  list-style: none;
}

.dragAngel {
  width: 0;
  height: 0;
  position: absolute;
  overflow: hidden;
  font-size: 0;
  line-height: 0px;
  border-width: 0px 10px 11px;
  border-style: solid solid solid solid;
  border-left-color: transparent;
  border-right-color: transparent;
  border-top-color: transparent;
  border-bottom-color: @mainColor;
  display: none;
  left: 44px;
  top: 89px;
}

// navigation style

.serverConBox,
.newsBox {
  ul {
    list-style-type: decimal;

    li {
      list-style-type: disc;
      list-style-position: inside;
      padding-left: 1em;

      &>p {
        display: inline;
      }
    }
  }

  ol {
    list-style-type: decimal;

    li {
      list-style-type: inherit;
      list-style-position: inside;
      padding-left: 1em;

      &>p {
        display: inline;
      }
    }
  }
}

.n_wrap {
  width: 1226px;
  margin: 0 auto;
}

.nav_first--li {
  position: relative;

  .nav_second {
    background: rgb(51, 51, 51);
    position: absolute;
    top: 84px;
    left: -17px;
    z-index: 2;
    width: 150px;
    display: none;
    border-top: 4px solid @mainColor;

    dd {
      height: 35px;
      line-height: 35px;
      text-align: center;

      a {
        color: #fff;
        display: inline-block;
        height: 35px;
        line-height: 35px;
        width: 100%;

        &:hover {
          background: #fff;
          color: @mainColor;
          font-weight: bold;
        }
      }
    }
  }
}

.information {
  margin-top: 33px;
}

.files {
  margin-top: 20px;
}

.download_login {
  background: #ffeedf;
  height: 40px;
  line-height: 40px;
  text-align: center;
  position: relative;

  span {
    color: #6a6a6a;
    font-size: 12px;
  }

  a {
    color: @mainColor;
    font-size: 14px;
    padding-left: 20px;
    font-weight: bold;
  }

  i {
    position: absolute;
    right: 30px;
    top: 15px;
    background: url('./../images/200304/download/close.png') no-repeat 100% 100%;
    width: 12px;
    height: 12px;
  }
}

.download_tab {
  width: 850px;
  height: 62px;
  line-height: 62px;
  background: #f8f8f8;

  ul {
    display: flex;
    flex-wrap: wrap;
    margin-left: 40px;

    li {
      position: relative;
      padding: 0 25px;
      cursor: pointer;

      &:hover::after {
        transform: scaleX(1);
      }

      &:hover {
        color: @mainColor;
      }

      &::after {
        position: absolute;
        content: "";
        top: 59px;
        left: 0;
        width: 100%;
        height: 3px;
        background: @mainColor;
        transform: scaleX(0);
        transition: 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
      }

      &.active::after {
        transform: scaleX(1);
      }

      i {
        display: none;
        width: 0;
        height: 0;
        position: absolute;
        overflow: hidden;
        font-size: 0;
        line-height: 0;
        border-width: 10px;
        border-style: solid dashed dashed dashed;
        border-color: transparent transparent #f8f8f8 transparent;
        top: 60px;
        left: 50%;
        transform: translateX(-50%);
      }

      &.active {
        i {
          display: block;
        }
      }
    }
  }
}

.download_filter {
  width: 810px;
  background: #f8f8f8;
  margin-top: 18px;
  padding: 0 20px 20px 20px;
  float: left;

  .download_filter_tit {
    color: #878787;
    font-size: 14px;
    width: 80px;
    text-align-last: justify;
    text-align: justify;
    flex: 1;
    padding-left: 20px;
  }

  .download_filter_action {
    border-bottom: 1px dashed #b5b5b5;
    float: left;
    width: 100%;
    padding: 13px 0;

    span,
    em {
      display: inline-block;
      color: #878787;
      font-size: 14px;
      height: 14px;
      line-height: 14px;
    }

    span {
      float: left;
      margin-left: 15px;
      background: url(../images/200304/download/right.png) no-repeat 60px 2px;
      padding-right: 15px;
    }

    em {
      float: right;
      margin-right: 15px;
      background: url(../images/200304/download/bottom.png) no-repeat 60px 4px;
      padding-right: 15px;
      cursor: pointer;

      &.active {
        color: @mainColor;
        background: url(../images/200304/download/top.png) no-repeat 60px 4px;
      }
    }
  }

  .download_filster_checkboxGroup {
    width: 695px;
    display: flex;
    flex-wrap: wrap;
    margin-left: 10px;
    padding-right: 15px;

    label {
      margin-right: 10px;
      margin-bottom: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      span {
        font-size: 12px;
        color: #333;
        padding-left: 3px;
      }

      i {
        display: inline-block;
        width: 12px;
        height: 12px;
        background: url('./../images/200304/checbox.png') no-repeat 100% 100%;
      }

      &.active {
        i {
          background: url('./../images/200304/checbox_active.png') no-repeat 100% 100%;
        }
      }
    }
  }


  .download_filster_industry,
  .download_filster_service {
    display: flex;
    border-bottom: 1px #d2d2d2 dashed;
    padding: 20px 0 5px 0;
    clear: both;
    // display: none;
  }

  .download_filster_keyword {
    display: flex;
    align-items: center;
    justify-content: start;
    width: 405px;
    float: left;
    // border-bottom: 1px #d2d2d2 dashed;
    padding: 20px 0 15px 0;

    div {
      display: flex;
      align-items: center;
      width: 305px;
      margin-left: 10px;
    }

    input {
      width: 218px;
      height: 26px;
      line-height: 26px;
      background: #fff;
      border: 1px solid #b5b5b5;
      padding-left: 10px;
      font-size: 12px;

      &.active {
        border: 1px solid @mainColor;
        color: #333;
      }
    }

    button {
      width: 75px;
      height: 26px;
      line-height: 26px;
      text-align: center;
      letter-spacing: 2px;
      color: #fff;
      font-size: 14px;
      background: @mainColor;
      border: 0;
      cursor: pointer;
      outline: 0;
    }
  }

  .download_filster_date {
    display: flex;
    align-items: center;
    justify-content: start;
    width: 355px;
    float: left;
    // border-bottom: 1px #d2d2d2 dashed;
    padding: 20px 20px 15px 30px;

    input {
      width: 262px;
      height: 26px;
      line-height: 26px;
      background: #fff;
      border: 1px solid #b5b5b5;
      font-size: 12px;
      text-align: center;

      &.active {
        border: 1px solid @mainColor;
        color: #333;
      }
    }
  }

  .download_filter_result {
    clear: both;
    // display: flex;
    padding: 20px 0 0 0;
    justify-content: start;
    align-items: center;
    display: none;
    border-top: 1px #d2d2d2 dashed;

    .download_filter_result--items {
      width: 720px;
      display: flex;
      flex-wrap: wrap;

      label {
        border: 1px solid #b5b5b5;
        height: 26px;
        line-height: 26px;
        color: #333;
        font-size: 12px;
        display: inline-block;
        margin: 0 10px 5px 0;

        span {
          padding-left: 10px;
        }

        i {
          padding: 0 10px;
          cursor: pointer;
        }
      }

      em {
        font-size: 12px;
        color: @mainColor;
        height: 28px;
        line-height: 28px;
        display: inline-block;
        margin-left: 15px;
        cursor: pointer;
      }
    }
  }
}

.download_list {
  margin: 10px 0 50px 0;
  float: left;
  width: 100%;

  li {
    border-bottom: 1px solid #ececec;
    height: 149px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:hover {
      h2 {
        a {
          color: @mainColor;
        }
      }

      p {
        color: #333;
      }
    }
  }

  .download_list_content {
    height: 95px;
    display: flex;
    flex-direction: column;
    flex: 1;

    &.hasImg {
      h2 {
        a {
          max-width: 539px;
        }
      }

      label {
        em {
          max-width: 486px;
        }
      }
    }

    h2 {
      display: flex;
      align-items: center;
      padding-bottom: 10px;

      a {
        color: #333;
        font-size: 18px;
        font-weight: normal;
        max-width: 754px;
        display: inline-block;
        height: 22px;
        line-height: 22px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .original {
        width: 31px;
        height: 18px;
        background: #EEEEEE;
        border-radius: 3px;
        font-size: 11px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #959595;
        line-height: 18px;
        margin-left: 10px;
        text-align: center;
      }

      .vip {
        width: 31px;
        height: 18px;
        background: #FFEAC9;
        border-radius: 3px;
        font-size: 11px;
        font-family: Arial;
        font-weight: 400;
        color: #8E6C32;
        line-height: 18px;
        margin-left: 9px;
        text-align: center;
      }

      i {
        font-size: 12px;
        height: 18px;
        line-height: 18px;
        font-weight: normal;
        margin-left: 10px;
        max-width: 74px;
        padding: 0 5px;
        text-align: center;
        background: #f7f7f7;
        color: #959595;
        border: 1px solid #e5e5e5;
        border-radius: 10px;

      }
    }

    p {
      color: #959595;
      font-size: 12px;
      line-height: 20px;
      height: 40px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }

    label {
      padding-top: 10px;
      height: 12px;
      line-height: 12px;

      span {
        color: #959595;
        font-size: 12px;
        padding-right: 18px;
        display: block;
        height: 12px;
        line-height: 12px;
        float: left;
        border-right: 1px solid #c2c2c2;
        padding-left: 18px;

        &:first-child {
          padding-left: 0;
        }

        &:last-child {
          border-right: 0;
          padding-right: 0;
        }
      }

      em {
        display: block;
        max-width: 652px;
        height: 12px;
        overflow: hidden;
        line-height: 12px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        float: left;
        padding-right: 18px;
        border-right: 1px solid #c2c2c2;

        span {
          &:first-child {
            padding-left: 18px;
            border-left: 1px solid #c2c2c2;

          }
        }
      }

      i {
        display: block;
        width: 10px;
        height: 16px;
        line-height: 16px;
        float: left;
        color: #c2c2cc;
      }
    }
  }

  .img {
    width: 150px;
    height: 95px;
    margin-left: 20px;
    background-size: cover;
  }

  h3 {
    text-align: center;
    font-weight: normal;
    height: 100px;
    line-height: 100px;
  }
}

.files {
  .files_list {
    margin: 10px 0 50px 0;
    float: left;
    width: 100%;

    li {
      height: 44px;
      line-height: 44px;
      width: 100%;
      border-bottom: 1px solid #ececec;
      padding: 25px 0;

      .files_list_checkbox {
        width: 18px;
        height: 18px;
        display: block;
        background: url('./../images/200304/download/checkbox_static.png') no-repeat 100% 100%;
        float: left;
        margin-top: 15px;

        &.active {
          background: url('./../images/200304/download/checkbox_active.png') no-repeat 100% 100%;
        }

        &.disabled {
          background: url('./../images/200304/download/checkbox_disabled.png') no-repeat 100% 100%;
        }
      }

      .files_list_icon {
        // i {
        display: block;
        width: 44px;
        height: 44px;
        float: left;
        margin-left: 10px;

        &.code {
          background: url('./../images/200304/download/code.png') no-repeat 100% 100%;
        }

        &.excel {
          background: url('./../images/200304/download/excel.png') no-repeat 100% 100%;
        }

        &.image {
          background: url('./../images/200304/download/image.png') no-repeat 100% 100%;
        }

        &.link {
          background: url('./../images/200304/download/link.png') no-repeat 100% 100%;
        }

        &.pdf {
          background: url('./../images/200304/download/pdf.png') no-repeat 100% 100%;
        }

        &.ppt {
          background: url('./../images/200304/download/ppt.png') no-repeat 100% 100%;
        }

        &.tar {
          background: url('./../images/200304/download/tar.png') no-repeat 100% 100%;
        }

        &.text {
          background: url('./../images/200304/download/text.png') no-repeat 100% 100%;
        }

        &.video {
          background: url('./../images/200304/download/video.png') no-repeat 100% 100%;
        }

        &.word {
          background: url('./../images/200304/download/word.png') no-repeat 100% 100%;
        }

        &.other {
          background: url('./../images/200304/download/other.png') no-repeat 100% 100%;
        }
      }

      .files_list_con {
        float: left;
        margin-left: 15px;
        height: 44px;
        line-height: 44px;

        .files_list_con_tit {
          height: 18px;
          line-height: 18px;

          em {
            color: #333;
            font-size: 18px;
            font-weight: normal;
            max-width: 700px;
            display: block;
            height: 22px;
            line-height: 22px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            float: left;
          }

          i {
            font-size: 12px;
            height: 18px;
            line-height: 18px;
            font-weight: normal;
            margin-left: 10px;
            max-width: 74px;
            padding: 2px 5px;
            text-align: center;
            background: #f7f7f7;
            color: #959595;
            border-radius: 10px;

            &.vip {
              width: 31px;
              height: 18px;
              background: #FFEAC9;
              border-radius: 3px;
              font-size: 11px;
              font-family: Arial;
              font-weight: 400;
              color: #8E6C32;
              line-height: 18px;
              margin-left: 9px;
              text-align: center;
            }
          }

          span {
            font-size: 12px;
            height: 18px;
            line-height: 18px;
            font-weight: normal;
            margin-left: 10px;
            max-width: 74px;
            padding: 0 5px;
            text-align: center;
            background: #f7f7f7;
            color: #959595;
            border: 1px solid #e5e5e5;
            border-radius: 10px;
            float: left;
            overflow: hidden;

          }
        }

        .files_list_con_tag {
          padding-top: 14px;
          height: 12px;
          line-height: 12px;

          span {
            color: #959595;
            font-size: 12px;
            padding-right: 18px;
            display: block;
            height: 12px;
            line-height: 12px;
            float: left;
            border-right: 1px solid #959595;
            padding-left: 18px;

            &:first-child {
              padding-left: 0;
            }

            &:last-child {
              border-right: 0;
              padding-right: 0;
            }
          }

          em {
            display: block;
            max-width: 420px;
            height: 12px;
            overflow: hidden;
            line-height: 12px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            float: left;

            span {
              &:first-child {
                padding-left: 18px;
                border-left: 1px solid #c2c2c2;

              }
            }
          }

          i {
            display: block;
            width: 10px;
            height: 16px;
            line-height: 16px;
            float: left;
            color: #c2c2cc;
          }
        }
      }

      .files_list_con_btn {
        float: right;

        label {
          display: block;
          height: 16px;
          line-height: 16px;

          #share,
          #download {
            cursor: pointer;

          }

          &:last-child {
            margin-top: 13px;
          }

          i {
            display: inline-block;
            width: 10px;
            height: 10px;

            &.share {
              background: url('./../images/200304/download/share_static.png') no-repeat 100% 100%;
            }

            &.download {
              background: url('./../images/200304/download/download_static.png') no-repeat 100% 100%;
            }
          }

          span,
          a {
            color: #333;
            font-size: 12px;
          }

          &:hover {
            i {
              &.share {
                background: url('./../images/200304/download/share_active.png') no-repeat 100% 100%;
              }

              &.download {
                background: url('./../images/200304/download/download_active.png') no-repeat 100% 100%;
              }
            }

            span,
            a {
              color: @mainColor;
            }
          }
        }
      }
    }

    h3 {
      text-align: center;
      height: 400px;
      line-height: 400px;
      font-weight: normal;
    }

    .files_list_option {
      margin-top: 20px;

      label {
        margin-right: 20px;
        cursor: pointer;
        height: 18px;
        line-height: 18px;
        float: left;

        &:hover {

          em,
          span,
          a {
            color: @mainColor;
          }

          i {
            &.files_list_option-download {
              background: url(./../images/200304/download/download_active.png) no-repeat 100% 100%;
            }

            &.files_list_option-share {
              background: url(./../images/200304/download/share_active.png) no-repeat 100% 100%;
            }
          }
        }

        i {
          margin-right: 5px;
          display: block;
          float: left;

          &.files_list_option-all {
            width: 18px;
            height: 18px;
            background: url(./../images/200304/download/checkbox_static.png) no-repeat 100% 100%;
          }

          &.files_list_option-download,
          &.files_list_option-share {
            width: 10px;
            height: 10px;
            margin-top: 4px;
          }

          &.files_list_option-download {
            background: url(./../images/200304/download/download_static.png) no-repeat 100% 100%;
          }

          &.files_list_option-share {
            background: url(./../images/200304/download/share_static.png) no-repeat 100% 100%;
          }
        }

        em {
          font-size: 14px;
          color: #333;
          display: block;
          height: 18px;
          line-height: 18px;
          float: left;
        }

        span,
        a {
          font-size: 12px;
          color: #333;
          float: left;
          display: block;
        }

        &.active {
          .files_list_option-all {
            background: url(./../images/200304/download/checkbox_active.png) no-repeat 100% 100%;
          }
        }
      }
    }
  }
}

//  home page
.n_position_box {
  position: relative;
  height: 412px;
  margin-bottom: 60px;
}

.homeCon8-body,
.homeCon9-body {
  padding-bottom: 10px;
}

.homeCon8-tab,
.homeCon9-tab {
  text-align: center;
  padding-bottom: 50px;

  span {
    border-radius: 50%;
    width: 8px;
    height: 8px;
    border: 1px solid @mainColor;
    display: inline-block;
    margin: 0 3px;
    cursor: pointer;

    &.active {
      background: @mainColor;
    }
  }
}

.homeCon9-tab {
  padding-bottom: 0 !important;
}

.homeCon9 {
  padding-bottom: 50px;

  .homeCon-head {
    height: 65px;
    line-height: 65px;

    a.icon,
    span {
      background: url('./../images/home/<USER>/title_car.png') no-repeat left center;
      padding-left: 50px;
    }
  }

  .onlineOrder_msg {
    height: 20px;
    line-height: 20px;
    width: 100%;
    overflow: hidden;
    position: relative;
    margin-bottom: 10px;

    ul {
      position: absolute;
      top: 0;
      left: 63px;
    }

    li {
      height: 20px;
    }

    span,
    a {
      font-size: 12px;
      padding-right: 15px;
      color: #6F6F6F;
    }

    a:hover {
      color: @mainColor;
    }

    .title {
      color: #ACACAC;
    }

    .price {
      color: #CA4300;
    }
  }
}

.homeCon10 {
  .homeCon-head {

    a,
    span {
      background: url('./../images/home/<USER>/title_oiq.png') no-repeat left center;
      padding-left: 50px;
    }
  }
}

.homeCon10 {
  .oiq-wrap {
    padding-bottom: 50px;

    .oiq-bg {
      float: left;
      width: 687px;
      height: 320px;
      position: relative;
      background-size: cover;
      cursor: pointer;

      a {
        position: absolute;
        bottom: 44px;
        left: 45px;
        width: 140px;
        height: 38px;
        line-height: 38px;
        background: rgba(0, 0, 0, 0);
        border: 1px solid #FFFFFF;
        border-radius: 19px;
        text-align: center;
        text-transform: uppercase;
        overflow: hidden;
        z-index: 5;

        span {
          font-size: 16px;
          color: #FFFFFF;
          display: block;
          float: left;
          margin-left: 27px;
        }

        i {
          display: block;
          width: 16px;
          height: 13px;
          background: url('./../images/home/<USER>/oiq_btn.png') no-repeat 100% 100%;
          float: left;
          margin-left: 10px;
          margin-top: 12px;
        }

        &:hover {
          background: @mainColor;
          color: #fff;
          border: 1px solid @mainColor;

          i {
            background: url('./../images/home/<USER>/oiq_btn_activity.png') no-repeat 100% 100%;
          }
        }

        &:before,
        &:after {
          z-index: -1;
          -webkit-transition: 0.2s;
          transition: 0.2s;
        }

        &:before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 80px;
          background: @mainColor;
        }

        &:hover:before {
          width: 140px;
        }

        &:active {
          background: @mainColor;
        }
      }
    }

    .oiq-plan {
      width: 524px;
      height: 320px;
      background: #FFFFFF;
      border: 3px solid #E5E5E5;
      box-sizing: border-box;
      float: right;
      padding: 40px;

      h3 {
        height: 22px;
        line-height: 22px;
        font-size: 22px;
        font-weight: bold;
        color: #333333;
      }

      .oiq_msg {
        height: 40px;
        width: 100%;
        overflow: hidden;
        margin-top: 10px;
        position: relative;

        ul {
          position: absolute;
          top: 0;
          left: 0;

          li {
            height: 40px;

            .time {
              font-size: 12px;
              font-weight: 400;
              color: #878787;
              line-height: 20px;
              height: 20px;
            }

            div * {
              font-size: 12px;
              padding-right: 3px;
              height: 20px;
              line-height: 20px;
            }

            b {
              color: #333;

              &.lastCategory {
                max-width: 123px;
                overflow: hidden;
                height: 14px;
              }
            }

            span {
              color: #878787;
            }
          }
        }
      }


      .oiq-search {
        margin-top: 20px;

        a {
          display: block;
          width: 424px;
          height: 45px;
          position: relative;

          &:hover {
            p {
              border: 1px solid @mainColor;
              background: #fff;
            }
          }
        }

        p {
          width: 424px;
          height: 45px;
          line-height: 45px;
          background: #F6F6F6;
          border-radius: 4px;
          font-size: 14px;
          color: #999;
          outline: 0;
          border: 1px solid #fff;
          padding-left: 12px;
          cursor: pointer;
        }

        i {
          width: 53px;
          height: 45px;
          border-radius: 4px;
          background: url('./../images/home/<USER>/oiq_search.png') no-repeat center center;
          display: block;
          position: absolute;
          top: 0;
          right: -10px;
          z-index: 2;
        }
      }

      .oiq-category {
        margin-top: 20px;
        width: 100%;
        height: 20px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;

        a,
        span {
          color: @mainColor;
          font-size: 14px;
          padding-right: 3px;
          height: 20px;
          line-height: 20px;
        }
      }

      .oiq_start {
        margin-top: 20px;
        width: 140px;
        text-align: center;
        height: 42px;
        line-height: 42px;
        background: @mainColor;
        font-size: 16px;
        color: #fff;
        display: block;
        position: relative;

        .oiq_start_text {
          color: #fff;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 2;
          width: 100%;
          text-align: center;
          font-size: 14px;
        }

        .oiq_start_layout {
          width: 0;
          height: 100%;
          background: #cc5500;
          position: absolute;
          top: 0;
          left: 0;
        }

        &:hover .oiq_start_layout {
          width: 100%;
          transition: 0.4s;
        }
      }
    }
  }
}

.n_nav {
  background: #404040;
  height: 39px;
  width: 100%;

  ul {
    width: 1226px;
    margin: 0 auto;
    height: 39px;
    padding: 0;
  }

  li {
    float: left;
    height: 39px;
    line-height: 39px;
    text-align: center;
    position: relative;
    width: 146px;

    &:hover::after {
      transform: scaleX(1);
    }

    &::after {
      position: absolute;
      content: "";
      top: 36px;
      left: 0;
      width: 100%;
      height: 3px;
      background: @mainColor;
      transform: scaleX(0);
      transition: 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    &.active::after {
      transform: scaleX(1);
    }

    a {
      color: #fff;
      font-size: 15px;
      width: 145px;
      display: block;
      float: left;
      display: flex;
      justify-content: center;
      align-items: center;

      // &:hover {
      //   color: @mainColor;
      // }
    }

    em {
      display: inline-block;
      padding-right: 10px;
    }

    i {
      display: inline-block;
      width: 10px;
      height: 6px;
      background: url('./../images/home/<USER>/arrow-down-static.png') no-repeat 100% 100%;
    }

    i.n_nav_hot {
      display: inline-block;
      width: 21px;
      height: 12px;
      background: url('./../images/home/<USER>/nav_hot.png') no-repeat 100% 100%;
      position: absolute;
      top: 6px;
      right: 30px;
    }

    .hot {
      position: absolute;
      width: 25px;
      height: 18px;
      right: 35px;
      top: 4px;
      background: url('./../images/home/<USER>') no-repeat 100% 100%;
    }
    .new {
      position: absolute;
      width: 20px;
      height: 15px;
      right: 20px;
      top: 6px;
      background: url('./../images/home/<USER>') no-repeat 100% 100%;
    }

    span {
      float: left;
      background: rgba(191, 191, 191, .2);
      height: 15px;
      width: 1px;
      margin-top: 12px;
    }

    &:hover:nth-child(3),
    &:hover:nth-child(4) {
      i {
        background: url('./../images/home/<USER>/arrow-up-static.png') no-repeat 100% 100%;
      }
    }

    &:hover:nth-child(2),
    &:hover:nth-child(1) {
      i {
        background: url('./../images/home/<USER>/arrow-down-active.png') no-repeat 100% 100%;
      }
    }

    &.active {
      background: @mainColor;

      a,
      a:hover {
        color: #fff;
      }

      span {
        background: @mainColor;
      }

      i {
        background: url('./../images/home/<USER>/arrow-up-static.png') no-repeat 100% 100%;
      }

      &:hover {
        i {
          background: url('./../images/home/<USER>/arrow-up-static.png') no-repeat 100% 100%;
        }
      }
    }
  }

  dl {
    background: #404040;
    width: 146px;
    position: absolute;
    top: 39px;
    border-top: 1px solid @mainColor;
    z-index: 2;
    display: none;

    a {
      width: 146px;
      color: #fff;

      &:hover {
        color: @mainColor;
        background: #fff;
        font-weight: bold;
      }
    }
  }
}

.n_banner {
  height: 412px;
  width: 736px;
  position: absolute;
  left: 245px;
  top: 0;

  .banner,
  .swiper-wrapper,
  .swiper-container {
    height: 412px;
  }

  .swiper-button-prev,
  .swiper-button-next {
    width: 50px;
    height: 50px;
    top: 181px;
    position: absolute;
  }

  .swiper-button-prev {
    background: url('./../../public/images/newPage/prev.png') 100% 100% no-repeat;
    left: 10px;
  }

  .swiper-button-next {
    background: url('./../../public/images/newPage/next.png') 100% 100% no-repeat;
    right: 10px;
  }

  .bannerTitle {
    top: 100px;
  }

  .bannerMore {
    left: 293px;
    bottom: 57px;
  }

  .bannermoreword {
    bottom: 90px;
  }

  .swiper-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 99;
    width: 100%;
    text-align: center;
    height: 35px;
  }

  .swiper-pagination {
    display: flex;
    width: 100%;
  }

  .swiper-pagination-bullet {
    flex: 1;
    height: 35px;
    line-height: 35px;
    border-radius: 0;
    margin-right: 1px;
    font-size: 12px;
    background: rgba(0, 0, 0, .3);
    color: #fff;
    opacity: 1;

    &:last-child {
      margin-right: 0;
    }
  }

  .swiper-pagination-bullet:hover {
    color: #fff;
  }

  .swiper-pagination-bullet-active {
    color: #fff;
    background: rgba(0, 0, 0, .5);
  }

  .swiper-button-next.swiper-button-disabled,
  .swiper-button-prev.swiper-button-disabled {
    opacity: 0;
  }
}

.home_side {
  height: 412px;
  width: 245px;
  position: absolute;
  right: 0;
  top: 0;
  background: #fff;

  .welcome_info {
    width: 200px;
    float: left;
    padding: 15px 20px 10px 20px;

    .welcome_info_portrait {
      width: 55px;
      height: 55px;
      background: url('./../images/home/<USER>/portrait.png') no-repeat 100% 100%;
      float: left;
    }

    .welcome_info_tips {
      float: left;
      white-space: nowrap;

      p {
        color: #333;
        font-size: 16px;
        margin-bottom: 5px;
        margin-top: 4px;
        max-width: 112px;
        height: 23px;
        line-height: 23px;
        background: url('./../images/home/<USER>/home_side_portrait.png') no-repeat left center;
        font-weight: bold;
        padding-left: 30px;
        overflow: hidden;
        display: inline-block;
      }

      em {
        color: #929299;
        font-size: 12px;
        background: url('./../images/home/<USER>/icon-1.png') no-repeat 0 2px;
        padding-left: 25px;
        height: 17px;
        line-height: 17px;
        display: block;
      }
    }
  }

  .login_info {
    clear: both;
    width: 200px;
    padding: 5px 20px;
    height: 78px;

    a {
      width: 100%;
      height: 35px;
      line-height: 35px;
      display: block;
      text-align: center;
      background: @mainColor;
      position: relative;
      border-radius: 3px;

      &.login_info_static {
        margin-top: 9px;
        background: #f8f8f8;
        border: 1px solid #bfbfbf;
        color: #333;
        font-size: 14px;
        box-sizing: border-box;
      }

      &.login_info_active:hover {
        .login_info_active_layout {
          width: 100%;
          transition: .4s;
        }
      }

      &.login_info_static:hover {
        .login_info_static_layout {
          width: 100%;
          transition: .4s;
        }
      }

      .login_info_active_text {
        color: #fff;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        width: 100%;
        text-align: center;
        font-size: 14px;
      }

      .login_info_static_text {
        color: #333;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        width: 100%;
        text-align: center;
      }

      .login_info_active_layout {
        width: 0;
        height: 100%;
        background: #cc5500;
        position: absolute;
        top: 0;
        left: 0;
      }

      .login_info_static_layout {
        width: 0;
        height: 100%;
        background: #efefef;
        position: absolute;
        top: 0;
        left: 0;
      }
    }
  }

  .static_items {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #E5E5E5;
    padding: 15px 0;

    span {
      font-size: 12px;
      color: #878787;
      display: inline-block;
      padding-left: 12px;
      background: url('./../images/home/<USER>/checked.png') no-repeat left center;
    }
  }

  .account_info {
    clear: both;
    width: 204px;
    // padding: 0 20px;
    height: 87px;
    line-height: 87px;
    border-bottom: 1px solid #e5e5e5;
    margin: 0 auto;

    a,
    label {
      display: block;
      width: 33%;
      text-align: center;
      float: left;
      cursor: pointer;
    }

    em,
    span {
      display: block;
      text-align: center;
      height: 30px;
      line-height: 30px;
    }

    em {
      font-size: 24px;
      color: @mainColor;
      padding-top: 15px;
    }

    span {
      font-size: 14px;
      color: #333;
    }
  }

  .shortcut_menu {
    clear: both;
    padding: 12px 20px 8px 20px;
    width: 204px;
    float: left;
    height: 70px;

    a {
      float: left;
      margin-right: 21px;
      width: 53px;
      text-align: center;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;

      &:last-child {
        margin-right: 0;
      }

      i {
        display: inline-block;
        width: 42px;
        height: 39px;

        &.shortcut_menu_report {
          background: url('./../images/home/<USER>/report_static.svg') center center no-repeat;
        }

        &.shortcut_menu_step {
          background: url('./../images/home/<USER>/car_static.png') center center no-repeat;
        }

        &.shortcut_menu_inquiry {
          background: url('./../images/home/<USER>/inquiry_static.png') center center no-repeat;
        }
      }

      span {
        color: #333;
        font-size: 13px;
        padding-top: 5px;
      }

      label {
        width: 11px;
        height: 12px;
        background: url('./../images/home/<USER>/home_side_hot.png') no-repeat;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 2;
      }

      &:hover {
        span {
          color: @mainColor;
        }

        .shortcut_menu_report {
          background: url('./../images/home/<USER>/report_active.svg') center center no-repeat;
        }

        .shortcut_menu_step {
          background: url('./../images/home/<USER>/car_active.png') center center no-repeat;
        }

        .shortcut_menu_inquiry {
          background: url('./../images/home/<USER>/inquiry_active.png') center center no-repeat;
        }
      }
    }
  }

  .informaition_info {
    clear: both;
    width: 204px;
    padding: 0 20px;
    height: 131px;

    .informaition_info_tit {
      border-top: 1px solid #e5e5e5;
      width: 100%;
      padding: 16px 0 10px 0;
      float: left;
      clear: both;
      height: 17px;
      line-height: 17px;

      span {
        color: #333;
        border-bottom: 1px solid #fff;
        color: #333;
        font-size: 12px;
        float: left;
        margin-right: 15px;
        cursor: pointer;

        &.active {
          color: @mainColor;
          border-bottom: 1px solid @mainColor;
        }
      }

      a {
        float: right;
        color: #969696;
        font-size: 12px;
        border-bottom: 1px solid #fff;
        margin: 0;

        // &:last-child {
        //   color: #969696;
        //   float: right;
        //   margin: 0;
        // }

        &:hover {
          color: @mainColor;
          border-bottom: 1px solid @mainColor;
        }
      }
    }

    .informaition_info_list {
      height: 100px;
      overflow: hidden;
      width: 209px;
      margin-left: -5px;
      position: relative;

      a {
        background: url('./../images/home/<USER>/dot.png') 0 center no-repeat;
        padding-left: 7px;
        max-width: 200px;
        margin-left: 5px;
      }

      ul {
        position: absolute;
      }

      li {
        width: 205px;
        overflow: hidden;
        height: 20px;
        line-height: 20px;

        a,
        span {
          height: 20px;
          line-height: 20px;
          display: block;
          float: left;
        }

        a {
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &:hover {
            color: @mainColor;
          }
        }

        span {
          color: @mainColor;
          font-size: 12px;
        }

        a {
          color: #929292;
          font-size: 12px;
        }
      }
    }
  }
}

.homeCon-head {
  height: 86px;
  line-height: 86px;
  border-top: 2px solid #dcdcdc;

  ul {
    float: right;
    margin-right: 10px;
    padding-top: 27px;
    position: relative;

    li {
      float: left;
      height: 30px;
      line-height: 30px;
      color: #333;
      cursor: pointer;
      width: 94px;
      text-align: center;
      font-size: 16px;

      &.active {
        background: @mainColor;
        color: #fff;
      }

      &:hover {
        color: #fff;
      }

      &.homeCon-head-bg {
        background: @mainColor;
        height: 30px;
        width: 84px;
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: -1;
      }
    }
  }

  span,
  a {
    float: left;
    font-size: 18px;
    color: #333;
  }

  a.more {
    float: right;
    color: #8b8b8b;
    font-size: 16px;
    display: none;
    padding-top: 34px;
    width: 50px;

    &.active {
      display: block;
    }

    &:hover i {
      background: url('../images/newPage/more-a.png') no-repeat 100% 100%;
    }

    em {
      font-style: normal;
      display: block;
      height: 16px;
      line-height: 16px;
      float: left;
    }

    i {
      float: left;
      display: block;
      width: 16px;
      height: 16px;
      background: url('../images/newPage/more.png') no-repeat 100% 100%;
    }
  }

  a {
    &:hover {
      color: @mainColor;
    }
  }
}

.homeCon6 {
  padding-bottom: 70px;
}

.homeCon6-body .homeCon6-item {
  float: left;
  width: 395px;
  margin-right: 20px;
}

.homeCon6-body .homeCon6-item:last-child {
  margin-right: 0;
}

.homeCon6-item-bg {
  height: 200px;
  background: #ddd;
  width: 100%;
  text-align: center;

  a {
    display: block;
    width: 100%;
    height: 100%;
  }
}

.homeCon6-item-bg span {
  color: #fff;
  font-size: 24px;
  display: block;
  height: 25px;
  line-height: 25px;
  text-align: center;
  padding-top: 85px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.homeCon6-item-bg i {
  display: inline-block;
  height: 3px;
  width: 80px;
  background: #fff;
}

.homeCon6-item-list {
  position: relative;
  height: 175px;
  background: #fff;
}

.homeCon6-item-line {
  position: absolute;
  z-index: 1;
  width: 345px;
  padding: 35px 25px 0 25px;
}

.homeCon6-item-line div {
  width: 100%;
  height: 30px;
  line-height: 30px;
  border-bottom: 1px dashed #ddd;
}


.homeCon6-item-list ul {
  padding: 38px 25px 0 25px;
  position: absolute;
  z-index: 2;
  width: 345px;
}

.homeCon6-item-list li {
  float: left;
  padding-right: 20px;
  height: 30px;
  line-height: 30px;
}

.homeCon6-item-list.homeCon6-li li {
  width: 33.33%;
  padding-right: 0;
}

.homeCon6-item-list.homeCon6-li a.more {
  right: 57px;
}

.homeCon6-item-list li a {
  color: #666;
  font-size: 14px;
  padding-left: 10px;
  display: block;
  width: 100%;
  height: 30px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.homeCon6-item-list li a:hover {
  color: @mainColor;
}

.homeCon6-item-list .more {
  color: #fff;
  background: @mainColor;
  display: inline-block;
  width: 72px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  position: absolute;
  right: 40px;
  top: 100px;
  font-size: 13px;
  border-radius: 22px;
  z-index: 2;
}

.homeCon6-item-list .more:hover {
  background: #cc5500;
  transform: .5s;
}

.homeCon7 {
  .homeCon7-body {
    height: 400px;
    padding-bottom: 70px;

    li {
      float: left;
      width: 395px;
      height: 400px;
      margin-right: 20px;
      position: relative;

      &:last-child {
        margin-right: 0;
      }

      &.active {
        // .homeCon7-mark {
        //   display: block;
        // }

        dl {
          display: block;
        }
      }
    }
  }

  .homeCon7-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 395px;
    height: 400px;
    z-index: 1;
    background: #ddd;
    background-size: cover;
  }

  .homeCon7-mark {
    // background: rgba(0, 0, 0, .5);
    background: #000;
    position: absolute;
    top: 0;
    left: 0;
    width: 395px;
    height: 400px;
    z-index: 2;
    opacity: 0;
  }

  .homeCon7-con {
    position: absolute;
    top: 0;
    left: 0;
    width: 335px;
    height: 340px;
    z-index: 3;
    padding: 30px;
    // transition-delay: 2s;
    // -moz-transition-delay: 2s;
    // -webkit-transition-delay: 2s;
    // -o-transition-delay: 2s;

    &:hover {
      dl {
        a {
          transform: translateY(0px);
          transition: all 0.8s;
          opacity: 1;
        }
      }
    }

    h2 {
      color: #fff;
      padding: 5px 0;
      font-size: 24px;
    }

    p {
      height: 48px;
      line-height: 24px;
      color: #fff;
      font-size: 15px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      width: 100%;
    }

    dl {
      position: absolute;
      top: 230px;
      // top: 370px;
      left: 30px;
      bottom: 30px;
      transition: all 0.25s;
      overflow: hidden;

      dd {
        height: 30px;
        line-height: 30px;
        _overflow: hidden;
        overflow: visible;

        a {
          font-size: 14px;
          text-decoration: none;
          color: #eee;
          display: block;
          transform: translateY(100px);
          opacity: 0;
        }
      }
    }

    a {
      color: #fff;
      display: block;
      height: 30px;
      line-height: 30px;
      width: 335px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      &:hover {
        color: @mainColor;
      }
    }
  }
}

.homeCon8-item,
.homeCon9-item {
  display: none;

  &.active {
    display: block;
  }
}

.homeCon8-body {
  .side {
    float: left;
    width: 305px;
    height: 406px;
    border-right: 2px solid #f9f9f9;
    padding: 20px 25px;
    box-sizing: border-box;
    position: relative;

    *:before,
    *:after {
      z-index: -1;
      -webkit-transition: 0.2s;
      transition: 0.2s;
    }

    img {
      position: absolute;
      z-index: 1;
      width: 305px;
      height: 406px;
      top: 0;
      left: 0;
    }

    a {
      text-transform: uppercase;
      overflow: hidden;
      z-index: 5;
      width: 112px;
      height: 38px;
      line-height: 38px;
      text-align: center;
      position: absolute;
      bottom: 50px;
      left: 95px;
      border: 1px solid #fff;
      color: #fff;
      font-size: 16px;
      font-style: normal;
      z-index: 2;

      &:hover {
        border: 1px solid @mainColor;
      }
    }

    a:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 80px;
      background: @mainColor;
    }

    a:hover:before {
      width: 112px;
    }

    a:active {
      background: @mainColor;
    }
  }

  .list {
    float: right;
    width: 919px;
    background: #fff;

    h2 {
      font-size: 16px;
      font-weight: normal;
      color: #333;
      width: 255px;

      span {
        display: block;
        float: left;
        height: 20px;
        line-height: 20px;
        max-width: 255px;
        overflow: hidden;
        font-size: 16px;
        color: #333;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.active {
          max-width: 203px;
        }
      }

      i {
        margin-left: 5px;
        display: block;
        float: left;
        width: 47px;
        height: 19px;
        background: url('./../images/newPage/isBuy.png') no-repeat 100% 100%;
        display: none;
      }
    }

    p {
      font-size: 12px;
      color: #b4b4b4;
      width: 255px;
      height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-top: 5px;
    }

    img {
      width: 255px;
      height: 100px;
      background: #ddd;
      display: block;
      margin-top: 11px;
      overflow: hidden;
    }

    li {
      float: left;
      width: 255px;
      border-right: 2px solid #f9f9f9;
      padding: 23px 25px;
      border-bottom: 2px solid #f9f9f9;

      &:nth-child(3n) {
        border-right: 0;
      }

      &:nth-child(n+4) {
        border-bottom: 0;
      }

      &:hover {
        h2 span {
          color: @mainColor;
        }

        p {
          color: #333;
        }
      }
    }
  }
}

.homeCon9-body {
  .hot {
    width: 30px;
    text-align: center;
    height: 18px;
    line-height: 18px;
    background: @mainColor;
    display: block;
    color: #fff;
    font-size: 10px;
    border-radius: 3px;
  }

  .side {
    float: left;
    width: 305px;
    height: 406px;
    border-right: 2px solid #f9f9f9;
    padding: 20px 19px;
    box-sizing: border-box;
    position: relative;
    background: #fff;

    h3 {
      font-size: 16px;
      color: #333333;
    }

    li {
      height: 100px;
      width: 266px;
      position: relative;
      background: #F8F8F8;
      margin-top: 16px;
      padding: 19px 0 0 26px;
      box-sizing: border-box;
      background-size: cover;

      &:nth-child(2) {
        label {
          color: #FF6600;
        }
      }

      &:nth-child(3) {
        label {
          color: #FF9000;
        }
      }

      label {
        font-style: italic;
        font-size: 20px;
        font-weight: bold;
        color: @mainColor;
        position: absolute;
        top: 17px;
        left: 5px;
      }

      h2 {
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 190px;
        height: 20px;
        line-height: 20px;
      }

      .sub-title {
        font-size: 12px;
        color: #878787;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 190px;
        height: 20px;
        line-height: 20px;
        margin: 5px 0 9px 0;
      }

      .num {
        span {
          font-size: 12px;
          color: #CA4300;
          padding-right: 12px;

          i {
            font-size: 14px;
            padding-right: 5px;
          }
        }

        em {
          font-size: 12px;
          color: #B0B0B0;
        }
      }
    }

    // span {
    //   position: absolute;
    //   left: -3px;
    //   top: -3px;
    //   z-index: 2;
    // }

    // *:before,
    // *:after {
    //   z-index: -1;
    //   -webkit-transition: 0.2s;
    //   transition: 0.2s;
    // }

    // img {
    //   position: absolute;
    //   z-index: 1;
    //   width: 305px;
    //   height: 406px;
    //   top: 0;
    //   left: 0;
    // }

    // a {
    //   text-transform: uppercase;
    //   overflow: hidden;
    //   z-index: 5;
    //   width: 112px;
    //   height: 38px;
    //   line-height: 38px;
    //   text-align: center;
    //   position: absolute;
    //   bottom: 50px;
    //   left: 95px;
    //   border: 1px solid #fff;
    //   color: #fff;
    //   font-size: 16px;
    //   font-style: normal;
    //   z-index: 2;

    //   &:hover {
    //     border: 1px solid @mainColor;
    //   }
    // }

    // a:before {
    //   content: '';
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   width: 0;
    //   height: 80px;
    //   background: @mainColor;
    // }

    // a:hover:before {
    //   width: 112px;
    // }

    // a:active {
    //   background: @mainColor;
    // }
  }

  .list {
    float: right;
    width: 919px;
    background: #fff;

    h2 {
      font-size: 16px;
      font-weight: normal;
      color: #333;
      width: 255px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      word-wrap: break-word;
      white-space: normal;
      word-break: break-all;

      span {
        display: block;
        float: left;
        height: 20px;
        line-height: 20px;
        max-width: 255px;
        overflow: hidden;
        font-size: 16px;
        color: #333;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.active {
          max-width: 203px;
        }
      }

      i {
        margin-left: 5px;
        display: block;
        float: left;
        width: 47px;
        height: 19px;
        background: url('./../images/newPage/isBuy.png') no-repeat 100% 100%;
      }
    }

    li {
      float: left;
      width: 255px;
      border-right: 2px solid #f9f9f9;
      padding: 23px 25px 20px 25px;
      border-bottom: 2px solid #f9f9f9;
      height: 158px;

      &:nth-child(3n) {
        border-right: 0;
      }

      &:nth-child(n+4) {
        border-bottom: 0;
      }

      h2 {
        padding: 8px 0 5px 0;
        height: 16px;
        line-height: 16px;
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: flex;
        justify-content: space-between;

        span {
          display: inline-block;
          text-align: left;
          font-size: 16px;
          color: #333333;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 190px;
          height: 20px;
        }

        em {
          display: inline-block;
          text-align: right;
          font-size: 13px;
          color: #B0B0B0;
        }
      }

      h3 {
        font-size: 12px;
        color: #B4B4B4;
        line-height: 24px;
        height: 24px;
        font-weight: 400;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: flex;
        justify-content: space-between;

        span {
          display: inline-block;
          text-align: left;
          font-size: 12px;
          color: #878787;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 190px;
          height: 20px;
        }

        em {
          display: inline-block;
          text-align: right;
          font-size: 12px;
          color: @mainColor;

          i {
            font-size: 14px;
            padding-right: 5px;
          }
        }
      }

      .img {
        position: relative;

        label {
          position: absolute;
          right: 10px;
          top: 10px;
          z-index: 2;
        }

        img {
          width: 255px;
          height: 100px;
          display: block;
          margin-top: 11px;
          overflow: hidden;
          background: #999;
        }

        p {
          position: absolute;
          z-index: 2;
          bottom: 10px;
          right: 10px;
          color: #fff;
          font-family: Arial;

          b {
            font-size: 15px;
            padding: 0 3px;
          }

          i,
          span {
            font-size: 12px;
          }
        }
      }
    }
  }
}

//  home page

// 内页
.n_position_box_inner {
  .main  {
    display: block;
  }
  .n_category {
    z-index: 2;
    background: #fff;
  }
}

// footer
.helpinfo {
  width: 100%;
  min-width: 1226px;
  padding: 61px 0 30px;
  box-sizing: border-box;
  border-bottom: 1px solid #282828;
  background: #111111;
  margin: 0 auto;
  line-height: 1;

  &:after {
    content: "\0020";
    display: block;
    visibility: hidden;
    clear: both;
    height: 0;
    overflow: hidden;
  }

  .helpinfo-item {
    float: left;
    width: 165px;

    .hotlineText,
    .highlight {
      color: @mainColor;
    }

    &.helpinfo-email {
      width: 224px;
      padding-right: 20px;
    }

    li {
      margin-bottom: 17px;
      color: #aaa;
      font-size: 16px;
    }
  }

  .helpinfo-title {
    color: #fff;
    font-size: 16px;
    margin: 0;
    margin-bottom: 30px;
  }

  ul {

    a,
    span,
    em,
    label {
      color: #858585;
      font-size: 14px;
    }

    a:hover {
      color: @mainColor;
    }

    label {
      display: inline-block;
      position: relative;
      top: -21px;
    }

    em {
      display: inline-block;
      line-height: 22px;
      width: 157px;
      height: 44px;
    }
  }

  .QRcode {
    float: right;
    border-left: 1px solid #333;
    width: 268px;
    padding-left: 52px;

    .QRcode_img {
      float: left;
      margin-top: 18px;
      margin-right: 35px;

      &.m-r {
        margin-right: 0;
      }

      img {
        width: 114px;
        height: 114px;
      }

      span {
        padding-top: 18px;
        width: 114px;
        text-align: center;
        font-size: 14px;
        color: #858585;
        display: block;
      }
    }
  }

  .helpinfo_seo {
    padding-top: 35px;
    font-size: 14px;

    * {
      color: #858585;
    }

    a:hover {
      color: @mainColor;
    }
  }
}

.footer {
  background: #111111;
  padding: 23px 0 57px 0;

  a,
  span {
    font-size: 14px;
    color: #464646;
  }

  a {
    padding-left: 34px;

    img {
      height: 13px;
    }

    &:focus,
    &:active,
    &:visited,
    &:focus-within,
    &:focus-visible,
    &:target {
      color: #464646;
    }

    &:hover,
    &:hover span {
      color: @mainColor;
    }
  }
}

// footer

// category page
#categoryDetail {
  .swiper-bottom {
    position: absolute;
    bottom: 0;
    left: 45.5%;
  }

  .swiper-pagination {
    position: relative;
    display: inline-block;
    top: -5px;

    .swiper-pagination-bullet {
      display: inline-block;
      width: 10px;
      height: 10px;
      border: 1px solid @mainColor;
      border-radius: 50%;
      background: transparent;
      opacity: 1;
      margin: 0 4px;

      &.swiper-pagination-bullet-active {
        background: @mainColor;
      }
    }
  }

  .swiper-button-prev {
    position: relative;
    width: 12px;
    height: 24px;
    overflow: hidden;
    right: auto;
    top: auto;
    left: auto;
    display: inline-block;
    background-size: auto 60%;
    margin-top: 0;
  }

  .swiper-button-next {
    position: relative;
    width: 12px;
    height: 24px;
    overflow: hidden;
    right: auto;
    top: auto;
    left: auto;
    display: inline-block;
    background-size: auto 60%;
    margin-top: 0;
  }
}

.cooperation {
  width: 100%;
  height: auto;
  background: #fafafa;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  padding-bottom: 20px;

  .pages {
    bottom: 25px;
  }
}

.cooperationBox {
  width: 1226px;
  margin: 0 auto;
  position: relative;
}

.cooperationDemo {
  margin-top: 25px;
  display: none;

  ul {
    max-height: unit(35*4, px);
    height: auto;
    overflow: hidden;

    li {
      float: left;
      width: 590px;
      margin-right: 46px;
      border-bottom: 1px dashed #d8d8d8;

      &:nth-child(2n) {
        margin-right: 0;
      }

      i,
      a,
      time {
        display: block;
        height: 34px;
        line-height: 34px;
        float: left;
      }

      i {
        width: 25px;
        background: url('./../images/category/v2/dot.png') no-repeat center center;

        &.new {
          background: url('./../images/category/v2/new.png') no-repeat center left;
        }
      }

      a {
        width: 480px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 16px;
        color: #333;
        padding-left: 5px;

        &:hover {
          color: @mainColor;
        }
      }

      time {
        width: 65px;
        color: #c2c2c2;
        font-size: 12px;
        float: right;
      }
    }
  }

  .cooperationDemo_slide {
    text-align: center;
    margin-top: 20px;

    span {
      width: 28px;
      height: 28px;
      background: url('./../images/category/v2/arrow-down.png') no-repeat 100% 100%;
      display: inline-block;
      cursor: pointer;

      &.active {
        background: url('./../images/category/v2/arrow-up.png') no-repeat 100% 100%;
      }
    }
  }
}

.resources {
  width: 1226px;
  height: auto;
  margin: 0 auto;
  padding-bottom: 10px;

  .serverBody {
    display: none;

    ul {
      max-height: unit(35*4, px);
      height: auto;
      overflow: hidden;

      li {
        width: 408px;
        float: left;

        a {
          padding-left: 25px;
          width: 365px;
          height: 35px;
          line-height: 35px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          display: block;
          color: #333;
          font-size: 16px;

          &:hover {
            color: @mainColor;
          }

          &.excel {
            background: url('./../images/category/v2/file-excel.png') center left no-repeat;
          }

          &.pdf {
            background: url('./../images/category/v2/file-pdf.png') center left no-repeat;
          }

          &.url {
            background: url('./../images/category/v2/file-url.png') center left no-repeat;
          }

          &.video {
            background: url('./../images/category/v2/file-video.png') center left no-repeat;
          }

          &.word {
            background: url('./../images/category/v2/file-word.png') center left no-repeat;
          }

          &.zip {
            background: url('./../images/category/v2/file-zip.png') center left no-repeat;
          }

          &.img {
            background: url('./../images/category/v2/file-img.png') center left no-repeat;
          }

          &.ppt {
            background: url('./../images/category/v2/file-ppt.png') center left no-repeat;
          }

          &.other {
            background: url('./../images/category/v2/file-other.png') center left no-repeat;
          }
        }
      }
    }

    .serverBody_slide {
      text-align: center;
      margin-top: 20px;

      span {
        width: 28px;
        height: 28px;
        background: url('./../images/category/v2/arrow-down.png') no-repeat 100% 100%;
        display: inline-block;
        cursor: pointer;

        &.active {
          background: url('./../images/category/v2/arrow-up.png') no-repeat 100% 100%;
        }
      }
    }
  }
}

#case,
#files {
  .serverHead {
    .serverLi {
      padding: 0 18px;
      min-width: 100px;

      a {
        position: absolute;
        right: 0;
        display: none;
        top: 0;
      }
    }
  }
}

#case,
#files {
  .serverHead {
    .serverLi.demo {
      a {
        display: block;
      }
    }
  }
}

.serverLi {
  width: auto;
  height: 74px;
  text-align: center;
  line-height: 80px;
  font-size: 16px;
  color: rgb(51, 51, 51);
  float: left;
  cursor: pointer;
  list-style: none;
  text-align: center;
  transition: border-bottom-color 0.4s;

  a {
    color: #333;
    padding: 0 18px;
    display: inline-block;
    min-width: 100px;
  }

  &.solu {
    border-bottom: 3px solid @mainColor;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    color: #333333;
    font-weight: bold;
  }

  &.demo {
    border-bottom: 3px solid @mainColor;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    color: #333333;
    font-weight: bold;

    a {
      display: inline-block;
      font-weight: normal;
      color: @mainColor;
      font-size: 16px;
      padding-right: 10px;

      &:hover {
        font-weight: bold;
      }

      i {
        // display: inline-block;
        // background: url('./../images/arrow.png') no-repeat -15px 100%;
        // width: 20px;
        // height: 20px;
        // margin: 28px 0 0 5px;
        // float: right;
        font-size: 0;
        line-height: 0;
        border-width: 5px;
        border-color: @mainColor;
        border-right-width: 0;
        border-style: dashed;
        border-left-style: solid;
        border-top-color: transparent;
        border-bottom-color: transparent;
        position: absolute;
        right: 0;
        top: 35px;
      }
    }


  }

  &.reletive {
    border-bottom: 3px solid @mainColor;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    color: #333333;
    font-weight: bold;
  }
}

.serverLiActive {
  border-bottom: 3px solid @mainColor;
  font-style: normal;
  font-stretch: normal;
  letter-spacing: normal;
  color: @mainColor;
  font-weight: bold;

  a {
    color: @mainColor;
  }
}

// category page


// search page
.search_list {
  li {
    border-bottom: 1px solid #eeeeee;
    padding: 10px 0;
    clear: both;

    h2 {
      font-weight: normal;
      margin: 10px 0;

      a {
        font-size: 16px;
        color: #000;
        margin-bottom: 20px;
        cursor: pointer;

        &:hover {
          color: @mainColor;
        }
      }

      span {
        font-size: 12px;
        color: #eee;
        display: inline-block;
        height: 14px;
        line-height: 14px;
        margin-left: 5px;
        background: #888;
        padding: 3px;

        &.active {
          background: @mainColor;
          color: #fff;
        }
      }
    }

    p {
      color: #999;
      font-size: 14px;
    }

    .search_list_down {
      a {
        font-size: 14px;
        color: #000;
        padding-left: 25px;
        background: url('./../images/down.png') center left no-repeat;
        height: 35px;
        line-height: 35px;
        display: inline-block;
      }
    }
  }

  .search_empty_list {
    text-align: center;
    height: 400px;
    line-height: 400px;
  }
}

// search page


// ticket page
.ticket {
  .messageCon {
    background: #fafafa;
    margin-top: 21px;
    margin-bottom: 20px;
    position: relative;

    .messageConT {
      padding: 10px 0;
      font-size: 32px;
      text-align: center;
      font-weight: 400;
    }

    .messageConS {
      text-align: center;
      font-size: 16px;
      color: #000000;
      padding-bottom: 20px;
    }

    .messageCon_switch {
      position: absolute;
      right: 20px;
      top: 20px;

      a {
        display: inline-block;
        width: 35px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        font-size: 14px;
        color: #666;

        &.active {
          color: #fff;
          background: @mainColor;
        }
      }
    }
  }

  .ticket_accept {
    padding-bottom: 100px;

    .accept {
      height: 36px;
    }

    .acceptIcon {
      margin-left: 135px;
    }

    .submit {
      width: 216px;
      margin-left: 310px;
    }
  }

  textarea {
    font-family: Arial;
  }
}

.yoursInfo {
  height: auto;

  .yoursT {
    height: 44px;
    background: #fafafa;
    text-align: center;
    line-height: 44px;
    margin-bottom: 44px;
    font-size: 16px;
  }

  .yoursInfoC {
    width: 905px;

    .yoursInfoCC {
      float: left;
      width: 400px;
      margin-right: 50px;
      display: block;
      position: relative;

      .selectC {
        width: 400px;
        padding: 5px 20px;
        box-sizing: border-box;
        outline: none;
        color: #000000;
        font-size: 14px;
      }

      .selectC1 {
        width: 400px;
        padding: 5px 20px;
        box-sizing: border-box;
        outline: none;
        color: #000000;
      }

      button {
        cursor: pointer;
        position: absolute;
        top: 38px;
        right: 2px;
        z-index: 2;
        height: 37px;
        line-height: 38px;
        border: 0;
        background: @mainColor;
        border-radius: 0 5px 5px 0;
        color: #fff;
        font-size: 14px;
        width: 100px;

        &.disabled {
          cursor: not-allowed;
          opacity: .5;
        }
      }
    }
  }
}

// ticket page

// detail\sku page
.bannerInfo {
  width: 345px;
  background: #FFFFFF;
  box-shadow: 0px 7px 10px 0px rgba(0, 0, 0, 0.12);
  padding-bottom: 5px;
  z-index: 99;

  .el-form-item {
    margin-bottom: 12px !important;
  }

  .el-form-item__label {
    line-height: 20px;
    padding: 0 12px 7px 0;
  }

  .el-form-item__error {
    padding-top: 1px;
  }

  .el-input {
    height: 32px;
    line-height: 32px;
  }

  .el-input-group__prepend {
    border: 0;
    background: #F2F2F2;
  }

  .side_line {
    width: 100%;
    height: 4px;
    background: #FF6600;

    &.step {
      width: 317px;
    }
  }

  .side_wrap {
    padding: 0 28px 0 27px;

    h2 {
      font-size: 18px;
      color: #333;
      padding-top: 22px;
    }

    p.des {
      color: #f60;
      font-size: 14px;
      padding: 10px 0 15px 0;
    }

    input {
      background: #F2F2F2;
      height: 32px;
      border: none;
      font-family: Roboto, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
    }

    textarea {
      background: #F2F2F2;
      border: none;
      font-family: Roboto, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
    }

    .side_tab {
      height: 25px;
      margin-top: 10px;
      margin-bottom: 0 !important;

      span {
        display: inline-block;
        width: 75px;
        height: 26px;
        text-align: center;
        line-height: 20px;
        font-size: 14px;
        color: #A6A6A6;
        background: #fff;
        border-radius: 4px 4px 0px 0px;
        cursor: pointer;

        &.active {
          color: #333;
          background: #F2F2F2;
        }
      }
    }

    .side_tips {
      padding: 0 !important;
      margin: 0 !important;

      * {
        padding: 0;
        margin: 0;
        color: @mainColorActive;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
      }
    }

    a.button {
      margin-top: 6px;
      color: #fff;
      font-size: 18px;
      width: 290px;
      line-height: 42px;
      height: 42px;
      text-align: center;
      background: @mainColor;
      border-radius: 3px;
      display: block;
      cursor: pointer;

      &:hover {
        background: #f49000;
      }
    }

    .side_protocol {
      margin-bottom: 0 !important;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-bottom: 10px;

      .el-checkbox {
        display: flex;
        align-items: center;
      }

      .el-checkbox__input {
        position: relative;
      }

      .el-checkbox__label {
        font-size: 12px;
      }

      a {
        color: @mainColor;

        &:hover {
          color: @mainColorActive;
        }
      }

      .is-checked .el-checkbox__label {
        color: #606266;
      }
    }
  }

  .side_success {
    padding: 0 28px 20px 27px;
    text-align: center;

    .side_success_email {
      width: 147px;
      height: 100px;
      padding-top: 32px;
    }

    .side_success_title {
      font-size: 22px;
      font-weight: bold;
      color: #333333;
      padding-top: 19px;
    }

    .side_success_des {
      font-size: 14px;
      color: #333333;
      padding-top: 23px;
      padding-bottom: 14px;
    }

    .side_success_ERcode {
      display: flex;
      justify-content: space-between;
      align-items: center;

      img {
        width: 78px;
        height: 80px;
      }

      p {
        width: 185px;
        font-size: 14px;
        color: #878787;
        line-height: 24px;

        i {
          color: #f60;
        }
      }
    }

    .side_success_userinfo {
      background: #F2F2F2;
      width: 300px;
      min-height: 40px;
      line-height: 40px;
      font-size: 14px;
      color: #000;
      margin-bottom: 20px;
      word-wrap: break-word;
      white-space: normal;
      word-break: break-all;

      a {
        color: @mainColor;
      }
    }

    .side_success_tips {
      color: #878787;
      font-size: 14px;
      text-align: center;
      line-height: 24px;
      text-align: center;

      a {
        color: @mainColor;
        text-decoration: underline;
      }
    }

    .side_success_countdown {
      font-size: 14px;
      color: #999;
      margin-top: 10px;

      i {
        color: @mainColor;
      }
    }
  }

  .side_step {
    padding: 0 28px 0 27px;

    h2 {
      width: 155px;
      font-size: 16px;
      font-weight: bold;
      font-style: italic;
      color: #333333;
      background: url('./../images/sku/arrow.png') no-repeat 0 center;
      padding-left: 20px;
      margin-top: 13px;
    }

    a.button {
      margin-top: 23px;
      color: #fff;
      font-size: 18px;
      width: 290px;
      line-height: 42px;
      height: 42px;
      text-align: center;
      background: @mainColor;
      border-radius: 3px;
      display: block;
      cursor: pointer;

      &:hover {
        background: #f49000;
      }
    }

    .side_step_gif {
      margin-top: 33px;
      width: 287px;
      height: 137px;
      border: 1px solid #D2D2D2;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;

      span {
        position: absolute;
        width: 49px;
        height: 11px;
        font-size: 12px;
        color: #ddd;
        display: inline-block;
        top: -9px;
        left: 30px;
        background: #fff;
        padding: 0 3px;
      }
    }

    img {
      cursor: pointer;
    }

    a.more {
      padding-top: 13px;
      font-size: 12px;
      color: #000000;
      opacity: 0.5;
      display: block;
      text-align: right;
      padding-bottom: 20px;

      &:hover {
        color: @mainColor;
      }
    }
  }
}

.abc {
  position: fixed;
  top: 0;
}

.detailbannerBox {
  width: 100%;
  height: 320px;
  margin-bottom: 10px;

  .detailbanner {
    width: 858px;
    height: 290px;
    float: left;
    overflow: hidden;

    img {
      display: block;
      width: 858px;
      height: 290px;
      background: #ccc;
    }
  }
}

.detailbannerContent {
  width: 100%;
  margin-bottom: 10px;
  min-height: 110px;
}

// detail\sku page


// list page filter

.nlSearchBox {
  width: 1226px;
  margin: 0 auto;
  padding-top: 13px;
}

.nlLists {
  width: 1226px;
  height: auto;
  margin: 0 auto;

  p {
    text-align: center;
    height: 400px;
    line-height: 400px;
  }
}

.nlListsUl {
  width: 100%;
  height: auto;
  margin-bottom: 28px;
  margin-top: 0;
  padding: 0;
  overflow: hidden;
}

.nlListsLi {
  width: 100%;
  height: 45px;
  border-bottom: 1px solid #e5e5e5;
  list-style: none;

  img {
    width: 25px;
  }
}


.nlListsI {
  width: 13px;
  height: 17px;
  display: inline-block;
  background: url(../images/textI.png);
  background-position: 0 50%;
}

.nlListsA {
  width: auto;
  height: 100%;
  line-height: 45px;
  color: #000000;

  &:hover {
    color: @mainColor;
    text-decoration: underline;
  }
}

.nlListsT {
  width: auto;
  height: 100%;
  line-height: 45px;
  display: block;
  float: right;
  color: #999999;
}

// list page filter

// class list page
.listBox {
  width: 100%;
  position: relative;
  margin-bottom: 80px;
  margin-top: 20px;

  .listUl {
    width: 100%;
    padding: 0;
    margin: 0;
    padding: 0;

    .listLi {
      width: 100%;
      height: 142px;
      margin-bottom: 20px;
      list-style: none;
      position: relative;

      .list-img {
        width: 284px;
        height: 142px;
        float: left;
        background: #ddd;

        img {
          width: 284px;
          height: 142px;
        }
      }
    }
  }

  p {
    text-align: center;
    height: 400px;
    line-height: 400px;
  }
}

// class list page

// sitemap page
.sitemap {
  background-color: #f8f8f8;
  padding: 25px;
  width: 796px;

  dl {
    padding: 0;
    margin: 0;
  }

  dt {
    font-size: 16px;
    color: #333;
  }

  ul {
    padding: 15px 0 20px 0;
    display: flex;
    flex-wrap: wrap;
  }

  li {
    //float: left;
    width: 33.33%;
    padding: 5px 0;
  }

  a {
    color: @mainColor;
    font-size: 14px;

    &:hover {
      text-decoration: underline;
    }
  }
}

// sitemap page

/*详情相关资料*/
.xgzl-ul {
  list-style-type: none;
  // display: flex;

  li {
    list-style-type: none !important;
    height: 34px;
    border-radius: 4px;
    background: #ccc;
    padding: 0 !important;
    line-height: 34px;
    margin-right: 10px;
    float: left;
    margin-bottom: 10px;
    position: relative;

    a {
      color: #fff;
      display: inline-block;
      padding: 0 14px;
      background: @mainColor;
      border-radius: 4px;
      //position: absolute;
      white-space: nowrap;
      z-index: 2;
    }

    .xgzl-icon {
      margin-left: 5px;
      display: inline-block;
      width: 12px;
      height: 14px;
      background: url(../images/xiazai.png) no-repeat 100% 100%;
      background-size: contain;
      position: relative;
      top: 3px
    }

    &:after {
      content: "";
      width: 0;
      height: 100%;
      background: #cc5500;
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 4px;
      transition: 0.4s;
    }

    &:hover:after {
      width: 100%;
    }
  }
}

/*详情合作案例*/
.hzal-ul {
  list-style-type: none;

  li {
    list-style-type: none !important;
    padding: 0 !important;
    border-bottom: 1px dashed #e9e9e9;
    line-height: 50px;

    .hzal-icon {
      margin: 0 10px;
      display: inline-block;
      width: 19px;
      height: 19px;
      background: url(../images/al.png) no-repeat 100% 100%;
      background-size: contain;
      position: relative;
      top: 5px
    }

    .hzal-title:hover {
      text-decoration: underline;
    }

    a {
      color: #333;

      &:hover {
        color: @mainColor;

        .hzal-icon {
          background: url(../images/al-active.png) no-repeat 100% 100%;
        }
      }
    }
  }
}

.fixed-description3 {
  position: absolute;
  left: 0;
  width: 100%;

  * {
    padding: 0;
    margin: 0;
  }
}

// contact v2.0
.contact_v2 {
  background: #f8f8f8;
  padding-top: 15px;

  .contact_main {
    width: 600px;
    margin: 0 auto;
    background: #fff;
    padding: 0 36px;

    input,
    textarea {
      background: #F2F2F2;
      border: 0;
    }

    textarea {
      font-size: 14px;
      font-family: 'Arial';
    }

    textarea[class='el-textarea__inner']::-webkit-input-placeholder,
    input[class='el-input__inner']::-webkit-input-placeholder {
      color: #575757 !important;
    }

    textarea[class='el-textarea__inner']:-moz-placeholder,
    input[class='el-input__inner']::-webkit-input-placeholder {
      color: #575757 !important;
    }

    textarea[class='el-textarea__inner']::-moz-placeholder,
    input[class='el-input__inner']::-webkit-input-placeholder {
      color: #575757 !important;
    }

    textarea[class='el-textarea__inner']:-ms-input-placeholder,
    input[class='el-input__inner']::-webkit-input-placeholder {
      color: #575757 !important;
      font-weight: 400 !important;
    }

    textarea[class='el-textarea__inner']::-ms-input-placeholder,
    input[class='el-input__inner']::-webkit-input-placeholder {
      color: #575757 !important;
    }

    textarea[class='el-textarea__inner']::placeholder,
    input[class='el-input__inner']::-webkit-input-placeholder {
      color: #575757 !important;
    }

    .contact_title {
      font-size: 22px;
      text-align: center;
    }

    .contact_des {
      color: #FF6600;
      font-size: 16px;
      text-align: center;
      padding-top: 15px;
    }

    .contact_form {
      padding: 30px 0;

      input,
      select,
      textarea {
        color: #333;
      }

      .el-form-item__label {
        color: #333;
      }

      .el-select .el-icon-arrow-up:before {
        content: "\e78f";
        color: #333333;
      }

      .el-form-item__error {
        top: 0;
        left: 630px;
        width: 200px;
      }

      .jump_dialog {
        .el-dialog__header {
          display: none;
        }

        .el-dialog {
          width: 678px;
          background: transparent;
          box-shadow: none;
        }

        .el-dialog__body {
          padding: 0;
          position: relative;
        }

        .jump_img {
          width: 100%;
        }

        .close-btn {
          position: absolute;
          top: 54px;
          right: 57px;
          cursor: pointer;
        }

        .jump-btn {
          width: 303px;
          height: 48px;
          border-radius: 5px;
          font-size: 18px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #FFFFFF;
          //line-height: 46px;
          position: absolute;
          top: 378px;
          left: 186px;
        }
      }

      .contact_form_content {
        display: flex;

        .el-input__inner {
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;
          border-color: #F2F2F2;
          background: #F2F2F2;
          padding: 0 13px;
        }

        .el-textarea .el-textarea__inner {
          border-radius: 5px;
          background: #F2F2F2;
          border-color: #F2F2F2;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;
          line-height: 1.5;
          padding: 13px;
        }

        .contact_form_left {
          width: 779px;
          box-sizing: border-box;
          //height: 333px;
          border-right: 1px solid #F2F2F2;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          padding-right: 40px;

          .el-form-item {
            margin-right: 0;

            &:not(:last-child) {
              margin-bottom: 25px !important;
            }

            &.is-hidden {
              visibility: hidden;
            }
          }

          .el-select {
            width: 260px;
          }

          .el-textarea .el-textarea__inner {
            width: 649px;
            height: 199px;
          }

          .el-input {
            width: 260px;
          }
        }

        .contact_form_right {
          flex: 1;
          padding-left: 27px;

          .el-form-item {
            display: block;
            margin-right: 0;
            margin-bottom: 25px !important;

            .el-input__inner {
              width: 260px;
              height: 40px;
              background: #F2F2F2;
              border-color: #F2F2F2;
              border-radius: 5px;
            }
          }

          .el-button {
            width: 261px;
            height: 40px;
            background: @mainColor;
            border-radius: 0;
          }
        }

        .el-form-item.code-item {
          .el-input.el-input-group.el-input-group--append {
            border-radius: 5px;
            overflow: hidden;
            border: 1px solid transparent;
            display: flex;
            width: 258px;
          }

          .el-input__inner {
            width: 130px !important;
            border: none;
          }

          .el-input-group__append {
            width: 116px;
            background: #F2F2F2;
            border: none;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            border-radius: 0;
            padding: 0;
            text-align: center;
            position: relative;
            cursor: pointer;

            &:before {
              content: "";
              position: absolute;
              width: 1px;
              height: 18px;
              background: #333333;
              left: 0;
              top: 11px;
            }

            .sendCaptcha {
              padding: 0 12px;
              line-height: 38px;
            }
          }

          .el-form-item__content {
            width: 260px;
            text-align: center;
          }

          .code-item-wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          img {
            width: 90px;
            height: 40px;
            cursor: pointer;
            display: block;
          }

          i {
            width: 30px;
            height: 18px;
            background: url(../images/refresh.png) no-repeat 100% 100%;
            display: flex;
            margin-left: 10px;
            cursor: pointer;
          }
        }

        .el-form--inline .el-form-item__label {
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;
          line-height: 40px;
        }

        .el-form-item.is-error {
          .el-form-item__label {
            color: #FF0000;
          }

          .el-input__inner,
          .el-textarea__inner {
            border-color: #FF0000;
          }

          .el-form-item__error {
            color: #FF0000;
          }

          &.code-item {
            .el-input.el-input-group.el-input-group--append {
              border: 1px solid #FF0000;
            }

            .el-input__inner,
            .el-input-group__append {
              border: none;
            }
          }
        }

        .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before,
        .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
          display: none;
        }

        .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:after,
        .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:after {
          content: '*';
          color: #FF0000;
        }
      }

      .hotline {
        span {
          display: block;
          float: left;

          &:first-child {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
            height: 18px;
            line-height: 18px;
            border-left: 4px solid @mainColor;
            padding-left: 14px;
          }

          &:last-child {
            height: 19px;
            font-size: 26px;
            font-weight: bold;
            color: @mainColor;
            line-height: 19px;
            font-family: Microsoft YaHei;
            background: url('./../images/quote_tel.png') no-repeat 100% 100%;
            margin-left: 10px;
            padding-left: 32px;
            padding-left: 32px;
          }
        }
      }

      .workcycle {
        margin-top: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #878787;
      }
    }

    #contact.contact_form {
      display: flex;

      .contact-img {
        width: 185px;
        height: 244px;
        margin-right: 76px;
        flex-shrink: 0;
      }

      .contact_form_content {
        display: block;

        .el-form-item {
          margin-bottom: 24px !important;

          &.margin-right {
            margin-right: 60px;
          }

          .el-input,
          .el-select {
            width: 320px;
          }

          .el-textarea .el-textarea__inner {
            width: 792px;
            height: 80px;
          }
        }

        .code-item {
          .el-input.el-input-group.el-input-group--append {
            width: 318px;
          }

          .el-input__inner {
            width: 201px !important;
          }
        }

        .contact-btn {
          width: 150px;
          height: 40px;
          background: #888888;
          border-radius: 0;
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 16px;
        }
      }
    }

    .contact_hotline {
      padding: 45px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      img {
        width: 185px;
        height: 237px;
      }

      .red-line {
        padding-left: 15px;
        position: relative;
        line-height: 30px;
        font-size: 16px;
        color: #333;

        &:before {
          content: "";
          width: 4px;
          height: 15px;
          background: @mainColor;
          position: absolute;
          top: 7px;
          left: 0;
        }
      }

      .contact-center {
        width: 305px;
        margin-right: 69px;
      }

      .contact-right {
        flex: 1;
      }
    }

    .contact_locatin {
      padding: 45px 0;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      img {
        width: 185px;
        height: 237px;
      }

      .red-line {
        padding-left: 15px;
        position: relative;
        line-height: 30px;
        font-size: 18px;
        color: #333;
        font-weight: bold;

        &:before {
          content: "";
          width: 4px;
          height: 15px;
          background: @mainColor;
          position: absolute;
          top: 7px;
          left: 0;
        }
      }

      .contact-center {
        padding-top: 21px;
        margin-right: 117px;
      }

      .contact-right {
        padding-top: 21px;
      }
    }

    .contact_approve {
      margin: 0 0 20px 0;
      display: flex;
      justify-content: center;

      a {
        color: @mainColor;

        &:hover {
          color: @mainColorActive;
        }
      }
    }

    .contact_submit {
      text-align: center;

      button {
        width: 200px;
        height: 42px;
        background: @mainColor;
        border: 0;
        font-size: 16px;

        &:hover {
          background: #f49000;
        }
      }
    }

    .contact_type {
      position: relative;
      background: #f2f2f2;

      .contact_radios {
        position: absolute;
        left: 15px;
        top: 0;
        z-index: 2;

        span {
          color: #575757;
        }

        i {
          color: @mainColor;
        }
      }

      .contact_type_swtich {
        position: absolute;
        left: 140px;
        top: 5px;
        z-index: 2;
        width: 125px;
        height: 30px;
        line-height: 30px;
        background: #fff;
        border-radius: 30px;
        box-shadow: 3px 3px 3px #ddd inset;

        em {
          position: absolute;
          z-index: 3;
          display: inline-block;
          width: 62px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          color: #575757;
          font-size: 15px;
          top: 0;
          cursor: pointer;


          &:first-child {
            left: 0;
          }

          &:last-child {
            right: 0;
          }

          &.active {
            background: @mainColor;
            color: #fff;
            border-radius: 35px;
          }

          label {
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background: @mainColorActive;
            position: absolute;
            top: 5px;
            right: 7px;
            z-index: 4;
            display: none;
          }

          &.on {
            label {
              display: block;
            }
          }
        }
      }

      .contact_type_line {
        width: 1px;
        height: 25px;
        position: absolute;
        background: @mainColor;
        z-index: 2;
        left: 300px;
        top: 8px
      }

      input {
        position: absolute;
        width: 300px;
        right: 3px;
        bottom: -8px;
        z-index: 2;
        border-left: 1px solid #ff6600;
        border-radius: 0;
        height: 25px;
        line-height: 25px;
      }
    }

    .contact_email_form,
    .contact_phone_form {
      position: relative;
      top: -22px;
      margin-bottom: 0;

      input {
        padding-left: 315px;
      }
    }

    .contact_tips {
      padding: 0 !important;
      margin: 0 !important;
      position: relative;
      top: -20px;

      * {
        padding: 0;
        margin: 0;
        color: @mainColorActive;
        height: 20px;
        line-height: 20px;
        text-align: center;
      }
    }
  }

  .contact_main1 {
    margin: 0 auto;
    width: 1154px;
    background: #fff;
    padding: 0 36px;

    .contact_tab {
      height: 72px;
      line-height: 72px;
      border-bottom: 3px #F2F2F2 solid;
      width: 100%;

      li {
        float: right;
        width: 100px;
        text-align: center;
        height: 72px;
        line-height: 72px;
        font-size: 20px;

        &.activity {
          border-bottom: 3px solid @mainColor;

          a {
            color: #333333;
          }
        }

        &:first-child {
          width: 112px;
          float: left;

          a {
            font-size: 20px;

          }
        }

        a {
          font-size: 14px;
          color: #878787;

          &:hover {
            color: #333333;
          }
        }
      }
    }

    .contact_des {
      height: 18px;
      line-height: 18px;
      border-left: 4px solid #fe6602;
      padding-left: 14px;
      margin-top: 25px;
      font-size: 16px;
      color: #333333;
    }

    .contact_form {
      padding: 40px 0;

      ::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #878787;
      }

      ::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: #878787;
      }

      :-ms-input-placeholder {
        /* Internet Explorer 10+ */
        color: #878787;
      }

      .el-form-item__label {
        color: #333;
      }

      .el-select .el-icon-arrow-up:before {
        content: "\e78f";
        color: #333333;
      }

      .jump_dialog {
        .el-dialog__header {
          display: none;
        }

        .el-dialog {
          width: 678px;
          background: transparent;
          box-shadow: none;
        }

        .el-dialog__body {
          padding: 0;
          position: relative;
        }

        .jump_img {
          width: 100%;
        }

        .close-btn {
          position: absolute;
          top: 54px;
          right: 57px;
          cursor: pointer;
        }

        .jump-btn {
          width: 303px;
          height: 48px;
          border-radius: 5px;
          font-size: 18px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #FFFFFF;
          //line-height: 46px;
          position: absolute;
          top: 378px;
          left: 186px;
        }
      }

      .contact_form_content {
        display: flex;

        .el-input__inner {
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;
          border-color: #F2F2F2;
          background: #F2F2F2;
          padding: 0 13px;
        }

        .el-textarea .el-textarea__inner {
          border-radius: 5px;
          background: #F2F2F2;
          border-color: #F2F2F2;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;
          line-height: 1.5;
          padding: 13px;
        }

        .contact_form_left {
          width: 779px;
          box-sizing: border-box;
          //height: 333px;
          border-right: 1px solid #F2F2F2;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          padding-right: 40px;

          .el-form-item {
            margin-right: 0;

            &:not(:last-child) {
              margin-bottom: 25px !important;
            }

            &.is-hidden {
              visibility: hidden;
            }
          }

          .el-select {
            width: 260px;
          }

          .el-textarea .el-textarea__inner {
            width: 649px;
            height: 199px;
          }

          .el-input {
            width: 260px;
          }
        }

        .contact_form_right {
          flex: 1;
          padding-left: 27px;

          .el-form-item {
            display: block;
            margin-right: 0;
            margin-bottom: 25px !important;

            .el-input__inner {
              width: 260px;
              height: 40px;
              background: #F2F2F2;
              border-color: #F2F2F2;
              border-radius: 5px;
            }
          }

          .el-button {
            width: 261px;
            height: 40px;
            background: #FE6602;
            border-radius: 0;
          }
        }

        .el-form-item.code-item {
          .el-input.el-input-group.el-input-group--append {
            border-radius: 5px;
            overflow: hidden;
            border: 1px solid transparent;
            display: flex;
            width: 258px;
          }

          .el-input__inner {
            width: 142px !important;
            border-radius: 0;
            border: none;
          }

          .el-input-group__append {
            width: 116px;
            background: #F2F2F2;
            border: none;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            border-radius: 0;
            padding: 0;
            text-align: center;
            position: relative;
            cursor: pointer;

            &:before {
              content: "";
              position: absolute;
              width: 1px;
              height: 18px;
              background: #333333;
              left: 0;
              top: 11px;
            }

            .sendCaptcha {
              padding: 0 12px;
              line-height: 38px;
            }
          }
        }

        .el-form--inline .el-form-item__label {
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;
          line-height: 40px;
        }

        .el-form-item.is-error {
          .el-form-item__label {
            color: #FF0000;
          }

          .el-input__inner,
          .el-textarea__inner {
            border-color: #FF0000;
          }

          .el-form-item__error {
            color: #FF0000;
          }

          &.code-item {
            .el-input.el-input-group.el-input-group--append {
              border: 1px solid #FF0000;
            }

            .el-input__inner,
            .el-input-group__append {
              border: none;
            }
          }
        }

        .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before,
        .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
          display: none;
        }

        .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:after,
        .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:after {
          content: '*';
          color: #FF0000;
        }
      }

      .hotline {
        span {
          display: block;
          float: left;

          &:first-child {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
            height: 18px;
            line-height: 18px;
            border-left: 4px solid @mainColor;
            padding-left: 14px;
          }

          &:last-child {
            height: 19px;
            font-size: 26px;
            font-weight: bold;
            color: @mainColor;
            line-height: 19px;
            font-family: Microsoft YaHei;
            background: url('./../images/quote_tel.png') no-repeat 100% 100%;
            margin-left: 10px;
            padding-left: 32px;
            padding-left: 32px;
          }
        }
      }

      .workcycle {
        margin-top: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #878787;
      }
    }

    #contact.contact_form {
      display: flex;

      .contact-img {
        width: 185px;
        height: 244px;
        margin-right: 76px;
        flex-shrink: 0;
      }

      .contact_form_content {
        display: block;

        .el-form-item {
          margin-bottom: 24px !important;

          &.margin-right {
            margin-right: 60px;
          }

          .el-input,
          .el-select {
            width: 320px;
          }

          .el-textarea .el-textarea__inner {
            width: 792px;
            height: 80px;
          }
        }

        .code-item {
          .el-input.el-input-group.el-input-group--append {
            width: 318px;
          }

          .el-input__inner {
            width: 201px !important;
          }
        }

        .contact-btn {
          width: 150px;
          height: 40px;
          background: #888888;
          border-radius: 0;
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 16px;
        }
      }
    }

    .contact_hotline {
      padding: 45px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      img {
        width: 185px;
        height: 237px;
      }

      .red-line {
        padding-left: 15px;
        position: relative;
        line-height: 30px;
        font-size: 16px;
        color: #333;

        &:before {
          content: "";
          width: 4px;
          height: 15px;
          background: #FE6602;
          position: absolute;
          top: 7px;
          left: 0;
        }
      }

      .contact-center {
        width: 305px;
        margin-right: 69px;
      }

      .contact-right {
        flex: 1;
      }
    }

    .contact_locatin {
      padding: 45px 0;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      img {
        width: 185px;
        height: 237px;
      }

      .red-line {
        padding-left: 15px;
        position: relative;
        line-height: 30px;
        font-size: 18px;
        color: #333;
        font-weight: bold;

        &:before {
          content: "";
          width: 4px;
          height: 15px;
          background: #FE6602;
          position: absolute;
          top: 7px;
          left: 0;
        }
      }

      .contact-center {
        padding-top: 21px;
        margin-right: 117px;
      }

      .contact-right {
        padding-top: 21px;
      }
    }
  }

  .contact_ads {
    // width: 1226px;
    // height: 92px;
    // background: url(../images/contact-bottom.jpg);
    // margin: 22px auto 0;
    // margin-top: 50px;
    background: url('../images/newPage/ads.jpg') no-repeat 50% 50% #e8e8e8;
    height: 100px;
  }

  .product-notice {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5px;
    background: #f2f2f2;
    border-radius: 3px;

    .product-notice-text {
      width: 444px;
      max-height: 44px;
      font-size: 14px;
      color: #333333;
      line-height: 22px;
      overflow: hidden;
    }
  }
}

// contact v2.0

// food safeguard
.food_safe_banner {
  height: 300px;
  background: url('./../../public/images/foodSafeguard/bg.jpg') no-repeat center 0;
  font-size: 36px;
  line-height: 300px;
  text-align: center;
  color: #fff;
}

.food_safe_tab {
  width: 1226px;
  margin: 0 auto;
  padding-top: 50px;

  li {
    background: #F2F2F2;
    height: 80px;
    line-height: 80px;
    width: 408px;
    float: left;
    text-align: center;
    font-size: 16px;
    color: #333;
    border-bottom: 3px solid #878787;
    cursor: pointer;

    &.active {
      background: #fff;
      color: @mainColor;
      font-weight: bold;
      border-bottom: 3px solid @mainColor;
    }

    &:hover {
      color: @mainColor;
      font-weight: bold;
      border-bottom: 3px solid @mainColor;
    }
  }
}

.food_safe_wrap {
  width: 1226px;
  margin: 0 auto;
  padding-top: 65px;
  padding-bottom: 80px;

  .food_safe_search {
    text-align: center;

    .el-cascader {
      float: left;
      margin-left: 220px;
      width: 630px;
      height: 44px;
      line-height: 44px;
    }

    .food_safe_search_btn {
      width: 153px;
      height: 44px;
      border-radius: 0px 5px 5px 0px;
      background: @mainColor;
      float: left;
    }

    .el-cascader,
    .el-input__inner {
      height: 44px;
      line-height: 44px;
      border-radius: 5px 0 0 5px;
    }
  }

  .food_safe_link {
    text-align: center;
    color: #878787;
    font-size: 16px;
    margin: 33px auto;

    a {
      color: @mainColor;
    }
  }

  .food_safe_tips {
    text-align: center;
    width: 780px;
    border: 1px solid #EBEEF5;
    border-bottom: 0;
    margin-left: 223px;
    background: #fff;
    height: 40px;
    line-height: 40px;
  }

  .food_safe_table {
    text-align: center;
  }

  .el-table {
    width: 780px;
    margin-left: 223px;
  }
}

// food safeguard

// modal
.modal {
  position: absolute;
  right: 66px;
  width: 423px;
  height: 180px;
  background: #FFFFFF;
  border: 1px solid #BFBFBF;
  top: 50%;
  transform: translate(0, -50%);
  text-align: center;
  z-index: 9;
  display: none;

  .modal_content {
    padding-top: 47px;
  }

  i {
    background: url('./../images/fixedMenu/success.png') no-repeat 100% 100%;
    display: block;
    float: left;
    width: 35px;
    height: 35px;
    margin-left: 55px;
  }

  span {
    height: 35px;
    line-height: 35px;
    display: block;
    float: left;
    padding-left: 18px;
  }

  p {
    font-size: 14px;
    color: #333333;
    line-height: 35px;
    height: 35px;
  }

  .modal_button {
    margin-top: 21px;
  }

  button {
    width: 60px;
    height: 32px;
    line-height: 32px;
    background: #F2F2F2;
    border: 1px solid #BFBFBF;
    border-radius: 3px;
    cursor: pointer;

    &:hover {
      background: @mainColor;
      border: 1px solid @mainColor;
      color: #fff;
    }
  }
}

// modal

// leadoo
.ld-launch-btn {
  // visibility: hidden !important;
  // width: 0;
  // height: 0;
  // display: none;
  // overflow: hidden;
  // border: 0;
  // padding: 0;
  // bottom: 100px !important;
}

.ld-chat-launcher {
  // right: -500px !important;
  // visibility: hidden !important;
  // top: 500px !important;
  // right: -80px !important;
}

// leadoo

// qoute page
.quote_modal {
  .el-dialog {
    border-radius: 20px;
  }

  .el-dialog__header {
    text-align: center;
    color: #333;
    font-weight: bold;
    font-size: 16px;
    border-bottom: 1px solid #E5E5E5;
  }

  .el-dialog__body {
    //padding: 0;
    //margin: 30px auto;
    //width: 300px;
  }

  .el-dialog__footer {
    text-align: center;
  }

  input {
    border: 1px solid #ddd !important;
  }

  .quote_modal_phone {
    font-size: 14px;
    color: #878787;
    padding-bottom: 20px;
    text-align: center;
  }

  .quote_modal_countdown {
    margin-top: 34px;
    font-size: 14px;
    color: #878787;
    text-align: center;

    span {
      color: @mainColor;
    }
  }

}

// qoute page

// success page
.success {
  padding: 30px 0 70px 0;
  text-align: center;

  .succes_infomation {
    width: 540px;
    border-bottom: 1px solid #ddd;
    margin: 0 auto;

    img {
      width: 101px;
      height: 67px;
    }

    h2 {
      font-size: 22px;
      font-weight: 400;
      color: #333;
      margin: 22px 0 20px 0;
    }

    p {
      font-size: 14px;
      color: #333;
      padding: 5px 0;

      &:last-child {
        padding-bottom: 25px;
      }
    }

    a {
      color: @mainColor;

      &:hover {
        color: @mainColorActive;
        text-decoration: underline;
      }
    }

    .countdown {
      color: #999;

      i {
        color: @mainColor;
      }
    }

    .succes_infomation_btn {
      background: @mainColor;
      color: #fff;
      font-size: 14px;
      height: 36px;
      line-height: 36px;
      width: 120px;
      text-align: center;
      border-radius: 3px;
      display: inline-block;

      &:hover {
        background: @mainColorActive;
      }
    }
  }

  .success_WXcode {
    padding-top: 25px;

    p {
      font-size: 14px;
      color: #878787;

      span {
        color: @mainColor;
      }
    }

    .success_WXcode_item {
      display: flex;
      justify-content: center;
      margin-top: 20px;

      img {
        width: 101px;
        height: 103px;
        margin-right: 25px;
      }

      ul {
        margin-top: 20px;

        li {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #878787;
          line-height: 24px;
          text-align: left;

          i {
            height: 4px;
            width: 4px;
            display: inline-block;
            background: @mainColor;
            margin-right: 5px;
          }
        }
      }
    }
  }
}

// success page

// registion clause
.register-terms {
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.w_popup_container {
  padding-top: 20px;
}

.popup_container {
  background: #fff;
  border-radius: 4px;
  border: 4px solid rgba(0, 0, 0, .2);
}

.popup_container {
  width: 850px;
  height: 600px;
}

.online_rule_title {
  text-align: center;
  margin-bottom: 20px;
}

.online_rule_title p {
  color: #333;
  font-size: 18px;
  margin-bottom: 10px;
  margin-top: 0;
  line-height: 1;
}

.online_rule_content {
  width: 800px;
  height: 466px;
  border-radius: 2px;
  background-color: #f8f8f8;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 20px;
  padding-right: 0px;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  overflow: auto;
  text-align: left;
}

.popup_container .online_rule_content .title {
  clear: both;
  height: 30px;
  font-size: 16px;
  font-weight: bold;
  padding-left: 32px;
  padding-top: 16px;
}

.online_rule_content ul li {
  clear: both;
  padding-top: 4px;
  line-height: 18px;
}

.online_rule_content ul li div.left {
  float: left;
  height: auto;
  width: 32px;
}

.online_rule_content ul li div.right {
  float: left;
  width: 720px;
}

.online_rule_submit {
  margin-top: 10px;
  text-align: center;
}

.online_rule_submit_button {
  display: inline-block;
  width: 195px;
  height: 45px;
  text-align: center;
  line-height: 45px;
  color: #999;
  font-size: 16px;
  cursor: pointer;
}

.online_rule_submit_button.active {
  color: #fff;
  background: #ca4300;
}

// registion clause

// 内页的留言表单
.inner_quote_form {
  width: 420px;
  height: 500px;
  background: rgba(0, 0, 0, 0.4);
  position: absolute;
  top: 0;
  right: 0;

  input {
    height: 32px;
    border: none;
    font-family: Roboto, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  }

  textarea {
    border: none;
    font-family: Roboto, "Helvetica Neue", Arial, Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  }

  .des {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    padding: 35px 0 20px 0;

    b {
      color: @mainColorActive;
    }
  }

  .side_tab {
    // height: 25px;
    // margin-top: 10px;
    margin-bottom: 5px !important;

    span {
      display: inline-block;
      width: 75px;
      height: 13px;
      line-height: 13px;
      text-align: center;
      font-size: 14px;
      cursor: pointer;
      color: #B4B4B4;

      &:last-child {
        border-left: 1px solid @mainColor;
      }

      &.active {
        color: #fff;
      }
    }

    .el-form-item__content {
      height: 20px;
      line-height: 20px;
    }
  }

  .side_tab_lable {
    margin: 0 !important;

    .el-form-item__label {
      display: none;
    }
  }

  .side_tips {
    color: #B4B4B4;

    .el-form-item__content {
      height: 20px;
      line-height: 20px;
    }
  }

  .button {
    width: 180px;
    text-align: center;
    height: 36px;
    line-height: 36px;
    background: @mainColor;
    box-shadow: 2px 2px 22px 0px rgba(0, 0, 0, 0.15);
    border-radius: 3px;
    font-size: 16px;
    color: #fff;
    display: block;
    margin-left: 75px;

    &:hover {
      background: @mainColorActive;
    }
  }

  .side_protocol {
    margin-bottom: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 10px;

    .el-checkbox {
      display: flex;
      align-items: center;
    }

    .el-checkbox__input {
      position: relative;
    }

    .el-checkbox__label {
      font-size: 12px;
      color: #fff;
    }

    a {
      color: @mainColor;

      &:hover {
        color: @mainColorActive;
      }
    }

    .is-checked .el-checkbox__label {
      color: #fff;
    }
  }

  .el-form-item__content {
    line-height: 0;
  }

  .el-form {
    width: 335px;
    margin-left: 43px;
  }

  .el-form-item {
    margin-bottom: 15px;
  }

  .el-form-item__label {
    color: #fff;
    height: 20px;
    line-height: 20px;
  }

  .el-form-item__error {
    color: #f00;
  }

  .el-input__inner {
    height: 34px;
    line-height: 34px;
  }
}

.inner_quote_success {
  width: 420px;
  height: 500px;
  background: rgba(0, 0, 0, 0.4);
  position: absolute;
  top: 0;
  right: 0;
  color: #fff;
  text-align: center;

  .side_success_email {
    padding-top: 65px;
  }

  .side_success_title {
    font-size: 22px;
    font-weight: bold;
    color: #fff;
    padding-top: 19px;
  }

  .side_success_des {
    font-size: 14px;
    color: #fff;
    padding-top: 23px;
    padding-bottom: 14px;
  }

  .side_success_ERcode {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 280px;
    margin-left: 69px;
    margin-top: 30px;

    img {
      width: 78px;
      height: 80px;
    }

    p {
      width: 185px;
      font-size: 14px;
      color: #fff;
      line-height: 24px;

      i {
        color: #f60;
      }
    }
  }

  .side_success_userinfo {
    width: 300px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #fff;
    margin-bottom: 20px;

    em {
      color: @mainColor;
      word-wrap: break-word;
      white-space: normal;
      word-break: break-all;
    }
  }

  .side_success_tips {
    color: #fff;
    font-size: 14px;
    text-align: center;
    line-height: 24px;
    text-align: center;

    a {
      color: @mainColorActive;
      text-decoration: underline;
    }
  }

  .side_success_countdown {
    font-size: 14px;
    color: #fff;
    margin-top: 30px;

    i {
      color: @mainColorActive;
    }
  }
}

// 内页的留言表单

/* 检测认证中心留言表单改版 */
.inner_quote_form {
  .inner_quote_form-item {
    display: flex;
    background: #fff;
    width: 342px;
    height: 44px;
    background: #F5F5F5;
    border-radius: 4px;
    align-items: center;

    .icon {
      display: block;
      width: 14px;
      height: 14px;
      margin: 0 7px 0 11px;
    }

    &.content {
      margin-bottom: 0;

      .icon {
        background: url('./../images/promotion/jiance/form/content.svg') no-repeat;
      }
    }

    &.customer {
      .icon {
        background: url('./../images/promotion/jiance/form/customer.svg') no-repeat;
      }
    }

    &.phone {
      margin-bottom: 20px;

      p {
        color: #FEA202;
        left: 95px;
        position: absolute;
        top: 55px;
        font-size: 12px;
      }

      .icon {
        background: url('./../images/promotion/jiance/form/phone.svg') no-repeat;
      }
    }

    &.provice {
      .icon {
        width: 16px;
        height: 16px;
        background: url('./../images/promotion/jiance/form/provice.svg') no-repeat;
      }
    }

    .label {
      width: 50px;
      height: 44px;
      line-height: 44px;
      text-align: center;
      font-size: 14px;
      font-weight: bold;
      color: #333;
    }

    .input-box {
      width: 250px;
    }
  }

  .inner_quote_form_customer {
    .el-textarea__inner,
    .el-input__inner {
      background: #f5f5f5;
    }

    .el-form-item__content {}

    .el-form-item {
      margin-bottom: 25px;
    }

    .el-form-item__error {
      margin-left: 95px;
    }

    .side_protocol {
      a {
        color: #FF8F1F;
      }
    }
  }
}

/* 检测认证中心留言表单改版 */

// 搜索结果页右侧关联lv3集合页数据展示
.order-line-search-result {
  width: 100%;
  border-bottom: 20px #fff solid;

  &>div {
    background: #fff;
    margin: 23px;
    padding-bottom: 20px;
    position: relative;
  }

  .icon {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 82px;
    height: 34px;
    background: url('./../images/buyicon.png') no-repeat;
    z-index: 1;
  }

  .img {
    width: 280px;
    height: 130px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    background-size: cover;

    // img {
    //   max-width: 280px;
    //   max-height: 130px;
    // }
  }

  .num {
    padding: 10px 15px 5px 15px;
    font-size: 15px;
    color: #333;
    display: flex;

    span {
      color: @mainColor;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 141px;
      display: inline-block;
    }
  }

  .description {
    padding: 5px 15px;
    color: #878787;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    i {
      display: inline-block;
      height: 4px;
      width: 4px;
      border-radius: 50%;
      background: #A7A7A7;
    }
  }

  .price {
    padding: 5px 15px;
    height: 14px;
    color: @mainColor;
  }

  a {
    display: block;
    height: 42px;
    background: @mainColor;
    border-radius: 4px;
    line-height: 42px;
    color: #fff;
    font-size: 16px;
    text-align: center;
    margin: 15px 15px 0 15px;

    &:hover {
      background: @mainColorActive;
    }
  }
}

// 搜索结果页右侧关联lv3集合页数据展示

// element UI rest
.el-loading-spinner .el-loading-text,
.el-loading-spinner i {
  color: @mainColor !important;
}

.pagination {
  width: 1186px;
  margin: 20px auto;
  text-align: left;
  clear: both;

  .el-pager {
    li {
      background: #f2f2f2;
      margin-left: 5px;
      font-weight: normal;

      &.active,
      &:hover {
        background: @mainColor;
        color: #fff;
      }
    }
  }

  .el-pagination__jump {
    .el-input__inner {
      &:focus {
        border: 1px solid #DCDFE6;
        outline: 1;
      }
    }
  }

  .serverpages {
    width: auto;
    height: auto;
    margin: 0;
    padding: 20px 0;
    overflow: hidden;
    float: right;
  }

  .serverpage {
    width: 40px;
    height: 30px;
    background: rgb(242, 242, 242);
    margin-right: 5px;
    float: left;
    text-align: center;
    line-height: 30px;
    font-size: 12px;
    border: 1px solid rgb(242, 242, 242);
    cursor: pointer;
    list-style: none;

    a {
      color: #666;
      display: block;
    }

    &:hover {
      background: white;
      border: 1px solid @mainColor;
      color: #000000;
    }

    &.pageActive {
      background: @mainColor;
      color: white;
      border: 1px solid @mainColor;

      a {
        color: #fff;
      }

      &:hover {
        background: @mainColor;
        border: 1px solid @mainColor;
        color: white;
      }
    }
  }

  .pageActive {
    background: @mainColor;
    color: white;
    border: 1px solid @mainColor;

    a {
      color: #fff;
    }
  }
}

.docCheck-title {
  font-size: 30px;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 30px;
  padding-top: 49px;
}

.docCheck-sub-title {
  font-size: 16px;
  color: #CADCE8;
  line-height: 16px;
  margin-top: 16px;
  display: flex;

  span:not(:last-child) {
    border-right: 1px solid #8DA6B6;
    padding-right: 16px;
  }

  span:not(:first-child) {
    padding-left: 16px;
  }
}

.docCheck-wrap {
  * {
    box-sizing: border-box;
  }

  .docCheck-content {
    display: flex;
    justify-content: space-between;
  }

  .docCheck-left {
    width: 880px;
  }

  .docCheck-right {
    width: 309px;
    height: 472px;
    background: #FAFAFA;
    border: 1px solid #ECECEC;
    box-shadow: 1px 5px 15px 0px rgba(0, 0, 0, 0.1);
    padding: 0 28px;
    margin-top: 13px;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #333333;
      border-bottom: 1px solid #DEDEDE;
      font-weight: bold;
      line-height: 49px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .more-link {
        font-size: 14px;
        color: #CA4300;
        line-height: 14px;
        margin-left: 22px;
      }
    }

    ul {
      font-size: 14px;
      color: #333333;
      line-height: 32px;
      margin-top: 20px;
      margin-bottom: 20px;

      a {
        color: #333;
        cursor: pointer;

        &:hover {
          color: #CA4300;
        }
      }

      li {
        position: relative;
        padding-left: 20px;

        &:before {
          content: "";
          width: 6px;
          height: 6px;
          background: #CA4300;
          position: absolute;
          left: 0;
          top: 13px;
        }
      }
    }

    .other-page {
      padding: 17px 0;
      border-top: 1px solid #DEDEDE;
      display: flex;
      align-items: center;
      img {
        width: 11px;
        height: 20px;
        margin-right: 11px;
      }
      a {
        font-size: 14px;
        color: #333333;
        line-height: 14px;
      }
    }

    .contact-text {
      border-top: 1px solid #DEDEDE;
      font-size: 14px;
      color: #878787;
      line-height: 14px;
      padding-top: 20px;
      padding-left: 20px;
      position: relative;

      img {
        position: absolute;
        top: 20px;
        left: 0;
      }

      p {
        display: flex;

        span {
          flex-shrink: 0;
        }
      }
    }
  }

  .docCheck-detail-text {
    width: 880px;
    height: 135px;
    background: rgba(255, 102, 0, 0.08);
    padding: 23px 43px 0 34px;
    font-size: 16px;
    color: #333333;
    line-height: 30px;
    margin-top: 18px;

    p {
      display: flex;
      align-items: flex-start;
    }

    .label {
      color: #1C1C1C;
      font-weight: bold;
      flex-shrink: 0;
      position: relative;
      padding-left: 25px;

      .icon-label {
        color: #CA4300;
        font-size: 24px;
        position: absolute;
        top: 0;
        left: 0;
      }
    }
  }

  .lang-tab {
    margin-top: 13px;
    display: flex;
    justify-content: flex-end;
    a {
      width: 40px;
      height: 22px;
      border: 1px solid #878787;
      border-radius: 3px;
      text-align: center;
      line-height: 20px;
      font-size: 12px;
      color: #878787;
      margin-left: 6px;
      &.is-active {
        border-color: #CA4300;
        background: #CA4300;
        color: #FFFFFF;
      }
    }
  }
  .form-content {
    width: 880px;
    margin: auto;
    margin-top: 17px;

    .form-tips {
      color: #F56C6C;
      font-size: 12px;
      line-height: 1;
      padding-top: 4px;
      position: absolute;
      top: 100%;
      left: 0;
      display: none;
      white-space: nowrap;
    }

    //.tips-item .el-form-item__error {
    //  top: 66px;
    //}
    .tips-item.is-success .form-tips {
      display: block;
    }

    .phone-item {
      .el-input {
        width: 320px;
        border: 1px solid #D2D2D2;
        border-radius: 4px;
        background: #F5F7FA;
      }

      .el-input-group__prepend {
        padding-left: 13px;
        padding-right: 0;
        font-size: 16px;
        color: #333333;
        line-height: 24px;
        border: none;

        i {
          color: #A2A2A2;
        }
      }

      .phone-prepend {
        padding-right: 13px;
        border-right: 1px solid #D2D2D2;
      }

      .el-input__inner {
        border: none;
        width: 243px;
      }
    }

    .el-form-item__label {
      font-size: 16px;
      color: #333333;
      line-height: 48px;
      white-space: nowrap;
    }

    .el-form-item {
      margin-bottom: 36px;
    }

    .el-form-item.is-required .el-form-item__label {
      &:before {
        display: none;
      }

      &:after {
        content: "*";
        color: #FF0000;
        margin-left: 4px;
        position: relative;
        top: 4px;
      }
    }

    .el-input__inner {
      width: 320px;
      height: 48px;
      background: #F6F9FC;
      border: 1px solid #D2D2D2;
      border-radius: 4px;
    }

    .file-upload-item {
      font-size: 14px;
      color: #878787;

      .el-form-item__content {
        height: 48px;
        background: #F6F9FC;
        border: 1px solid #D2D2D2;
        border-radius: 4px;
        position: relative;
        padding-left: 130px;
        line-height: 46px;
        display: flex;

        .upload-btn {
          width: 118px;
          height: 48px;
          background: #3C515B;
          border-radius: 4px;
          font-size: 16px;
          color: #FFFFFF;
          position: absolute;
          top: -1px;
          left: -1px;

          &:hover,
          &:focus {
            border-color: #D2D2D2;
          }
        }
        .file-name {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    .upload-tips {
      padding-left: 102px;
      font-size: 14px;
      color: #878787;
      line-height: 24px;
      margin-bottom: 30px;

      .check-box {
        display: flex;
      }

      label {
        padding-right: 10px;
      }

      span,
      a {
        color: #CA4300;
        cursor: pointer;
      }
    }

    .isCheckedTips {
      color: #F56C6C;
      font-size: 12px;
    }

    .check-content {
      padding-left: 102px;

      .el-checkbox__input.is-checked+.el-checkbox__label {
        color: #333;
      }
    }

    .submit-btn {
      margin: 0;
      margin-left: 97px;
    }
  }

  .submit-btn {
    width: 220px;
    height: 60px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    color: #FFFFFF;
    margin: 68px auto 0;
    display: block;
  }

  .result {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-top: 13px;
    min-height: 455px;

    .submit-btn {
      margin: 55px auto;
    }

    .pdf-result {
      width: 880px;
    }

    .tips {
      background: rgba(255, 102, 0, 0.08);
      padding: 21px 37px 36px 26px;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333333;
        line-height: 1;
        margin-bottom: 21px;
      }

      .result-title {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 400;
        color: #333333;
        line-height: 1;
        font-weight: bold;

        img {
          margin-right: 13px;
        }
      }

      p {
        text-align: center;
        font-size: 16px;
        color: #333333;
        line-height: 30px;
      }

      .red-color {
        color: #CA4300;
      }

      .sub-tips {
        display: flex;
        padding-top: 17px;
        padding-left: 103px;
        margin-top: 16px;
        font-size: 14px;
        color: #999999;
        line-height: 30px;
        border-top: 1px solid #FFDFC9;
        .sub-btn {
          cursor: pointer;
          color: #333;
        }
      }
    }

    .pdf-tips {
      font-size: 16px;
      color: #333333;
      margin: 24px 0;
    }

    .invalidated-tips {
      width: 880px;
      height: 230px;
      background: #FFFFFF;
      border: 1px solid #C5C5C5;
      padding: 19px 24px;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
    }
  }

  .docCheck-dialog {
    .dialog-tips {
      width: 437px;
      background: #F5F5F5;
      display: flex;
      padding: 16px 24px 15px 29px;
      font-size: 12px;
      color: #999999;
      line-height: 24px;
      margin: 29px auto 23px;
      text-align: left;
      word-break: break-word;
    }
    .el-dialog {
      border-radius: 12px;
    }

    .el-dialog__header {
      display: none;
    }

    .el-dialog__body {
      padding: 48px 0 37px;
      font-size: 16px;
      color: #333333;
      line-height: 30px;
      text-align: center;
      position: relative;

      img {
        position: absolute;
        top: 22px;
        right: 23px;
        cursor: pointer;
      }
    }

    .dialog-footer {
      text-align: center;
    }

    .el-button {
      width: 80px;
      height: 38px;
      border-radius: 4px;
    }
  }
}

.form-item-approve {
  padding: 18px 0!important;
  text-align: center;
  display: flex;
  justify-content: center;
  label:first-child {
    margin: 0!important;
  }
  .el-checkbox__label{
    font-size: 12px;
    color: #878787!important;
    margin-left: 5px;
    a {
      color: #CA4300;
    }
  }
}

// .el-form-item {
//   margin-bottom: 0 !important;
// }

// element UI rest