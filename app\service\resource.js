'use strict';

const Service = require('egg').Service;
const moment = require('moment');
const _info = require('../../config/info').siteInfo;
const request = require('request');

class ResourceService extends Service {
  async getList(params) {
    const {
      app
    } = this;
    let query = ['n.is_delete=0', 'n.is_publish=1'];
    if (params.type && params.type != 0) {
      query.push('c.id=' + params.type);
    }

    if (params.title) {
      query.push('n.title LIKE "%' + params.title + '%"');
    }

    if (params && typeof params.is_publish == 'number') {
      query.push('n.is_publish=' + params.is_publish);
    }

    // 合并行业和服务
    if (params && (params.service || params.trade)) {
      let str = '';
      if (params.service && params.trade) {
        str = params.service + ',' + params.trade;
      }
      if (params.service && !params.trade) {
        str = params.service
      }
      if (!params.service && params.trade) {
        str = params.trade
      }
      const result = str.split(',');
      if (result.length > 1) {
        let temp = '('
        result.forEach(item => {
          temp += `cr.catalog_id=${item} or `
        })
        temp = temp.substr(0, temp.length - 3)
        temp += ')'
        query.push(temp);
      } else {
        query.push(`cr.catalog_id=${result[0]}`);
      }
    }

    if (params.time) {
      let time = params.time.split(' - ');
      query.push('n.gmt_publish_time>="' + time[0] + '"');
      query.push('n.gmt_publish_time<"' + time[1] + '"');
    }

    let page = params.page || 1,
      limit = params.limit || 10;
    let start = (page - 1) * limit;

    const total = await app.mysql.query('SELECT n.id FROM resource n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.resource_id=n.id WHERE ' + query.join(' AND ') + ' GROUP BY n.id');
    const list = await app.mysql.query('SELECT n.id,n.title,n.is_publish,n.is_public,n.size,n.gmt_publish_time AS time,n.path,n.download_num,n.up_type,c.name AS typeName FROM resource n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.resource_id=n.id WHERE ' + query.join(' AND ') + ' GROUP BY n.id ORDER BY n.gmt_publish_time DESC LIMIT ' + start + ',' + limit);

    console.log('SELECT n.id,n.title,n.is_publish,n.is_public,n.size,n.gmt_publish_time AS time,n.path,n.download_num,n.up_type,c.name AS typeName FROM resource n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.resource_id=n.id WHERE ' + query.join(' AND ') + ' GROUP BY n.id ORDER BY n.gmt_publish_time DESC LIMIT ' + start + ',' + limit)

    for (let item of list) {
      item.time = moment(item.time).format('YYYY-MM-DD');
      item.isActive = false;
      let tradeList = [],
        serviceList = [];
      let tradeCata = await this.app.mysql.select('catalog_relation', {
        where: {
          resource_id: item.id,
          catalog_type: 1
        }
      });
      let serviceCate = await this.app.mysql.select('catalog_relation', {
        where: {
          resource_id: item.id,
          catalog_type: 2
        }
      });

      if (tradeCata && tradeCata.length > 0) {
        for (let ti of tradeCata) {
          let tradeInfo = await this.app.mysql.get('catalog', {
            id: ti.catalog_id
          });
          tradeList.push(tradeInfo && tradeInfo.name);
        }
      }

      if (serviceCate && serviceCate.length > 0) {
        for (let si of serviceCate) {
          let serviceInfo = await this.app.mysql.get('catalog', {
            id: si.catalog_id
          });
          serviceList.push(serviceInfo && serviceInfo.name);
        }
      }

      item.tradeList = tradeList;
      item.serviceList = serviceList;


      // 文件类型
      let suffix = item.path.substr(item.path.lastIndexOf('.') + 1, item.path.length).toLocaleLowerCase();
      if (suffix == 'jpg' || suffix === 'jpeg' || suffix === 'png' || suffix === 'gif' || suffix === 'bmp' || suffix === 'wbmp' || suffix === 'webp' || suffix === 'tif' || suffix === 'psd') {
        item.fileType = 'image'
      } else if (suffix === 'pdf') {
        item.fileType = 'pdf'
      } else if (suffix === 'gz' || suffix === 'tgz' || suffix === 'gzip' || suffix === '7z' || suffix === 'zip') {
        item.fileType = 'tar'
      } else if (suffix === 'doc' || suffix === 'docx') {
        item.fileType = 'word'
      } else if (suffix === 'xls' || suffix === 'xlsx') {
        item.fileType = 'excel'
      } else if (suffix === 'ppt' || suffix === 'pptx') {
        item.fileType = 'ppt'
      } else if (suffix === 'mp3' || suffix === 'mp4' || suffix === 'avi') {
        item.fileType = 'video'
      } else if (suffix === 'js' || suffix === 'jsx' || suffix === 'json' || suffix === 'css' || suffix === 'less' || suffix === 'html' || suffix === 'htm' || suffix === 'xml') {
        item.fileType = 'code'
      } else if (suffix === 'txt') {
        item.fileType = 'text'
      } else if (suffix === 'cn' || suffix === 'cn/' || suffix === 'com' || suffix === 'com/' || suffix === 'net' || suffix === 'net/' || suffix === 'top' || suffix === 'vip') {
        item.fileType = 'link'
      } else {
        item.fileType = 'other'
      }

      // 获取文件大小
      if (!item.size && item.fileType !== 'link' && item.fileType !== 'other') {
        item.size = await getResult();

        function doRequest(options) {
          return new Promise(function (resolve, reject) {
            request(options, function (error, res, body) {
              if (res && res.statusCode == 200) {
                const size = Number(res.headers['content-length'] / 1024 / 1024).toFixed(2);
                resolve(size);
                const row = {
                  id: item.id,
                  size: size
                }
                // app.mysql.update('resource', row)
              } else {
                resolve(0);
              }
            });
          });
        }

        async function getResult() {
          let options = {
            url: encodeURI(_info.url + item.path),
            method: 'GET'
          }
          let res = await doRequest(options);
          return res;
        }
      }
    }

    return {
      list: list,
      total: total.length,
      page: page,
      limit: limit
    }
  }

  async download(params) {
    const {
      app
    } = this;
    const data = await this.app.mysql.select('resource', {
      where: {
        id: params.id
      }
    });

    // 更新下载次数
    let {
      download_num
    } = data[0]
    if (download_num) {
      download_num += 1
    } else {
      download_num = 1
    }
    // data.download_num = download_num > 999 ? '999+' : download_num
    const row = {
      id: params.id,
      download_num: download_num
    }
    // await app.mysql.update('resource', row)
    return {
      path: data[0]
    }
  }

  async getFileType(id) {
    const {
      app
    } = this;
    const catalogName = await this.app.mysql.select('catalog', {
      where: {
        id
      }
    })
    return {
      fileType: catalogName[0].name
    }
  }
}

module.exports = ResourceService;