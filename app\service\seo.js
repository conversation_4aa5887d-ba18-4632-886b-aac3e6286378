'use strict';

const Service = require('egg').Service;
const moment = require('moment');

// 服务推广
class SeoService extends Service {
  async getSeoList(params) {
    const {
      app
    } = this
    let page = params.page || 1,
      limit = params.limit || 50;
    let start = (page - 1) * limit;
    const list = await app.mysql.query(`SELECT distinct a.id, a.name, a.alias, a.gmt_modify AS time FROM sku a INNER JOIN catalog_relation b ON b.sku_id=a.id where is_delete=0 AND b.catalog_id in (996,997,998,999) ORDER BY a.gmt_create DESC LIMIT ${start}, ${limit}`)
    const total = await app.mysql.query(`SELECT COUNT(distinct a.id) FROM sku a INNER JOIN catalog_relation b ON b.sku_id=a.id where is_delete=0 AND b.catalog_id in (996,997,998,999)`)
    for (let item of list) {
      item.time = moment(item.time).format('YYYY-MM-DD HH:mm:ss');
    }

    return {
      list,
      total: total[0],
      page,
      limit
    }
  }
}

module.exports = SeoService;