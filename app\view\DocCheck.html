<% include header.html %>
  <% include ./components/header.html %>
    <div class="swiper-container"
      style="height: 150px; background:url('<%- locals.static %>/images/DocCheck/banner.png') 50% 50%">
      <h1 class="docCheck-title wrap">SGS报告证书快速在线验证</h1>
      <div class="docCheck-sub-title wrap">
        <span>官方验证</span>
        <span>真伪查询</span>
        <span>即时反馈</span>
      </div>
    </div>
    <div class="docCheck-wrap wrap" id="docCheck" v-cloak>
      <div class="wrap docCheck-content">
        <div class="docCheck-left" v-if="checkType === 0">
          <div class="docCheck-con" id='con'>
            <div class="docCheck-con-box" v-show="tabIndex === 0" v-loading="checkLoading" :element-loading-text="loadingText[loadingTextIndex]">
              <div class="lang-tab">
                <a href="javascript:;" class="is-active">中文</a>
                <a href="/DocCheck/en">EN</a>
              </div>
              <el-form class="form-content" :model="form" :rules="rules" ref="form" label-width="97px"
                label-position="left">
                <el-row :gutter="53">
                  <el-col :span="12">
                    <el-form-item prop="userName" label="您的姓名">
                      <el-input placeholder="请输入查验人姓名" v-model="form.userName" maxlength="20"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="companyName" label="公司名称">
                      <el-input placeholder="请输入查验人公司名称" v-model="form.companyName" maxlength="200"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="userEmail" label="您的邮箱" class="tips-item">
                      <el-input placeholder="请输入查验人邮箱" v-model="form.userEmail" maxlength="80" :disabled="isLogin && userInfo.userEmail !== ''"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="userPhone" label="手机号码" class="phone-item">
                      <el-input placeholder="请输入手机号码" v-model="form.userPhone" maxlength="11" :disabled="isLogin && userInfo.userPhone !== ''">
                        <div slot="prepend" class="phone-prepend">
                          <span>+86</span>
                        </div>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item prop="url" label="上传文件" class="file-upload-item" v-if="checkType === 0">
                  <el-upload class="upload-demo" :show-file-list="false"
                    :action="host + '/ticCenter/business/api.v0.platform/fileUpload/uploadOss'"
                    :before-upload="beforeUpload" :on-success="onSuccess" :on-error="onError" accept="">
                    <el-button size="small" class="upload-btn" v-loading="uploadDisabled">选择文件</el-button>
                  </el-upload>
                  <span v-if="fileList.length" class="file-name">{{fileList[0].name}}</span>
                  <span v-else>请一次上传一份文件</span>
                </el-form-item>
                <div class="upload-tips">
                  请上传完整的文件页数，确保页面清晰，无内容遮盖。PDF、JPG、PNG，ZIP格式文件均可，文件<20M；
                </div>
                <div class="upload-tips" v-if="!isLogin">
                  <el-checkbox v-model="isChecked" @change="handleChangeChecked"></el-checkbox>我已阅读并同意<span
                    @click="dialogVisibleReg = true">注册条款</span>，依照<a
                    href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" target="_blank">SGS在线隐私声明</a>使用我的数据来处理我的请求。
                    <div class="isCheckedTips" v-if="isCheckedTips">请同意并勾选</div>
                </div>
                <div class="upload-tips" v-else>
                  <el-checkbox v-model="isChecked" @change="handleChangeChecked"></el-checkbox>我同意依照<a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" target="_blank">SGS在线隐私声明</a>使用我的数据来处理我的请求。
                  <div class="isCheckedTips" v-if="isCheckedTips">请同意并勾选</div>
                </div>
                <button class="submit-btn el-button el-button--primary" monitor-uac-node="portal.user.tools.0007.verify"
                  @click.prevent="verifyForm(true)">提交验证申请</button>
              </el-form>
            </div>
            <div class="docCheck-con-box" v-show="tabIndex === 1">
              <h2>一、SGS体系认证报告查验方式说明</h2>
              <h3>
                <i></i><em>查验范围：</em>
              </h3>
              <p>查验带有SGS标识的体系认证证书。</p>
              <h3>
                <i></i><em>查验步骤：</em>
              </h3>
              <div class="step clearfix">
                <p><i>1</i><em>填写证书ID/企业名称</em></p>
                <span></span>
                <p><i>2</i><em>勾选企业所在地</em></p>
                <span></span>
                <p><i>3</i><em>在线获取证书信息</em></p>
              </div>
              <p>填写证书信息（证书ID、企业名称、企业所在地），在线获取证书信息：</p>
              <a class="btn" href='https://www.sgs.pl/en/vr/certified-client-directory' target="_blank">验证SGS文件</a>
              <h3>
                <i></i><em>查验周期：</em>
              </h3>
              <p>提交查询后，在线获取证书信息。</p>
              <h2>二、查验表单填写指南</h2>
              <img class="pic-3" src='<%- locals.static %>/images/DocCheck/pic-3.png' />
              <h3>
                <i></i><em>在线获取证书相关信息披露</em>
              </h3>
              <p class='tips'>相关信息披露</p>
              <img class="pic-4" src='<%- locals.static %>/images/DocCheck/pic-4.png' />
            </div>
          </div>
        </div>
        <div class="docCheck-left result" v-if="checkType !== 0" id="checkResult">
          <div class="pdf-result">
            <div class="tips" v-if="checkType === 1">
              <div class="title">验证结果</div>
              <div class="result-title">
                <img src="<%- locals.static %>/images/DocCheck/success.png" alt="">
                您提交的文件是一份真实的SGS文件
              </div>
              <p>文件名称：<span class="red-color">{{form.fileName}}</span></p>
              <div class="sub-tips" style="padding-left: 0;justify-content: center;">
                <div>提示：</div>
                <div>
                  一份真实的SGS文件表明该文件由SGS发布，并且没有被修改。
                </div>
              </div>
            </div>
            <div class="tips" v-if="checkType === 2">
              <div class="title">验证结果</div>
              <div class="result-title">
                <img src="<%- locals.static %>/images/DocCheck/qrCode.png" alt="">
                通过您提交文件上的二维码，识别到SGS原始文件如下：
              </div>
              <p>请您<span class="red-color">自行比对</span>与您手中文件内容的<span class="red-color">一致性</span>。</p>
            </div>
            <div class="tips" v-if="checkType === 3 || checkType === 4">
              <div class="title">验证结果</div>
              <div class="result-title" v-if="checkType === 3">
                <img src="<%- locals.static %>/images/DocCheck/error.png" alt="">
                未匹配到您提交的文件
              </div>
              <div class="result-title" v-if="checkType === 4">
                <img src="<%- locals.static %>/images/DocCheck/email.png" alt="">
                已发送至人工查验
              </div>
              <p>已发送文件 <span class="red-color">{{form.fileName}}</span> 至人工进行查验，</p>
              <p>您将通过电子邮箱：{{form.userEmail}}收到反馈结果。</p>
              <div class="sub-tips">
                <div>提示：</div>
                <div>
                  1. SGS大陆出具的报告2个工作日反馈，SGS香港、澳门、台湾地区以及海外报告预计5-7个工作日； <br>
                  2. 同一份报告请勿多次重复提交验证。
                </div>
              </div>
            </div>
            <div class="tips" v-if="checkType === 7">
              <div class="title">验证结果</div>
              <div class="result-title">
                <img src="<%- locals.static %>/images/DocCheck/error.png" alt="">
                您提交的文件非SGS原始文件
              </div>
              <p>您提交的文件 <span class="red-color">{{form.fileName}}</span> 不是SGS的原始文件。</p>
              <p>这份文件无论如何没有价值，我们建议您不要在任何用途使用。</p>
              <div class="sub-tips">
                <div style="color: red;font-weight: bold;">
                  鉴于这份文件的发布和使用构成了对SGS商标的歪曲、滥用和误用，请您不要使用或发布这份文件给任何第三方。
                </div>
              </div>
            </div>
            <template v-if="checkType === 5 || checkType === 6">
              <div class="tips">
                <div class="title">验证结果</div>
                <div class="result-title">
                  <img src="<%- locals.static %>/images/DocCheck/qrCode.png" alt="">
                  通过您提交文件上的二维码，识别到SGS原始文件{{checkType === 5 ? '已失效' : '已取消'}}
                </div>
              </div>
              <div>
                <div class="pdf-tips">以下为扫码结果：</div>
                <div class="invalidated-tips" v-if="checkType === 5 ">
                  This report has been invalidated <br>
                  此报告已失效。
                </div>
                <div class="invalidated-tips" v-else>
                  请注意，此报告已取消! <br>
                  Unauthorized Access!
                </div>
              </div>
            </template>
            <div v-if="checkType === 2&&reload">
              <div class="pdf-tips">以下为SGS原始文件：</div>
              <iframe :src="`<%- locals.static %>/pdfjs/web/viewer.html?file=${pdfUrl}`" frameborder="0" width="100%"
                height="600"></iframe>
            </div>
            <el-button class="submit-btn" type="primary" @click="goCheck">验证其他报告</el-button>
          </div>
        </div>
        <div class="docCheck-right">
          <div class="title">
            <span>帮助中心</span>
            <a href="https://portal.sgsonline.com.cn/question/64" class="more-link" target="_blank">查看更多></a>
          </div>
          <ul>
            <li><a href="https://portal.sgsonline.com.cn/question/65" target="_blank">在线查验的流程是什么？</a></li>
            <li><a href="https://portal.sgsonline.com.cn/question/64" target="_blank">报告查验范围有哪些？</a></li>
            <li><a href="https://portal.sgsonline.com.cn/question/64" target="_blank">有多份报告如何批量查验？</a></li>
            <li><a href="https://portal.sgsonline.com.cn/question/64" target="_blank">为什么转人工查验，周期是多久？</a></li>
            <li><a href="https://portal.sgsonline.com.cn/question/64" target="_blank">“此报告已失效”是什么情况？</a></li>
          </ul>
          <div class="other-page">
            <img src="<%- locals.static %>/images/DocCheck/otherPage.png" alt="">
            <a href="/certified-client-directory" target="_blank">体系认证证书查验</a>
          </div>
          <div class="contact-text">
            <img src="<%- locals.static %>/images/DocCheck/tel.png" alt="">
            <p>如遇任何问题，请联系我们</p>
            <p style="margin: 16px 0;">
              <span>客服热线：</span>
              <span style="color:#CA4300;">4000-558-581</span>
            </p>
            <p>
              <span>工作时间：</span>
              <span>
                周一~周六 9:00至17:00 <br>
                <span style="margin-top: 3px;display: inline-block">(节假日除外)</span>
              </span>
            </p>
          </div>
        </div>
      </div>
      <el-dialog class="docCheck-dialog" :visible.sync="confirmDialog" width="500px" :close-on-click-modal="false"
        :close-on-press-escape="false" :show-close="false" v-if="confirmDialog">
        <img src="<%- locals.static %>/images/DocCheck/close.png" alt="" @click="confirmDialog = false">
        <div style="padding: 0 60px;">您上传的文件为ZIP文件包，将发送至SGS人工查验，
          如需在线查验，请上传PDF、JPG或PNG文件。</div>
        <div style="margin: 22px 0 33px;">是否继续？</div>
        <div class="dialog-footer">
          <el-button @click="handleConfirm">是</el-button>
          <el-button type="primary" @click="confirmDialog = false">否</el-button>
        </div>
      </el-dialog>
      <el-dialog class="docCheck-dialog" :visible.sync="personCheckDialog" width="500px" :close-on-click-modal="false"
                 :close-on-press-escape="false" :show-close="false" v-if="personCheckDialog">
        <img src="<%- locals.static %>/images/DocCheck/close.png" alt="" @click="personCheckDialog = false">
        <div>您上传的文件将发送至SGS人工查验。</div>
        <div class="dialog-tips">
          <div>提示：</div>
          <div>
            <div>1、SGS大陆出具的报告2个工作日反馈；</div>
            <div>2、SGS香港、澳门、台湾地区以及海外报告预计5-7个工作日</div>
          </div>
        </div>
        <div class="dialog-footer">
          <el-button @click="personCheckDialog = false">取消</el-button>
          <el-button type="primary" @click="handlerCheck(recordId)">确认</el-button>
        </div>
      </el-dialog>
      <% include ./components/login.html %>
        <!-- 注册协议 -->
        <% include ./components/registionClause.html %>
          <!-- 获取验证码弹窗 -->
      <tic-login-verify-code ref="loginVerifyCode" :env="env" :captcha-app-id="captchaAppId" v-model="showModal" :captcha-show="captchaShow" :number="form.userPhone" :data="pageInfo" :login-success="loginSuccess" :verify-params="verifyParams" />
    </div>
    <script uac_busitype="user" uac_busisubtype="tools" uac_busicode="0007"></script>
    <% if(locals.env !='prod' ){ %>
      <script src="<%- locals.static %>/plugin/v2.test.js"></script>
      <% }else{ %>
        <script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js"
          type="text/javascript"></script>
        <% } %>
          <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
          <script src="<%- locals.static %>/js/md5.js"></script>
          <script>
            const validatorUserPhone = (rule, value, callback) => {
              const reg = /^1[2|3|4|5|6|7|8|9][0-9]\d{8}$/
              if (!value) {
                return callback(new Error('请输入手机'));
              } else if (!reg.test(value)) {
                return callback(new Error('请输入正确的手机号码'));
              } else {
                return callback()
              }
            }
            const validatorEmail = (rule, value, callback) => {
              const reg = /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/
              if (!value) {
                return callback(new Error('请输入邮箱'));
              } else if (!reg.test(value)) {
                return callback(new Error('请输入正确的邮箱'));
              } else {
                return callback()
              }
            }
            const session = {
              set(key, val) {
                if (window) typeof val == 'string' ? window.sessionStorage.setItem(key, val) : window.sessionStorage.setItem(key, JSON.stringify(val))
              },
              get(key) {
                if (window) {
                  let val = window.sessionStorage.getItem(key)
                  try {
                    return typeof val == 'string' ? JSON.parse(val) : val
                  } catch (e) {
                    return val
                  }
                }
              },
              clear() {
                window.sessionStorage.clear()
              },
              remove(key) {
                window.sessionStorage.removeItem(key)
              }
            }

            var newVue = new Vue({
              name: 'docCheck',
              el: '#docCheck',
              data: {
                showLoginModal: false,
                isRedirect: 1,
                domain: '<%- domain %>',
                tabs: ['检测报告查验', '体系认证报告查验'],
                tabIndex: <%- tabIndex %>,
                form: {
                  userName: '',
                  userPhone: '',
                  userEmail: '',
                  companyName: '',
                  url: '',
                  fileName: '',
                  verifyCode: ''
                },
                rules: {
                  userName: [{ required: true, message: '请填写您的姓名' }],
                  userPhone: [{ required: true, message: '请填写手机号码' }, { validator: validatorUserPhone }],
                  userEmail: [{ required: true, message: '请填写您的邮箱' }, { validator: validatorEmail }],
                  url: [{ required: true, message: '请上传文件' }],
                },
                fileList: [],
                checkRegister: false,
                tipsVisible: false,
                checkType: 0,
                appendToBody: true,
                closeBtn: true,
                env: '<%- env %>',
                // env: 'test',
                parameter: {
                  regSource: 'mall',
                  regMode: 'USER_REG',
                  regFrom: location.href
                },
                checkLoading: false,
                pdfUrl: '',
                host: '<%- host %>',
                memberUrl: '<%- memberUrl %>',
                reportNo: '',
                uploadDisabled: false,
                isLogin: <%- isLogin %>,
                userInfo: {
                  userPhone: '<%- userInfo.userPhone %>',
                  userEmail: '<%- userInfo.userEmail %>',
                  userName: '<%- userInfo.userNick %>',
                  companyName: '<%- userInfo.companyName %>'
                },
                pid: 'pid.mall',
                pcode: 'Z0zCnRE3IaY9Kzem',
                reload: true,
                isConfirm: false,
                confirmDialog: false,
                /* 新流程相关交互 */
                dialogVisibleReg: false,
                isChecked: false,
                showModal: false,
                seconds: 59,
                timer: null,
                loading: false,
                pageInfo: {
                  title: '验证手机号',
                  sendModel: '已发送到手机号：+86',
                  prepend: '手机验证码'
                },
                captchaAppId: '<%- captchaAppId %>',
                isCheckedTips: false,
                loadingText: [
                  '正在上传文件',
                  '正在上传文件.',
                  '正在上传文件..',
                  '正在上传文件...',
                  '正在解析文件数据',
                  '正在解析文件数据.',
                  '正在解析文件数据..',
                  '正在解析文件数据...',
                  '正在匹配在线数据库',
                  '正在匹配在线数据库.',
                  '正在匹配在线数据库..',
                  '正在匹配在线数据库...',
                  '正在生成验证结果',
                  '正在生成验证结果.',
                  '正在生成验证结果..',
                  '正在生成验证结果...',
                ],
                loadingTextIndex: 0,
                captchaShow: true,
                verifyParams: {
                  projectName: 'TIC_TEMPLATE',
                  verifyType: 3
                },
                recordId: '',
                personCheckDialog: false
              },
              computed: {
                isZip() {
                  return /\.(zip)$/.test(this.form.fileName.toLowerCase())
                }
              },
              watch: {
                checkLoading(val) {
                  let timer
                  if (val) {
                    timer = setInterval(() => {
                      if (this.loadingTextIndex < this.loadingText.length - 1) {
                        this.loadingTextIndex += 1
                      } else {
                        this.loadingTextIndex -= 3
                      }
                    }, 1000)
                  } else {
                    if (timer) clearInterval(timer)
                    this.loadingTextIndex = 0
                  }
                }
              },
              created() {
                if (location.pathname === '/certified-client-directory') {
                  this.tabIndex = 1
                  return
                }
                console.log('created-------------------', this.userInfo, this.form)
                if (this.userInfo.userPhone || this.userInfo.userEmail) {
                  this.form.userPhone = this.userInfo.userPhone
                  this.form.userEmail = this.userInfo.userEmail
                  this.form.userName = this.userInfo.userName
                  this.form.companyName = this.userInfo.companyName
                }
                this.form = JSON.parse(JSON.stringify(this.form))
                const isZip = session.get('isZip')
                const form = session.get('checkForm')
                const data = session.get('checkResult')
                session.remove('checkForm')
                session.remove('checkResult')
                session.remove('isZip')
                if (form) {
                  // this.form = Object.assign(form, this.form)
                  this.form = Object.assign(this.form, form)
                  this.form.fileName = form.fileName
                }
                if (isZip) {
                  this.checkType = 4
                  return
                }
                if (!data) {
                  this.checkType = 0
                  return
                }
                if (data.success && data.qryType === 1) {
                  this.checkLoading = true
                  if (data.success) {
                    this.checkType = data.qryType === 0 ? 1 : 2
                  } else {
                    this.checkType = data.failType === 1 ? 5 : data.failType === 3 ? 6 : data.failType === 4 ? 7 : 3
                  }
                  this.reportNo = data.reportNo
                  this.getBlob(data)
                } else {
                  this.setCheckResult(data)
                }
              },
              methods: {
                phoneDialog() {
                  phoneDialog()
                },
                sing: function (param, timestamp) {
                  var pmd5 = md5((this.pid + this.pcode).toUpperCase());
                  var str = JSON.stringify(param) + pmd5.toUpperCase();
                  return md5(str + timestamp);
                },
                setCheckResult(data, url) {
                  this.checkLoading = false
                  if (data.success) {
                    this.checkType = data.qryType === 0 ? 1 : 2
                  } else {
                    this.checkType = data.failType === 1 ? 5 : data.failType === 3 ? 6 : data.failType === 4 ? 7 : 3
                  }
                  this.reportNo = data.reportNo
                  this.recordId = data.recordId
                  this.pdfUrl = url
                  document.body.scrollTop = document.documentElement.scrollTop = 0;
                  if (url) {
                    this.reload = false
                    this.$nextTick(() => this.reload = true)
                  }
                },
                handleConfirm() {
                  this.isConfirm = true
                  this.confirmDialog = false
                  this.verifyForm()
                },
                verifyForm(needLogin) {
                  this.$refs.form.validate((valid) => {
                    if (valid) {
                      if (this.isZip && !this.isConfirm) {
                        this.confirmDialog = true
                        return
                      }
                      if (!this.isChecked) {
                        // this.$message.error('请勾选同意注册条款')
                        this.isCheckedTips = true
                        return
                      } else {
                        this.isCheckedTips = false
                      }
                      if (!this.isLogin && needLogin) {
                        this.showModal = true
                      } else {
                        this.handlerCheck()
                      }
                    }
                  })
                },
                handlerCheck(recordId) {
                  this.checkLoading = true
                  var that = this
                  var timestamp = new Date().valueOf();
                  let accessToken = Cookies.get('SSO_TOKEN') || '';
                  const url = recordId ? '/ticMember/business/api.v2.user/tool/updateReportRecord' : this.isZip ? '/ticMember/business/api.v2.user/tool/reportAuthSaveRecordByZip' : '/ticMember/business/api.v2.user/tool/reportAuthSaveRecord'
                  const params = JSON.parse(JSON.stringify(this.form))
                  params.recordId = recordId
                  $.ajax({
                    type: 'POST',
                    url: this.host + url,
                    data: JSON.stringify(params),
                    contentType: 'application/json',
                    headers: {
                      pid: this.pid,
                      sign: this.sing(params, timestamp),
                      timestamp: timestamp,
                      frontUrl: window.location.href,
                      accessToken,
                      appId: '99999999',
                      lang: 'zh_CN'
                    },
                    success: function (res) {
                      if (res.resultCode === '0') {
                        that.personCheckDialog = false
                        if (location.pathname + location.hash === '/DocCheck#topbar') {
                          if (that.isZip || recordId) {
                            that.checkLoading = false
                            that.checkType = 4
                            return
                          }
                          if (res.data.success && res.data.qryType === 1) {
                            that.getBlob(res.data)
                          } else {
                            that.setCheckResult(res.data)
                          }
                          return
                        }
                        const form = { ...that.form }
                        form.url = ''
                        session.set('checkResult', res.data)
                        session.set('checkForm', form)
                        if (that.isZip) session.set('isZip', true)
                        location.href = `/DocCheck#topbar`
                        setTimeout(() => {
                          location.reload()
                        })
                      } else {
                        that.checkLoading = false
                        if (res && res.resultCode === '9978') {
                          that.showModal = true
                        } else {
                          that.$message.error(res.resultMsg)
                        }
                      }
                    },
                    fail: function (error) {
                      console.log(error);
                      that.$message.error('网络异常')
                    },
                    complete: function (complete) {
                    }
                  })
                },
                getBlob(datas) {
                  const params = {
                    url: datas.qrCode,
                    name: '报告.pdf',
                  }
                  var timestamp = new Date().valueOf();
                  axios({
                    data: params,
                    url: this.host + '/ticCenter/business/api.v0.platform/fileUpload/getFileStream',
                    method: "post",
                    responseType: "blob",
                    headers: {
                      pid: this.pid,
                      sign: this.sing(params, timestamp),
                      timestamp: timestamp,
                      frontUrl: window.location.href,
                      appId: '99999999'
                    },
                  }).then(res => {
                    console.log(res);
                    let data = res.data;
                    console.log(data);
                    let fileReader = new FileReader();
                    fileReader.onload = (e) => {
                      try {
                        this.checkLoading = false
                        let jsonData = JSON.parse(e.target.result);  // 说明是普通对象数据，后台转换失败
                        this.$message.error(jsonData.resultMsg || '网络异常')
                      } catch (err) {   // 解析成对象失败，说明是正常的文件流
                        let url
                        if (res && res.data && res.data.size) {
                          var blob = new Blob([res.data], {
                            type: "application/actet-stream;charset=utf-8"
                          });
                          // @ts-ignore
                          if (window.createObjectURL != undefined) { // basic
                            // @ts-ignore
                            url = window.createObjectURL(blob)
                          } else if (window.webkitURL != undefined) { // webkit or chrome
                            url = window.webkitURL.createObjectURL(blob);
                          } else if (window.URL != undefined) { // mozilla(firefox)
                            url = window.URL.createObjectURL(blob);
                          }
                        } else {
                          this.$message.error('网络异常')
                        }
                        this.setCheckResult(datas, url)
                      }
                    }
                    fileReader.readAsText(data)
                  }).catch(e => {
                    this.checkLoading = false
                  });
                },
                // 继续验证
                goCheck() {
                  // this.fileList = []
                  // this.form.url = ''
                  // this.checkType = 0
                  // document.body.scrollTop = document.documentElement.scrollTop = 0;
                  // this.isConfirm = false
                  window.location.reload()
                },
                // 登录成功后的回调
                // successCallback() {
                //   this.showLoginModal = false
                //   this.isLogin = true
                //   this.handlerCheck(false)
                //   reloadHeader()
                // },
                // 关闭登录弹窗
                handleCloseLogin() {
                  this.showLoginModal = false
                },
                beforeUpload(file) {
                  const isLt10M = file.size / 1024 / 1024 < 20;
                  let isImg = true
                  if (!/\.(jpg|jpeg|png|pdf|zip)$/.test(file.name.toLowerCase())) {
                    this.$message.error('请上传PDF,JPG，PNG,ZIP文件');
                    isImg = false
                  }

                  if (!isLt10M) {
                    this.$message.error('上传文件大小不能超过 20MB!');
                  }
                  if (isLt10M && isImg) {
                    this.uploadDisabled = true
                  }
                  return isLt10M && isImg;
                },
                onSuccess(res, file, fileList) {
                  this.uploadDisabled = false
                  if (res.resultCode === "0") {
                    this.fileList = [{
                      name: file.name,
                      url: res.data.fileName
                    }]
                    this.form.url = res.data.fileName
                    this.form.fileName = file.name
                  } else {
                    this.$message.error(res.resultMsg)
                  }
                },
                onError(res, file, fileList) {
                  this.uploadDisabled = false
                  this.$message.error('网络异常')
                },
                loginSuccess(){
                  this.isLogin = true
                  reloadHeader()
                  if (this.personCheckDialog) {
                    this.handlerCheck(this.recordId)
                    return
                  }
                  this.handlerCheck()
                },
                handleChangeChecked: function (val) {
                  if (val) this.isCheckedTips = false
                }
              },
            })
          </script>

          <% include footer.html %>
