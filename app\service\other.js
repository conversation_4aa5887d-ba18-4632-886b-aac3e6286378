'use strict';

const Service = require('egg').Service;
class OtherService extends Service {
  async getDetail(id) {
    const {
      app
    } = this;
    const detail = await app.mysql.get('other', {
      id: id,
      is_delete: 0
    });

    if (detail.content) {
      detail.content = detail.content.replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
    }

    return {
      detail
    }
  }

  async getDetailByAlias(id) {
    const {
      app
    } = this;
    const detail = await app.mysql.get('other', {
      alias: id,
      is_delete: 0
    });

    return {
      detail
    }
  }
}

module.exports = OtherService;