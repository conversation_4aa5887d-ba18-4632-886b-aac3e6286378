const Controller = require('egg').Controller;
const util = require('./util');
const {
  mallPid,
  mallPcode,
  env
} = require('../../config/info').siteInfo;
const captchaAppId = '193600977'

class ActiveController extends Controller {
  async fangyi() {
    const {
      ctx
    } = this;

    const host = await util.getHost(this.app.locals.env, ctx);
    const tokenUrl = await util.getTokenUrl(this.app.locals.env);
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();
    let detail = {
      name: '疫情防护用品检测认证'
    };
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('promotion/fangyi', {
      cap<PERSON><PERSON><PERSON><PERSON><PERSON>d,
      is<PERSON>hatbot,
      is<PERSON><PERSON><PERSON><PERSON>,
      is<PERSON><PERSON><PERSON><PERSON>,
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      hotKeyword,
      hotKeyword: hotKeyword.list,
      host,
      tokenUrl,
      detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      csrf: ctx.csrf,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
    })
  }

  async fangyiEn() {
    const {
      ctx
    } = this;

    const host = await util.getHost(this.app.locals.env, ctx);
    const tokenUrl = await util.getTokenUrl(this.app.locals.env);
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();
    let detail = {
      name: 'PPE-service'
    };
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('en/fangyi', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      hotKeyword,
      hotKeyword: hotKeyword.list,
      host,
      tokenUrl,
      detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      csrf: ctx.csrf,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
    })
  }
}

module.exports = ActiveController;
