$(function () {

    var $_drag = $(".drag");
    var dragH;
    for(var i=0;i<$_drag.length;i++){
        dragH = $_drag.eq(i).height();
        $(".navBor").eq(i).css("height",dragH);
    }

    var $_serverConLi = $(".show>.serverConLi");
    for(var i=0;i<$_serverConLi.length;i++){
        if(i==3 || i==7 || i==11){
            $_serverConLi.eq(i).addClass("serverRight0");
        }
    }

    var $_solutionConLi = $(".solutionConLi");

    for(var i=0;i<$_solutionConLi.length;i++){
        if(i==1 || i==3){
            $_solutionConLi.eq(i).addClass("serverRight0");
        }
    }

    var $_resourceConLi = $(".resourceConLi");
    for(var i=0;i<$_resourceConLi.length;i++){
        if(i==2 || i==5){
            // alert(1);
            $_resourceConLi.eq(i).addClass("serverRight0");
        }else if(i==3 || i==7){
            $_resourceConLi.eq(i).css("marginLeft","14px");
        }
    }

    var $_recomentLi = $(".recomentLi");

    for(var i=0;i<$_recomentLi.length;i++){

        if(i==2 || i==5){
            // alert(1);
            $_recomentLi.eq(i).addClass("serverRight0");
        }
    }
//
    $(window).resize(function () {
        var banner = null
    })

    //banner('.bannerUl>li','active','.point>li','.bannerLeft','.bannerRight');
    function banner(a,b,c,d,e){    // a是背景图，b是active，c是背景对应按钮，d是上一页，e是下一页
        var width = $(a).width();
        index=0;
        len=$(a).length-1;

        function teb(index){

            $(c).eq(index).addClass(b).siblings("").removeClass(b);
            $(".bannerUl").animate({marginLeft:-width*index});
        };
        $(c).click(function(){
            index=$(this).index();
            teb(index);
        });
        $(d).click(function(){
            index--;
            if(index<0){
                index=len;
            };
            teb(index);
        });
        $(e).click(function(){
            index++;
            if(index>len){
                index=0;
            };
            teb(index);
        });
        function timeRun(){
            time=setInterval(function(){
                index++;
                if(index>len){
                    index=0;
                };
                teb(index);
            },5000);
        };
        timeRun();
    };

    var $_bor = $(".bor");

    $(".server .serverLi").click(function () {
        $(this).addClass("serverLiActive").siblings(".serverLi").removeClass("serverLiActive");
        // var $_serverli = $(this);
        var $_con = $(".serverCon ul");
        var index = $(this).index();

        $_con.eq(index).addClass("show").siblings('.serverCon>ul').removeClass("show");

        var $_serverConLi = $(".show>.serverConLi");
        for(var i=0;i<$_serverConLi.length;i++){
            if(i==3 || i==7){
                $_serverConLi.eq(i).addClass("serverRight0");
            }
        }

        $_bor.animate({left:138*index});
        // for(var i=0;i<5;i++){
        //     $_con.eq(i).css({"z-index":10}).siblings($_con).css({"z-index":1});
        // }
    })

    $('.pagesLi').click(function () {
        $(this).addClass("pagesLiActive").siblings(".pagesLi").removeClass("pagesLiActive");
    })
    //导航js

    var $_nav = $(".nav");
    var sc = $(document);

    // var $_nav1 = $(".nav");
    $(window).scroll(function () {

        if(sc.scrollTop()>=110){

            $_nav.addClass("fix");
            // $_nav1.addClass("fix");
        }else{

            $_nav.removeClass("fix");
            // $_nav1.removeClass("fix");

        }
    })

    var out;

    $('.navUl>li').hover(function(){

        clearTimeout(out);
        var $this = $(this),$c = $(this).children('ul');

        out = setTimeout(function(){
            $c.stop().fadeIn(300);
        },100);
    },function(){
        clearTimeout(out);
        var $this = $(this),$c = $(this).children('ul');
        $c.stop().fadeOut(300);
    })



})
