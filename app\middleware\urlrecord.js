module.exports = () => {
  return async function urlrecord(ctx, next) {
    await next();

    const {
      url,
      ip
    } = ctx.request
    const source = ctx.request.header.referer || 'unknow';

    const row = {
      url,
      ip,
      source
    }
    const flitterArr = Object.values(row.toString())
    let fillterResult = flitterArr.some((item => {
      return fillter(item.toLowerCase());
    }))

    function fillter(str) {
      let flag = false;
      if (str.includes('script') || str.includes('select') || str.includes('insert') || str.includes('update') || str.includes('delete') || str.includes('truncate') || str.includes('src')) {
        flag = true;
      }
      return flag;
    }
    // if (!fillterResult) ctx.app.mysql.insert('analysis_entry', row);
    // if (!fillterResult) await ctx.service.external.saveEntry(row)
  };
};