<% include header.html %>
<% include ./components/header.html %>
<!-- 面包屑 -->
<style>
  body,
  html {
    background: #fafafa;
  }
</style>
<div id='foodSafeguard' v-cloak>
  <div class="food_safe_banner">
    农药、兽药残留限量快速查询
  </div>
  <div class="food_safe_tab clearfix">
    <ul>
      <li @click='handleChangeTab(tab.standardId, index)' v-for='(tab, index) of tabs' :key='index'
        :class="{active: activeTab === index}">{{ tab.standardName }}</li>
    </ul>
  </div>
  <div class="food_safe_wrap">
    <div class="food_safe_search clearfix">
      <el-cascader ref="cascader" v-model="value" :options="options" @change="handleChange" :props="props"
        placeholder="请选择" clearable></el-cascader>
      <el-button type="primary" class="food_safe_search_btn" @click="queryList">点击查询</el-button>
    </div>
    <div class="food_safe_link">您可以访问
      <a target="_blank" href="/industry/food/">食品与农产品板块</a>
      ，了解更多服务咨询</div>
    <div class="food_safe_tips" v-if='roleInfo'>{{roleInfo}}</div>
    <div class="food_safe_table" v-if="list.length">
      <el-table :data="list" border v-if="standardId === 1">
        <el-table-column type="index" label="序号" :width="56"></el-table-column>
        <el-table-column prop="testNameCn" label="Chinese Name"></el-table-column>
        <el-table-column prop="testNameEn" label="English Name"></el-table-column>
        <el-table-column prop="testQuota" :label="label"></el-table-column>
      </el-table>
      <el-table :data="list" border v-if="standardId === 2">
        <el-table-column type="index" label="序号" :width="56"></el-table-column>
        <el-table-column prop="testNameCn" label="中文名"></el-table-column>
        <el-table-column prop="testNameEn" label="英文名"></el-table-column>
        <el-table-column prop="testQuota" :label="label"></el-table-column>
      </el-table>
      <el-table :data="list" border v-if="standardId === 3">
        <el-table-column type="index" label="序号" :width="56"></el-table-column>
        <el-table-column prop="testNameEn" label="Chemical Name"></el-table-column>
        <el-table-column prop="testNameCn" label="Chinese Name"></el-table-column>
        <el-table-column :label="item.objectName" v-for="(item, index) in label" :key="index"
          :width="label.length === 1 ? 217 : ''">
          <template slot-scope="scope">
            {{allList[index][scope.$index].testQuota}}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</div>
<script src="<%- locals.static %>/js/md5.js"></script>
<script>
  var pid = 'pid.mall',
    pcode = 'Z0zCnRE3IaY9Kzem'
  var newVue = new Vue({
    name: 'newsList',
    el: '#foodSafeguard',
    data: {
      options: [],
      tabs: [],
      value: [],
      host: '<%- host %>',
      props: {
        label: 'objectName',
        value: 'objectId',
        leaf: 'isEnd',
        lazy: true,
        // checkStrictly: true,
        lazyLoad: function (node, resolve) {
          if (node.level === 0) {
            return
          }
          debugger
          var objectId = node.data.objectId
          var standardId = node.data.standardId
          var qryType = standardId === 3 ? 2 : 1
          newVue.qryChild(objectId, standardId, qryType, resolve)
        }
      },
      list: [],
      roleInfo: '',
      loading: true,
      standardId: 0,
      label: '',
      allList: [],
      activeTab: 0
    },
    created: function () {
      this.qryLevel1()
    },
    methods: {
      handleChangeTab: function(id, index) {
        // this.activeTab = index
        // this.standardId = id
        // this.options = []
        // this.list = []
        // this.allList = []
        // this.roleInfo = ''
        // this.qryChildOption()
        if (index === 0) {
          window.location.href = 'nycl'
        } else if (index === 1) {
          window.location.href = 'shoucan'
        } else if (index === 2) {
          window.location.href = 'teanycl'
        }
      },
      handleChange: function (value) { },
      sing: function (param, timestamp) {
        var pmd5 = md5((pid + pcode).toUpperCase());
        var str = JSON.stringify(param) + pmd5.toUpperCase();
        return md5(str + timestamp);
      },
      qryLevel1: function () {
        var param = {}
        var timestamp = new Date().valueOf();
        var that = this
        $.ajax({
          type: 'POST',
          url: this.host + '/ticCenter/business/api.v1.center/testobject/qryStandard',
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: pid,
            sign: this.sing(param, timestamp),
             timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            that.loading = false
            if (res.resultCode === '0') {
              that.tabs = res.data.items
              var pathname = window.location.pathname.substr(1, window.location.pathname.length);
              var index = 0;
              if (pathname === 'nycl') {
                that.activeTab = index = 0
              } else if (pathname === 'shoucan') {
                that.activeTab = index = 1
              } else if (pathname === 'teanycl') {
                that.activeTab = index = 2
              }
              that.standardId = res.data.items[index].standardId
              that.qryChildOption()
            } else {
              alert(data.resultMsg)
            }
          },
          fail: function (data) {
            that.loading = false
            alert(data.resultMsg)
          },
          complete: function (complete) {
          }
        })
      },
      qryChildOption: function () {
        var param = {
          parentId: 0,
          qryType: this.standardId === 3 ? 2 : 1,
          standardId: this.standardId
        }
        var timestamp = new Date().valueOf();
        var that = this
        $.ajax({
          type: 'POST',
          url: this.host + '/ticCenter/business/api.v1.center/testobject/qryObject',
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: pid,
            sign: this.sing(param, timestamp),
             timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            if (res.resultCode === '0') {
              that.options = res.data.items
            } else {
              alert(data.resultMsg)
            }
          },
          fail: function (data) {
            alert(data.resultMsg)
          },
          complete: function (complete) {
          }
        })
      },
      qryChild: function (parentId, standardId, qryType, resolve) {
        var param = { standardId: standardId, parentId: parentId, qryType: qryType }
        var timestamp = new Date().valueOf();
        var that = this
        $.ajax({
          type: 'POST',
          url: this.host + '/ticCenter/business/api.v1.center/testobject/qryObject',
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: pid,
            sign: this.sing(param, timestamp),
             timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            if (res.resultCode === '0') {
              resolve(res.data.items)
            } else {
              alert(data.resultMsg)
              resolve([])
            }
          },
          fail: function (data) {
            alert(data.resultMsg)
          },
          complete: function (complete) {
          }
        })
      },
      queryList: function () {
        var checkedNodes = this.$refs.cascader.getCheckedNodes()
        if (!checkedNodes || !checkedNodes.length) {
          alert('请选择')
          return
        }
        var checkedNode = checkedNodes[0].data
        var param = {
          objectId: checkedNode.objectId,
          objectItem: [{ objectId: checkedNode.objectId }],
          parentId: checkedNode.parentId,
          standardId: checkedNode.standardId
        }
        debugger
        if (checkedNode.standardId === 3) {
          param.objectItem = []
          param.parentId = checkedNode.objectId
          if (checkedNode.objects) {
            for (var i = 0; i < checkedNode.objects.length; i++) {
              param.objectItem.push({
                objectId: checkedNode.objects[i].objectId
              })
            }
          } else {
            param.objectItem = [{ objectId: checkedNode.objectId }]
          }
        }
        var timestamp = new Date().valueOf();
        var that = this
        this.loading = true
        $.ajax({
          type: 'POST',
          url: this.host + '/ticCenter/business/api.v1.center/testquota/qryQuota',
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: pid,
            sign: this.sing(param, timestamp),
             timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            that.loading = false
            if (res.resultCode === '0') {
              that.roleInfo = checkedNode.ruleInfo
              that.standardId = checkedNode.standardId
              that.list = res.data.items[0]
              that.allList = res.data.items
              if (that.standardId === 3) {
                that.label = checkedNode.objects
              } else {
                that.label = checkedNode.objectName
              }
              console.log(that.list)
            } else {
              that.list = []
              alert(data.resultMsg)
            }
          },
          fail: function (data) {
            that.loading = false
            that.list = []
            alert(data.resultMsg)
          },
          complete: function (complete) {
          }
        })
      }
    }
  })
</script>

<% include footer.html %>