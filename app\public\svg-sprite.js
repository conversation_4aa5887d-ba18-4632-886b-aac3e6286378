/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./app/web/icons sync recursive .svg$":
/*!***********************************!*\
  !*** ./app/web/icons/ sync .svg$ ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var map = {\n\t\"./arrow-down-static.svg\": \"./app/web/icons/arrow-down-static.svg\",\n\t\"./arrow-up-static.svg\": \"./app/web/icons/arrow-up-static.svg\",\n\t\"./car.svg\": \"./app/web/icons/car.svg\",\n\t\"./car_static.svg\": \"./app/web/icons/car_static.svg\",\n\t\"./checked.svg\": \"./app/web/icons/checked.svg\",\n\t\"./home-bottom-1.svg\": \"./app/web/icons/home-bottom-1.svg\",\n\t\"./home-bottom-2.svg\": \"./app/web/icons/home-bottom-2.svg\",\n\t\"./home-bottom-3.svg\": \"./app/web/icons/home-bottom-3.svg\",\n\t\"./home-bottom-4.svg\": \"./app/web/icons/home-bottom-4.svg\",\n\t\"./home_menu_icon.svg\": \"./app/web/icons/home_menu_icon.svg\",\n\t\"./home_side_portrait.svg\": \"./app/web/icons/home_side_portrait.svg\",\n\t\"./icon-1.svg\": \"./app/web/icons/icon-1.svg\",\n\t\"./inquiry_static.svg\": \"./app/web/icons/inquiry_static.svg\",\n\t\"./oiq_btn.svg\": \"./app/web/icons/oiq_btn.svg\",\n\t\"./oiq_search.svg\": \"./app/web/icons/oiq_search.svg\",\n\t\"./phone_icon.svg\": \"./app/web/icons/phone_icon.svg\",\n\t\"./report_static.svg\": \"./app/web/icons/report_static.svg\",\n\t\"./shearch_icon.svg\": \"./app/web/icons/shearch_icon.svg\",\n\t\"./slogan-icon.svg\": \"./app/web/icons/slogan-icon.svg\",\n\t\"./title_car.svg\": \"./app/web/icons/title_car.svg\",\n\t\"./title_oiq.svg\": \"./app/web/icons/title_oiq.svg\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"./app/web/icons sync recursive .svg$\";\n\n//# sourceURL=webpack://ticview/./app/web/icons/_sync_.svg$?");

/***/ }),

/***/ "./app/web/index.js":
/*!**************************!*\
  !*** ./app/web/index.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("const req = __webpack_require__(\"./app/web/icons sync recursive .svg$\");\r\nconsole.log(req);\r\nconst requireAll = requireContext => requireContext.keys().map(name => requireContext(name));\r\nrequireAll(req);\n\n//# sourceURL=webpack://ticview/./app/web/index.js?");

/***/ }),

/***/ "./node_modules/svg-baker-runtime/browser-symbol.js":
/*!**********************************************************!*\
  !*** ./node_modules/svg-baker-runtime/browser-symbol.js ***!
  \**********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("(function (global, factory) {\n\t true ? module.exports = factory() :\n\t0;\n}(this, (function () { 'use strict';\n\nvar SpriteSymbol = function SpriteSymbol(ref) {\n  var id = ref.id;\n  var viewBox = ref.viewBox;\n  var content = ref.content;\n\n  this.id = id;\n  this.viewBox = viewBox;\n  this.content = content;\n};\n\n/**\n * @return {string}\n */\nSpriteSymbol.prototype.stringify = function stringify () {\n  return this.content;\n};\n\n/**\n * @return {string}\n */\nSpriteSymbol.prototype.toString = function toString () {\n  return this.stringify();\n};\n\nSpriteSymbol.prototype.destroy = function destroy () {\n    var this$1 = this;\n\n  ['id', 'viewBox', 'content'].forEach(function (prop) { return delete this$1[prop]; });\n};\n\n/**\n * @param {string} content\n * @return {Element}\n */\nvar parse = function (content) {\n  var hasImportNode = !!document.importNode;\n  var doc = new DOMParser().parseFromString(content, 'image/svg+xml').documentElement;\n\n  /**\n   * Fix for browser which are throwing WrongDocumentError\n   * if you insert an element which is not part of the document\n   * @see http://stackoverflow.com/a/7986519/4624403\n   */\n  if (hasImportNode) {\n    return document.importNode(doc, true);\n  }\n\n  return doc;\n};\n\nvar commonjsGlobal = typeof window !== 'undefined' ? window : typeof __webpack_require__.g !== 'undefined' ? __webpack_require__.g : typeof self !== 'undefined' ? self : {};\n\n\n\n\n\nfunction createCommonjsModule(fn, module) {\n\treturn module = { exports: {} }, fn(module, module.exports), module.exports;\n}\n\nvar deepmerge = createCommonjsModule(function (module, exports) {\n(function (root, factory) {\n    if (false) {} else {\n        module.exports = factory();\n    }\n}(commonjsGlobal, function () {\n\nfunction isMergeableObject(val) {\n    var nonNullObject = val && typeof val === 'object';\n\n    return nonNullObject\n        && Object.prototype.toString.call(val) !== '[object RegExp]'\n        && Object.prototype.toString.call(val) !== '[object Date]'\n}\n\nfunction emptyTarget(val) {\n    return Array.isArray(val) ? [] : {}\n}\n\nfunction cloneIfNecessary(value, optionsArgument) {\n    var clone = optionsArgument && optionsArgument.clone === true;\n    return (clone && isMergeableObject(value)) ? deepmerge(emptyTarget(value), value, optionsArgument) : value\n}\n\nfunction defaultArrayMerge(target, source, optionsArgument) {\n    var destination = target.slice();\n    source.forEach(function(e, i) {\n        if (typeof destination[i] === 'undefined') {\n            destination[i] = cloneIfNecessary(e, optionsArgument);\n        } else if (isMergeableObject(e)) {\n            destination[i] = deepmerge(target[i], e, optionsArgument);\n        } else if (target.indexOf(e) === -1) {\n            destination.push(cloneIfNecessary(e, optionsArgument));\n        }\n    });\n    return destination\n}\n\nfunction mergeObject(target, source, optionsArgument) {\n    var destination = {};\n    if (isMergeableObject(target)) {\n        Object.keys(target).forEach(function (key) {\n            destination[key] = cloneIfNecessary(target[key], optionsArgument);\n        });\n    }\n    Object.keys(source).forEach(function (key) {\n        if (!isMergeableObject(source[key]) || !target[key]) {\n            destination[key] = cloneIfNecessary(source[key], optionsArgument);\n        } else {\n            destination[key] = deepmerge(target[key], source[key], optionsArgument);\n        }\n    });\n    return destination\n}\n\nfunction deepmerge(target, source, optionsArgument) {\n    var array = Array.isArray(source);\n    var options = optionsArgument || { arrayMerge: defaultArrayMerge };\n    var arrayMerge = options.arrayMerge || defaultArrayMerge;\n\n    if (array) {\n        return Array.isArray(target) ? arrayMerge(target, source, optionsArgument) : cloneIfNecessary(source, optionsArgument)\n    } else {\n        return mergeObject(target, source, optionsArgument)\n    }\n}\n\ndeepmerge.all = function deepmergeAll(array, optionsArgument) {\n    if (!Array.isArray(array) || array.length < 2) {\n        throw new Error('first argument should be an array with at least two elements')\n    }\n\n    // we are sure there are at least 2 values, so it is safe to have no initial value\n    return array.reduce(function(prev, next) {\n        return deepmerge(prev, next, optionsArgument)\n    })\n};\n\nreturn deepmerge\n\n}));\n});\n\nvar namespaces_1 = createCommonjsModule(function (module, exports) {\nvar namespaces = {\n  svg: {\n    name: 'xmlns',\n    uri: 'http://www.w3.org/2000/svg'\n  },\n  xlink: {\n    name: 'xmlns:xlink',\n    uri: 'http://www.w3.org/1999/xlink'\n  }\n};\n\nexports.default = namespaces;\nmodule.exports = exports.default;\n});\n\n/**\n * @param {Object} attrs\n * @return {string}\n */\nvar objectToAttrsString = function (attrs) {\n  return Object.keys(attrs).map(function (attr) {\n    var value = attrs[attr].toString().replace(/\"/g, '&quot;');\n    return (attr + \"=\\\"\" + value + \"\\\"\");\n  }).join(' ');\n};\n\nvar svg = namespaces_1.svg;\nvar xlink = namespaces_1.xlink;\n\nvar defaultAttrs = {};\ndefaultAttrs[svg.name] = svg.uri;\ndefaultAttrs[xlink.name] = xlink.uri;\n\n/**\n * @param {string} [content]\n * @param {Object} [attributes]\n * @return {string}\n */\nvar wrapInSvgString = function (content, attributes) {\n  if ( content === void 0 ) content = '';\n\n  var attrs = deepmerge(defaultAttrs, attributes || {});\n  var attrsRendered = objectToAttrsString(attrs);\n  return (\"<svg \" + attrsRendered + \">\" + content + \"</svg>\");\n};\n\nvar BrowserSpriteSymbol = (function (SpriteSymbol$$1) {\n  function BrowserSpriteSymbol () {\n    SpriteSymbol$$1.apply(this, arguments);\n  }\n\n  if ( SpriteSymbol$$1 ) BrowserSpriteSymbol.__proto__ = SpriteSymbol$$1;\n  BrowserSpriteSymbol.prototype = Object.create( SpriteSymbol$$1 && SpriteSymbol$$1.prototype );\n  BrowserSpriteSymbol.prototype.constructor = BrowserSpriteSymbol;\n\n  var prototypeAccessors = { isMounted: {} };\n\n  prototypeAccessors.isMounted.get = function () {\n    return !!this.node;\n  };\n\n  /**\n   * @param {Element} node\n   * @return {BrowserSpriteSymbol}\n   */\n  BrowserSpriteSymbol.createFromExistingNode = function createFromExistingNode (node) {\n    return new BrowserSpriteSymbol({\n      id: node.getAttribute('id'),\n      viewBox: node.getAttribute('viewBox'),\n      content: node.outerHTML\n    });\n  };\n\n  BrowserSpriteSymbol.prototype.destroy = function destroy () {\n    if (this.isMounted) {\n      this.unmount();\n    }\n    SpriteSymbol$$1.prototype.destroy.call(this);\n  };\n\n  /**\n   * @param {Element|string} target\n   * @return {Element}\n   */\n  BrowserSpriteSymbol.prototype.mount = function mount (target) {\n    if (this.isMounted) {\n      return this.node;\n    }\n\n    var mountTarget = typeof target === 'string' ? document.querySelector(target) : target;\n    var node = this.render();\n    this.node = node;\n\n    mountTarget.appendChild(node);\n\n    return node;\n  };\n\n  /**\n   * @return {Element}\n   */\n  BrowserSpriteSymbol.prototype.render = function render () {\n    var content = this.stringify();\n    return parse(wrapInSvgString(content)).childNodes[0];\n  };\n\n  BrowserSpriteSymbol.prototype.unmount = function unmount () {\n    this.node.parentNode.removeChild(this.node);\n  };\n\n  Object.defineProperties( BrowserSpriteSymbol.prototype, prototypeAccessors );\n\n  return BrowserSpriteSymbol;\n}(SpriteSymbol));\n\nreturn BrowserSpriteSymbol;\n\n})));\n\n\n//# sourceURL=webpack://ticview/./node_modules/svg-baker-runtime/browser-symbol.js?");

/***/ }),

/***/ "./app/web/icons/arrow-down-static.svg":
/*!*********************************************!*\
  !*** ./app/web/icons/arrow-down-static.svg ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"arrow-down-static\",\n  \"use\": \"arrow-down-static-usage\",\n  \"viewBox\": \"0 0 10 6.3\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 10 6.3\\\" id=\\\"arrow-down-static\\\"><path d=\\\"M5 3.6 1.4 0 0 1.3l5 5 5-5L8.7 0 5 3.6z\\\" style=\\\"fill:#cbcbcb\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/arrow-down-static.svg?");

/***/ }),

/***/ "./app/web/icons/arrow-up-static.svg":
/*!*******************************************!*\
  !*** ./app/web/icons/arrow-up-static.svg ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"arrow-up-static\",\n  \"use\": \"arrow-up-static-usage\",\n  \"viewBox\": \"0 0 10 6.3\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 10 6.3\\\" id=\\\"arrow-up-static\\\"><path d=\\\"m5 2.7 3.7 3.6L10 5 5 0 0 5l1.3 1.3L5 2.7z\\\" style=\\\"fill:#fff\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/arrow-up-static.svg?");

/***/ }),

/***/ "./app/web/icons/car.svg":
/*!*******************************!*\
  !*** ./app/web/icons/car.svg ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"car\",\n  \"use\": \"car-usage\",\n  \"viewBox\": \"0 0 12 11\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 12 11\\\" id=\\\"car\\\"><path d=\\\"M4 8.7c.2 0 .3 0 .4.1s.3.1.4.2c.1.1.2.2.3.4.1.1.1.3.1.4s0 .3-.1.4c-.1.1-.1.3-.2.4-.1.1-.2.2-.4.2s-.3.1-.5.1-.3 0-.4-.1c-.1-.1-.3-.1-.4-.2s-.2-.2-.2-.3c-.1-.2-.1-.3-.1-.5s0-.3.1-.4c0-.2.1-.3.2-.4.1-.1.2-.2.4-.2.1-.1.3-.1.4-.1zm5.1 0c.2 0 .3 0 .4.1.*******.4.2.1.1.2.2.2.4.1.1.1.3.1.4s0 .3-.1.4c-.1.1-.1.3-.2.4s-.2.2-.4.2-.3.2-.4.2-.3 0-.4-.1-.3-.1-.4-.2-.2-.3-.3-.4v-.9c.1-.1.2-.3.3-.4.1-.1.2-.2.4-.2.1 0 .2-.1.4-.1zm2-6.9c.2 0 .4 0 .5.1s.2.1.3.2c.1.1.1.2.1.3v.3c0 .1-.1.2-.2.5-.1.2-.2.5-.3.7s-.2.5-.3.8c0 .3-.1.5-.2.6-.1.3-.2.5-.4.7s-.3.2-.6.2H3.7l.2 1.1H10c.4 0 .6.2.6.5 0 .2 0 .3-.1.4s-.3.2-.5.2H3.6c-.2 0-.3 0-.4-.1L2.9 8c-.1-.1-.1-.3-.1-.4 0-.1-.1-.3-.1-.4 0 0 0-.2-.1-.4 0-.2-.1-.4-.1-.7-.1-.2-.2-.5-.2-.9s-.1-.7-.2-1c-.2-.8-.3-1.8-.5-2.8h-1c-.1 0-.2 0-.3-.1l-.2-.2C0 1 .1 1 0 .9s0-.2 0-.3C0 .4.1.3.2.2S.4 0 .6 0H1.8c.2 0 .3 0 .4.1.1 0 .2.1.2.2.1.1.1.2.1.2s0 .2.1.2v.7c0 .1 0 .3.1.5h8.4z\\\" style=\\\"fill:#707070\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/car.svg?");

/***/ }),

/***/ "./app/web/icons/car_static.svg":
/*!**************************************!*\
  !*** ./app/web/icons/car_static.svg ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"car_static\",\n  \"use\": \"car_static-usage\",\n  \"viewBox\": \"0 0 32 26.1\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 32 26.1\\\" id=\\\"car_static\\\"><path d=\\\"M26.6 20.3H11.7c-2.4 0-4.3-2-4.3-4.4l-1-8.7c-.1-.4-.1-.8-.2-1.3-.3-2.6-.5-4.2-5-3.8-.5.1-1-.4-1.1-.9S.5.2 1 .1C7.4-.5 7.8 3 8.1 5.6c.1.4.1.8.2 1.2l1 8.9c0 1.4 1 2.5 2.3 2.5h14.9c1.3 0 2.1-.9 2.3-2.6.3-2.1 1.1-8 1.2-8.7 0-1.3-1.1-2.4-2.3-2.4l-14.5.1c-.5 0-1-.4-1-1s.4-1 1-1l14.5-.1c2.4 0 4.3 2 4.3 4.5v.1c0 .1-.9 6.5-1.2 8.8-.2 2.8-1.8 4.4-4.2 4.4z\\\" class=\\\"st0\\\" /><circle cx=\\\"12.8\\\" cy=\\\"24.1\\\" r=\\\"2\\\" class=\\\"st0\\\" /><circle cx=\\\"26.3\\\" cy=\\\"24.1\\\" r=\\\"2\\\" class=\\\"st0\\\" /><path d=\\\"M25 10.4H14.1c-.5 0-1-.4-1-1 0-.5.4-1 1-1H25c.5 0 1 .5 1 1s-.5 1-1 1zM25 14.9H14.1c-.5 0-1-.4-1-1s.4-1 1-1H25c.5 0 1 .4 1 1s-.5 1-1 1z\\\" class=\\\"st0\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/car_static.svg?");

/***/ }),

/***/ "./app/web/icons/checked.svg":
/*!***********************************!*\
  !*** ./app/web/icons/checked.svg ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"checked\",\n  \"use\": \"checked-usage\",\n  \"viewBox\": \"0 0 10 7.2\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 10 7.2\\\" id=\\\"checked\\\"><path d=\\\"m.1 3.3 3.3 3.8c.*******.5 0l6-6.5c.1-.1.1-.3 0-.4-.1-.2-.3-.2-.4-.1L3.8 4.7c-.1.1-.3.1-.4 0L.5 2.8c-.1-.1-.3-.1-.4 0-.1.2-.1.3 0 .5z\\\" style=\\\"fill:#878787\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/checked.svg?");

/***/ }),

/***/ "./app/web/icons/home-bottom-1.svg":
/*!*****************************************!*\
  !*** ./app/web/icons/home-bottom-1.svg ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"home-bottom-1\",\n  \"use\": \"home-bottom-1-usage\",\n  \"viewBox\": \"0 0 32.3 37.4\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 32.3 37.4\\\" id=\\\"home-bottom-1\\\"><style>#home-bottom-1 .st0{fill:#ca4300}</style><path d=\\\"m16.1 2.3 14.2 8.1.1 16.4L16.3 35 2.1 26.9 2 10.6l14.1-8.3m0-2.3L0 9.4l.1 18.7 16.2 9.3L32.4 28l-.1-18.7L16.1 0z\\\" class=\\\"st0\\\" /><path d=\\\"M24.4 18.6c-2.5 5.3-8.5 7.3-15.1 8 0-.5-.3-1.2-.6-1.6 3-.3 5.8-.8 8.2-1.8-.6-.5-1.3-1.1-1.9-1.5l1.4-.8c.6.5 1.4 1.1 2.1 1.6 1.4-.8 2.6-1.8 3.4-3h-4.5c-1.5 1.1-3.3 2.1-5.4 3-.2-.4-.7-1-1-1.3 3.6-1.2 6.2-3.1 7.5-4.9l1.8.4c-.4.5-.7.9-1.1 1.3H23l.3-.1 1.1.7zm-2.5-6.5c-2.3 4.1-7.5 6.4-12.3 7.3-.1-.4-.5-1.1-.8-1.4 1.9-.3 3.9-.9 5.7-1.7-.5-.5-1.2-1-1.8-1.4l1.2-.8c.7.4 1.5 1 2.1 1.5 1.3-.7 2.4-1.6 3.3-2.5h-4.8c-1.2.9-2.5 1.7-4 2.4-.3-.4-.8-.9-1.1-1.2 2.9-1.2 5-2.8 6.2-4.2l1.8.4c-.3.4-.7.8-1.1 1.2h4.3l.3-.1 1 .5z\\\" class=\\\"st0\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/home-bottom-1.svg?");

/***/ }),

/***/ "./app/web/icons/home-bottom-2.svg":
/*!*****************************************!*\
  !*** ./app/web/icons/home-bottom-2.svg ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"home-bottom-2\",\n  \"use\": \"home-bottom-2-usage\",\n  \"viewBox\": \"0 0 32.3 37.4\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 32.3 37.4\\\" id=\\\"home-bottom-2\\\"><style>#home-bottom-2 .st0{fill:#ca4300}</style><path d=\\\"m16.1 2.3 14.2 8.1.1 16.4L16.3 35 2 26.9l-.1-16.3 14.2-8.3m0-2.3L-.1 9.4 0 28.1l16.2 9.3L32.3 28V9.3L16.1 0z\\\" class=\\\"st0\\\" /><path d=\\\"M7.9 18.1c.5-1.2.8-3.2.9-4.6l1.2.2c-.1 1.5-.4 3.6-.9 4.9l-1.2-.5zm5.2-1.3c-.2-.8-.6-2-1.1-3v12.7h-1.6V10H12v3.3l1-.4c.6 1 1.2 2.4 1.4 3.3l-1.3.6zm6.8 2.9c.9 2.4 2.5 4.4 4.9 5.3-.4.4-.9 1.1-1.2 1.5-2.4-1.1-3.9-3.1-4.9-5.7-.7 2.1-2 4.2-4.8 5.8-.2-.3-.8-1-1.2-1.3 3-1.5 4.1-3.5 4.6-5.6h-3.9v-1.6h4.2c.1-.7.1-1.4.1-2v-1.7h-3.3v-1.5h3.3V10h1.7v2.8h3.9v5.3h1.4v1.6h-4.8zm-.6-3.6c0 .6 0 1.3-.1 2h2.4v-3.7h-2.3v1.7z\\\" class=\\\"st0\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/home-bottom-2.svg?");

/***/ }),

/***/ "./app/web/icons/home-bottom-3.svg":
/*!*****************************************!*\
  !*** ./app/web/icons/home-bottom-3.svg ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"home-bottom-3\",\n  \"use\": \"home-bottom-3-usage\",\n  \"viewBox\": \"0 0 32.3 37.4\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 32.3 37.4\\\" id=\\\"home-bottom-3\\\"><style>#home-bottom-3 .st0{fill:#ca4300}</style><path d=\\\"m16.1 2.3 14.2 8.1.1 16.4L16.3 35 2 26.9V10.6l14.1-8.3m0-2.3L0 9.4v18.7l16.2 9.3L32.3 28V9.3L16.1 0z\\\" class=\\\"st0\\\" /><path d=\\\"M15.2 13.8c-.3 3.2-.9 5.7-1.8 7.7.8.7 1.5 1.5 2 2.1L14.3 25c-.4-.6-1-1.3-1.8-1.9-1 1.5-2.2 2.6-3.6 3.4-.2-.4-.7-1-1-1.3 1.4-.7 2.5-1.8 3.4-3.2-.9-.8-1.9-1.6-2.8-2.2.4-1.2.8-2.8 1.2-4.6H8.2v-1.5H10c.2-1.2.4-2.5.5-3.6l1.7.1c-.1 1.1-.3 2.3-.6 3.5h2.3l.3-.1 1 .2zm-3.9 1.4c-.3 1.4-.6 2.8-1 4 .6.4 1.2.9 1.8 1.4.6-1.5 1.1-3.3 1.4-5.3h-2.2zm13.4 3.9h-3.8v5.6c0 .9-.2 1.4-.8 1.6-.6.3-1.5.3-3 .3-.1-.5-.3-1.2-.6-1.6h2.3c.3 0 .4-.1.4-.3v-5.5h-4v-1.6h4v-1.9c.8-.7 1.7-1.8 2.4-2.8H16v-1.5h6.8l.4-.1 1.2.8c-.9 1.3-2.1 2.8-3.4 4v1.6h3.8v1.4z\\\" class=\\\"st0\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/home-bottom-3.svg?");

/***/ }),

/***/ "./app/web/icons/home-bottom-4.svg":
/*!*****************************************!*\
  !*** ./app/web/icons/home-bottom-4.svg ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"home-bottom-4\",\n  \"use\": \"home-bottom-4-usage\",\n  \"viewBox\": \"0 0 32.3 37.4\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 32.3 37.4\\\" id=\\\"home-bottom-4\\\"><style>#home-bottom-4 .st0{fill:#ca4300}</style><path d=\\\"m16.1 2.3 14.2 8.1.1 16.4L16.3 35 2 26.9V10.6l14.1-8.3m0-2.3L0 9.4v18.7l16.2 9.3L32.3 28V9.3L16.1 0z\\\" class=\\\"st0\\\" /><path d=\\\"M21.3 14c-1.3 1.5-3.3 2.6-5.5 3.5h6.5v9h-1.7v-.7H13v.8h-1.6v-7.8c-.8.2-1.6.3-2.3.4-.2-.4-.7-1.1-1-1.5 2.6-.3 5.2-.8 7.4-1.7v-6h1.7v5.2c1.1-.6 2-1.2 2.7-2l1.4.8zm-7.6-2.5c-.8 1.8-2.2 3.5-3.4 4.6-.3-.3-1-.8-1.4-1 1.3-1 2.5-2.5 3.2-4.1l1.6.5zm6.9 7.2H13v1.1h7.6v-1.1zM13 22.1h7.6V21H13v1.1zm7.6 2.4v-1.2H13v1.2h7.6zm-.1-13.6c1.4 1.1 3.1 2.7 3.9 3.9l-1.4 1c-.7-1.2-2.4-2.8-3.8-4l1.3-.9z\\\" class=\\\"st0\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/home-bottom-4.svg?");

/***/ }),

/***/ "./app/web/icons/home_menu_icon.svg":
/*!******************************************!*\
  !*** ./app/web/icons/home_menu_icon.svg ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"home_menu_icon\",\n  \"use\": \"home_menu_icon-usage\",\n  \"viewBox\": \"0 0 6 11.3\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 6 11.3\\\" id=\\\"home_menu_icon\\\"><path d=\\\"M.5 11.3c-.1 0-.2 0-.3-.1-.2-.2-.2-.5 0-.7L5 5.7.1.9C0 .7 0 .4.1.2s.5-.2.7 0l5 5c.3.3.3.7 0 1l-5 5c-.1 0-.2.1-.3.1zm4.6-5.5z\\\" class=\\\"st0\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/home_menu_icon.svg?");

/***/ }),

/***/ "./app/web/icons/home_side_portrait.svg":
/*!**********************************************!*\
  !*** ./app/web/icons/home_side_portrait.svg ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"home_side_portrait\",\n  \"use\": \"home_side_portrait-usage\",\n  \"viewBox\": \"0 0 23 23\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 23 23\\\" id=\\\"home_side_portrait\\\"><path d=\\\"M11.5 0C5.1 0 0 5.1 0 11.5S5.1 23 11.5 23 23 17.9 23 11.5 17.9 0 11.5 0zm0 3.9c2 0 3.7 1.7 3.7 3.7s-1.7 3.7-3.7 3.7-3.7-1.7-3.7-3.7 1.7-3.7 3.7-3.7zM4.8 17.6c.1-.9.5-4 3.8-5.7.8.6 1.8 1 3 1 1.1 0 2.1-.4 3-1 3.3 1.7 3.7 4.8 3.8 5.7H4.8z\\\" style=\\\"fill:#c6c6c6\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/home_side_portrait.svg?");

/***/ }),

/***/ "./app/web/icons/icon-1.svg":
/*!**********************************!*\
  !*** ./app/web/icons/icon-1.svg ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"icon-1\",\n  \"use\": \"icon-1-usage\",\n  \"viewBox\": \"0 0 15 12.7\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" viewBox=\\\"0 0 15 12.7\\\" id=\\\"icon-1\\\"><defs><path id=\\\"icon-1_a\\\" d=\\\"M0 0h15v12.7H0z\\\" /></defs><clipPath id=\\\"icon-1_b\\\"><use xlink:href=\\\"#icon-1_a\\\" style=\\\"overflow:visible\\\" /></clipPath><path d=\\\"M9.4.2C9.1 0 8.6 0 8.2.1l-5 2.6h-2C.5 2.7 0 3.3 0 3.9v4.9C0 9.5.5 10 1.2 10h2l5 2.5c.*******.5.1s.4-.1.6-.2c.4-.2.6-.6.6-1V1.2c.1-.4-.1-.8-.5-1zM4.3 7.9c0 .4-.4.8-.8.8-.5.1-.8-.3-.8-.8v-3c0-.5.3-.9.8-.9.4 0 .*******v3.1zM11.7 3c-.3 0-.5-.1-.7-.4-.2-.4-.1-.9.3-1.1l1.7-1c.4-.2.9-.1 *******.4.1.9-.3 1.1l-1.7 1c-.2.1-.3.1-.4.1zm2.5 4.1h-1.9c-.4 0-.8-.4-.8-.8s.4-.8.8-.8h1.9c.4 0 .******* 0 .5-.4.8-.8.8zm-.9 5.2c-.1 0-.3 0-.4-.1l-1.7-1c-.4-.2-.5-.7-.3-1.1.2-.4.7-.5 1.1-.3l1.7 1c.*******.3 1.1-.1.3-.4.4-.7.4zm0 0\\\" style=\\\"clip-path:url(#icon-1_b);fill:#ca4300\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/icon-1.svg?");

/***/ }),

/***/ "./app/web/icons/inquiry_static.svg":
/*!******************************************!*\
  !*** ./app/web/icons/inquiry_static.svg ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"inquiry_static\",\n  \"use\": \"inquiry_static-usage\",\n  \"viewBox\": \"0 0 28.1 28.1\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 28.1 28.1\\\" id=\\\"inquiry_static\\\"><style>#inquiry_static .st0{fill:#fff}</style><path d=\\\"M19 25.1H5c-1.6 0-3-1.3-3-3V7c0-1.6 1.3-3 3-3h14c1.6 0 3 1.3 3 3v15.1c0 1.7-1.4 3-3 3z\\\" class=\\\"st0\\\" /><path d=\\\"M19 4c1.6 0 3 1.3 3 3v15.1c0 1.6-1.3 3-3 3H5c-1.6 0-3-1.3-3-3V7c0-1.6 1.3-3 3-3h14m0-2H5C2.3 2 0 4.3 0 7v15.1c0 2.8 2.2 5 5 5h14c2.8 0 5-2.2 5-5V7c0-2.7-2.3-5-5-5z\\\" class=\\\"st1\\\" /><path d=\\\"M16.2 4H7.8c-.1 0-.2 0-.2-.2V2.2c0-.1.1-.2.2-.2h8.5c.1 0 .2.1.2.2v1.6c-.1.2-.2.2-.3.2z\\\" class=\\\"st0\\\" /><path d=\\\"M16.2 2c.1 0 .2.1.2.2v1.6c0 .2-.1.2-.2.2H7.8c-.1 0-.2 0-.2-.2V2.2c0-.1.1-.2.2-.2h8.4m0-2H7.8C6.5 0 5.6 1 5.6 2.2v1.6C5.6 5 6.6 6 7.8 6h8.5c1.2 0 2.2-1 2.2-2.2V2.2C18.4 1 17.4 0 16.2 0zM17.4 11.1H6.6c-.5 0-1-.4-1-1s.4-1 1-1h10.9c.5 0 1 .4 1 1-.1.6-.5 1-1.1 1zM10.6 16.1h-4c-.5 0-1-.4-1-1s.4-1 1-1h4c.5 0 1 .4 1 1s-.5 1-1 1zM9.6 21.1h-3c-.5 0-1-.4-1-1 0-.5.4-1 1-1h3c.5 0 1 .5 1 1 0 .6-.5 1-1 1z\\\" class=\\\"st1\\\" /><circle cx=\\\"20.1\\\" cy=\\\"20.1\\\" r=\\\"8\\\" class=\\\"st0\\\" /><path d=\\\"M20.1 14.1c3.3 0 6 2.7 6 6s-2.7 6-6 6-6-2.7-6-6 2.7-6 6-6m0-2c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z\\\" class=\\\"st1\\\" /><path d=\\\"m17.5 17 1-1 2.6 2.6-1 1-2.6-2.6z\\\" class=\\\"st1\\\" /><path d=\\\"M17.3 16.5c0 .4.3.7.7.7.4 0 .7-.3.7-.7 0-.4-.3-.7-.7-.7-.4 0-.7.3-.7.7zM21.7 16l1 1-2.6 2.6-1-1 2.6-2.6z\\\" class=\\\"st1\\\" /><path d=\\\"M21.5 16.5c0 .4.3.7.7.7s.7-.3.7-.7c0-.4-.3-.7-.7-.7s-.7.3-.7.7zM17.5 18.8h5.2v1.4h-5.2v-1.4z\\\" class=\\\"st1\\\" /><path d=\\\"M16.8 19.5c0 .4.3.7.7.7s.7-.3.7-.7c0-.4-.3-.7-.7-.7s-.7.3-.7.7zM22 19.5c0 .4.3.7.7.7.4 0 .7-.3.7-.7 0-.4-.3-.7-.7-.7-.4 0-.7.3-.7.7zM17.5 21.3h5.2v1.4h-5.2v-1.4z\\\" class=\\\"st1\\\" /><path d=\\\"M16.8 22c0 .4.3.7.7.7s.7-.3.7-.7c0-.4-.3-.7-.7-.7s-.7.3-.7.7zM22 22c0 .4.3.7.7.7.4 0 .7-.3.7-.7 0-.4-.3-.7-.7-.7-.4 0-.7.3-.7.7zM19.4 18.8h1.4v5.4h-1.4v-5.4z\\\" class=\\\"st1\\\" /><path d=\\\"M19.4 24.2c0 .4.3.7.7.7.4 0 .7-.3.7-.7 0-.4-.3-.7-.7-.7-.4 0-.7.3-.7.7z\\\" class=\\\"st1\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/inquiry_static.svg?");

/***/ }),

/***/ "./app/web/icons/oiq_btn.svg":
/*!***********************************!*\
  !*** ./app/web/icons/oiq_btn.svg ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"oiq_btn\",\n  \"use\": \"oiq_btn-usage\",\n  \"viewBox\": \"0 0 15 13\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 15 13\\\" id=\\\"oiq_btn\\\"><path d=\\\"m9.3 12.6 5.4-5.4c.4-.4.4-1.1 0-1.5L9.3.4C8.9 0 8.2 0 7.8.4c-.4.4-.4 1.1 0 1.5l3.5 3.5H1.1C.5 5.4 0 5.9 0 6.5c0 .6.5 1.1 1.1 1.1h10.3l-3.5 3.5c-.2.2-.3.5-.3.8 0 .3.1.5.3.8.3.3 1 .3 1.4-.1zm0 0\\\" class=\\\"st0\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/oiq_btn.svg?");

/***/ }),

/***/ "./app/web/icons/oiq_search.svg":
/*!**************************************!*\
  !*** ./app/web/icons/oiq_search.svg ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"oiq_search\",\n  \"use\": \"oiq_search-usage\",\n  \"viewBox\": \"0 0 20 20\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 20 20\\\" id=\\\"oiq_search\\\"><path d=\\\"M2 9.1C2 5.2 5.1 2 9 1.9c4 0 7.2 3.1 7.2 7.1.1 3.9-3.1 7.1-7 7.2-3.9 0-7.1-3.1-7.2-7.1zm17.7 9.2-3.5-3.5c3.1-3.9 2.5-9.7-1.4-12.8C10.9-1.1 5.2-.5 2 3.4c-3.2 3.9-2.5 9.7 1.4 12.8 3.3 2.7 8.1 2.7 11.4 0l3.5 3.5c.4.4 1 .4 1.4 0 .4-.4.4-1 0-1.4z\\\" style=\\\"fill:#ca4300\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/oiq_search.svg?");

/***/ }),

/***/ "./app/web/icons/phone_icon.svg":
/*!**************************************!*\
  !*** ./app/web/icons/phone_icon.svg ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"phone_icon\",\n  \"use\": \"phone_icon-usage\",\n  \"viewBox\": \"0 0 20 20\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 20 20\\\" id=\\\"phone_icon\\\"><path d=\\\"M14 .8C8.9-1.4 3 .9.8 6-1.4 11.1.9 17 6 19.2c1.5.6 3 .9 4.5.8 1.3-.1 2.6-.4 3.7-1 .1 0 1.2-.5 1.5-1.4.2-.6 0-1.3-.5-2 .1-.2.2-.4.1-.6l-1.9-3c-.1-.2-.4-.3-.7-.2l-1 .8c-.5.2-.9-.2-1.7-1.2L8.6 9c-.3-.6-.6-1.3-.1-1.6l1.1-.6c.2-.1.3-.5.2-.7l-1.9-3c-.2-.2-.5-.2-.7-.1l-1 .8c-.6.5-2 2.8 1.1 7.8 3.2 5.2 6 5 6.8 4.7l.3-.1-.1.1c.3.4.5.8.4 1.1-.1.3-.7.7-.9.8-1.1.5-2.2.8-3.4.9-1.3.1-2.7-.2-4-.7-4.5-2-6.6-7.2-4.6-11.7C3.8 2.2 9 .1 13.5 2.1c4.5 2 6.6 7.2 4.6 11.7 0 .1-.1.2-.1.3-.1.3.1.4.5.5.3.1.4.1.6-.1 0 0 .1-.2.1-.3C21.3 8.9 19 3 14 .8\\\" style=\\\"fill:#a8a8a8\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/phone_icon.svg?");

/***/ }),

/***/ "./app/web/icons/report_static.svg":
/*!*****************************************!*\
  !*** ./app/web/icons/report_static.svg ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"report_static\",\n  \"use\": \"report_static-usage\",\n  \"viewBox\": \"0 0 23 28.1\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 23 28.1\\\" id=\\\"report_static\\\"><path d=\\\"M11.5 2.8c1.4 1 4.4 2.7 9.3 2.8v11c0 1.9 0 5.5-9.3 9.2-9.3-3.7-9.3-7.3-9.3-9.2v-11c5 0 8-1.8 9.3-2.8m0-2.8-.6.7S8.2 3.6 1.8 3.6H.2v13.1c0 3.4 1.1 7.4 11 11.3l.3.1.3-.1c9.9-3.8 11-7.8 11-11.3V3.6h-1.6c-6.3 0-9-2.9-9.1-2.9l-.6-.7z\\\" class=\\\"st0\\\" /><path d=\\\"M10.2 19.1c-.3 0-.5-.1-.7-.3l-4.1-4.4c-.4-.4-.4-1 .1-1.4.4-.4 1-.4 1.4.1l3.4 3.6 5.9-6.4c.4-.4 1-.4 1.4-.1.4.4.4 1 .1 1.4L11 18.7c-.2.3-.5.4-.8.4z\\\" class=\\\"st0\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/report_static.svg?");

/***/ }),

/***/ "./app/web/icons/shearch_icon.svg":
/*!****************************************!*\
  !*** ./app/web/icons/shearch_icon.svg ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"shearch_icon\",\n  \"use\": \"shearch_icon-usage\",\n  \"viewBox\": \"0 0 22 22\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 22 22\\\" id=\\\"shearch_icon\\\"><path d=\\\"M2.2 10.1c0-4.4 3.4-7.9 7.7-8 4.3-.1 7.9 3.4 7.9 7.7.1 4.3-3.4 7.9-7.7 7.9-4.3.1-7.8-3.3-7.9-7.6zm19.5 10-3.8-3.8c3.5-4.3 2.8-10.6-1.6-14.1C12-1.2 5.7-.5 2.2 3.8-1.3 8.1-.6 14.4 3.7 17.9c3.7 2.9 8.9 2.9 12.6 0l3.8 3.8c.4.4 1.1.4 1.6 0 .4-.4.4-1.1 0-1.6z\\\" style=\\\"fill:#fff\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/shearch_icon.svg?");

/***/ }),

/***/ "./app/web/icons/slogan-icon.svg":
/*!***************************************!*\
  !*** ./app/web/icons/slogan-icon.svg ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"slogan-icon\",\n  \"use\": \"slogan-icon-usage\",\n  \"viewBox\": \"0 0 32 24\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 32 24\\\" id=\\\"slogan-icon\\\"><style>#slogan-icon .st1{fill:#fff}</style><linearGradient id=\\\"slogan-icon_SVGID_1_\\\" x1=\\\"16\\\" x2=\\\"16\\\" y1=\\\"1.633\\\" y2=\\\"23.981\\\" gradientUnits=\\\"userSpaceOnUse\\\"><stop offset=\\\"0\\\" style=\\\"stop-color:#cf4b4a\\\" /><stop offset=\\\".111\\\" style=\\\"stop-color:#c93332\\\" /><stop offset=\\\".267\\\" style=\\\"stop-color:#c11817\\\" /><stop offset=\\\".405\\\" style=\\\"stop-color:#bd0706\\\" /><stop offset=\\\".509\\\" style=\\\"stop-color:#bb0100\\\" /><stop offset=\\\".613\\\" style=\\\"stop-color:#bd0908\\\" /><stop offset=\\\".776\\\" style=\\\"stop-color:#c31f1e\\\" /><stop offset=\\\".976\\\" style=\\\"stop-color:#cd4342\\\" /><stop offset=\\\"1\\\" style=\\\"stop-color:#ce4847\\\" /></linearGradient><path d=\\\"M29 24H3c-1.7 0-3-1.3-3-3V3c0-1.7 1.3-3 3-3h26c1.7 0 3 1.3 3 3v18c0 1.7-1.3 3-3 3z\\\" style=\\\"fill:url(#slogan-icon_SVGID_1_)\\\" /><path d=\\\"M8.4 9.7h15.1v4.7H8.4z\\\" class=\\\"st1\\\" /><path d=\\\"M18.3 4.4v15.1h-4.7V4.4z\\\" class=\\\"st1\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/slogan-icon.svg?");

/***/ }),

/***/ "./app/web/icons/title_car.svg":
/*!*************************************!*\
  !*** ./app/web/icons/title_car.svg ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"title_car\",\n  \"use\": \"title_car-usage\",\n  \"viewBox\": \"0 0 32 26.1\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 32 26.1\\\" id=\\\"title_car\\\"><style>#title_car .st0{fill:#ca4300}</style><path d=\\\"M26.6 20.3H11.7c-2.4 0-4.3-2-4.3-4.4l-1-8.7c-.1-.4-.1-.8-.2-1.3-.3-2.6-.5-4.2-5-3.8-.5.1-1-.4-1.1-.9S.5.2 1 .1C7.4-.5 7.8 3 8.1 5.6c.1.4.1.8.2 1.2l1 8.9c0 1.4 1 2.5 2.3 2.5h14.9c1.3 0 2.1-.9 2.3-2.6.3-2.1 1.1-8 1.2-8.7 0-1.3-1.1-2.4-2.3-2.4l-14.5.1c-.5 0-1-.4-1-1s.4-1 1-1l14.5-.1c2.4 0 4.3 2 4.3 4.5v.1c0 .1-.9 6.5-1.2 8.8-.2 2.8-1.8 4.4-4.2 4.4z\\\" class=\\\"st0\\\" /><circle cx=\\\"12.8\\\" cy=\\\"24.1\\\" r=\\\"2\\\" class=\\\"st0\\\" /><circle cx=\\\"26.3\\\" cy=\\\"24.1\\\" r=\\\"2\\\" class=\\\"st0\\\" /><path d=\\\"M25 10.4H14.1c-.5 0-1-.4-1-1 0-.5.4-1 1-1H25c.5 0 1 .5 1 1s-.5 1-1 1zM25 14.9H14.1c-.5 0-1-.4-1-1s.4-1 1-1H25c.5 0 1 .4 1 1s-.5 1-1 1z\\\" class=\\\"st0\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/title_car.svg?");

/***/ }),

/***/ "./app/web/icons/title_oiq.svg":
/*!*************************************!*\
  !*** ./app/web/icons/title_oiq.svg ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/svg-baker-runtime/browser-symbol.js */ \"./node_modules/svg-baker-runtime/browser-symbol.js\");\n/* harmony import */ var _node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js */ \"./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\");\n/* harmony import */ var _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar symbol = new (_node_modules_svg_baker_runtime_browser_symbol_js__WEBPACK_IMPORTED_MODULE_0___default())({\n  \"id\": \"title_oiq\",\n  \"use\": \"title_oiq-usage\",\n  \"viewBox\": \"0 0 35 24\",\n  \"content\": \"<symbol xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 35 24\\\" id=\\\"title_oiq\\\"><style>#title_oiq .st0{fill:#ca4300}#title_oiq .st1{fill:#fff}</style><path d=\\\"m33.5 20.5-9.2 2.7c-4.5.8-9.1.8-13.6 0l-9.2-2.7c-.9-.2-1.5-1-1.5-1.9V4.9C0 4 .7 3.2 1.5 3L10.7.3c4.5-.8 9.1-.8 13.5 0L33.5 3c.8.2 1.5 1 1.5 1.9v13.6c0 1-.7 1.8-1.5 2z\\\" class=\\\"st0\\\" /><path d=\\\"M3.9 11.8c0-1.1.2-2.1.5-2.9.2-.6.6-1.1 1-1.5.4-.5.8-.8 1.3-1 .6-.3 1.3-.5 2.1-.5 1.5 0 2.7.5 3.6 1.5.9 1 1.4 2.4 1.4 4.3 0 1.8-.5 3.2-1.4 4.2-.9 1-2.1 1.5-3.6 1.5s-2.8-.5-3.7-1.5c-.8-.9-1.2-2.3-1.2-4.1zm2.1-.1c0 1.3.3 2.3.8 2.9.5.7 1.2 1 2.1 1 .8 0 1.5-.3 2-1s.8-1.6.8-2.9c0-1.3-.3-2.2-.8-2.9-.5-.6-1.2-.9-2.1-.9-.9 0-1.5.3-2.1 1-.4.6-.7 1.5-.7 2.8zM16.4 17.3V6.1h2.2v11.2h-2.2zM20.9 11.8c0-1.1.2-2.1.5-2.9.2-.6.6-1.1 1-1.5.4-.5.8-.8 1.3-1 .6-.3 1.4-.4 2.2-.4 1.5 0 2.7.5 3.6 1.5.9 1 1.4 2.4 1.4 4.3 0 1.8-.5 3.2-1.4 4.2s-2.1 1.5-3.6 1.5-2.8-.5-3.7-1.5c-.9-1-1.3-2.4-1.3-4.2zm2.1-.1c0 1.3.3 2.2.8 2.9.5.7 1.2 1 2.1 1 .8 0 1.5-.3 2-1s.8-1.6.8-2.9c0-1.3-.3-2.2-.8-2.9-.5-.6-1.2-.9-2.1-.9-.9 0-1.5.3-2.1 1-.4.6-.7 1.5-.7 2.8z\\\" class=\\\"st1\\\" /><path d=\\\"m28 10 2.8 2.8.2 3.8-3.8-3.8zM25.1 14.5l1.7 1.6.1 2.9 2.6-3-3.4-3.2z\\\" class=\\\"st0\\\" /><path d=\\\"m30.5 16.3 3.1-.1-2-1.9h-2l-2.9-2.9-1.3-.3.2 1.3 2.8 2.9v2.1l2.1 1.9v-3z\\\" class=\\\"st1\\\" /></symbol>\"\n});\nvar result = _node_modules_svg_sprite_loader_runtime_browser_sprite_build_js__WEBPACK_IMPORTED_MODULE_1___default().add(symbol);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (symbol);\n\n//# sourceURL=webpack://ticview/./app/web/icons/title_oiq.svg?");

/***/ }),

/***/ "./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js":
/*!************************************************************************!*\
  !*** ./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js ***!
  \************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("(function (global, factory) {\n\t true ? module.exports = factory() :\n\t0;\n}(this, (function () { 'use strict';\n\nvar commonjsGlobal = typeof window !== 'undefined' ? window : typeof __webpack_require__.g !== 'undefined' ? __webpack_require__.g : typeof self !== 'undefined' ? self : {};\n\n\n\n\n\nfunction createCommonjsModule(fn, module) {\n\treturn module = { exports: {} }, fn(module, module.exports), module.exports;\n}\n\nvar deepmerge = createCommonjsModule(function (module, exports) {\n(function (root, factory) {\n    if (false) {} else {\n        module.exports = factory();\n    }\n}(commonjsGlobal, function () {\n\nfunction isMergeableObject(val) {\n    var nonNullObject = val && typeof val === 'object';\n\n    return nonNullObject\n        && Object.prototype.toString.call(val) !== '[object RegExp]'\n        && Object.prototype.toString.call(val) !== '[object Date]'\n}\n\nfunction emptyTarget(val) {\n    return Array.isArray(val) ? [] : {}\n}\n\nfunction cloneIfNecessary(value, optionsArgument) {\n    var clone = optionsArgument && optionsArgument.clone === true;\n    return (clone && isMergeableObject(value)) ? deepmerge(emptyTarget(value), value, optionsArgument) : value\n}\n\nfunction defaultArrayMerge(target, source, optionsArgument) {\n    var destination = target.slice();\n    source.forEach(function(e, i) {\n        if (typeof destination[i] === 'undefined') {\n            destination[i] = cloneIfNecessary(e, optionsArgument);\n        } else if (isMergeableObject(e)) {\n            destination[i] = deepmerge(target[i], e, optionsArgument);\n        } else if (target.indexOf(e) === -1) {\n            destination.push(cloneIfNecessary(e, optionsArgument));\n        }\n    });\n    return destination\n}\n\nfunction mergeObject(target, source, optionsArgument) {\n    var destination = {};\n    if (isMergeableObject(target)) {\n        Object.keys(target).forEach(function (key) {\n            destination[key] = cloneIfNecessary(target[key], optionsArgument);\n        });\n    }\n    Object.keys(source).forEach(function (key) {\n        if (!isMergeableObject(source[key]) || !target[key]) {\n            destination[key] = cloneIfNecessary(source[key], optionsArgument);\n        } else {\n            destination[key] = deepmerge(target[key], source[key], optionsArgument);\n        }\n    });\n    return destination\n}\n\nfunction deepmerge(target, source, optionsArgument) {\n    var array = Array.isArray(source);\n    var options = optionsArgument || { arrayMerge: defaultArrayMerge };\n    var arrayMerge = options.arrayMerge || defaultArrayMerge;\n\n    if (array) {\n        return Array.isArray(target) ? arrayMerge(target, source, optionsArgument) : cloneIfNecessary(source, optionsArgument)\n    } else {\n        return mergeObject(target, source, optionsArgument)\n    }\n}\n\ndeepmerge.all = function deepmergeAll(array, optionsArgument) {\n    if (!Array.isArray(array) || array.length < 2) {\n        throw new Error('first argument should be an array with at least two elements')\n    }\n\n    // we are sure there are at least 2 values, so it is safe to have no initial value\n    return array.reduce(function(prev, next) {\n        return deepmerge(prev, next, optionsArgument)\n    })\n};\n\nreturn deepmerge\n\n}));\n});\n\n//      \n// An event handler can take an optional event argument\n// and should not return a value\n                                          \n// An array of all currently registered event handlers for a type\n                                            \n// A map of event types and their corresponding event handlers.\n                        \n                                   \n  \n\n/** Mitt: Tiny (~200b) functional event emitter / pubsub.\n *  @name mitt\n *  @returns {Mitt}\n */\nfunction mitt(all                 ) {\n\tall = all || Object.create(null);\n\n\treturn {\n\t\t/**\n\t\t * Register an event handler for the given type.\n\t\t *\n\t\t * @param  {String} type\tType of event to listen for, or `\"*\"` for all events\n\t\t * @param  {Function} handler Function to call in response to given event\n\t\t * @memberOf mitt\n\t\t */\n\t\ton: function on(type        , handler              ) {\n\t\t\t(all[type] || (all[type] = [])).push(handler);\n\t\t},\n\n\t\t/**\n\t\t * Remove an event handler for the given type.\n\t\t *\n\t\t * @param  {String} type\tType of event to unregister `handler` from, or `\"*\"`\n\t\t * @param  {Function} handler Handler function to remove\n\t\t * @memberOf mitt\n\t\t */\n\t\toff: function off(type        , handler              ) {\n\t\t\tif (all[type]) {\n\t\t\t\tall[type].splice(all[type].indexOf(handler) >>> 0, 1);\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Invoke all handlers for the given type.\n\t\t * If present, `\"*\"` handlers are invoked after type-matched handlers.\n\t\t *\n\t\t * @param {String} type  The event type to invoke\n\t\t * @param {Any} [evt]  Any value (object is recommended and powerful), passed to each handler\n\t\t * @memberof mitt\n\t\t */\n\t\temit: function emit(type        , evt     ) {\n\t\t\t(all[type] || []).map(function (handler) { handler(evt); });\n\t\t\t(all['*'] || []).map(function (handler) { handler(type, evt); });\n\t\t}\n\t};\n}\n\nvar namespaces_1 = createCommonjsModule(function (module, exports) {\nvar namespaces = {\n  svg: {\n    name: 'xmlns',\n    uri: 'http://www.w3.org/2000/svg'\n  },\n  xlink: {\n    name: 'xmlns:xlink',\n    uri: 'http://www.w3.org/1999/xlink'\n  }\n};\n\nexports.default = namespaces;\nmodule.exports = exports.default;\n});\n\n/**\n * @param {Object} attrs\n * @return {string}\n */\nvar objectToAttrsString = function (attrs) {\n  return Object.keys(attrs).map(function (attr) {\n    var value = attrs[attr].toString().replace(/\"/g, '&quot;');\n    return (attr + \"=\\\"\" + value + \"\\\"\");\n  }).join(' ');\n};\n\nvar svg = namespaces_1.svg;\nvar xlink = namespaces_1.xlink;\n\nvar defaultAttrs = {};\ndefaultAttrs[svg.name] = svg.uri;\ndefaultAttrs[xlink.name] = xlink.uri;\n\n/**\n * @param {string} [content]\n * @param {Object} [attributes]\n * @return {string}\n */\nvar wrapInSvgString = function (content, attributes) {\n  if ( content === void 0 ) content = '';\n\n  var attrs = deepmerge(defaultAttrs, attributes || {});\n  var attrsRendered = objectToAttrsString(attrs);\n  return (\"<svg \" + attrsRendered + \">\" + content + \"</svg>\");\n};\n\nvar svg$1 = namespaces_1.svg;\nvar xlink$1 = namespaces_1.xlink;\n\nvar defaultConfig = {\n  attrs: ( obj = {\n    style: ['position: absolute', 'width: 0', 'height: 0'].join('; '),\n    'aria-hidden': 'true'\n  }, obj[svg$1.name] = svg$1.uri, obj[xlink$1.name] = xlink$1.uri, obj )\n};\nvar obj;\n\nvar Sprite = function Sprite(config) {\n  this.config = deepmerge(defaultConfig, config || {});\n  this.symbols = [];\n};\n\n/**\n * Add new symbol. If symbol with the same id exists it will be replaced.\n * @param {SpriteSymbol} symbol\n * @return {boolean} `true` - symbol was added, `false` - replaced\n */\nSprite.prototype.add = function add (symbol) {\n  var ref = this;\n    var symbols = ref.symbols;\n  var existing = this.find(symbol.id);\n\n  if (existing) {\n    symbols[symbols.indexOf(existing)] = symbol;\n    return false;\n  }\n\n  symbols.push(symbol);\n  return true;\n};\n\n/**\n * Remove symbol & destroy it\n * @param {string} id\n * @return {boolean} `true` - symbol was found & successfully destroyed, `false` - otherwise\n */\nSprite.prototype.remove = function remove (id) {\n  var ref = this;\n    var symbols = ref.symbols;\n  var symbol = this.find(id);\n\n  if (symbol) {\n    symbols.splice(symbols.indexOf(symbol), 1);\n    symbol.destroy();\n    return true;\n  }\n\n  return false;\n};\n\n/**\n * @param {string} id\n * @return {SpriteSymbol|null}\n */\nSprite.prototype.find = function find (id) {\n  return this.symbols.filter(function (s) { return s.id === id; })[0] || null;\n};\n\n/**\n * @param {string} id\n * @return {boolean}\n */\nSprite.prototype.has = function has (id) {\n  return this.find(id) !== null;\n};\n\n/**\n * @return {string}\n */\nSprite.prototype.stringify = function stringify () {\n  var ref = this.config;\n    var attrs = ref.attrs;\n  var stringifiedSymbols = this.symbols.map(function (s) { return s.stringify(); }).join('');\n  return wrapInSvgString(stringifiedSymbols, attrs);\n};\n\n/**\n * @return {string}\n */\nSprite.prototype.toString = function toString () {\n  return this.stringify();\n};\n\nSprite.prototype.destroy = function destroy () {\n  this.symbols.forEach(function (s) { return s.destroy(); });\n};\n\nvar SpriteSymbol = function SpriteSymbol(ref) {\n  var id = ref.id;\n  var viewBox = ref.viewBox;\n  var content = ref.content;\n\n  this.id = id;\n  this.viewBox = viewBox;\n  this.content = content;\n};\n\n/**\n * @return {string}\n */\nSpriteSymbol.prototype.stringify = function stringify () {\n  return this.content;\n};\n\n/**\n * @return {string}\n */\nSpriteSymbol.prototype.toString = function toString () {\n  return this.stringify();\n};\n\nSpriteSymbol.prototype.destroy = function destroy () {\n    var this$1 = this;\n\n  ['id', 'viewBox', 'content'].forEach(function (prop) { return delete this$1[prop]; });\n};\n\n/**\n * @param {string} content\n * @return {Element}\n */\nvar parse = function (content) {\n  var hasImportNode = !!document.importNode;\n  var doc = new DOMParser().parseFromString(content, 'image/svg+xml').documentElement;\n\n  /**\n   * Fix for browser which are throwing WrongDocumentError\n   * if you insert an element which is not part of the document\n   * @see http://stackoverflow.com/a/7986519/4624403\n   */\n  if (hasImportNode) {\n    return document.importNode(doc, true);\n  }\n\n  return doc;\n};\n\nvar BrowserSpriteSymbol = (function (SpriteSymbol$$1) {\n  function BrowserSpriteSymbol () {\n    SpriteSymbol$$1.apply(this, arguments);\n  }\n\n  if ( SpriteSymbol$$1 ) BrowserSpriteSymbol.__proto__ = SpriteSymbol$$1;\n  BrowserSpriteSymbol.prototype = Object.create( SpriteSymbol$$1 && SpriteSymbol$$1.prototype );\n  BrowserSpriteSymbol.prototype.constructor = BrowserSpriteSymbol;\n\n  var prototypeAccessors = { isMounted: {} };\n\n  prototypeAccessors.isMounted.get = function () {\n    return !!this.node;\n  };\n\n  /**\n   * @param {Element} node\n   * @return {BrowserSpriteSymbol}\n   */\n  BrowserSpriteSymbol.createFromExistingNode = function createFromExistingNode (node) {\n    return new BrowserSpriteSymbol({\n      id: node.getAttribute('id'),\n      viewBox: node.getAttribute('viewBox'),\n      content: node.outerHTML\n    });\n  };\n\n  BrowserSpriteSymbol.prototype.destroy = function destroy () {\n    if (this.isMounted) {\n      this.unmount();\n    }\n    SpriteSymbol$$1.prototype.destroy.call(this);\n  };\n\n  /**\n   * @param {Element|string} target\n   * @return {Element}\n   */\n  BrowserSpriteSymbol.prototype.mount = function mount (target) {\n    if (this.isMounted) {\n      return this.node;\n    }\n\n    var mountTarget = typeof target === 'string' ? document.querySelector(target) : target;\n    var node = this.render();\n    this.node = node;\n\n    mountTarget.appendChild(node);\n\n    return node;\n  };\n\n  /**\n   * @return {Element}\n   */\n  BrowserSpriteSymbol.prototype.render = function render () {\n    var content = this.stringify();\n    return parse(wrapInSvgString(content)).childNodes[0];\n  };\n\n  BrowserSpriteSymbol.prototype.unmount = function unmount () {\n    this.node.parentNode.removeChild(this.node);\n  };\n\n  Object.defineProperties( BrowserSpriteSymbol.prototype, prototypeAccessors );\n\n  return BrowserSpriteSymbol;\n}(SpriteSymbol));\n\nvar defaultConfig$1 = {\n  /**\n   * Should following options be automatically configured:\n   * - `syncUrlsWithBaseTag`\n   * - `locationChangeAngularEmitter`\n   * - `moveGradientsOutsideSymbol`\n   * @type {boolean}\n   */\n  autoConfigure: true,\n\n  /**\n   * Default mounting selector\n   * @type {string}\n   */\n  mountTo: 'body',\n\n  /**\n   * Fix disappearing SVG elements when <base href> exists.\n   * Executes when sprite mounted.\n   * @see http://stackoverflow.com/a/18265336/796152\n   * @see https://github.com/everdimension/angular-svg-base-fix\n   * @see https://github.com/angular/angular.js/issues/8934#issuecomment-56568466\n   * @type {boolean}\n   */\n  syncUrlsWithBaseTag: false,\n\n  /**\n   * Should sprite listen custom location change event\n   * @type {boolean}\n   */\n  listenLocationChangeEvent: true,\n\n  /**\n   * Custom window event name which should be emitted to update sprite urls\n   * @type {string}\n   */\n  locationChangeEvent: 'locationChange',\n\n  /**\n   * Emit location change event in Angular automatically\n   * @type {boolean}\n   */\n  locationChangeAngularEmitter: false,\n\n  /**\n   * Selector to find symbols usages when updating sprite urls\n   * @type {string}\n   */\n  usagesToUpdate: 'use[*|href]',\n\n  /**\n   * Fix Firefox bug when gradients and patterns don't work if they are within a symbol.\n   * Executes when sprite is rendered, but not mounted.\n   * @see https://bugzilla.mozilla.org/show_bug.cgi?id=306674\n   * @see https://bugzilla.mozilla.org/show_bug.cgi?id=353575\n   * @see https://bugzilla.mozilla.org/show_bug.cgi?id=1235364\n   * @type {boolean}\n   */\n  moveGradientsOutsideSymbol: false\n};\n\n/**\n * @param {*} arrayLike\n * @return {Array}\n */\nvar arrayFrom = function (arrayLike) {\n  return Array.prototype.slice.call(arrayLike, 0);\n};\n\nvar browser = {\n  isChrome: function () { return /chrome/i.test(navigator.userAgent); },\n  isFirefox: function () { return /firefox/i.test(navigator.userAgent); },\n\n  // https://msdn.microsoft.com/en-us/library/ms537503(v=vs.85).aspx\n  isIE: function () { return /msie/i.test(navigator.userAgent) || /trident/i.test(navigator.userAgent); },\n  isEdge: function () { return /edge/i.test(navigator.userAgent); }\n};\n\n/**\n * @param {string} name\n * @param {*} data\n */\nvar dispatchEvent = function (name, data) {\n  var event = document.createEvent('CustomEvent');\n  event.initCustomEvent(name, false, false, data);\n  window.dispatchEvent(event);\n};\n\n/**\n * IE doesn't evaluate <style> tags in SVGs that are dynamically added to the page.\n * This trick will trigger IE to read and use any existing SVG <style> tags.\n * @see https://github.com/iconic/SVGInjector/issues/23\n * @see https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/10898469/\n *\n * @param {Element} node DOM Element to search <style> tags in\n * @return {Array<HTMLStyleElement>}\n */\nvar evalStylesIEWorkaround = function (node) {\n  var updatedNodes = [];\n\n  arrayFrom(node.querySelectorAll('style'))\n    .forEach(function (style) {\n      style.textContent += '';\n      updatedNodes.push(style);\n    });\n\n  return updatedNodes;\n};\n\n/**\n * @param {string} [url] If not provided - current URL will be used\n * @return {string}\n */\nvar getUrlWithoutFragment = function (url) {\n  return (url || window.location.href).split('#')[0];\n};\n\n/* global angular */\n/**\n * @param {string} eventName\n */\nvar locationChangeAngularEmitter = function (eventName) {\n  angular.module('ng').run(['$rootScope', function ($rootScope) {\n    $rootScope.$on('$locationChangeSuccess', function (e, newUrl, oldUrl) {\n      dispatchEvent(eventName, { oldUrl: oldUrl, newUrl: newUrl });\n    });\n  }]);\n};\n\nvar defaultSelector = 'linearGradient, radialGradient, pattern, mask, clipPath';\n\n/**\n * @param {Element} svg\n * @param {string} [selector]\n * @return {Element}\n */\nvar moveGradientsOutsideSymbol = function (svg, selector) {\n  if ( selector === void 0 ) selector = defaultSelector;\n\n  arrayFrom(svg.querySelectorAll('symbol')).forEach(function (symbol) {\n    arrayFrom(symbol.querySelectorAll(selector)).forEach(function (node) {\n      symbol.parentNode.insertBefore(node, symbol);\n    });\n  });\n  return svg;\n};\n\n/**\n * @param {NodeList} nodes\n * @param {Function} [matcher]\n * @return {Attr[]}\n */\nfunction selectAttributes(nodes, matcher) {\n  var attrs = arrayFrom(nodes).reduce(function (acc, node) {\n    if (!node.attributes) {\n      return acc;\n    }\n\n    var arrayfied = arrayFrom(node.attributes);\n    var matched = matcher ? arrayfied.filter(matcher) : arrayfied;\n    return acc.concat(matched);\n  }, []);\n\n  return attrs;\n}\n\n/**\n * @param {NodeList|Node} nodes\n * @param {boolean} [clone=true]\n * @return {string}\n */\n\nvar xLinkNS = namespaces_1.xlink.uri;\nvar xLinkAttrName = 'xlink:href';\n\n// eslint-disable-next-line no-useless-escape\nvar specialUrlCharsPattern = /[{}|\\\\\\^\\[\\]`\"<>]/g;\n\nfunction encoder(url) {\n  return url.replace(specialUrlCharsPattern, function (match) {\n    return (\"%\" + (match[0].charCodeAt(0).toString(16).toUpperCase()));\n  });\n}\n\nfunction escapeRegExp(str) {\n  return str.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"); // $& means the whole matched string\n}\n\n/**\n * @param {NodeList} nodes\n * @param {string} startsWith\n * @param {string} replaceWith\n * @return {NodeList}\n */\nfunction updateReferences(nodes, startsWith, replaceWith) {\n  arrayFrom(nodes).forEach(function (node) {\n    var href = node.getAttribute(xLinkAttrName);\n    if (href && href.indexOf(startsWith) === 0) {\n      var newUrl = href.replace(startsWith, replaceWith);\n      node.setAttributeNS(xLinkNS, xLinkAttrName, newUrl);\n    }\n  });\n\n  return nodes;\n}\n\n/**\n * List of SVG attributes to update url() target in them\n */\nvar attList = [\n  'clipPath',\n  'colorProfile',\n  'src',\n  'cursor',\n  'fill',\n  'filter',\n  'marker',\n  'markerStart',\n  'markerMid',\n  'markerEnd',\n  'mask',\n  'stroke',\n  'style'\n];\n\nvar attSelector = attList.map(function (attr) { return (\"[\" + attr + \"]\"); }).join(',');\n\n/**\n * Update URLs in svg image (like `fill=\"url(...)\"`) and update referencing elements\n * @param {Element} svg\n * @param {NodeList} references\n * @param {string|RegExp} startsWith\n * @param {string} replaceWith\n * @return {void}\n *\n * @example\n * const sprite = document.querySelector('svg.sprite');\n * const usages = document.querySelectorAll('use');\n * updateUrls(sprite, usages, '#', 'prefix#');\n */\nvar updateUrls = function (svg, references, startsWith, replaceWith) {\n  var startsWithEncoded = encoder(startsWith);\n  var replaceWithEncoded = encoder(replaceWith);\n\n  var nodes = svg.querySelectorAll(attSelector);\n  var attrs = selectAttributes(nodes, function (ref) {\n    var localName = ref.localName;\n    var value = ref.value;\n\n    return attList.indexOf(localName) !== -1 && value.indexOf((\"url(\" + startsWithEncoded)) !== -1;\n  });\n\n  attrs.forEach(function (attr) { return attr.value = attr.value.replace(new RegExp(escapeRegExp(startsWithEncoded), 'g'), replaceWithEncoded); });\n  updateReferences(references, startsWithEncoded, replaceWithEncoded);\n};\n\n/**\n * Internal emitter events\n * @enum\n * @private\n */\nvar Events = {\n  MOUNT: 'mount',\n  SYMBOL_MOUNT: 'symbol_mount'\n};\n\nvar BrowserSprite = (function (Sprite$$1) {\n  function BrowserSprite(cfg) {\n    var this$1 = this;\n    if ( cfg === void 0 ) cfg = {};\n\n    Sprite$$1.call(this, deepmerge(defaultConfig$1, cfg));\n\n    var emitter = mitt();\n    this._emitter = emitter;\n    this.node = null;\n\n    var ref = this;\n    var config = ref.config;\n\n    if (config.autoConfigure) {\n      this._autoConfigure(cfg);\n    }\n\n    if (config.syncUrlsWithBaseTag) {\n      var baseUrl = document.getElementsByTagName('base')[0].getAttribute('href');\n      emitter.on(Events.MOUNT, function () { return this$1.updateUrls('#', baseUrl); });\n    }\n\n    var handleLocationChange = this._handleLocationChange.bind(this);\n    this._handleLocationChange = handleLocationChange;\n\n    // Provide way to update sprite urls externally via dispatching custom window event\n    if (config.listenLocationChangeEvent) {\n      window.addEventListener(config.locationChangeEvent, handleLocationChange);\n    }\n\n    // Emit location change event in Angular automatically\n    if (config.locationChangeAngularEmitter) {\n      locationChangeAngularEmitter(config.locationChangeEvent);\n    }\n\n    // After sprite mounted\n    emitter.on(Events.MOUNT, function (spriteNode) {\n      if (config.moveGradientsOutsideSymbol) {\n        moveGradientsOutsideSymbol(spriteNode);\n      }\n    });\n\n    // After symbol mounted into sprite\n    emitter.on(Events.SYMBOL_MOUNT, function (symbolNode) {\n      if (config.moveGradientsOutsideSymbol) {\n        moveGradientsOutsideSymbol(symbolNode.parentNode);\n      }\n\n      if (browser.isIE() || browser.isEdge()) {\n        evalStylesIEWorkaround(symbolNode);\n      }\n    });\n  }\n\n  if ( Sprite$$1 ) BrowserSprite.__proto__ = Sprite$$1;\n  BrowserSprite.prototype = Object.create( Sprite$$1 && Sprite$$1.prototype );\n  BrowserSprite.prototype.constructor = BrowserSprite;\n\n  var prototypeAccessors = { isMounted: {} };\n\n  /**\n   * @return {boolean}\n   */\n  prototypeAccessors.isMounted.get = function () {\n    return !!this.node;\n  };\n\n  /**\n   * Automatically configure following options\n   * - `syncUrlsWithBaseTag`\n   * - `locationChangeAngularEmitter`\n   * - `moveGradientsOutsideSymbol`\n   * @param {Object} cfg\n   * @private\n   */\n  BrowserSprite.prototype._autoConfigure = function _autoConfigure (cfg) {\n    var ref = this;\n    var config = ref.config;\n\n    if (typeof cfg.syncUrlsWithBaseTag === 'undefined') {\n      config.syncUrlsWithBaseTag = typeof document.getElementsByTagName('base')[0] !== 'undefined';\n    }\n\n    if (typeof cfg.locationChangeAngularEmitter === 'undefined') {\n        config.locationChangeAngularEmitter = typeof window.angular !== 'undefined';\n    }\n\n    if (typeof cfg.moveGradientsOutsideSymbol === 'undefined') {\n      config.moveGradientsOutsideSymbol = browser.isFirefox();\n    }\n  };\n\n  /**\n   * @param {Event} event\n   * @param {Object} event.detail\n   * @param {string} event.detail.oldUrl\n   * @param {string} event.detail.newUrl\n   * @private\n   */\n  BrowserSprite.prototype._handleLocationChange = function _handleLocationChange (event) {\n    var ref = event.detail;\n    var oldUrl = ref.oldUrl;\n    var newUrl = ref.newUrl;\n    this.updateUrls(oldUrl, newUrl);\n  };\n\n  /**\n   * Add new symbol. If symbol with the same id exists it will be replaced.\n   * If sprite already mounted - `symbol.mount(sprite.node)` will be called.\n   * @fires Events#SYMBOL_MOUNT\n   * @param {BrowserSpriteSymbol} symbol\n   * @return {boolean} `true` - symbol was added, `false` - replaced\n   */\n  BrowserSprite.prototype.add = function add (symbol) {\n    var sprite = this;\n    var isNewSymbol = Sprite$$1.prototype.add.call(this, symbol);\n\n    if (this.isMounted && isNewSymbol) {\n      symbol.mount(sprite.node);\n      this._emitter.emit(Events.SYMBOL_MOUNT, symbol.node);\n    }\n\n    return isNewSymbol;\n  };\n\n  /**\n   * Attach to existing DOM node\n   * @param {string|Element} target\n   * @return {Element|null} attached DOM Element. null if node to attach not found.\n   */\n  BrowserSprite.prototype.attach = function attach (target) {\n    var this$1 = this;\n\n    var sprite = this;\n\n    if (sprite.isMounted) {\n      return sprite.node;\n    }\n\n    /** @type Element */\n    var node = typeof target === 'string' ? document.querySelector(target) : target;\n    sprite.node = node;\n\n    // Already added symbols needs to be mounted\n    this.symbols.forEach(function (symbol) {\n      symbol.mount(sprite.node);\n      this$1._emitter.emit(Events.SYMBOL_MOUNT, symbol.node);\n    });\n\n    // Create symbols from existing DOM nodes, add and mount them\n    arrayFrom(node.querySelectorAll('symbol'))\n      .forEach(function (symbolNode) {\n        var symbol = BrowserSpriteSymbol.createFromExistingNode(symbolNode);\n        symbol.node = symbolNode; // hack to prevent symbol mounting to sprite when adding\n        sprite.add(symbol);\n      });\n\n    this._emitter.emit(Events.MOUNT, node);\n\n    return node;\n  };\n\n  BrowserSprite.prototype.destroy = function destroy () {\n    var ref = this;\n    var config = ref.config;\n    var symbols = ref.symbols;\n    var _emitter = ref._emitter;\n\n    symbols.forEach(function (s) { return s.destroy(); });\n\n    _emitter.off('*');\n    window.removeEventListener(config.locationChangeEvent, this._handleLocationChange);\n\n    if (this.isMounted) {\n      this.unmount();\n    }\n  };\n\n  /**\n   * @fires Events#MOUNT\n   * @param {string|Element} [target]\n   * @param {boolean} [prepend=false]\n   * @return {Element|null} rendered sprite node. null if mount node not found.\n   */\n  BrowserSprite.prototype.mount = function mount (target, prepend) {\n    if ( target === void 0 ) target = this.config.mountTo;\n    if ( prepend === void 0 ) prepend = false;\n\n    var sprite = this;\n\n    if (sprite.isMounted) {\n      return sprite.node;\n    }\n\n    var mountNode = typeof target === 'string' ? document.querySelector(target) : target;\n    var node = sprite.render();\n    this.node = node;\n\n    if (prepend && mountNode.childNodes[0]) {\n      mountNode.insertBefore(node, mountNode.childNodes[0]);\n    } else {\n      mountNode.appendChild(node);\n    }\n\n    this._emitter.emit(Events.MOUNT, node);\n\n    return node;\n  };\n\n  /**\n   * @return {Element}\n   */\n  BrowserSprite.prototype.render = function render () {\n    return parse(this.stringify());\n  };\n\n  /**\n   * Detach sprite from the DOM\n   */\n  BrowserSprite.prototype.unmount = function unmount () {\n    this.node.parentNode.removeChild(this.node);\n  };\n\n  /**\n   * Update URLs in sprite and usage elements\n   * @param {string} oldUrl\n   * @param {string} newUrl\n   * @return {boolean} `true` - URLs was updated, `false` - sprite is not mounted\n   */\n  BrowserSprite.prototype.updateUrls = function updateUrls$1 (oldUrl, newUrl) {\n    if (!this.isMounted) {\n      return false;\n    }\n\n    var usages = document.querySelectorAll(this.config.usagesToUpdate);\n\n    updateUrls(\n      this.node,\n      usages,\n      ((getUrlWithoutFragment(oldUrl)) + \"#\"),\n      ((getUrlWithoutFragment(newUrl)) + \"#\")\n    );\n\n    return true;\n  };\n\n  Object.defineProperties( BrowserSprite.prototype, prototypeAccessors );\n\n  return BrowserSprite;\n}(Sprite));\n\nvar ready$1 = createCommonjsModule(function (module) {\n/*!\n  * domready (c) Dustin Diaz 2014 - License MIT\n  */\n!function (name, definition) {\n\n  { module.exports = definition(); }\n\n}('domready', function () {\n\n  var fns = [], listener\n    , doc = document\n    , hack = doc.documentElement.doScroll\n    , domContentLoaded = 'DOMContentLoaded'\n    , loaded = (hack ? /^loaded|^c/ : /^loaded|^i|^c/).test(doc.readyState);\n\n\n  if (!loaded)\n  { doc.addEventListener(domContentLoaded, listener = function () {\n    doc.removeEventListener(domContentLoaded, listener);\n    loaded = 1;\n    while (listener = fns.shift()) { listener(); }\n  }); }\n\n  return function (fn) {\n    loaded ? setTimeout(fn, 0) : fns.push(fn);\n  }\n\n});\n});\n\nvar spriteNodeId = '__SVG_SPRITE_NODE__';\nvar spriteGlobalVarName = '__SVG_SPRITE__';\nvar isSpriteExists = !!window[spriteGlobalVarName];\n\n// eslint-disable-next-line import/no-mutable-exports\nvar sprite;\n\nif (isSpriteExists) {\n  sprite = window[spriteGlobalVarName];\n} else {\n  sprite = new BrowserSprite({\n    attrs: {\n      id: spriteNodeId,\n      'aria-hidden': 'true'\n    }\n  });\n  window[spriteGlobalVarName] = sprite;\n}\n\nvar loadSprite = function () {\n  /**\n   * Check for page already contains sprite node\n   * If found - attach to and reuse it's content\n   * If not - render and mount the new sprite\n   */\n  var existing = document.getElementById(spriteNodeId);\n\n  if (existing) {\n    sprite.attach(existing);\n  } else {\n    sprite.mount(document.body, true);\n  }\n};\n\nif (document.body) {\n  loadSprite();\n} else {\n  ready$1(loadSprite);\n}\n\nvar sprite$1 = sprite;\n\nreturn sprite$1;\n\n})));\n\n\n//# sourceURL=webpack://ticview/./node_modules/svg-sprite-loader/runtime/browser-sprite.build.js?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	(() => {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./app/web/index.js");
/******/ 	
/******/ })()
;