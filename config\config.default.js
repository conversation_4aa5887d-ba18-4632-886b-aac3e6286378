"use strict";
const path = require("path");
const fs = require("fs");

module.exports = (appInfo) => {
  const config = (exports = {});
  console.log(path.join(appInfo.baseDir, "../cms/app/public/upload"));

  // use for cookie sign key, should change to your own and keep security
  config.keys = appInfo.name + "_1526494317988_1266";

  // add your config here
  config.middleware = ["error404"];
  config.siteFile = {
    "/favicon.ico": "/static/favicon.ico",
    "/robots.txt": fs.readFileSync(appInfo.baseDir + "/app/public/robots.txt"),
    "/silian.txt": fs.readFileSync(appInfo.baseDir + "/app/public/silian.txt"),
    "/silian1.txt": fs.readFileSync(appInfo.baseDir + "/app/public/silian1.txt"),
    "/silian2.txt": fs.readFileSync(appInfo.baseDir + "/app/public/silian2.txt"),
    "/silian3.txt": fs.readFileSync(appInfo.baseDir + "/app/public/silian3.txt"),
    "/silian4.txt": fs.readFileSync(appInfo.baseDir + "/app/public/silian4.txt"),
    "/silian5.txt": fs.readFileSync(appInfo.baseDir + "/app/public/silian5.txt"),
    "/sitemap.txt": fs.readFileSync(appInfo.baseDir + "/app/public/sitemap.txt"),
    "/ad/": fs.readFileSync(appInfo.baseDir + "/ad/index.html"),
    // '/sogousiteverification.txt': fs.readFileSync(appInfo.baseDir + '/app/public/sogousiteverification.txt'),
    // '/e8bf7d7507d96159edc7bcb8de38a064.txt': fs.readFileSync(appInfo.baseDir + '/app/public/e8bf7d7507d96159edc7bcb8de38a064.txt'),
    // '/baidu_verify_QTOJN7kgAr.html': fs.readFileSync(appInfo.baseDir + '/app/public/baidu_verify_QTOJN7kgAr.html'),
  };

  config.view = {
    defaultExt: ".html",
    mapping: {
      ".ejs": "ejs",
      ".html": "ejs",
    },
  };

  // 日志写入目录
  exports.logger = {
    dir: "/mnt/datadisk1/applogs/SGSmall",
  };

  const mysql = require('./mysql')(appInfo)
  config.mysql = mysql.mysql;

  exports.sentry = {
    dsn: 'https://<EMAIL>/11',
  };

  config.security = {
    csrf: {
      enable: false,
      ignoreJSON: true,
    },
    // 开通iframe引用，用于百度点击图
    // xframe: {
    //   enable: true,
    //   value: "ALLOW-FROM http://baidu.com",
    // },
    // 跨域白名单
    domainWhiteList: [
      'http://localhost:9511',
      'sgsonline.com.cn',
      'sgsstore.com.cn',
      'sgsmall.com.cn'
    ]
  };

  config.cors = {
    // origin: "*", // 匹配规则  域名+端口  *则为全匹配 （注释则只允许白名单）
    allowMethods: "GET,HEAD,PUT,POST,DELETE,PATCH",
  };

  config.multipleStatic = [
    {
      prefix: "/static",
      dir: path.join(appInfo.baseDir, "app/public"),
      // dir: '../../sgsweb/cms/app/public',
      //maxAge: 31536000,
      maxAge: 0,
    },
    // ali static files
    // {
    //     prefix: '/static/upload',
    //     dir: path.join(appInfo.baseDir, '../cms/app/public/upload'),
    //     //maxAge: 31536000,
    //     maxAge: 0,
    // },
    // azure static files
    {
      prefix: "/static/upload",
      // dir: path.join(appInfo.baseDir, '../sgsweb/cms/app/public/upload'),
      // dir: '/mnt/datadisk1/sgsweb/cms/app/public/upload',
      dir: "../../sgsweb/cms/app/public/upload",
      //maxAge: 31536000,
      maxAge: 0,
    },
    // {
    //     prefix: '/',
    //     dir: path.join(appInfo.baseDir, 'app/static'),
    //     maxAge: 0,
    // }
  ];

  config.urlrecord = {};

  /* files whitlist*/
  config.multipart = {
    whitelist: [
      ".jpg",
      ".jpeg", // image/jpeg
      ".png", // image/png, image/x-png
      ".gif", // image/gif
      ".bmp", // image/bmp
      ".wbmp", // image/vnd.wap.wbmp
      ".webp",
      ".tif",
      ".psd",
      ".pdf",
      // text
      ".svg",
      ".js",
      ".jsx",
      ".json",
      ".css",
      ".less",
      ".html",
      ".htm",
      ".xml",
      // tar
      ".zip",
      ".gz",
      ".tgz",
      ".gzip",
      // video
      ".mp3",
      ".mp4",
      ".avi",
      // document
      ".doc",
      ".docx",
      ".xls",
      ".xlsx",
      ".ppt",
      ".pptx",
    ],
  };

  // UAT
  config.alinode = {
    appid: "84341",
    secret: "693d0847759268517a29e384eb7dd0a59936e12b",
  };

  // prod
  // config.alinode = {
  //   appid: '84447',
  //   secret: '3161793d2c9d1898450b644e9ed546f3ad1b9ab7',
  // };

  return config;
};
