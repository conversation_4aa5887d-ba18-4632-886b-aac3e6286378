const Controller = require('egg').Controller;
const util = require('./util');
const {
  mallPid,
  mallPcode,
  env
} = require('../../config/info').siteInfo;

class ActiveController extends Controller {
  async ticket() {
    const {
      ctx
    } = this;

    const mobile = await util.isMobile(ctx);
    if (mobile) {
      ctx.redirect(encodeURI(`${mobileHost}/ticket`))
      return;
    }
    const host = await util.getHost(this.app.locals.env, ctx);

    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi, } = await ctx.service.common.navigation();
    const hotsku = await ctx.service.search.hotsku();
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;

    let detail = {
      name: 'CONTACT US'
    };

    await ctx.render('en/ticket', {
      is<PERSON><PERSON><PERSON>,
      isI<PERSON><PERSON>bot,
      is<PERSON><PERSON><PERSON><PERSON>,
      userInfo,
      isLogin,
      host,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      hot: hotsku.list,
      detail,
      csrf: ctx.csrf,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
    })
  }

  async inquiry() {
    const {
      ctx
    } = this;

    const mobile = await util.isMobile(ctx);
    if (mobile) {
      ctx.redirect(encodeURI(`${mobileHost}/ticket`))
      return;
    }
    const host = await util.getHost(this.app.locals.env, ctx);

    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi, } = await ctx.service.common.navigation();
    const hotsku = await ctx.service.search.hotsku();

    let detail = {
      name: 'CONTACT US'
    };

    await ctx.render('en/inquiry', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      host,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      hot: hotsku.list,
      detail,
      csrf: ctx.csrf,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
    })
  }
}

module.exports = ActiveController;
