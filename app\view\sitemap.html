<% include header.html %>
<div class="myLocation">
    <div class="myLocationBox">
        <div class="myLocation-word1"><a href="/">首页</a></div>
        <div class="myLocation-icon1"></div>
        <div class="myLocation-word2">网站地图</div>
    </div>
</div>
<div class="SearchBox" style="padding-bottom: 55px;">
    <div class="searchleftBox sitemap">
        <% for(var item of detail.sitemap){ %>
        <dl>
            <dt><%- item.title %></dt>
            <dd>
                <ul class="clearfix">
                    <% for(var list of item.list){ %>
                    <li><a href='<%- list.link %>' target="_blank"><%- list.subTitle %></a></li>
                    <% } %>
                </ul>
            </dd>
        </dl>
        <% } %>
    </div>
    <div class="searchrightBox">
        <div class="searchrightBox1">
            <div class="your">
                <div class="yours">
                    <span class="your-word">行业解决方案</span>
                    <span class="dragbut">
                        <img src="<%- locals.static %>/images/jianhao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons first">
                    <div class="yourcon">
                        <ul>
                            <% if(tradeNavi && tradeNavi.length > 0){ %>
                            <% for (var item of tradeNavi){ %>
                            <li class="yourconLi">
                                <a href="/industry/<%- item.alias %>/<%- locals.keywordid %>">
                                    <%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="your">
                <div class="yours">
                    <span class="your-word">我们的服务</span>
                    <span class="dragbut">
                        <img src="<%- locals.static %>/images/jiahao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons">
                    <div class="yourcon">
                        <ul>
                            <% if(serviceNavi && serviceNavi.length > 0){ %>
                            <% for (var item of serviceNavi){ %>
                            <li class="yourconLi">
                                <a href="/service/<%- item.alias %>/<%- locals.keywordid %>">
                                    <%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="your">
                <div class="yours">
                    <span class="your-word">热点服务</span>
                    <span class="dragbut">
                        <img src="<%- locals.static %>/images/jiahao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons">
                    <div class="yourcon">
                        <ul>
                            <% if(hot && hot.length > 0){ %>
                            <% for (var item of hot){ %>
                            <li class="yourconLi">
                              <a href="/sku/<%- item.alias %>/<%- item.id %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $(function () {
        var $_yours = $(".yours");
        $_yours.click(function () {
            var $_con = $(this).siblings(".your-cons");
            if ($_con.css("height") == "0px") {
                $_con.animate({ "height": 486 }).end().parents(".your").siblings(".your").children(".your-cons").animate({ "height": 0 });
                $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jianhao.png');
                $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(0).attr("src", "<%- locals.static %>/images/jiahao.png");
                $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(1).attr("src", "<%- locals.static %>/images/jiahao.png")
            } else {
                $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jiahao.png');
                $_con.animate({ "height": 0 });
            }
        })
    });
</script>
<% include footer.html %>