<% include ./common/head.html %>

<body class="n_home">
  <% if(locals.env != 'prod'){ %>
  <noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-W9K275F" height="0" width="0"
      style="display:none;visibility:hidden"></iframe>
  </noscript>
  <% }else{ %>
  <noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WQLR3T8" height="0" width="0"
      style="display:none;visibility:hidden"></iframe></noscript>
  <% } %>
  <% include ./pages/home/<USER>
  <% include ./pages/home/<USER>
  <script src="/static/svg-sprite.js"></script>
  <div class="n_nav" id='n_nav'>
    <ul>
      <li data-item='service' class="active">
        <a href='javascript:void(0)' rel="nofollow"><em>我们的服务</em><i></i></a>
        <span></span>
      </li>
      <li data-item='industry'>
        <a href='javascript:void(0)'><em>您的行业</em><i></i></a>
        <span></span>
      </li>
      <li>
        <a href="/information"><em>资源中心</em><i></i></a>
        <span></span>
        <dl>
          <% if(informationTypes && informationTypes.length > 0){ %>
          <% for (var item of informationTypes){ %>
          <dd>
            <a href='/information?type=<%- item.id %>'><%- item.name %></a>
          </dd>
          <% } %>
          <% } %>
          <dd>
            <a href="/news">集团新闻</a>
          </dd>
          <dd>
            <a href="/files">资料下载</a>
          </dd>
        </dl>
      </li>
      <li>
          <label class="new"></label>
          <a href="https://a.sgsonline.com.cn/" rel="nofollow" target="_blank">在线讲堂</a>
          <span></span>
      </li>
      <!-- <li>
          <a href="/baojia?spm=EJHxGX9b.1YCEZc6a..." rel="nofollow">业务咨询</a>
          <span></span>
      </li> -->
      <li>
          <a href="<%- memberUrl %>/questionnaire/step1?fromSource=navigation&fromUrl=<%- clientUrl %>" rel="nofollow" target="_blank">快速报价</a>
          <span></span>
      </li>
      <li>
          <a href="<%- portalHost %>/order-online" rel="nofollow" target="_blank">检测超市</a>
          <span></span>
      </li>
      <li>
        <a href="/overview/aboutSGS"><em>关于SGS</em></a>
        <span></span>
      </li>
    </ul>
  </div>
  <div class="n_wrap n_position_box">
    <div class="n_category">
      <div id='service'></div>
      <div id="industry"></div>
    </div>
    <div class="n_banner">
      <div class="swiper-container">
        <div class="swiper-wrapper">
          <% if(detail.banners){ %>
          <% for(item of detail.banners){ %>
          <div class="swiper-slide bannerLi1"
            style="background-image:url(<%- item.img_path.replace(/\/static/g,locals.static) %>);">
            <a target="_blank" <% if(item.btn_url){ %>href="<% if(locals.env == 'prod'){ %> <%- item.btn_url.replace('https://www.sgsonline.com.cn', '') %> <% }else{ %> <%- item.btn_url.replace('http://**************', '') %> <% } %>"<% } %>
              target="blank" 
              style="display:block; width:100%; height:100%;">
                <div class="baa">
                  <div class="bannerDetail"><%- item.btn_description %></div>
                  <% if(item.btn_value){ %>
                    <a target="_blank" class="bannerMore"
                      <% if(item.btn_url){ %>href="<% if(locals.env == 'prod'){ %> <%- item.btn_url.replace('https://www.sgsonline.com.cn', '') %> <% }else{ %> <%- item.btn_url.replace('http://**************', '') %> <% } %>"<% } %>
                      target="blank">
                      <div class="bannermoreBox">
                      </div>
                      <div class="bannermoreword"><%- item.btn_value %></div>
                    </a>
                  <% } %>
                </div>
            </a>
          </div>
          <% } %>
          <% }else{ %>
          <!-- <div class="swiper-slide bannerLi1" style="background: none"></div> -->
          <% } %>
        </div>
        <% if(detail.banners && detail.banners.length > 1){ %>
        <div class="swiper-button-prev"></div>
        <div class="swiper-button-next"></div>
        <div class="swiper-bottom">
          <div class="swiper-pagination"></div>
        </div>
        <% } %>
      </div>
    </div>
    <div class='home_side'>
      <div class="welcome_info">
        <!-- <div class="welcome_info_portrait"></div> -->
        <div class="welcome_info_tips">
          <% if(isLogin){ %>
          <p>Hi,<%- userInfo.userNick || userInfo.userPhone || userInfo.userEmailCopy || userInfo.userEmail %></p>
          <% }else{ %>
          <p>Hi，您好</p>
          <% } %>
          <em>在线下单，轻松管理我的订单.</em>
        </div>
      </div>
      <% if(isLogin){ %>
      <div class="account_info">
          <a href="<%- memberUrl %>/quote/list">
              <em class='cartNumberClass'><%- enquiryNum || 0 %></em>
              <span>咨询</span>
          </a>
        <a href="javascript:;"  onclick="goOrderList()">
          <em><%- orderNum >=1000?'999+':orderNum  %></em>
          <span>订单</span>
        </a>
		<a href="<%- memberUrl %>/member/coupon">
		  <em class='cartNumberClass'><%- couponNum || 0 %></em>
		  <span>优惠券</span>
		</a>
      </div>

      <% }else{ %>
      <div class="login_info">
        <a class="login_info_active" href="<%- memberUrl %>/login">
          <span class="login_info_active_text">登录/注册</span>
          <span class="login_info_active_layout"></span>
        </a>
        <div class="static_items">
          <span>技术文章</span>
          <span>下载资料</span>
          <span>免费工具</span>
        </div>
      </div>
      <% } %>
      <div class="shortcut_menu">
        <a href='<%- memberUrl %>/questionnaire/step1?fromSource=homeBannerSide&fromUrl=<%- clientUrl %>' target="_blank">
          <i class="shortcut_menu_inquiry"></i>
          <span>快速报价</span>
        </a>
        <a href='<%- portalHost %>/order-online' target="_blank">
          <i class="shortcut_menu_step"></i>
          <span>检测超市</span>
        </a>
        <a href='/DocCheck' target="_blank">
          <i class="shortcut_menu_report"></i>
          <span>报告真伪</span>
        </a>
      </div>
      <div class="informaition_info">
        <div class="informaition_info_tit">
          <span class="active">展会及研讨会</span>
          <span>热点资讯</span>
          <a href='/information?type=88' target="_blank">更多></a>
        </div>
        <div class="informaition_info_list" id='home_informaition'>
          <ul>
            <% if(informationList.seminar && informationList.seminar.length > 0){ %>
            <% for (var item of informationList.seminar){ %>
            <li>
              <a target="_blank" href='/case/<%- item.alias %>/detail-<%- item.id %>.html'><%- item.title %></a>
            </li>
            <% } %>
            <% } %>
          </ul>
        </div>
        <div class="informaition_info_list" id='home_informaition_1' style="display: none;">
          <ul>
            <% if(informationList.other && informationList.other.length > 0){ %>
            <% for (var item of informationList.other){ %>
            <li>
              <a target="_blank" href='/case/<%- item.alias %>/detail-<%- item.id %>.html'><%- item.title %></a>
            </li>
            <% } %>
            <% } %>
          </ul>
        </div>
      </div>
    </div>
  </div>
    <div class="homeConBox">
      <div class="homeCon9">
        <div class="wrap">
            <div class="homeCon-head">
              <!-- 楼层标题 -->
                <% if(pageFloors.floorUrl){ %>
                  <a target="_blank" href="<%- pageFloors.floorUrl %>" class="icon"><%- pageFloors.floorName %></a>
                <% } else { %>
                  <span><%- pageFloors.floorName %></span>
                <% } %>
                <!-- 楼层更多链接-->
                <% if(pageFloors.items){ %>
                  <% for(let [n, node] of pageFloors.items.entries()){ %>
                    <a class="more <% if(n == 0){ %>active<% } %>" target="_blank" href="<%- node.itemUrl %>" rel="nofollow">
                      <% if(node.itemName){ %>
                        <em>更多</em>
                        <i></i>
                      <% } %>
                    </a>
                  <% } %>
                <% } %>
                <!-- 楼层tab选项卡 -->
                <ul>
                    <% if(pageFloors.items){ %>
                      <% for(let [n, node] of pageFloors.items.entries()){ %>
                      <li class="<% if(n == 0){ %>active<% } %>">
                        <%- node.itemName %>
                      </li>
                      <% } %>
                    <% } %>
                    <li class="homeCon-head-bg"></li>
                </ul>
            </div>
            <!-- 最新成交信息 -->
            <div class="onlineOrder_msg" id='onlineOrder_msg'>
              <span class="title">最新成交</span>
              <ul>
                <% for(let item of pageOnlineOrder){ %>
                  <li>
                    <span class="date"><%- item.date %></span>
                    <span class="province"><%- item.province %></span>
                    <span class="userName"><%- item.userName %></span>
                    <a href="<%- storeUrl %>/item.html?item_id=<%- item.prodId %>" target="_blank" class="category"><%- item.productName %></a>
                    <span class="price">￥ <%- item.orderAmount %></span>
                  </li>
                <% } %>
              </ul>
            </div>
             <div class="homeCon9-body clearfix">
              <% if(pageFloors.items){ %>
                <% for(let [n, node] of pageFloors.items.entries()){ %>
                  <div class="homeCon9-item <% if(n == 0){ %>active<% } %>">
                      <div class="side">
                      <h3><%- node.itemTitle %></h3>
                      <% if(node.shows.length){ %>
                        <ul>
                          <% for(let [index, item] of node.shows.entries()){ %>
                          <% if(index < 3){ %>
                          <li class="over" style="background-image: url('<%- item.showImg %>')">
                            <a href='<%- item.businessUrl %>' target="_blank">
                              <label><%- index + 1 %></label>
                              <h2><%- item.showTitle %></h2>
                              <p class="sub-title"><%- item.showDesc %></p>
                              <p class="num"><span>￥<i><%- item.showPrice %></i>起</span><em>已售<%- item.ct > 9999 ? '9999+' : item.ct %></em></p>
                            </a>
                          </li>
                          <% } %>
                          <% } %>
                        </ul>
                      <% } %>
                      </div>
                      <ul class="list">
                        <% for(let [index, item] of node.shows.entries()){ %>
                          <% if(index >= 3){ %>
                            <li class="over">
                              <a href='<%- item.businessUrl %>' target="_blank">
                                <div class="img">
                                  <% if(item.isHot == "1"){ %>
                                    <label class="hot">热门</label>
                                  <% } %>
                                  <img src="<%- item.showImg %>" alt='<%- item.showAlt || item.title %>' />
                                </div>
                                <h2>
                                  <span><%- item.showTitle %></span>
                                  <em>已售<%- item.ct > 9999 ? '9999+' : item.ct %></em>
                                </h2>
                                <h3>
                                  <span><%- item.showDesc %></span>
                                  <em>￥<i><%- item.showPrice %></i>起</em>
                                </h3>
                              </a>
                            </li>
                            <% } %>
                        <% } %>
                      </ul>
                  </div>
                <% } %>
                <% } %>
            </div>
            <% if(pageFloors.items && pageFloors.items.length > 1){ %>
              <div class="homeCon9-tab clearfix">
                <% if(pageFloors.items){ %>
                  <% for(let [n, node] of pageFloors.items.entries()){ %>
                    <span class="<% if(n == 0){ %>active<% } %>"></span>
                  <% } %>
                <% } %>
              </div>
            <% } %>
        </div>
    </div>
        <% if(detail.plan.isShow){ %>
          <div class="homeCon10">
            <div class="wrap">
              <div class="homeCon-head">
                <% if(detail.planTitle.link){ %>
                  <a href="<%- detail.planTitle.link %>"><%- detail.planTitle.title %></a>
                <% } else { %>
                  <span><%- detail.planTitle.title %></span>
                <% } %>
              </div>
              <div class="oiq-wrap clearfix">
                  <div class="oiq-bg" style="background-image: url('<%- detail.plan.backgroundImg %>');" onclick="window.location.href = '/oiq'">
                    <a href='/oiq'>
                      <span>查看详情</span>
                      <i></i>
                    </a>
                  </div>
                  <div class="oiq-plan">
                    <h3>简单3个步骤，获取定制方案</h3>
                    <div class="oiq_msg" id='oiq_msg'>
                      <ul>
                        <% for(let oiq of oiqList){ %>
                          <li>
                            <div class="time"><%- oiq.dateStr %></div>
                            <div>
                              <b><%- oiq.city %></b>
                              <span><%- oiq.userName %></span>
                              <span><%- oiq.quotationTimeStr %>收到了关于</span>
                              <b class="lastCategory"><%- oiq.lastCategory %></b>
                              <span>的测试方案</span>
                            </div>
                          </li>
                        <% } %>
                      </ul>
                    </div>
                    <div class="oiq-search">
                      <a href='javascrpit:void(0);' onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=-1', '&')">
                        <p>请输入产品关键词搜索产品类别</p>
                        <i></i>
                      </a>
                    </div>
                    <div class="oiq-category">
                      <a href='javascrpit:void(0);' onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1581,1582,1583,1632', '&')">不锈钢</a>
                      <a href='javascrpit:void(0);' onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1581,1921,1936', '&')">焊板</a>
                      <a href='javascrpit:void(0);' onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1332,1350,1353', '&')">聚丙烯</a>
                      <a href='javascrpit:void(0);' onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=1332,1391,1393', '&')">密封圈</a>
                      <a href='javascrpit:void(0);' onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=2359,2398,2417,2419', '&')">餐盘</a>
                      <a href='javascrpit:void(0);' onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=2359,2360,2361,2364', '&')">烘焙模具</a>
                      <a href='javascrpit:void(0);' onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=2089,2461', '&')">毛绒玩具</a>
                      <a href='javascrpit:void(0);' onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=2243,2248', '&')">纸箱</a>
                      <a href='javascrpit:void(0);' onclick="jumpOIQAddtionSource('<%- memberUrl %>/questionnaire/step1?category=5293,5310,5317,5318', '&')">餐椅</a>
                    </div>
                    <a class="oiq_start" href='<%- memberUrl %>/questionnaire/step1?fromSource=homeFloorPlan&fromUrl=<%- clientUrl %>' target="_blank">
                      <span class="oiq_start_text">立即开始</span>
                      <span class="oiq_start_layout"></span>
                    </a>
                  </div>
              </div>
            </div>
          </div>
        <% } %>
        <% for(let item of detail.items){ %>
        <% if(item.type == 1){ %>
        <div class="homeCon1">
            <div class="homeContitle">
                <% for(let [n,node] of item.node.entries()){ %>
                <span class="title<% if(n == 0){ %> active<% } %>"><%- node.title %></span>
                <% } %>
                <span class="spanL">
                    <% for(let [n,node] of item.node.entries()){ %>
                    <span class="spanL-word" <% if(n != 0){ %> style="display:none" <% } %>><a
                            href="<% if(locals.env == 'prod'){ %> <%- node.link.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- node.link.replace('http://**************', '') %> <% } %>">查看更多</a></span>
                    <% } %>
                    <div class="spanL-icon"></div>
                </span>
                <div class="titleBor">

                </div>
            </div>
            <div class="homeConCon1">
                <% for(let [n,node] of item.node.entries()){ %>
                <div <% if(n!=0){ %>style="display:none" <% } %>>
                    <div class="homeConConL">
                        <% if(node.skus[0].thumb_img){ %>
                        <a
                            href="<% if(locals.env == 'prod'){ %> <%- node.skus[0].url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- node.skus[0].url.replace('http://**************', '') %> <% } %>">
                            <em>立即查看 ></em>
                            <img src="<%- node.skus[0].thumb_img.replace(/\/static/g,locals.static) %>" alt='<%- node.skus[0].alt || node.skus[0].name %>'>
                            <% if(node.skus[0].is_buy == 1){ %><span class="buyicon"></span><% } %>
                        </a>
                        <% } %>
                    </div>
                    <div class="homeConConR">
                        <ul>
                            <% for(let [i,o] of node.skus.entries()){ %>
                            <% if(i > 0 && o.thumb_img){ %>
                            <li class="homeConConRLi over">
                                <a
                                    href="<% if(locals.env == 'prod'){ %> <%- o.url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- o.url.replace('http://**************', '') %> <% } %>">
                                    <img src="<%- o.thumb_img.replace(/\/static/g,locals.static) %>" alt="<%- o.alt || o.name %>"
                                        class="homeConConRLi-img" style="display: block;width:100%;height:142px;">
                                    <% if(o.is_buy == 1){ %><span class="buyicon"></span><% } %>
                                    <div class="homeConConRLi-word1"><%- o.name %></div>
                                    <div class="homeConConRLi-word2"><%- o.sub_title %></div>
                                </a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
                <% } %>
            </div>
        </div>
        <% }else if(item.type == 2){ %>
        <div class="homeCon2">
            <div class="homeCon2Box">
                <div class="homeContitle">
                    <% for(let [n,node] of item.node.entries()){ %>
                    <span class="title<% if(n == 0){ %> active<% } %>"><%- node.title %></span>
                    <% } %>

                    <span class="spanL">
                        <% for(let [n,node] of item.node.entries()){ %>
                        <span class="spanL-word" <% if(n != 0){ %> style="display:none" <% } %>><a
                                href="<% if(locals.env == 'prod'){ %> <%- node.link.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- node.link.replace('http://**************', '') %> <% } %>">查看更多</a></span>
                        <% } %>
                        <div class="spanL-icon"></div>
                    </span>
                    <div class="titleBor">

                    </div>
                </div>

                <div class="homeConCon2">
                    <% for(let [n,node] of item.node.entries()){ %>
                    <ul<% if(n!=0){ %> style="display:none" <% } %>>
                        <% for(let [i,o] of node.skus.entries()){ %>
                        <% if(i < 3 && o.thumb_img){ %>
                        <li class="homeConCon2Li over">
                            <a
                                href="<% if(locals.env == 'prod'){ %> <%- o.url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- o.url.replace('http://**************', '') %> <% } %>">
                                <img src="<%- o.thumb_img.replace(/\/static/g,locals.static) %>" alt="<%- o.alt || o.name %>"
                                    class="homeConConRLi-img" style="width:390px;height:192px;">
                                <% if(o.is_buy == 1){ %><span class="buyicon"></span><% } %>
                                <div class="homeConCon2Li-word1"><%- o.name %></div>
                                <div class="homeConCon2Li-borderBox">
                                    <div class="homeConCon2Li-border"></div>
                                </div>
                                <div class="homeConConRLi-word2"><%- o.sub_title %></div>
                            </a>
                        </li>
                        <% } %>
                        <% } %>
                        </ul>
                        <% } %>
                </div>
            </div>
        </div>
        <% }else if(item.type == 3){ %>
        <div class="homeCon4">
            <div class="homeCon2Box">
                <div class="homeContitle">
                    <% for(let [n,node] of item.node.entries()){ %>
                    <span class="title<% if(n == 0){ %> active<% } %>"><%- node.title %></span>
                    <% } %>

                    <span class="spanL">
                        <% for(let [n,node] of item.node.entries()){ %>
                        <span class="spanL-word" <% if(n != 0){ %> style="display:none" <% } %>><a
                                href="<% if(locals.env == 'prod'){ %> <%- node.link.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- node.link.replace('http://**************', '') %> <% } %>">查看更多</a></span>
                        <% } %>
                        <div class="spanL-icon"></div>
                    </span>
                    <div class="titleBor">

                    </div>
                </div>

                <div class="homeConCon2">
                    <% for(let [n,node] of item.node.entries()){ %>
                    <ul<% if(n!=0){ %> style="display:none" <% } %>>
                        <% for(let [i,o] of node.skus.entries()){ %>
                        <% if(i < 3 && o.thumb_img){ %>
                        <li class="homeConCon2Li over homeConCon4Li">
                            <img alt="<%- o.name %>" src="<%- o.thumb_img.replace(/\/static/g,locals.static) %>" alt="<%- o.alt || o.name %>"
                                class="homeConCon2Li-img2">
                            <img  alt="<%- o.alt || o.name %>" src="<%- locals.static %>/images/opacity.png" alt=""
                                class="homeConCon2Li-img1">
                            <div class="homeConCon4Li-word1"><%- o.name %></div>
                            <div class="homeConCon4Li-word2"><%- o.sub_title %></div>
                            <a class="learnMore"
                                href="<% if(locals.env == 'prod'){ %> <%- o.url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- o.url.replace('http://**************', '') %> <% } %>">
                                <div class="deepen"></div>
                                <div class="learnMore-word">了解更多</div>
                                <div class="learnMore-icon"></div>
                            </a>
                        </li>
                        <% } %>
                        <% } %>
                        </ul>
                        <% } %>
                </div>
            </div>
        </div>
        <% }else if(item.type == 4){ %>
        <!-- 新楼层7格子 -->
        <div class="homeCon8">
            <div class="wrap">
                <div class="homeCon-head">
                    <% if(item.link){ %>
                      <a href="<%- item.link %>"><%- item.title %></a>
                    <% } else { %>
                      <span><%- item.title %></span>
                    <% } %>
                    <% for(let [n,node] of item.node.entries()){ %>
                    <a class="more <% if(n == 0){ %>active<% } %>" target="_blank"
                        href="<% if(locals.env == 'prod'){ %> <%- node.link.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- node.link.replace('http://**************', '') %> <% } %>"
                        rel="nofollow">
                        <em>更多</em>
                        <i></i>
                    </a>
                    <% } %>
                    <% if(item.node.length > 1){ %>
                    <ul>
                        <% for(let [n,node] of item.node.entries()){ %>
                        <li class="<% if(n == 0){ %>active<% } %>">
                            <%- node.title %>
                        </li>
                        <% } %>
                        <li class="homeCon-head-bg"></li>
                    </ul>
                    <% } %>
                </div>
                <div class="homeCon8-body clearfix">
                    <% for(let [n,node] of item.node.entries()){ %>
                    <div class="homeCon8-item <% if(n == 0){ %>active<% } %>">
                        <% if(node.skus[0].thumb_img){ %>
                        <div class="side over">
                            <img src="<%- node.skus[0].thumb_img.replace(/\/static/g, locals.static) %>" alt='<%- node.skus[0].alt || node.skus[0].name %>' />
                            <a target="_blank"
                                href="<% if(locals.env == 'prod'){ %> <%- node.skus[0].url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- node.skus[0].url.replace('http://**************', '') %> <% } %>">
                                立即查看
                            </a>
                        </div>
                        <% } %>
                        <ul class="list">
                            <% for(let [i,o] of node.skus.entries()){ %>
                            <% if(i > 0 && o.thumb_img){ %>
                            <li class="over">
                                <a onclick="homeConProjectClick(event, '<%- o.name %>', '<% if(locals.env == 'prod'){ %> <%- o.url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- o.url.replace('http://**************', '') %> <% } %>')"
                                    href="<% if(locals.env == 'prod'){ %> <%- o.url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- o.url.replace('http://**************', '') %> <% } %>">
                                    <h2>
                                        <span class="<% if(o.is_buy == 1){ %>active<% } %>"><%- o.name %></span>
                                        <% if(o.is_buy == 1){ %><i></i><% } %>
                                    </h2>
                                    <p><%- o.sub_title %></p>
                                    <img src="<%- o.thumb_img.replace(/\/static/g, locals.static) %>" alt='<%- o.alt || o.name %>' />
                                </a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                    <% } %>
                </div>
                <div class="homeCon8-tab clearfix">
                    <% if(item.node.length > 1){ %>
                    <% for(let [n,node] of item.node.entries()){ %>
                    <span class="<% if(n == 0){ %>active<% } %>"></span>
                    <% } %>
                    <% } %>
                </div>
            </div>
        </div>
        <% } %>
        <% } %>
        <!-- 新楼层自定义格子 -->
        <% if(detail.customItems) {%>
        <% for(let item of detail.customItems){ %>
        <% if(item.type === 1 || item.type === 3){ %>
        <div class="homeCon6">
            <div class="wrap">
                <div class="homeCon-head">
                    <span><%- item.titleName %></span>
                    <a style="display: block;" class="more" target="_blank"
                        href="<% if(locals.env == 'prod'){ %> <%- item.titleUrl && item.titleUrl.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- item.titleUrl && item.titleUrl.replace('http://**************', '') %> <% } %>"
                        rel="nofollow">
                        <em>更多</em>
                        <i></i>
                    </a>
                </div>
                <div class="homeCon6-body clearfix">
                    <% for(let [n,box] of item.items.entries()){ %>
                    <div class="homeCon6-item over">
                        <div class="homeCon6-item-bg"
                            style="background-image: url('<%- box.bg && box.bg.replace(/\/static/g, locals.static) %>');">
                            <% if(n == 0 && item.type === 1){ %>
                            <a href='/service/certification/' target="_blank">
                                <span><%- box.title %></span>
                                <i></i>
                            </a>
                            <% }else if(n == 1 && item.type === 1){ %>
                            <a href='/service/certification/#/30' target="_blank">
                                <span><%- box.title %></span>
                                <i></i>
                            </a>
                            <% }else if(n == 2 && item.type === 1){ %>
                            <a href='/service/auditing/' target="_blank">
                                <span><%- box.title %></span>
                                <i></i>
                            </a>
                            <% }else{ %>
                            <span><%- box.title %></span>
                            <i></i>
                            <% } %>
                        </div>
                        <div class="homeCon6-item-list <% if(item.type === 1){ %>homeCon6-li<% } %>">
                            <div class="homeCon6-item-line">
                                <div></div>
                                <div></div>
                                <div></div>
                            </div>
                            <ul class="clearfix">
                                <% for(let list of box.list){ %>
                                <li>
                                    <a target="_blank" onclick="homeConProjectClick(event, '<%- list.name %>', '<% if(locals.env == 'prod'){ %> <%- list.url && list.url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- list.url && list.url.replace('http://**************', '') %> <% } %>')"
                                        href="<% if(locals.env == 'prod'){ %> <%- list.url && list.url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- list.url && list.url.replace('http://**************', '') %> <% } %>">
                                        <%- list.name %>
                                    </a>
                                </li>
                                <% } %>
                            </ul>
                            <a class="more" target="_blank"
                                href="<% if(locals.env == 'prod'){ %> <%- box.more && box.more.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- box.more && box.more.replace('http://**************', '') %> <% } %>"
                                rel="nofollow">更多></a>
                        </div>
                    </div>
                    <% } %>

                </div>
            </div>
        </div>
        <% }else if(item.type === 2){ %>
        <div class="homeCon7">
            <div class="wrap">
                <div class="homeCon-head">
                    <% if(item.titleUrl){ %>
                      <a href="<%- item.titleUrl %>"><%- item.titleName %></a>
                    <% } else { %>
                      <span><%- item.titleName %></span>
                    <% } %>
                    <a style="display: block;" class="more" target="_blank"
                        href="<% if(locals.env == 'prod'){ %> <%- item.titleUrl && item.titleUrl.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- item.titleUrl && item.titleUrl.replace('http://**************', '') %> <% } %>"
                        rel="nofollow">
                        <em>更多</em>
                        <i></i>
                    </a>
                </div>
                <div class="homeCon7-body clearfix">
                    <ul>
                        <% for(let box of item.items){ %>
                        <li class="over">
                            <div class="homeCon7-bg"
                                style="background-image: url('<%- box.bg && box.bg.replace(/\/static/g, locals.static) %>');">
                            </div>
                            <div class="homeCon7-mark"></div>
                            <div class="homeCon7-con">
                                <h2>
                                    <a target="_blank"
                                        href="<% if(locals.env == 'prod'){ %> <%- box.more && box.more.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- box.more && box.more.replace('http://**************', '') %> <% } %>"><%- box.title %></a>
                                </h2>
                                <p><%- box.des %></p>
                                <dl>
                                    <% for(let list of box.list){ %>
                                    <dd>
                                        <a target="_blank" onclick="homeConProjectClick(event, '<%- list.name %>', '<% if(locals.env == 'prod'){ %> <%- list.url && list.url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- list.url && list.url.replace('http://**************', '') %> <% } %>')"
                                            href="<% if(locals.env == 'prod'){ %> <%- list.url && list.url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- list.url && list.url.replace('http://**************', '') %> <% } %>"><%- list.name %></a>
                                    </dd>
                                    <% } %>
                                </dl>
                            </div>
                        </li>
                        <% } %>
                    </ul>
                </div>
            </div>
        </div>
        <% } %>
        <% } %>
        <% } %>
        <!-- 相关logo集合 -->
        <div class="home-logos">
            <div class="wrap">
                <div class="homeCon-head">
                    <span>认证标识服务</span>
                </div>
                <div class="home-logos-item">
                    <div class="item1">
                        <ul>
                            <li class="over">
                                <a href='/sku/product/137' target='_blank'>
                                    <img src="<%- locals.static %>/images/newPage/logos/logo1.png" alt='ISO9001 体系认证' />
                                </a>
                            </li>
                            <li class="over">
                                <a href='/sku/product/258' target='_blank'>
                                    <img src="<%- locals.static %>/images/newPage/logos/logo2.png" alt='RoHS产品认证' />
                                </a>
                            </li>
                            <li class="over">
                                <img src="<%- locals.static %>/images/newPage/logos/logo3.png" alt='product safety' />
                            </li>
                            <li class="over">
                                <a href='/sku/product/544' target='_blank'>
                                    <img src="<%- locals.static %>/images/newPage/logos/logo4.png" alt='碳足迹' />
                                </a>
                            </li>
                            <li class="over">
                                <a href='/sku/product/66' target='_blank'>
                                    <img src="<%- locals.static %>/images/newPage/logos/logo5.png" alt='北美认证' />
                                </a>
                            </li>
                            <li class="over">
                                <img src="<%- locals.static %>/images/newPage/logos/logo6.png" alt='SGS' />
                            </li>
                        </ul>
                    </div>
                    <div class="item2">
                        <ul>
                            <li class="over">
                                <a href='/sku/product/477' target='_blank'>
                                    <img src="<%- locals.static %>/images/newPage/logos/logo7.png" alt='供应链全称监控' />
                                </a>
                            </li>
                            <li class="over">
                                <img src="<%- locals.static %>/images/newPage/logos/logo8.png" alt='performance tested' />
                            </li>
                        </ul>
                    </div>
                    <div class="item3">
                        <ul>
                            <li class="over">
                                <a href='/sku/product/90' target='_blank'>
                                    <img src="<%- locals.static %>/images/newPage/logos/logo9.png" alt='独立慧鉴' />
                                </a>
                            </li>
                            <li class="over">
                                <a href='/sku/product/28' target='_blank'>
                                    <img src="<%- locals.static %>/images/newPage/logos/logo10.png" alt='民宿认证' />
                                </a>
                            </li>
                            <li class="over">
                              <a href="https://www.sgsmall.com.cn/coreSupplier" target="_blank">
                                <img src="<%- locals.static %>/images/newPage/logos/logo11.png" alt='核证供应商白金' />
                              </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="home-ads">
          <div class="wrap">
            <div class="bottom-item">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#home-bottom-1"></use>
              </svg>
              服务齐全，一站全包
            </div>
            <div class="bottom-item">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#home-bottom-2"></use>
              </svg>
              快捷受理，高效省时
            </div>
            <div class="bottom-item">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#home-bottom-3"></use>
              </svg>
              专业服务，国际权威
            </div>
            <div class="bottom-item">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#home-bottom-4"></use>
              </svg>
              官方自营，价格透明
            </div>
          </div>
        </div>
        <div class="descrip">
            <div class="descripBox">
                <div class="menu">
                    <% if(newsTypeList.length > 0){ %>
                    <% for(let [i,item] of newsTypeList.entries()){ %>
                    <% if(i < 3 ){ %>
                    <a href='/news?type=<%- item.id %>' target="_blank"><%- item.name %></a>
                    <% } %>
                    <% } %>
                    <% } %>
                    <a href='/coreSupplier' target="_blank">SGS核心供应商</a>
                </div>
                <ul id="desShow">
                    <% for(let [i,o] of detail.newsItems.entries()){ %>
                    <li class="descripLi<% if(i==0){ %> big<% } %>">
                        <div class="bigBox">
                            <div class="liTitle" title="<%- o.title %>"><%- o.title %></div>
                            <% if(i==0){ %><div class="liTime"><%- o.time %></div><% } %>
                            <div class="liInfo"><%- o.content %></div>
                            <% if(i!=0){ %><div class="liTime"><%- o.time %></div><% } %>
                            <a class="learnMore"
                                href="<% if(locals.env == 'prod'){ %> <%- o.url.replace('https://www.sgsmall.com.cn', '') %> <% }else{ %> <%- o.url.replace('http://**************', '') %> <% } %>"
                                target="_blank">
                                <div class="deepen"></div>
                                <div class="learnMore-word">了解更多</div>
                                <div class="learnMore-icon"></div>
                            </a>
                        </div>
                    </li>
                    <% } %>
                </ul>
            </div>
        </div>
        </div>
    <script src="<%- locals.static %>/js/swiper.min.js"></script>
    <script src="<%- locals.static %>/js/indexcom.js"></script>
    <script>
    function projectClick(id, name, href, listName) {
      document.location = href;
        // ga('ec:addProduct', {
        // 'id': id,
        // 'name': name,
        // 'category': '',
        // 'brand': '',
        // 'variant': '',
        // 'position': 1
        // });
        // ga('ec:setAction', 'click', {list: listName});
        //
        // // Send click with an event, then send user to product page.
        // ga('send', 'event', 'UX', 'click', 'Results', {
        // hitCallback: function() {
        //     document.location = href;
        // }
        // });
    }
    function homeConProjectClick(e, name, url) {
        e.preventDefault()
        if (url) {
        var arr = url.split('/')
        var id = arr[arr.length - 1]
        projectClick(id, name, url, 'index floor')
        }
    }
    </script>
    <script>
        $(function () {
            // 热搜词
            $("#searchBtn").on("click", function () {
                var value = $("#keyWord").val().trim();
                if (value) {
                    if (value === '在线询价' || value === '在线报价') {
                        window.open('/oiq/start', '_blank')
                    } else {
                        hotKeywordClick(value)
                    }
                }
            });
            var linkageWordDom = '';
            var linkageWordBox = $('.linkageWord ul')
            $('#keyWord').on('keyup', function (e) {
                var value = $(this).val().trim()
                if (value) {
                    var keycode = e.keyCode;
                    if (keycode == 13) {
                        $("#searchBtn").trigger("click")
                    }
                } else { }
                hotKeywordQry(value)
            })
            $("#keyWord").focus(function () {
                $('.hotKeyword').fadeOut();
                hotKeywordQry($(this).val())
            });
            $("#keyWord").blur(function () {
                if (!$('#keyWord').val()) {
                    $('.hotKeyword').fadeIn();
                }
                $('.linkageWord').fadeOut();
            });
            $('.hotWord').on("click", 'span', function () {
                var value = $(this).html();
                $("#keyWord").val(value)
                if (value) {
                    if (value === '在线询价' || value === '在线报价') {
                        window.open('/oiq/start', '_blank')
                    } else {
                        hotKeywordClick(value)
                    }
                }
            });
            $('.linkageWord').on("click", 'li', function () {
                var value = $(this).data('keyword');
                $("#keyWord").val(value);
                $('.linkageWord').fadeOut();
                $('.hotKeyword').hide();
                if (value) {
                    if (value === '在线询价' || value === '在线报价') {
                        window.open('/oiq/start', '_blank')
                    } else {
                        hotKeywordClick(value)
                    }
                }
            });

            function hotKeywordClick(value) {
                var param = {
                    keyword: value,
                    _csrf: '<%- csrf %>'
                }
                $.ajax({
                    type: 'POST',
                    url: '/hotWord/click',
                    data: param,
                    success: function (res) {

                    },
                    fail: function (data) {

                    },
                    complete: function (complete) {
                        window.open('/search?q=' + encodeURIComponent(value), '_blank')
                    }
                })
            }

            function hotKeywordQry(value) {
                var param = {
                    keyword: value,
                    pageNum: 1,
                    pageRow: 6,
                    _csrf: '<%- csrf %>'
                }
                $.ajax({
                    type: 'POST',
                    url: '/hotWord/qry',
                    data: param,
                    success: function (res) {
                        var data = typeof res === 'string' ? JSON.parse(res) : res;
                        var datas = data.data
                        if (data.resultCode === '0' && datas.items.length) {
                            linkageWordDom = '';
                            var items = datas.items;
                            for (var i = 0; i < items.length; i++) {
                                if (items[i].keyword) {
                                    linkageWordDom += '<li data-keyword="' + items[i].keyword + '">' + items[i].title + '</li>'
                                }
                            }
                            if (!$('.linkageWord').is(":visible")) $('.linkageWord').show();
                        } else {
                            $('.linkageWord').hide();
                            linkageWordDom = ''
                        }
                        linkageWordBox.empty().append(linkageWordDom)
                    },
                    fail: function (data) { },
                    complete: function (complete) { }
                })
            }
             //bannner轮播
             var banners = <%- JSON.stringify(detail.banners) %>;
            var swiper = new Swiper('.swiper-container', {
                navigation: {
                  nextEl: '.swiper-button-next',
                  prevEl: '.swiper-button-prev',
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                    renderBullet: function (index, className) {
                        // 自定义banner分页 https://www.swiper.com.cn/api/pagination/70.html
                        switch (index) {
                            case 0: text = banners[0] && banners[0].btn_text; break;
                            case 1: text = banners[1] && banners[1].btn_text; break;
                            case 2: text = banners[2] && banners[2].btn_text; break;
                            case 3: text = banners[3] && banners[3].btn_text; break;
                            case 4: text = banners[4] && banners[4].btn_text; break;
                            case 5: text = banners[5] && banners[5].btn_text; break;
                            case 6: text = banners[6] && banners[6].btn_text; break;
                            case 7: text = banners[7] && banners[7].btn_text; break;
                        }
                        return '<span href="'+banners[index].btn_url+'" data-url="'+banners[index].btn_url+'" target="blank" class="' + className + '">' + text + '</span>';
                    }
                },
                speed: 1000,
                loop: false,
                autoplay: {
                  delay: 5000,
                  disableOnInteraction: false,
                },
                preventClicks:false,
                effect: 'fade',
                fadeEffect: {
                  crossFade: true,
                },
            });
            $(".swiper-pagination").on("click","a",function(){
              var index = $(this).index();
              var url=$(this).attr('href')
              window.open(url)
            })
            var scontainer = $('.swiper-container')[0];
            scontainer.onmouseenter = function () {
                swiper.autoplay.stop();
            };
            scontainer.onmouseleave = function () {
                swiper.autoplay.start();
            };
            // $(".swiper-pagination-bullet").hover(function () {
            //   $(this).click(); //鼠标划上去之后，自动触发点击事件来模仿鼠标划上去的事件
            // }, function () {
            //   swiper.autoplay.start(); //鼠标移出之后，自动轮播开启
            // })

            // navigation


            /*
              TIC-5198
              异步渲染首页导航
              异步渲染内页导航
            */
            asyncRenderNavication();
            function asyncRenderNavication() {
              $.ajax({
                type: 'POST',
                url: '/getNavication',
                data: {},
                success: function (res) {
                  createHomeNavivation(res)
                },
                fail: function (data) {
                },
                complete: function (complete) {
                }
              })
            }

            // 创建首页导航
            function createHomeNavivation(res) {
              var serviceID = $(".n_category #service"),
                tradeID = $(".n_category #industry"),
                serviceNavication = res.serviceNavication,
                tradeNavication = res.tradeNavication;
              if (serviceNavication.length) {
                renderNavicationHome(serviceNavication, serviceID, '/service/')
              }
              if (tradeNavication.length) {
                renderNavicationHome(tradeNavication, tradeID, '/industry/')
              }
            }

            function renderNavicationHome(data, boxId, path) {
              var mainDom = '<div class="main">',
                sideDom = '<div class="side"><ul>';
              data.forEach(function (v1, index) {
                sideDom += '<li><a data-id="'+ v1.id +'" href="' + path + v1.alias + '">' + v1.name + '</a><span></span></li>';
              })
              sideDom += '</ul></div>';
              data.forEach(function (v1, index) {
                mainDom += '<div class="list clearfix ">';
                if (v1.lstSub && v1.lstSub.length >= 5) {
                  mainDom += '<div class="second"><ul>';
                  v1.lstSub.forEach(function (v2, index2) {
                    if (!index2) {
                      mainDom += '<li style="display: list-item;" class="active"><a href="javascript:void(0);" data-id="' + v2.id + '" data-link="' + path + v1.alias + '/#/' + v2.id + '">' + v2.name + '</a></li>';
                    } else {
                      mainDom += '<li><a href="javascript:void(0);" data-id="' + v2.id + '" data-link="' + path + v1.alias + '/#/' + v2.id + '">' + v2.name + '</a></li>';
                    }
                  })
                  mainDom += '</ul></div>';
                  mainDom += '<div class="secondList">';
                  mainDom += '<div class="clearfix">';
                  mainDom += '<ul class="secondListLi">';
                  v1.lstSub.forEach(function (v2, index2) {
                    // 3级菜单start
                    mainDom += '<li>';
                    mainDom += '<div class="main-list2">';
                    mainDom += '<label>';
                    v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
                      if (v3.showType === 1) {
                          if (v3.linkUrl === "") {
                              mainDom += '<p class="cleafix" title="' + v3.showName + '">' + v3.showName + '</p>';
                          } else {
                              mainDom += '<a class="cleafix" href="' + v3.linkUrl + '" title="' + v3.showName + '">' + v3.showName + '</a>';
                          }
                      }
                      if (v3.showType === 2) {
                        mainDom += '<span>';
                        if (v3.isHot) {
                          // 是否热门
                          mainDom += '<a class="hot" href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
                        } else {
                          mainDom += '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
                        }
                        mainDom += '</span>';
                      }
                      // 最后一条之后添加分隔符
                      if (index3 === v2.lstNavi.length - 1) {
                        mainDom += '<div class="dashedLine"></div>';
                      }
                    })
                    mainDom += '</label>';
                    mainDom += '</div>';
                    // 3级菜单end
                    // 广告start
                    mainDom += '<div class="ads clearfix">';
                    mainDom += '<ul class="clearfix">';
                    v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
                      if (v3.showType === 3) {
                        mainDom += '<li>' +
                          '<a href="' + v3.linkUrl + '" style="background-image:url(' + v3.imgUrl + ');" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '"></a>' +
                          '<div class="info">' +
                          '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
                          '<h3 title="' + v3.showName + '">' + v3.showName + '</h3>' +
                          '<p title="' + v3.showSummary + '">' + v3.showSummary + '</p>' +
                          '</a>' +
                          '</div>' +
                          '</li>';
                      }
                    })
                    mainDom += '</ul><div class="moreService"><a href="/service/'+ v1.alias +'/service" class="moreServiceA">查看更多服务<i></i></a></div></div>';
                    // 广告end
                    mainDom += '</li>';
                  })
                  mainDom += '</ul>';
                  mainDom += '</div>';
                  mainDom += '</div>';
                } else {
                  mainDom += '<div class="main-list">';
                  v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
                    mainDom += '<dl class="clearfix">';
                    // 2级start
                    mainDom += '<dt>' +
                      '<a href="javascript:void(0);" data-id="' + v2.id + '" data-link="'+ path + v1.alias + '/#/' + v2.id + '">' + v2.name + '</a>' +
                      '</dt>';
                    // 2级end
                    v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
                      if (v3.showType === 2) {
                        // 3级start
                        mainDom += '<dd>';
                        if (v3.isHot) {
                          // 是否热门
                          mainDom += '<a class="hot" href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
                        } else {
                          mainDom += '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
                        }
                        mainDom += '</dd>';
                        // 3级end
                      }
                    })
                    mainDom += '</dl><div class="main-list-line"></div>';

                  })
                  mainDom += '</div>';
                  mainDom += '<div class="ads">';
                  // 广告start
                  mainDom += '<ul>';
                  v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
                    v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
                      if (v3.showType === 3) {
                        mainDom += '<li>' +
                          '<a href="' + v3.linkUrl + '" style="background-image:url(' + v3.imgUrl + ');" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
                          '</a>' +
                          '<div class="info">' +
                          '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
                          '<h3 title="' + v3.showName + '">' + v3.showName + '</h3>' +
                          '<p title="' + v3.showSummary + '">' + v3.showSummary + '</p>' +
                          '</a>' +
                          '</div>' +
                          '</li>';
                      }
                    })
                  })
                  mainDom += '</ul>';
                  // 广告end
                  mainDom += '<div class="moreService">' +
                    '<a href="/service/'+ v1.alias +'/service">查看更多服务<i></i></a>' +
                    '</div>';
                  mainDom += '</div>';
                }
                mainDom += '</div>';
              })
              mainDom += '</div>';
              boxId.html(sideDom + mainDom)
            }


            // main nav click envet
            $("#n_nav").on('mouseenter', 'li', function () {
                var index = $(this).index();
                var item = $(this).data('item');
                if (item) {
                  $('.n_category').find('#' + item).show().siblings().hide();
                  $(this).addClass("active").siblings().removeClass('active');
                }
            })

            // first nav event
            $('.n_category').on('mouseenter', '.side li', function () {
              var index = $(this).index();
              $(this).addClass('active').siblings().removeClass('active');
              $(this).closest('.side').siblings('.main').show();
              var mainEl = $(this).closest('.side').siblings('.main').find('.list');
              mainEl.eq(index).show().siblings().hide();
            })

            $('.n_category').on('mouseenter', '.side', function () {
              $(this).siblings().show();
            })

            $('.n_category').on('mouseenter', '.second li', function () {
              var index = $(this).index();
              $(this).addClass('active').siblings().removeClass('active');
              $(this).closest('.second').siblings('.secondList').find('.secondListLi > li').eq(index).show().siblings().hide();
            })

            $("#industry, #service").mouseleave(function () {
              $(this).find('.main').hide();
              $(this).find('.side li').removeClass("active")
            });

            /*
              TIC-5202
              数据模型： 站点.页面.模块.标识位.session
              实例化后：spm=EJHxGX9b.h.n-${id}.${collectCode}.${sessionId}&token=${token}
              a:EJHxGX9b 站点名称 ticView(EJHxGX9b) member(HNCO2i11) store(Y0tCOIDS)
              b:1YCEZc6a(home首页) M3nTPXTW(inner)内页 网站页面
              c:n-${id} (navication)导航的菜单服务或行业id
              d:${collectCode} 服务端唯一标识
              e:${sessionId} sessionid
              token: 登录后用户token
            */
            $("#service, #industry").on("click", "a", function (e) {
              if (e && e.preventDefault) {
                e.preventDefault();
              } else {
                window.event.returnValue = false;
              }
              var href = $(this).attr('href')
              var link = $(this).data('link')
              var collectcode = '';
              var id = '';
              if ($(this).data('collectcode')) collectcode = $(this).data('collectcode')
              if ($(this).data('id')) id = '-' + $(this).data('id')
              if (link) {
                createSPM('1YCEZc6a', id, collectcode, 'sessionId')
                window.location.href = link
              } else {
                createSPM('1YCEZc6a', id, collectcode, 'sessionId')
                window.location.href = href
              }
            });
            function createSPM(b, c, d, e) {
              var website = 'EJHxGX9b';
              var origin = window.location.origin;
              if (origin.indexOf('member') > -1) {
                website = 'HNCO2i11';
              } else if (origin.indexOf('tic') > -1) {
                website = 'Y0tCOIDS';
              }
              var spm = [website, b, 'n' + c, d, e];
              var query = {
                spm: spm.join('.'),
                token: Cookies.get('SSO_TOKEN'),
                tx: ''
              }
              $.ajax({
                type: 'POST',
                url: '/sendSPM',
                // url: 'http://localhost:9511/sendSPM',
                data: query,
                success: function (res) { },
                fail: function (data) { },
                complete: function (complete) { }
              })
            }
        })
    </script>
    <script>
        $('ul.zns').css('top', -$('>li', $('ul.zns')).length * 33 / 2 + 28);
    </script>
<% include footer.html %>
