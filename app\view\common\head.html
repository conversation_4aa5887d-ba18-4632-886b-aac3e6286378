<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta http-equiv="Content-Security-Policy">
  <% if(locals.env != 'prod'){ %>
  <meta name="baidu-site-verification" content="rVqh4kcxit" /><% }else{ %>
  <meta name="baidu-site-verification" content="LAo7KbfttB" /><% } %>
  <title><%= detail.page_title || detail.name || detail.title %><% if(locals.env != 'prod'){ %>(测试版)<% } %>
  </title>
  <meta name="keywords" content="<%- detail.page_keywords %>" />
  <meta name="description" content="<%- detail.page_description %>" />
  <meta name="format-detection" content="telephone=no">
  <link rel="icon" href="<%- locals.static %>/favicon.ico" type="image/x-icon" />
  <link rel="shortcut icon" href="<%- locals.static %>/favicon.ico" type="image/x-icon" />
  <link rel="stylesheet" href="<%- locals.static %>/css/swiper.min.css">
  <link rel="stylesheet" href="<%- locals.static %>/css/select2.min.css">
  <link rel="stylesheet" href="<%- locals.static %>/css/style.css">
  <link rel="stylesheet" href="<%- locals.static %>/css/style_less.css">
  <link rel="stylesheet" href="<%- locals.static %>/css/index.css">
  <link rel="stylesheet" href="<%- locals.static %>/css/extend.css">
  <script src="<%- locals.static %>/js/jquery.min.js"></script>
  <script src="<%- locals.static %>/js/js.cookie.min.js"></script>
  <script src="<%- locals.static %>/js/navigation.js"></script>
  <script src="<%- locals.static %>/svg-sprite.js"></script>
  <% if(locals.env == 'prod'){ %>
    <script src="/static/js/bundle.tracing.min.js"></script>
  <script>
    Sentry.init({
      dsn: "https://<EMAIL>/11",
      release: '2024.06.20',
      integrations: [new Sentry.BrowserTracing()],
      tracesSampleRate: 1.0,
      ignoreErrors: [
        'undefined'
      ]
    });
  </script>
  <% } %>
  <script src="<%- locals.static %>/js/jquery.min.js"></script>
  <% if(locals.env != 'prod'){ %>
  <!-- Google Tag Manager -->
  <script>
    (function (w, d, s, l, i) {
      w[l] = w[l] || [];
      w[l].push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
      });
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s),
        dl = l != 'dataLayer' ? '&l=' + l : '';
      j.async = true;
      j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
      f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-W9K275F');
  </script>
  <!-- Google Analytics -->
  <script>
    (function (i, s, o, g, r, a, m) {
      i['GoogleAnalyticsObject'] = r; i[r] = i[r] || function () {
        (i[r].q = i[r].q || []).push(arguments)
      }, i[r].l = 1 * new Date(); a = s.createElement(o),
        m = s.getElementsByTagName(o)[0]; a.async = 1; a.src = g; m.parentNode.insertBefore(a, m)
    })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

    // ga('create', 'UA-126797271-5', 'auto');
    // ga('send', 'pageview');
    // ga('require', 'ec');
  </script>
  <!-- End Google Analytics -->
  <% }else{ %>
  <script>
    (function (w, d, s, l, i) {
      w[l] = w[l] || [];
      w[l].push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
      });
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s),
        dl = l != 'dataLayer' ? '&l=' + l : '';
      j.async = true;
      j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-WQLR3T8');
  </script>
  <!-- End Google Tag Manager -->
  <!-- Google Analytics -->
  <script>
    (function (i, s, o, g, r, a, m) {
      i['GoogleAnalyticsObject'] = r; i[r] = i[r] || function () {
        (i[r].q = i[r].q || []).push(arguments)
      }, i[r].l = 1 * new Date(); a = s.createElement(o),
        m = s.getElementsByTagName(o)[0]; a.async = 1; a.src = g; m.parentNode.insertBefore(a, m)
    })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

    // ga('create', 'UA-126797271-5', 'auto');
    // ga('send', 'pageview');
    // ga('require', 'ec');
  </script>
  <!-- End Google Analytics -->
  <% } %>
  <script>
    window._agl = window._agl || [];
    (function () {
      _agl.push(
        ['production', '_f7L2XwGXjyszb4d1e2oxPybgD']
      );
      (function () {
        var agl = document.createElement('script');
        agl.type = 'text/javascript';
        agl.async = true;
        agl.src = 'https://fxgate.baidu.com/angelia/fcagl.js?production=_f7L2XwGXjyszb4d1e2oxPybgD';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(agl, s);
      })();
    })();
  </script>
  <script>
    $.ajaxSetup({
      beforeSend: function (xhr) {
        //可以设置自定义标头
        xhr.setRequestHeader('ia', sessionStorage.getItem('ia') || '');
        xhr.setRequestHeader('si', Cookies.get('sessionId') || '');
      },
      headers: {
        si: Cookies.get('sessionId') || '',
        ia: sessionStorage.getItem('ia') || ''
      }
    });
  </script>
</head>
