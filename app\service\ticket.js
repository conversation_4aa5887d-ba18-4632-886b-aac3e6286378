'use strict';

const Service = require('egg').Service;
const axios = require('axios');
const crypto = require('crypto');
const util = require('./../controller/util');
class ticketService extends Service {
  async list(params) {
    const {
      app
    } = this;
    const page = params.page || 1,
      limit = 10;

    const content = params.content,
      customer = params.customer,
      phone = params.phone,
      email = params.email;

    let qarr = [];
    if (content) {
      qarr.push('content LIKE "%' + content + '%"');
    }

    if (customer) {
      qarr.push('customer LIKE "%' + customer + '%"');
    }

    if (phone) {
      qarr.push('phone LIKE "%' + phone + '%"');
    }

    if (email) {
      qarr.push('email="' + email + '"');
    }

    if (qarr.length > 0) {
      qarr = qarr.join(' AND ');
      qarr = ' WHERE ' + qarr;
    } else {
      qarr = '';
    }

    let total = await app.mysql.query('SELECT id FROM ticket' + qarr);
    const list = await app.mysql.query('SELECT * FROM ticket' + qarr + ' ORDER BY gmt_create DESC LIMIT ' + (page - 1) * limit + ',' + limit);

    for (let item of list) {
      let tradeInfo = await app.mysql.get('catalog', {
        id: item.trade_Id
      });
      let serviceInfo = await app.mysql.get('catalog', {
        id: item.serviceId
      });

      item.trade = tradeInfo.name;
      item.service = serviceInfo.name;
    }

    total = total.length;
    return {
      list,
      total
    };
  }

  async add(params, token) {
    const {
      app,
      ctx
    } = this;


    if (params.customer.length >= 55 || params.email.length >= 55 || params.email.company >= 255) {
      return {
        success: false,
        resultMsg: '您输入的内容过长，请修改后重试。'
      };
    } else {
      const pid = 'pid.mall'
      const pcode = 'Z0zCnRE3IaY9Kzem'
      const flitterArr = Object.values(params)

      let fillterResult = flitterArr.some((item => {
        if (item) {
          return fillter(String(item).toLowerCase());
        }
      }))

      function fillter(str) {
        let flag = false;
        if (str.includes('<') || str.includes('>') || str.includes('script') || str.includes('select') || str.includes('insert') || str.includes('update') || str.includes('delete') || str.includes('truncate') || str.includes('src')) {
          flag = true;
        }
        return flag;
      }

      if (fillterResult) {
        return {
          success: false,
          resultMsg: '您输入的内容存在非法字符，请修改后重试。'
        };
      } else {
        const pmd5 = crypto.createHash('md5').update((pid + pcode).toUpperCase()).digest('hex')
        const param = JSON.stringify(params) + pmd5.toUpperCase()
        const timestamp = new Date().getTime();
        const sign = crypto.createHash('md5').update(param + timestamp).digest('hex')
        const headers = {
          'Content-Type': 'application/json',
          pid,
          pcode,
          timestamp,
          sign,
          accessToken: token || '',
        }

        const host = await util.getHost(app.locals.env, ctx);
        let result = await axios({
          url: host + '/ticMall/business/api.v1.mall/ticket/create',
          method: 'post',
          data: JSON.stringify(params),
          headers,
        }).then(res => {
          return res.data
        }).catch(err => {
          if (err.response.status == '405') {
            return {
              success: 0,
              resultMsg: '您输入的内容存在非法字符，请修改后重试。'
            }
          }
        });

        return {
          success: result && result.result_code === '0' ? true : false,
          resultMsg: result && result.resultMsg
        };
      }
    }
  }

  async update(params) {
    const {
      app
    } = this;

    const row = {
      id: params.id,
      result_code: params.result_code,
      result_text: params.result_text,
      sendby: params.sendBy,
    }

    // app.mysql.update('ticket', row);
  }
}

module.exports = ticketService;