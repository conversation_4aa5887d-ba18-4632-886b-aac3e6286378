const Service = require('egg').Service;


class OrderService extends Service {
  async headSing(params) {
    const pid = 'pid.member'
    const pcode = 'CGz2RFHATB3XAsvg'
    const pmd5 = md5((pid + pcode).toUpperCase(), 'hex')
    const param = JSON.stringify(params) + pmd5.toUpperCase()
    const timestamp = new Date().valueOf()
    const sign = md5(param + timestamp, 'hex')

    const headers = {
      pid,
      sign,
      timestamp
    }
    return headers
  }

  async getList(params) {
    const ctx = this.ctx;
    const headers = {}

    const result = await ctx.curl(
      `https://gateuat.sgsonline.com.cn/ticMember/openapi/api.v1.packages/packages/qry`, {
        method: "POST",
        dataType: "json",
        data: {},
        timeout: 10000,
        headers
      }
    )
    return {
      result
    }
  }
}

module.exports = OrderService