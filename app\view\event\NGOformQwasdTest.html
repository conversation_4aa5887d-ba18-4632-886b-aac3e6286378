<% include ../header.html %>
<link rel="stylesheet" href="<%- locals.static %>/css/event/NGOform.css">
<div class="NGOform wrap">
  <div class="shade">
    <img src='/static/images/event/NGO/loading.gif' />
    <p></p>
  </div>
  <div class="tool">
    <select id='tool'>
      <option value='en' id='enBtn'></option>
      <option value='cn' id='cnBtn'></option>
    </select>
  </div>
  <h1 id='title'></h1>
  <div class='resultBox'>
    <div id='isEnd'>
      <span id='resultHtml'></span>
      <a href='javascript:void(0);' id='reportDown'></a>
    </div>
    <div id='noHasExport'>
      <span id='text1'></span>
      <a class='text2 payAgain' href='javascript:void(0);'></a>
    </div>
  </div>
  <div class="des" id='des'></div>
  <div id='items'></div>

</div>
<div class="button-group">
  <div class="wrap">
    <button id='custom'></button>
    <button id='prev'></button>
    <button id='next' style="display: none;"></button>
    <button id='submit'></button>
    <a href='javascript:void(0);' class="text7" id='fixedSave'></a>
    <p class="tips" id='tips'></p>
  </div>
</div>
<div class="answerEnd">
  <div class="box">
    <h2></h2>
    <div class="dom">
      <p class='resultHtml'></p>
      <p class='text1'></p>
    </div>
    <div class="btn">
      <button id='payAgain' class='text2'></button>
      <button id='cancel' class="cancel"></button>
    </div>
  </div>
</div>
<div class="dialog">
  <div class="dialog-mask"></div>
  <div class="dialog-box">
    <div class="dialog-title">提示</div>
    <div class="dialog-content">
      提示内容
    </div>
    <div class="dialog-btns">
      <button id='dialogTrue'>确定</button>
      <button id='dialogCandel'>取消</button>
    </div>
  </div>
</div>
</div>
<script src='<%- locals.static %>/js/event/lanPackage.js'></script>
<script>
  $(function () {
    var replyId = '',
      // todo
      // userId = 10615,
      // isLogin = 'true',
      isLogin = '<%- isLogin %>',
      userId = Cookies.get("SGS_USER_ID") || null,
      storeUrl = '<%- storeUrl %>',
      pid = '<%- mallPid %>',
      pcode = '<%- mallPcode %>',
      host = '<%- host %>',
      host1 = '<%- host1 %>', // tic service接口
      orderNum = '<%- orderNum %>',
      orderId = '<%- orderId %>',
      timestamp = '',
      stepLen = '',
      fileKey = '', // 获取报告地址必须字段
      isEnd = false, // 答题结束
      isOpenFaPiao = false, // 是否已经开票
      isPayAgain = false, // 是否付过199
      pmd5 = md5((pid + pcode).toUpperCase()),
      NGO = {
        currPage: 0
      },
      localNGO = window.localStorage.getItem('NGO'),
      hash = window.location.hash,
      currHash = '',
      btnLeft = ($('body').outerWidth() - 1226) / 2;

    if (localNGO) {
      NGO = JSON.parse(window.localStorage.getItem('NGO'))
    }

    // set hash
    if (hash.indexOf('en') > -1) {
      currHash = 'en'
    } else {
      currHash = 'cn'
    }

    // change lange
    $("#tool").val(currHash)
    $("#tool").on('change', function (params) {
      window.location.hash = $(this).val()
    })

    // set static text
    $(".shade p").html(lanPack[currHash].loading)
    $("#title").html(lanPack[currHash].title)
    $("#des").html(lanPack[currHash].des)
    $("#next").html(lanPack[currHash].next)
    $("#prev").html(lanPack[currHash].prev)
    $("#submit").html(lanPack[currHash].submit)
    $("#enBtn").html(lanPack[currHash].enBtn)
    $("#cnBtn").html(lanPack[currHash].cnBtn)
    $("#custom").html(lanPack[currHash].startAnwser)
    $('#tips').html(lanPack[currHash].tips)
    $('#tips').find('#myOrder').attr('href', storeUrl + '/ngo-order.html');
    $('#resultHtml, .answerEnd .dom .resultHtml').html(lanPack[currHash].resultHtml)
    $('#reportDown').html(lanPack[currHash].reportDown)
    $('#text1').html(lanPack[currHash].text1)
    $('.text1').html(lanPack[currHash].text1)
    $('.text2').html(lanPack[currHash].text2)
    $('.cancel').html(lanPack[currHash].cancel)
    $(".answerEnd h2").html(lanPack[currHash].tetx5)
    $(".text6").html(lanPack[currHash].text6)
    $(".text7").html(lanPack[currHash].text7)

    $("#fixedSave").css({
      right: btnLeft
    })

    // 静态弹窗
    var dialog = {
      show: function (option) {
        if (!option) option = {}
        var $dialog = $('.dialog')
        $dialog.find('.dialog-title').html(option.title || '提示');
        $dialog.find('.dialog-content').html(option.content || '');
        $dialog.find('#dialogTrue').html(option.onText || '确定');
        $dialog.find('#dialogCandel').html(option.cancelText || '取消');
        $dialog.on('click', '#dialogTrue', function () {
          option.onOk && option.onOk()
        });
        $dialog.on('click', '#dialogCandel', function () {
          option.onCancel && option.onCancel()
        });
        $dialog.show()
      },
      hide: function () {
        $('.dialog').hide()
      }
    }

    // loading切换
    var loadingAnimate = {
      start: function () {
        var box = $('.shade');
        if (box.is(":hidden")) {
          box.show()
        }
        setTimeout(function () {
          box.hide()
          submitFlag = true
          $('#next').prop('disabled', false);
          $('#submit').prop('disabled', false);
        }, 10000);
      },
      end: function () {
        var box = $('.shade');
        if (box.is(":visible")) {
          box.hide()
          submitFlag = true
          $('#next').prop('disabled', false);
          $('#submit').prop('disabled', false);
        }
      }
    }

    // 监听滚动
    var scrollAnimate = {
      saveBtn: function () {
        $(window).scroll(function () {
          // console.log($(window).scrollTop(), $(window).height(), $(document).height())
          if ($(window).scrollTop() + $(window).height() >= $(document).height() - 460) {
            $('.button-group').css({
              position: 'static',
              paddingBottom: '100px',
              boxShadow: 'none'
            });
            $('.NGOform').css({
              paddingBottom: '0'
            })
          } else {
            $('.button-group').css({
              position: 'fixed',
              paddingBottom: 0,
              boxShadow: '0 0 2px #ddd'
            });
            $('.NGOform').css({
              paddingBottom: '160px'
            })
          }
        });
      }
    }
    scrollAnimate.saveBtn()

    // 拦截未登录、没有订单号、orderNum校验
    if (isLogin !== 'false') {
      $("#tips").show();
      if (!orderNum) {
        reloadVerify()
      } else {
        // 每次进入项目都获取题目
        qryByQuestion({ questionId: 1 })
        // 校验订单号
        verificationOrderNum(orderNum, cb)
        function cb(res) {
          if (!res) {
            reloadVerify()
          } else {
            // 查询问卷状态
            qryQuestion()
            // 开始答题
            questionCreate()
          }
        }
      }
    } else {
      reloadVerify()
    }

    $('#prev').on('click', function () {
      if (!isEnd && replyId) {
        qupartSubmitAnswer(getAnswer(), 'prev')
      } else {
        if (NGO.currPage && NGO.currPage > 1) {
          NGO.currPage--
          backQuestuon('NGO-' + NGO.currPage)
        } else {
          NGO.currPage--
          switchPage()
        }
      }
    })

    var submitFlag = true;
    $('#next').on('click', function () {
      if (submitFlag) {
        $(this).prop('disabled', true);
        $('.button-group .wrap').hide();
        if (isLogin === 'false') {
          window.location.href = memberUrl + '/login';
        } else if (!orderNum) {
          createOrderForBBC()
        } else {
          submitFlag = false
          // 验证通过之后进行下一步
          if (isVerification() === true) {
            sbumitAnswer()
          } else if (isVerification() === 'error') {
            alert('题目加载失败，请刷新后重试!');
            submitFlag = true;
            $('#next').prop('disabled', false);
            $('.button-group .wrap').show();
          } else {
            // alert('请完成所有题目!');
            submitFlag = true;
            $('#next').prop('disabled', false);
            $('.button-group .wrap').show();
          }
        }
      }
    })

    $('#submit').on('click', function () {
      if (submitFlag) {
        $(this).prop('disabled', true);
        $('.button-group .wrap').hide();
        if (isLogin === 'false') {
          window.location.href = memberUrl + '/login';
        } else if (!orderNum) {
          createOrderForBBC()
        } else {
          submitFlag = false
          // 验证通过之后进行下一步
          if (isVerification() === true) {
            var items = $('#items');
            var stepLen = $("#items .step").length;
            if (stepLen === 9) {
              var flag = true;
              for (var i = 0; i < stepLen; i++) {
                var $step = items.find('.step').eq(i);
                var title = items.find('.step').eq(i).find('h2').html();
                if ($step.attr('id') === 'NGO-1') {
                  $(this).find('input').each(function () {
                    var that = $(this)
                    if (!that.val()) {
                      flag = false
                    }
                  });
                } else {
                  $step.find('dl').each(function () {
                    var $dl = $(this)
                    if ($(this).is(":visible") && $(this).attr('questionType') != 5) {
                      var name = $dl.find('input').attr('name');
                      // 有没选的题
                      if (!$dl.find('input[name="' + name + '"]:checked').val() && !$dl.find('input[type="text"]').val()) {
                        flag = false
                      }
                    }
                    if ($(this).is(":visible") && $(this).attr('questionType') == 5) {
                      var len = 0;
                      var isNoCheckd = 0;
                      $dl.find('input').each(function () {
                        var checkbox = $(this);
                        len++
                        // 复选框没有选中的个数
                        if (!checkbox.is(':checked')) {
                          isNoCheckd++
                        }
                      })
                      if (len === isNoCheckd) {
                        flag = false
                      }
                    }
                  });
                }
                if (!flag) {
                  alert('“' + title + '“章，有未完成的题目');
                  submitFlag = true;
                  $("#submit").prop('disabled', false);
                  $('.button-group .wrap').show();
                  return;
                }
              }
              if (flag) {
                sbumitAnswer()
              }
            } else {
              alert('请刷新后提交！')
              submitFlag = true;
              $(this).prop('disabled', false);
              $('.button-group .wrap').show();
            }
          } else if (isVerification() === 'error') {
            alert('题目加载失败，请刷新后重试!');
            submitFlag = true;
            $('#submit').prop('disabled', false);
            $('.button-group .wrap').show();
          } else {
            // alert('请完成所有题目!');
            submitFlag = true;
            $('#submit').prop('disabled', false);
            $('.button-group .wrap').show();
          }
        }
      }
    })

    // 自定义按钮
    $('#custom').on("click", function () {
      if (isLogin === 'false') {
        window.location.href = memberUrl + '/login';
      } else if (!orderNum) {
        createOrderForBBC()
      } else {
        verificationOrderNum(orderNum, cb)
        function cb(status) {
          if (status) {
            // 订单号校验成功
            NGO.currPage = 1
            showCurrPage()
          } else {
            createOrderForBBC()
          }
        }
      }
    });

    // 临时保存
    $('#fixedSave').on("click", function () {
      qupartSubmitAnswer(getAnswer(), 'save')
    });

    // 结束后再支付
    $("#payAgain, .payAgain").on("click", function () {
      $(".answerEnd").hide();
      if (orderId) {
        window.location.href = storeUrl + '/payment.html?tid=' + orderId + '&type=ngo';
      } else {
        alert('订单ID不存在');
      }
    });

    // 结束后取消
    $("#cancel").on("click", function () {
      $(".answerEnd").hide();
      window.window.location.reload();
    });

    // 下载报告
    $("#reportDown").on("click", function () {
      if (!fileKey) {
        window.location.href = storeUrl + '/ngo-order-detail.html?order_id=' + orderId
      } else if (!replyId) {
      } else {
        apis.reportDown()
      }
    });

    // 隐藏题目交互
    $('.NGOform').on("click", 'input', function (event) {
      // event.stopPropagation();
      var subjectid = $(this).closest('dl').attr('subjectid')
      var thisVal = $(this).val()
      var thisName = $(this).attr('name');
      if (subjectid) {
        // 步骤2
        if (subjectid == 19) {
          if (thisVal == 'Y') {
            $('dl').each(function () {
              if ($(this).attr('parnetsubjectid') == subjectid) {
                $(this).show();
              }
            })
          } else {
            $('dl').each(function () {
              if ($(this).attr('parnetsubjectid') == subjectid) {
                $(this).hide();
              }
            })
          }
        }
        if (subjectid == 10) {
          if (thisVal == 'Y') {
            $('dl').each(function () {
              if ($(this).attr('subjectno') == '3.1') {
                $(this).show();
              }
              if ($(this).attr('subjectno') == '3.2') {
                $(this).hide();
              }
            })
          } else {
            $('dl').each(function () {
              if ($(this).attr('subjectno') == '3.2') {
                $(this).show();
              }
              if ($(this).attr('subjectno') == '3.1') {
                $(this).hide();
              }
            })
          }
        }
        // 步骤3
        if (subjectid == 23) {
          if (thisVal == 'Y') {
            $('dl').each(function () {
              if ($(this).attr('parnetsubjectid') == subjectid) {
                $(this).show();
              }
            })
          } else {
            $('dl').each(function () {
              if ($(this).attr('parnetsubjectid') == subjectid) {
                $(this).hide();
              }
            })
          }
        }
      }
      // 其他（请注明）绑定事件
      if ($(this).attr('name') === 'NGO-2/3.2/E' && $(this).attr('type') === 'checkbox') {
        $(this).closest('dd').find('input[type="text"]').toggle();
      }
      // 其他（请注明）绑定事件
      if ($(this).attr('name') === 'NGO-6/18') {
        if ($(this).val() === 'J') {
          $(this).closest('dl').find('input[type="text"]').show();
        } else {
          $(this).closest('dl').find('input[type="text"]').hide();
        }
      }
      // 步骤5
      // 如果Q12选了否，组织没有行为准则，NGO-5/12.1/B 只能选否
      if ($(this).attr('name') === 'NGO-5/12') {
        if ($(this).val() === 'D') {
          $('#NGO-5 input').each(function () {
            if ($(this).attr('name') === 'NGO-5/12.1/B') {
              $(this).prop('disabled', true);
              if ($(this).val() === 'N') {
                $(this).prop('checked', true);
              }
            }
          });
        } else {
          $('#NGO-5 input').each(function () {
            if ($(this).attr('name') === 'NGO-5/12.1/B') {
              $(this).prop('disabled', false);
            }
          });
        }
      }
      // 步骤6
      // Q19选了否， NGO-6/19.1 只能选D/E
      if ($(this).attr('name') === 'NGO-6/19') {
        var that = $(this);
        // 清空19.1选项
        $('#NGO-6 input').each(function () {
          if ($(this).attr('name') === 'NGO-6/19.1') {
            $(this).prop('checked', false);
          }
        });
        // 获取相关的答案
        qupartQryAnswer({ partNo: 'NGO-2' }, cb2)
        function cb2(datas) {
          $.each(datas, function (index, item) {
            // 当自测组织无上级机构时 且 Q19选了否  19.1只能选"否，且组织未进行过审计"
            if (item.partNo === 'NGO-2' && item.subjectNo === '4' && item.optionVal === 'N') {
              if (that.val() === 'N') {
                $('#NGO-6 input').each(function () {
                  if ($(this).attr('name') === 'NGO-6/19.1') {
                    $(this).prop('disabled', true);
                    if ($(this).val() === 'D') {
                      $(this).prop('checked', true);
                    }
                    if ($(this).val() === 'E') {
                      $(this).closest('dd').hide();
                    }
                  }
                });
              }
            }
            // 当自测组织有上级机构时 且 Q19选了否 19.1 显示不适用
            if (item.partNo === 'NGO-2' && item.subjectNo === '4' && item.optionVal === 'Y') {
              if (that.val() === 'N') {
                $('#NGO-6 input').each(function () {
                  if ($(this).attr('name') === 'NGO-6/19.1') {
                    if ($(this).val() === 'E') {
                      $(this).closest('dd').show();
                    }
                  }
                });
              }
            }
          });
        }
        // 选其他隐藏 '不适用'
        if ($(this).val() === 'Y') {
          $('#NGO-6 input').each(function () {
            if ($(this).attr('name') === 'NGO-6/19.1') {
              $(this).prop('disabled', false);
              if ($(this).val() === 'E') {
                $(this).closest('dd').hide();
              }
            }
          });
        }
      }
      // 步骤7
      // Q23第A1（NGO-7/23/A）选了否，A2（NGO-7/23/B）、A3（NGO-7/23/C）只能选否
      if (thisName === 'NGO-7/23/A') {
        if (thisVal === 'N') {
          $('#NGO-7 input').each(function () {
            if ($(this).attr('name') === 'NGO-7/23/B' || $(this).attr('name') === 'NGO-7/23/C') {
              $(this).prop('disabled', true);
              if ($(this).val() === 'N') {
                $(this).prop('checked', true);
              }
            }
          });
        } else {
          $('#NGO-7 input').each(function () {
            if ($(this).attr('name') === 'NGO-7/23/B' || $(this).attr('name') === 'NGO-7/23/C') {
              $(this).prop('disabled', false);
            }
          });
        }
      }
      // Q23第A2 NGO-7/23/B选了否，A3（NGO-7/23/C）只能选否
      if (thisName === 'NGO-7/23/B') {
        if (thisVal === 'N') {
          $('#NGO-7 input').each(function () {
            if ($(this).attr('name') === 'NGO-7/23/C') {
              $(this).prop('disabled', true);
              if ($(this).val() === 'N') {
                $(this).prop('checked', true);
              }
            }
          });
        } else {
          $('#NGO-7 input').each(function () {
            if ($(this).attr('name') === 'NGO-7/23/C') {
              $(this).prop('disabled', false);
            }
          });
        }
      }
      // 步骤7
      // 如果Q26选了A2、A3，NGO-7/26.1只能选否
      if (thisName === 'NGO-7/26') {
        if (thisVal === 'B' || thisVal === 'C') {
          $('#NGO-7 input').each(function () {
            if ($(this).attr('name') === 'NGO-7/26.1') {
              $(this).prop('disabled', true);
              if ($(this).val() === 'N') {
                $(this).prop('checked', true);
              }
            }
          });
        } else {
          $('#NGO-7 input').each(function () {
            if ($(this).attr('name') === 'NGO-7/26.1') {
              $(this).prop('disabled', false);
            }
          });
        }
      }
    });

    // 未登录、没有订单号、orderNum校验失败都回到第一页
    function reloadVerify() {
      NGO.currPage = 0
      // console.log('NGO.currPage', NGO.currPage)
      switchPage()
    }

    // 前往BBC创建订单
    function createOrderForBBC() {
      window.location.href = storeUrl + '/ngo-confirm-order.html?sku_id=101';
    }

    // 校验订单号
    function verificationOrderNum(orderNum, cb) {
      const appKey = 'TICJAVA';
      const pcode = '07cc5a890687c310'
      const timestamp = new Date().valueOf()
      const sign = md5(timestamp + appKey + pcode)
      const headers = {
        'Content-Type': 'application/json',
        sign: sign,
         timestamp: timestamp,
            frontUrl: window.location.href,
        appKey: appKey
      }
      var param = {
        orderNO: orderNum,
        userID: userId
      }

      // loadingAnimate.start()
      $.ajax({
        type: 'POST',
        url: host1 + '/order/checkNgoOrder',
        data: JSON.stringify(param),
        contentType: 'application/json',
        headers: headers,
        success: function (res) {
          if (res.status === 200) {
            fileKey = fileKey ? fileKey : res.data.fileKey
            if (res.data.isPay && res.data.isPay === 'N') {
              isOpenFaPiao = true
            }
            if (res.data.buyReort && res.data.buyReort === 'N') {
              isPayAgain = true
            }
            if (cb) {
              cb && cb(true)
            }
          } else {
            // 订单号检验失败
            if (cb) {
              cb && cb(false)
            }
          }
        },
        fail: function (data) {
          alert(data.resultMsg)
        },
        complete: function (complete) {
          if (complete.status === 200) {
            // loadingAnimate.end()
          } else {
            alert('服务异常，请稍后重试！')
          }
        }
      })
    }

    // header签名
    function sing(param) {
      timestamp = new Date().valueOf();
      var str = JSON.stringify(param) + pmd5.toUpperCase();
      return md5(str + timestamp);
    }

    // 查询问卷章节
    function qryByQuestion(param) {
      loadingAnimate.start()
      $.ajax({
        type: 'POST',
        url: host + '/ticCenter/business/api.v1.question/qupart/qryByQuestion',
        data: JSON.stringify(param),
        contentType: 'application/json',
        headers: {
          pid: pid,
          sign: sing(param),
           timestamp: timestamp,
            frontUrl: window.location.href
        },
        success: function (res) {
          var data = JSON.parse(res)
          if (data.result_code == '0') {
            var datas = data.data.items;
            var dom = '';
            var partName = 'partName'
            if (currHash === 'en') {
              partName = 'partNameEn'
            }
            stepLen = datas.length
            for (var i = 0; i < datas.length; i++) {
              dom += '<div class="step" data-partNo="' + datas[i].partNo + '" id="' + datas[i].partNo + '">' +
                '<h2>' + datas[i][partName] + '</h2>' +
                '</div>';
            }
            $("#items").html(dom);
            // dome创建后初始化页面.
            showCurrPage()
          }
          loadingAnimate.end()
        },
        fail: function (data) {
          alert(data.resultMsg)
        },
        complete: function (complete) {
        }
      })
    }

    // 根据章节查询题目
    function qryByPart(num) {
      loadingAnimate.start()
      var param = {
        partNo: 'NGO-' + num
      }
      $.ajax({
        type: 'POST',
        url: host + '/ticCenter/business/api.v1.question/qsubject/qryByPart',
        data: JSON.stringify(param),
        contentType: 'application/json',
        headers: {
          pid: pid,
          sign: sing(param),
           timestamp: timestamp,
            frontUrl: window.location.href
        },
        success: function (res) {
          var data = JSON.parse(res)
          if (data.result_code == '0') {
            createPart(param.partNo, data.data)
          } else {
            // alert(data.resultMsg)
          }
          loadingAnimate.end()
        },
        fail: function (data) {
          alert(data.resultMsg)
        },
        complete: function (complete) {

        }
      })
    }
    // 创建题目的html
    function createPart(partNo, datas) {
      function sortShow(a, b) {
        return a.sortShow - b.sortShow
      }
      datas = datas.sort(sortShow)
      // console.log('根据章节查询题目', datas)
      // console.log('partNo', partNo)
      var dom = '';
      var box = $('body').find('#' + partNo);
      var questionTitle = 'questionTitle'
      var optionInfo = 'optionInfo'
      if (currHash === 'en') {
        questionTitle = 'questionTitleEn'
        optionInfo = 'optionInfoEn'
      }
      var answerNumber = true;
      if (partNo === 'NGO-1' || partNo === 'NGO-2') {
        answerNumber = false;
      }
      for (var i = 0; i < datas.length; i++) {
        if (!datas[i].parnetSubjectId) {
          dom += '<dl parnetSubjectId="' + datas[i].parnetSubjectId + '" subjectId="' + datas[i].subjectId + '"  subjectNo="' + datas[i].subjectNo + '" questionType="' + datas[i].questionType + '">';
        } else {
          dom += '<dl style="display: none;" parnetSubjectId="' + datas[i].parnetSubjectId + '" subjectId="' + datas[i].subjectId + '"  subjectNo="' + datas[i].subjectNo + '" questionType="' + datas[i].questionType + '">';
        }
        if (answerNumber) {
          dom += '<dt>' + datas[i].subjectNo + '、' + datas[i][questionTitle] + '</dt>';
        } else {
          dom += '<dt>' + datas[i][questionTitle] + '</dt>';
        }
        if (datas[i].options.length) {
          var options = datas[i].options.sort(sortShow)
          for (var j = 0; j < options.length; j++) {
            if (datas[i].questionType === 1) {
              dom += '<dd><input type="text" /></dd>';
            } else if (datas[i].questionType === 2) {
              dom += '<dd>';
              if (options[j].optionType === 2) {
                dom += '<label><input type="radio" value="' + options[j].optionNo + '" name="' + datas[i].partNo + '/' + datas[i].subjectNo + '" />' + options[j][optionInfo] + '</label>';
                dom += '<input type="text" style="display: none;" name="' + datas[i].partNo + '/' + datas[i].subjectNo + '/' + options[j].optionNo + '" />';
              } else {
                dom += '<label><input type="radio" value="' + options[j].optionNo + '" name="' + datas[i].partNo + '/' + datas[i].subjectNo + '" />' + options[j][optionInfo] + '</label>';
              }
              dom += '</dd>';
            } else if (datas[i].questionType === 3) {
              dom += '<dd>' + options[j][optionInfo] +
                '<dl><dd>' +
                '<label><input type="radio" value="Y" name="' + datas[i].partNo + '/' + datas[i].subjectNo + '/' + options[j].optionNo + '" />' + lanPack[currHash].yes + '</label>' +
                '<label><input type="radio" value="N" name="' + datas[i].partNo + '/' + datas[i].subjectNo + '/' + options[j].optionNo + '" />' + lanPack[currHash].no + '</label>';
              if (options[j].isNa) {
                dom += '<label><input type="radio" value="NA" name="' + datas[i].partNo + '/' + datas[i].subjectNo + '/' + options[j].optionNo + '" />' + lanPack[currHash].na + '</label>';
              }
              if (options[j].optionType === 2) {
                dom += '<input type="text" name="' + datas[i].partNo + '/' + datas[i].subjectNo + '/' + options[j].optionNo + 'a" />';
              }
              dom += '</dd></dl>' +
                '</dd>';
            } else if (datas[i].questionType === 5) {
              dom += '<dd><label>';
              dom += '<input type="checkbox" name="' + datas[i].partNo + '/' + datas[i].subjectNo + '/' + options[j].optionNo + '" />' + options[j][optionInfo] + '</label>';
              if (options[j].optionType === 2) {
                dom += '<input type="text" name="' + datas[i].partNo + '/' + datas[i].subjectNo + '/' + options[j].optionNo + '" style="display: none;" />';
              }
              dom += '</dd>';
            }
          }
        } else {
          if (datas[i].questionType === 1) {
            dom += '<dd><input type="text" name="' + datas[i].partNo + '/' + datas[i].subjectNo + '" /></dd>';
          }
        }
        dom += '</dl>';
      }
      // 没有题目的时候添加题目
      if (!box.children('dl').length) {
        box.append(dom);
      }
      window.scroll(0, 40);

      // 创建完题目的时候获取答案
      qupartQryAnswer({ partNo: 'NGO-' + NGO.currPage })
      // 如果已经结束答题，锁定所有input框
      if (isEnd) {
        setDisabale()
      }
      /*
        * 页面交互逻辑
      */
      // 第2步
      if (partNo === 'NGO-2') {
        setTimeout(function () {
          qupartQryAnswer({ partNo: 'NGO-2' }, cb2)
        }, 1000);
        function cb2(datas) {
          $.each(datas, function (index, item) {
            if (item.partNo === 'NGO-2' && item.subjectNo === '3.2' && item.optionNo === 'E' && item.optionVal !== 'N') {
              $('#NGO-2 input').each(function () {
                if ($(this).attr('name') === 'NGO-2/3.2/E' && $(this).attr('type') === 'text') {
                  $(this).show();
                }
              });
            }
          });
        }
      }
      // 第4步
      if (partNo === 'NGO-4') {
        // 显示隐藏题目 doto后台应该直接显示
        $('#NGO-4 dl').each(function () {
          $(this).show();
        });
      }
      // 第5步
      if (partNo === 'NGO-5') {
        // 显示隐藏题目 doto后台应该直接显示
        $('#NGO-5 dl').each(function () {
          $(this).show();
        });

        setTimeout(function () {
          qupartQryAnswer({ partNo: 'NGO-5' }, cb5)
        }, 1000);
        function cb5(datas) {
          $.each(datas, function (index, item) {
            // 获取Q8.1答案，如果选择了是， NGO-6/21/E 只能选择是
            if (item.partNo === 'NGO-5' && item.subjectNo === '12' && item.optionVal === 'D') {
              $('#NGO-5 input').each(function () {
                if ($(this).attr('name') === 'NGO-5/12' && $(this).val() === 'D') {
                  $(this).trigger('click');
                }
              });
            }
          });
        }
      }
      // 第6步
      if (partNo === 'NGO-6') {
        // 显示隐藏题目 doto后台应该直接显示
        $('#NGO-6 dl').each(function () {
          $(this).show();
        });

        // qupartQryAnswer({ partNo: 'NGO-4' }, cb4)
        // function cb4(datas) {
        //   console.log('cb4', datas)
        //   $.each(datas, function (index, item) {
        //     // 获取Q8.1答案，如果选择了是， NGO-6/21/E 只能选择是
        //     if (item.partNo === 'NGO-4' && item.subjectNo === '8.1' && item.optionVal === 'Y') {
        //       $('#NGO-6 input').each(function () {
        //         if ($(this).attr('name') === 'NGO-6/21/E') {
        //           $(this).prop('disabled', true);
        //           if ($(this).val() === 'Y') {
        //             $(this).prop('checked', true);
        //           }
        //         }
        //       });
        //     } else if (item.partNo === 'NGO-4' && item.subjectNo === '8.1' && item.optionVal !== 'Y') {
        //       $('#NGO-6 input').each(function () {
        //         if ($(this).attr('name') === 'NGO-6/21/E') {
        //           $(this).prop('disabled', false);
        //         }
        //       });
        //     }
        //   });
        // }

        setTimeout(function () {
          qupartQryAnswer({ partNo: '' }, cbAll)
        }, 1000);
        function cbAll(datas) {
          var ngo2 = '';
          var ngo6 = '';

          for (var i = 0; i < datas.length; i++) {
            if (datas[i].partNo === 'NGO-2' && datas[i].subjectNo === '4' && datas[i].optionVal === 'Y') {
              ngo2 = 'Y'
            }
            if (datas[i].partNo === 'NGO-2' && datas[i].subjectNo === '4' && datas[i].optionVal === 'N') {
              ngo2 = 'N'
            }
            if (datas[i].partNo === 'NGO-6' && datas[i].subjectNo === '19' && datas[i].optionVal === 'Y') {
              ngo6 = 'Y'
            }
            if (datas[i].partNo === 'NGO-6' && datas[i].subjectNo === '19' && datas[i].optionVal === 'N') {
              ngo6 = 'N'
            }
            if (datas[i].partNo === 'NGO-6' && datas[i].subjectNo === '18' && datas[i].optionNo === 'J') {
              $('#NGO-6 input').each(function () {
                if ($(this).attr('name') === 'NGO-6/18' && $(this).val() === 'J') {
                  $(this).prop('checked', true);
                }
                if ($(this).attr('name') === 'NGO-6/18/' && $(this).attr('type') === 'text') {
                  $(this).show();
                  $(this).val(datas[i].optionVal)
                }
              });
            }
          }

          if (ngo2 === 'N' && ngo6 === 'N') {
            $('#NGO-6 input').each(function () {
              if ($(this).attr('name') === 'NGO-6/19' && $(this).val() === 'N') {
                $(this).trigger('click');
              }
            });
          }
          if (ngo2 === 'N') {
            $('#NGO-6 input').each(function () {
              if ($(this).attr('name') === 'NGO-6/19.1') {
                if ($(this).val() === 'E') {
                  $(this).closest('dd').hide();
                }
              }
            });
          }
          $.each(datas, function (index, item) {
            // 获取Q8.1答案，如果选择了是， NGO-6/21/E 只能选择是
            if (item.partNo === 'NGO-4' && item.subjectNo === '8.1' && item.optionVal === 'Y') {
              $('#NGO-6 input').each(function () {
                if ($(this).attr('name') === 'NGO-6/21/E') {
                  $(this).prop('disabled', true);
                  if ($(this).val() === 'Y') {
                    $(this).prop('checked', true);
                  }
                }
              });
            } else if (item.partNo === 'NGO-4' && item.subjectNo === '8.1' && item.optionVal !== 'Y') {
              $('#NGO-6 input').each(function () {
                if ($(this).attr('name') === 'NGO-6/21/E') {
                  $(this).prop('disabled', false);
                }
              });
            }
          })
        }
      }
      // 第7步
      if (partNo === 'NGO-7') {
        // 显示隐藏题目 doto后台应该直接显示
        $('#NGO-7 dl').each(function () {
          $(this).show();
        });

        setTimeout(function () {
          qupartQryAnswer({ partNo: 'NGO-7' }, cb7)
        }, 1000);

        function cb7(datas) {
          $.each(datas, function (index, item) {
            if (item.partNo === 'NGO-7' && item.subjectNo === '23' && item.optionNo === 'A' && item.optionVal === 'N') {
              $('#NGO-7 input').each(function () {
                if ($(this).attr('name') === 'NGO-7/23/A' && $(this).val() === 'N') {
                  $(this).trigger('click');
                }
              });
            }
            if (item.partNo === 'NGO-7' && item.subjectNo === '23' && item.optionNo === 'B' && item.optionVal === 'N') {
              $('#NGO-7 input').each(function () {
                if ($(this).attr('name') === 'NGO-7/23/B' && $(this).val() === 'N') {
                  $(this).trigger('click');
                }
              });
            }
            if (item.partNo === 'NGO-7' && item.subjectNo === '26' && (item.optionVal === 'B' || item.optionVal === 'C')) {
              $('#NGO-7 input').each(function () {
                if ($(this).attr('name') === 'NGO-7/26.1') {
                  $(this).prop('disabled', true);
                }
              });
            }
          });
        }
      }
      // 第8步
      if (partNo === 'NGO-8') {
        // 显示隐藏题目 doto后台应该直接显示
        // 优化
        $('#NGO-8 dl').each(function () {
          $(this).show();
        });
        // qupartQryAnswer({ partNo: 'NGO-4' }, cb4)
        // function cb4(datas) {
        //   $.each(datas, function (index, item) {
        //     // 获取Q9的答案，如果选了否，NGO-8/29/F 只能选否
        //     if (item.partNo === 'NGO-4' && item.subjectNo === '9' && item.optionVal === 'E') {
        //       $('#NGO-8 input').each(function () {
        //         if ($(this).attr('name') === 'NGO-8/29/F') {
        //           $(this).prop('disabled', true);
        //           if ($(this).val() === 'N') {
        //             $(this).prop('checked', true);
        //           }
        //         }
        //       });
        //     } else if (item.partNo === 'NGO-4' && item.subjectNo === '9' && item.optionVal !== 'E') {
        //       if ($(this).attr('name') === 'NGO-8/29/F') {
        //         $(this).prop('disabled', false);
        //       }
        //     }
        //     // 获取Q.10-A.6的答案，如果选了否，NGO-8/28.1 只能选否
        //     if (item.partNo === 'NGO-4' && item.subjectNo === '10' && item.optionNo === 'F' && item.optionVal === 'N') {
        //       $('#NGO-8 input').each(function () {
        //         if ($(this).attr('name') === 'NGO-8/28.1') {
        //           $(this).prop('disabled', true);
        //           if ($(this).val() === 'N') {
        //             $(this).prop('checked', true);
        //           }
        //         }
        //       });
        //     } else if (item.partNo === 'NGO-4' && item.subjectNo === '10' && item.optionNo === 'F' && item.optionVal === 'Y') {
        //       $('#NGO-8 input').each(function () {
        //         if ($(this).attr('name') === 'NGO-8/28.1') {
        //           $(this).prop('disabled', false);
        //         }
        //       });
        //     }
        //   });
        // }

        // qupartQryAnswer({ partNo: 'NGO-7' }, cb7)
        // function cb7(datas) {
        //   console.log('cb7', datas)
        //   $.each(datas, function (index, item) {
        //     // 获取Q23.A1答案， 如果选了否，NGO-8/28 只能选否
        //     if (item.partNo === 'NGO-7' && item.subjectNo === '23' && item.optionNo === 'A' && item.optionVal === 'N') {
        //       $('#NGO-8 input').each(function () {
        //         if ($(this).attr('name') === 'NGO-8/28') {
        //           $(this).prop('disabled', true);
        //           if ($(this).val() === 'N') {
        //             $(this).prop('checked', true);
        //           }
        //         }
        //       });
        //     } else if (item.partNo === 'NGO-7' && item.subjectNo === '23' && item.optionNo === 'A' && item.optionVal === 'Y') {
        //       $('#NGO-8 input').each(function () {
        //         if ($(this).attr('name') === 'NGO-8/28') {
        //           $(this).prop('disabled', false);
        //         }
        //       });
        //     }
        //     // 获取Q23.A4答案, 如果选了否，NGO-8/27/B 只能选否
        //     if (item.partNo === 'NGO-7' && item.subjectNo === '23' && item.optionNo === 'D' && item.optionVal === 'N') {
        //       $('#NGO-8 input').each(function () {
        //         if ($(this).attr('name') === 'NGO-8/27/B') {
        //           $(this).prop('disabled', true);
        //           if ($(this).val() === 'N') {
        //             $(this).prop('checked', true);
        //           }
        //         }
        //       });
        //     } else if (item.partNo === 'NGO-7' && item.subjectNo === '23' && item.optionNo === 'D' && item.optionVal === 'Y') {
        //       $('#NGO-8 input').each(function () {
        //         if ($(this).attr('name') === 'NGO-8/27/B') {
        //           $(this).prop('disabled', false);
        //         }
        //       });
        //     }
        //     // 获取Q22.A1答案, 如果选了否，NGO-8/27/A 只能选否
        //     if (item.partNo === 'NGO-7' && item.subjectNo === '22' && item.optionNo === 'A' && item.optionVal === 'N') {
        //       $('#NGO-8 input').each(function () {
        //         if ($(this).attr('name') === 'NGO-8/27/A') {
        //           $(this).prop('disabled', true);
        //           if ($(this).val() === 'N') {
        //             $(this).prop('checked', true);
        //           }
        //         }
        //       });
        //     } else if (item.partNo === 'NGO-7' && item.subjectNo === '22' && item.optionNo === 'A' && item.optionVal === 'Y') {
        //       $('#NGO-8 input').each(function () {
        //         if ($(this).attr('name') === 'NGO-8/27/A') {
        //           $(this).prop('disabled', false);
        //         }
        //       });
        //     }

        //   });
        // }

        setTimeout(function () {
          qupartQryAnswer({ partNo: '' }, cbAll)
        }, 1000);

        function cbAll(datas) {
          $.each(datas, function (index, item) {
            // 获取Q9的答案，如果选了否，NGO-8/29/F 只能选否
            if (item.partNo === 'NGO-4' && item.subjectNo === '9' && item.optionVal === 'E') {
              $('#NGO-8 input').each(function () {
                if ($(this).attr('name') === 'NGO-8/29/F') {
                  $(this).prop('disabled', true);
                  if ($(this).val() === 'N') {
                    $(this).prop('checked', true);
                  }
                }
              });
            } else if (item.partNo === 'NGO-4' && item.subjectNo === '9' && item.optionVal !== 'E') {
              if ($(this).attr('name') === 'NGO-8/29/F') {
                $(this).prop('disabled', false);
              }
            }
            // 获取Q.10-A.6的答案，如果选了否，NGO-8/28.1 只能选否
            if (item.partNo === 'NGO-4' && item.subjectNo === '10' && item.optionNo === 'F' && item.optionVal === 'N') {
              // debugger
              $('#NGO-8 input').each(function () {
                if ($(this).attr('name') === 'NGO-8/28.1') {
                  var self = $(this);
                  if ($(this).val() === 'N') {
                    var that = $(this)
                    setTimeout(function () {
                      that.closest('label').trigger('click');
                      self.prop('disabled', true);
                    }, 1);
                  } else {
                    self.prop('disabled', true);
                  }
                }
              });
            } else if (item.partNo === 'NGO-4' && item.subjectNo === '10' && item.optionNo === 'F' && item.optionVal === 'Y') {
              $('#NGO-8 input').each(function () {
                if ($(this).attr('name') === 'NGO-8/28.1') {
                  $(this).prop('disabled', false);
                }
              });
            }
            // 获取Q23.A1答案， 如果选了否，NGO-8/28 只能选否
            if (item.partNo === 'NGO-7' && item.subjectNo === '23' && item.optionNo === 'A' && item.optionVal === 'N') {
              $('#NGO-8 input').each(function () {
                if ($(this).attr('name') === 'NGO-8/28') {
                  $(this).prop('disabled', true);
                  if ($(this).val() === 'N') {
                    $(this).prop('checked', true);
                  }
                }
              });
            } else if (item.partNo === 'NGO-7' && item.subjectNo === '23' && item.optionNo === 'A' && item.optionVal === 'Y') {
              $('#NGO-8 input').each(function () {
                if ($(this).attr('name') === 'NGO-8/28') {
                  $(this).prop('disabled', false);
                }
              });
            }
            // 获取Q23.A4答案, 如果选了否，NGO-8/27/B 只能选否
            if (item.partNo === 'NGO-7' && item.subjectNo === '23' && item.optionNo === 'D' && item.optionVal === 'N') {
              $('#NGO-8 input').each(function () {
                if ($(this).attr('name') === 'NGO-8/27/B') {
                  $(this).prop('disabled', true);
                  if ($(this).val() === 'N') {
                    $(this).prop('checked', true);
                  }
                }
              });
            } else if (item.partNo === 'NGO-7' && item.subjectNo === '23' && item.optionNo === 'D' && item.optionVal === 'Y') {
              $('#NGO-8 input').each(function () {
                if ($(this).attr('name') === 'NGO-8/27/B') {
                  $(this).prop('disabled', false);
                }
              });
            }
            // 获取Q22.A1答案, 如果选了否，NGO-8/27/A 只能选否
            if (item.partNo === 'NGO-7' && item.subjectNo === '22' && item.optionNo === 'A' && item.optionVal === 'N') {
              $('#NGO-8 input').each(function () {
                if ($(this).attr('name') === 'NGO-8/27/A') {
                  $(this).prop('disabled', true);
                  if ($(this).val() === 'N') {
                    $(this).prop('checked', true);
                  }
                }
              });
            } else if (item.partNo === 'NGO-7' && item.subjectNo === '22' && item.optionNo === 'A' && item.optionVal === 'Y') {
              $('#NGO-8 input').each(function () {
                if ($(this).attr('name') === 'NGO-8/27/A') {
                  $(this).prop('disabled', false);
                }
              });
            }
          });
        }
      }
    }

    // 遍历题库，提交前校验是否都必填
    function isVerification() {
      var flag = true
      $('.step').each(function () {
        var $step = $(this);
        if ($step.is(":visible")) {
          var dlLength = $step.find('dl').length;
          if (dlLength) {
            var emptyArr = []
            for (var i = 0; i < dlLength; i++) {
              var $dl = $step.find('dl').eq(i);
              var $questiontype = $dl.attr('questiontype');
              if ($dl.is(":visible")) {
                if ($questiontype == 1) {
                  var value = $dl.find('input').val();
                  if (!value) {
                    emptyArr.push(i)
                  }
                } else if ($questiontype == 2) {
                  var name = $dl.find('input').attr('name');
                  var value = $dl.find('input[name="' + name + '"]:checked').val();
                  if (!value) {
                    emptyArr.push(i)
                  }
                } else if ($questiontype == 3) {
                  $dl.find('dl').each(function (index, item) {
                    var temp = [i];
                    var $dlChild = $(this);
                    var name = $dlChild.find('input').attr('name');
                    var value = $dlChild.find('input[name="' + name + '"]:checked').val();
                    // console.log(name, value)
                    if (!value) {
                      temp.push(index)
                      emptyArr.push(temp)
                    }
                  })
                } else if ($questiontype == 5) {
                  var len = 0;
                  var isNoCheckd = 0;
                  $dl.find('input').each(function () {
                    var checkbox = $(this);
                    len++
                    // 复选框没有选中的个数
                    if (!checkbox.is(':checked')) {
                      isNoCheckd++
                    }
                  })
                  if (len === isNoCheckd) {
                    emptyArr.push(i)
                  }
                }
              }
            }
            // console.log('emptyArr', emptyArr)

            // 空答案的集合
            if (emptyArr.length) {
              setActiveClass()
              var option = {
                onText: lanPack[currHash].confirm,
                cancelText: lanPack[currHash].cancel,
                content: lanPack[currHash].text8,
                onOk: function () {
                  dialog.hide()
                  scrollFirstActive()
                },
                onCancel: function () {
                  dialog.hide()
                  scrollFirstActive()
                }
              }
              dialog.show(option);
              flag = false
            }

            // 空答案设置高亮样式
            function setActiveClass(params) {
              $('.emptyAnser').removeClass('emptyAnser');
              for (var j = 0; j < emptyArr.length; j++) {
                // console.log(typeof emptyArr[j])
                if (typeof emptyArr[j] === 'number') {
                  $step.find('dl').eq(emptyArr[j]).addClass('emptyAnser');
                } else if (typeof emptyArr[j] === 'object') {
                  $step.find('dl').eq(emptyArr[j][0]).children('dd').eq(emptyArr[j][1]).addClass('emptyAnser');
                }
              }

              $('.emptyAnser input').on('change', function () {
                if ($(this).closest('.emptyAnser')) {
                  $(this).closest('.emptyAnser').removeClass('emptyAnser')
                }
              });
            }

            // 滚动到第一个高亮的位置
            function scrollFirstActive() {
              if ($('.emptyAnser') && $('.emptyAnser').eq(0).offset()) {
                var top = $('.emptyAnser').eq(0).offset().top;
                $(window).scrollTop(top - 102);
              }
            }
          } else {
            flag = 'error'
          }
        }
      });
      return flag;
    }

    // 必填校验之后获取当前题目答案
    function getAnswer() {
      var answers = {
        partNo: '',
        items: [],
        isEnd: 0
      }
      $('.step').each(function () {
        var $step = $(this);
        if ($(this).is(":visible")) {
          answers.partNo = $(this).attr('id')
          if ($(this).attr('id') === 'NGO-1') {
            $(this).find('input').each(function () {
              var name = $(this).attr('name').split('/');
              var obj = {
                subjectNo: name[1],
                // optionNo: ,
                optionVal: $(this).val()
              }
              answers.items.push(obj)
            });
          }
          if ($(this).attr('id') !== 'NGO-1') {
            $step.find('dl').each(function () {
              var $dl = $(this)
              if ($(this).is(":visible") && ($(this).attr('questionType') != 5 && $(this).attr('questionType') != 3)) {
                var name = $dl.find('input').attr('name');
                var val = $dl.find('input[name="' + name + '"]:checked').val() || $dl.find('input[type="text"]').val();
                var inputTextVal = $dl.find('input[type="text"]').val();
                var obj = {
                  subjectNo: name.split('/')[1],
                  optionNo: name.split('/')[2] || val,
                  optionVal: inputTextVal || val
                }
                answers.items.push(obj)
              }

              if ($(this).is(":visible") && $(this).attr('questionType') == 5) {
                $dl.find('input').each(function () {
                  var obj = {
                  }
                  var checkbox = $(this);
                  var name = checkbox.attr('name');
                  obj.subjectNo = name.split('/')[1];
                  obj.optionNo = name.split('/')[2];
                  if (checkbox.is(':checked')) {
                    obj.optionVal = 'Y'
                  } else {
                    obj.optionVal = 'N'
                  }
                  answers.items.push(obj)
                });
              }
            });
          }
        }
      });
      return answers;
    }

    // 答案提交到后台
    function sbumitAnswer() {
      if (!isEnd && replyId) {
        qupartSubmitAnswer(getAnswer(), 'next')
      } else {
        NGO.currPage++
        showCurrPage()
      }
    }

    // 开始答题
    function questionCreate() {
      loadingAnimate.start()
      var param = {
        userId: userId,
        orderNo: orderNum,
        questionId: 1
      }
      $.ajax({
        type: 'POST',
        url: host + '/ticMember/business/api.v2.question/question/create',
        data: JSON.stringify(param),
        contentType: 'application/json',
        headers: {
          pid: pid,
          sign: sing(param),
           timestamp: timestamp,
            frontUrl: window.location.href
        },
        success: function (res) {
          var data = JSON.parse(res)
          if (data.result_code == '0') {
            if (data.data && data.data.replyId) {
              replyId = data.data.replyId
            }
          } else {
            // alert(data.resultMsg)
          }
          loadingAnimate.end()
        },
        fail: function (data) {
          // alert(data.resultMsg)
        },
        complete: function (complete) {
        }
      })
    }

    // 章节提交答案
    function qupartSubmitAnswer(param, arrow) {
      var param = {
        replyId: replyId,
        partNo: param.partNo || '',
        questionId: 1,
        userId: userId,
        orderNo: orderNum || 0,
        isEnd: 0, // 1是最后一页
        items: param.items || [],
        languageFlag: 'cn'
      }
      if (NGO.currPage === 9 && arrow === 'next') {
        param.isEnd = 1
        param.languageFlag = currHash
      }
      if (param.isEnd) {
        // 最后一步提交确认
        var dialogOption = {
          title: lanPack[currHash].text3 + '？',
          content: lanPack[currHash].text4,
          onText: lanPack[currHash].text3,
          cancelText: lanPack[currHash].cancel,
          onOk: function () {
            submit()
            dialog.hide()
          },
          onCancel: function () {
            dialog.hide()
            $('#submit').prop('disabled', false);
            $('.button-group .wrap').show();
          }
        }
        dialog.show(dialogOption)
      } else {
        submit()
      }
      function submit() {
        loadingAnimate.start()
        $.ajax({
          type: 'POST',
          url: host + '/ticMember/business/api.v2.question/qupart/submitAnswer',
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: pid,
            sign: sing(param),
             timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            var data = JSON.parse(res)
            // debugger
            if (data.result_code == '0') {
              if (param.isEnd) {
                answerEnd(data)
              } else {
                if (arrow === 'next') {
                  NGO.currPage++
                  backQuestuon('NGO-' + NGO.currPage)
                } else if (arrow === 'prev') {
                  NGO.currPage--
                  backQuestuon('NGO-' + NGO.currPage)
                } else if (arrow === 'save') {
                  alert('保存成功！')
                }
              }
            } else {
              alert(data.resultMsg)
            }
            loadingAnimate.end()
          },
          fail: function (data) {
            alert(data.resultMsg)
          },
          complete: function (complete) { }
        })
      }
    }

    // 根据章节查询答案
    function qupartQryAnswer(params, cb) {
      loadingAnimate.start()
      var param = {
        partNo: params.partNo,
        questionId: 1,
        userId: userId,
        orderNo: orderNum || 0
      }
      $.ajax({
        type: 'POST',
        url: host + '/ticMember/business/api.v2.question/qupart/qryAnswer',
        data: JSON.stringify(param),
        contentType: 'application/json',
        headers: {
          pid: pid,
          sign: sing(param),
           timestamp: timestamp,
            frontUrl: window.location.href
        },
        success: function (res) {
          var data = JSON.parse(res)
          if (data.result_code == '0') {
            cb && cb(data.data)
            setAnswer(data.data)
          } else {
            alert(data.resultMsg)
          }
          loadingAnimate.end()
        },
        fail: function (data) {
          alert(data.resultMsg)
        },
        complete: function (complete) {
        }
      })

    }

    // 查询问卷状态
    function qryQuestion() {
      loadingAnimate.start()
      var param = {
        userId: userId,
        orderNo: orderNum,
      }
      $.ajax({
        type: 'POST',
        url: host + '/ticMember/business/api.v2.question/question/qryQuestion',
        data: JSON.stringify(param),
        contentType: 'application/json',
        headers: {
          pid: pid,
          sign: sing(param),
           timestamp: timestamp,
            frontUrl: window.location.href
        },
        success: function (res) {
          var data = JSON.parse(res)
          if (data.resultCode == '0') {
            if (data.data.state) {
              replyId = data.data.replyId
              fileKey = fileKey ? fileKey : data.data.fileKey
              issureIsOver(data.data.finalScore, data.data.languageFlag)
            } else {
              // 跳转到历史答题
              var currPage = data.data.currentPart && Number(data.data.currentPart.substr(4, 1))
              NGO.currPage = currPage ? currPage : 0
              // if (currPage && currPage <= 9) {
              //   NGO.currPage = currPage
              // } else {
              //   NGO.currPage = 0
              // }
              showCurrPage()
            }
          } else {
            // alert(data.resultMsg)
          }
          loadingAnimate.end()
        },
        fail: function (data) {
          alert(data.resultMsg)
        },
        complete: function (complete) {
        }
      })
    }

    // 查询到答案赋值
    function setAnswer(datas) {
      if (NGO.currPage === 1) {
        $.each(datas, function (index, item) {
          var name = item.partNo + '/' + item.subjectNo
          $('#NGO-1').find('input').each(function () {
            if ($(this).attr('name') == name) {
              $(this).val(item.optionVal)
              return
            }
          });
        })
      } else {
        var showIssue = {
          partNo: '',
          items: []
        }
        $.each(datas, function (index, item) {
          var name = '';
          var value = item.optionVal;
          if (item.optionNo && (item.optionNo !== value)) {
            name = item.partNo + '/' + item.subjectNo + '/' + item.optionNo
          } else {
            name = item.partNo + '/' + item.subjectNo
          }


          $('#NGO-' + NGO.currPage).find('input').each(function () {
            if ($(this).attr('type') === 'text') {
              if ($(this).attr('name') == name) {
                $(this).val(value)
              }
            } else if ($(this).attr('type') === 'radio') {
              if ($(this).attr('name') == name && $(this).val() === value) {
                $(this).prop('checked', true)
              }
            } else if ($(this).attr('type') === 'checkbox') {
              if ($(this).attr('name') == name && value === 'Y') {
                $(this).prop('checked', true)
              }
            }
          });

          showIssue.partNo = '#NGO-' + NGO.currPage;
          // 步骤2
          if (item.partNo === 'NGO-2' && item.subjectNo === '10' && item.optionVal === 'Y') {
            showIssue.items.push(10.1)
          }
          if (item.partNo === 'NGO-2' && item.subjectNo === '3' && item.optionVal === 'Y') {
            showIssue.items.push(3.1)
          }
          if (item.partNo === 'NGO-2' && item.subjectNo === '3' && item.optionVal === 'N') {
            showIssue.items.push(3.2)
          }
          // 步骤3
          if (item.partNo === 'NGO-3' && item.subjectNo === '3' && item.optionVal === 'Y') {
            showIssue.items.push(3.1)
          }
          // 4 - 9 TODO
        })
        // 隐藏题有答案，显示隐藏题
        if (showIssue.items.length) {
          showHideIssue(showIssue)
        }
      }
      if (isEnd) {
        setDisabale()
      }
    }
    // 隐藏题有答案，显示隐藏题
    function showHideIssue(showIssue) {
      $(showIssue.partNo).find('dl').each(function () {
        var that = $(this)
        for (var i = 0; i < showIssue.items.length; i++) {
          if (that.attr('subjectno') == showIssue.items[i]) {
            that.show();
            return
          }
        }
      });
    }

    // 点击按钮之后的切换动作
    function switchPage() {
      submitFlag = true;
      $('#next').prop('disabled', false);
      $('#submit').prop('disabled', false);
      $('.button-group .wrap').show();
      window.localStorage.setItem('NGO', JSON.stringify(NGO));

      // 有记录,隐藏开始答题
      if (NGO.currPage) {
        $("#custom").hide();
        $('#items').show()
        $("#prev").show();
        $("#des").hide();
        $(".resultBox").hide();
        $("#next").show();
        $("#tips").hide();
        $("#fixedSave").show()
      } else {
        $("#custom").show();
        $('#items').hide()
        $("#prev").hide();
        $("#next").hide();
        $("#des").show();
        $(".resultBox").show();
        $("#fixedSave").hide()
      }

      if (NGO.currPage === stepLen) {
        $("#next").hide()
        if (!isEnd) {
          if (stepLen > 8) {
            $("#submit").show()
          }
        }
      } else {
        $("#submit").hide()
      }

      if (isEnd) {
        $('#tips').hide();
        $("#fixedSave").hide()
        $('#custom').html(lanPack[currHash].resultBtn)
      }
    }

    // 显示缓存中保存的当前页
    function showCurrPage() {
      switchPage()
      if (NGO.currPage) {
        $('.step').each(function () {
          if ($(this).attr('id') === 'NGO-' + NGO.currPage) {
            // 获取当前页面的题目和答案
            if (NGO.currPage) {
              qryByPart(NGO.currPage);
            }
            $(this).show();
          } else {
            $(this).hide()
          }
        })
      }
    }

    // 答题结束后所有题目设置为disable
    function setDisabale() {
      $("#items").find('input').each(function () {
        $(this).prop('disabled', true);
      })
    }

    // 答题结束弹窗
    function answerEnd(datas) {
      loadingAnimate.start()
      const appKey = 'TICJAVA';
      const pcode = '07cc5a890687c310'
      const timestamp = new Date().valueOf()
      const sign = md5(timestamp + appKey + pcode)
      const headers = {
        'Content-Type': 'application/json',
        sign: sign,
         timestamp: timestamp,
            frontUrl: window.location.href,
        appKey: appKey
      }
      var param = {
        orderNO: orderNum,
        userID: userId
      }

      $.ajax({
        type: 'POST',
        url: host1 + '/order/checkNgoOrder',
        data: JSON.stringify(param),
        contentType: 'application/json',
        headers: headers,
        success: function (res) {
          if (res.status === 200) {
            if (res.data.isPay && res.data.isPay === 'N') {
              $('#payAgain').hide();
            }
          }
          loadingAnimate.end()
        },
        fail: function (data) {
          alert(data.resultMsg)
        },
        complete: function (complete) {
          $('.box .dom').find('.finalScore').html(datas.data.finalScore)
          $(".answerEnd").show();
        }
      })
    }

    // 答题结束，在访问页面重置到答题结果页
    function issureIsOver(source, languageFlag) {
      $("#isEnd").show();
      if (fileKey) {
        $("#reportDown").show();
        $('#noHasExport').hide()
      } else {
        if (!isOpenFaPiao) {
          $('#noHasExport').show()
        }
      }

      // 语言标签有值
      if (languageFlag) {
        var lan = '';
        if (currHash === 'en') {
          lan = languageFlag === 'cn' ? 'chinese' : 'english';
        } else {
          lan = languageFlag === 'cn' ? '中文' : '英文';
        }
        $('#noHasExport').find('.lan').html(lan)
      }
      $('.resultBox').find('.finalScore').html(source || 0)
      NGO.currPage = 0
      isEnd = true
      switchPage()
    }

    // 问卷退回
    function backQuestuon(currentPart) {
      loadingAnimate.start()
      var param = {
        replyId: replyId,
        currentPart: currentPart,
      }
      $.ajax({
        type: 'POST',
        url: host + '/ticMember/business/api.v2.question/question/backQuestuon',
        data: JSON.stringify(param),
        contentType: 'application/json',
        headers: {
          pid: pid,
          sign: sing(param),
           timestamp: timestamp,
            frontUrl: window.location.href
        },
        success: function (res) {
          var data = JSON.parse(res)
          if (data.resultCode == '0') {
            showCurrPage()
          }
          loadingAnimate.end()
        },
        fail: function (data) {
          alert(data.resultMsg)
        },
        complete: function (complete) {
        }
      })
    }

    // 异步接口
    var apis = {
      // 报告下载
      reportDown: function () {
        loadingAnimate.start()
        var param = {
          replyId: replyId
        }
        $.ajax({
          type: 'POST',
          url: host + '/ticMember/business/api.v2.question/question/getDownUrl',
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: pid,
            sign: sing(param),
             timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            var data = JSON.parse(res)
            if (data.result_code == '0') {
              var datas = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
              window.location.href = datas.url
            } else {
              alert(data.resultMsg)
            }
            loadingAnimate.end()
          },
          fail: function (data) {
            alert(data.resultMsg)
          },
          complete: function (complete) {
          }
        })
      },
      // 获取答案
      getAnswer: function (params, cb) {
        loadingAnimate.start()
        var param = {
          partNo: params.partNo,
          questionId: 1,
          userId: userId,
          orderNo: orderNum || 0
        }
        $.ajax({
          type: 'POST',
          url: host + '/ticMember/business/api.v2.question/qupart/qryAnswer',
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: pid,
            sign: sing(param),
             timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            var data = JSON.parse(res)
            if (data.result_code == '0') {
              cb && cb(data.data)
              // setAnswer(data.data)
            } else {
              alert(data.resultMsg)
            }
            loadingAnimate.end()
          },
          fail: function (data) {
            alert(data.resultMsg)
          },
          complete: function (complete) {
          }
        })

      }
    }
  });
</script>
<% include ../footer.html %>
