'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class CaseService extends Service {

    async getList(params) {
        const {
            app
        } = this;
        let query = ['n.is_delete=0'];
        if (params.type && params.type != 0) {
            query.push('c.id=' + params.type);
        }

        if (params.title) {
            query.push('n.title LIKE "%' + params.title + '%"');
        }

        if (params && typeof params.is_publish == 'number') {
            query.push('n.is_publish=' + params.is_publish);
        }

        if (params && params.service) {
            query.push('cr.catalog_id=' + params.service);
        }

        if (params && params.trade) {
            query.push('cr.catalog_id=' + params.trade);
        }

        if (params.time) {
            let time = params.time.split(' - ');
            query.push('n.gmt_publish_time>="' + time[0] + '"');
            query.push('n.gmt_publish_time<"' + time[1] + '"');
        }

        let page = params.page || 1,
            limit = params.limit || 10;
        let start = (page - 1) * limit;

        const total = await app.mysql.query('SELECT n.id FROM cases n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.case_id=n.id WHERE ' + query.join(' AND ') + ' GROUP BY n.id');
        const list = await app.mysql.query('SELECT n.id,n.title,n.alias,n.is_top,n.is_publish,n.gmt_publish_time AS time,c.name AS typeName FROM cases n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.case_id=n.id WHERE ' + query.join(' AND ') + ' GROUP BY n.id ORDER BY n.is_top DESC,n.gmt_publish_time DESC LIMIT ' + start + ',' + limit);

        for (let item of list) {
            item.time = moment(item.time).format('YYYY-MM-DD HH:mm:ss');

            let tradeList = [],
                serviceList = [];
            let tradeCata = await this.app.mysql.select('catalog_relation', {
                where: {
                    case_id: item.id,
                    catalog_type: 1
                }
            });
            let serviceCate = await this.app.mysql.select('catalog_relation', {
                where: {
                    case_id: item.id,
                    catalog_type: 2
                }
            });

            if (tradeCata && tradeCata.length > 0) {
                for (let ti of tradeCata) {
                    let tradeInfo = await this.app.mysql.get('catalog', {
                        id: ti.catalog_id
                    });
                    //let tradeParantInfo = await this.app.mysql.get('catalog', { id: tradeInfo.parent_id });
                    //tradeList.push(tradeParantInfo.name + '-' + tradeInfo.name);
                    if (tradeInfo) {
                        tradeList.push(tradeInfo.name);
                    }

                }
            }

            if (serviceCate && serviceCate.length > 0) {
                for (let si of serviceCate) {
                    let serviceInfo = await this.app.mysql.get('catalog', {
                        id: si.catalog_id
                    });
                    //let serviceParantInfo = await this.app.mysql.get('catalog', { id: serviceInfo.parent_id });
                    //serviceList.push(serviceParantInfo.name + '-' + serviceInfo.name);
                    if (serviceInfo) {
                        serviceList.push(serviceInfo.name);
                    }

                }
            }

            item.tradeList = tradeList;
            item.serviceList = serviceList;
        }

        return {
            list: list,
            total: total.length,
            page: page,
            limit: limit
        }
    }

    async getDetail(id) {
        const {
            app
        } = this;

        const detail = await app.mysql.get('cases', {
            id: id
        });

        // 获取更新访问次数
        let hits = detail.hits
        if (hits) {
            hits += 1
        } else {
            hits = 1
        }
        // detail.hits = hits > 999 ? '999+' : hits
        const row = {
            id,
            hits: hits
        }
        // await app.mysql.update('cases', row)

        const tag = await app.mysql.select('tag_relation', {
            columns: ['id', 'sku_id', 'tag_name'],
            where: {
                'sku_id': detail.id
            },
        });

        const tradeCata = await app.mysql.select('catalog_relation', {
            where: {
                case_id: id,
                catalog_type: 1
            }
        });
        const serviceCata = await app.mysql.select('catalog_relation', {
            where: {
                case_id: id,
                catalog_type: 2
            }
        });
        const tagList = await app.mysql.select('tag_relation', {
            where: {
                case_id: id
            }
        });

        if (tradeCata && tradeCata.length > 0) {
            for (let ti of tradeCata) {
                let tradeInfo = await app.mysql.get('catalog', {
                    id: ti.catalog_id
                });
                let tradeParantInfo = await app.mysql.get('catalog', {
                    id: tradeInfo.parent_id
                });
                ti.parentName = tradeParantInfo.name;
                ti.name = tradeInfo.name;
                ti.id = tradeInfo.id;
            }
        }

        if (serviceCata && serviceCata.length > 0) {
            for (let si of serviceCata) {
                let serviceInfo = await app.mysql.get('catalog', {
                    id: si.catalog_id
                });
                let serviceParantInfo = await app.mysql.get('catalog', {
                    id: serviceInfo.parent_id
                });
                si.parentName = serviceParantInfo.name;
                si.name = serviceInfo.name;
                si.id = serviceInfo.id;
            }
        }

        if (detail.is_publish == 1) {
            detail.is_publish = true;
        } else {
            detail.is_publish = false;
        }

        if (detail.is_top == 1) {
            detail.is_top = true;
        } else {
            detail.is_top = false;
        }

        detail.tag = [];
        for (let item of tagList) {
            detail.tag.push(item.tag_name);
        }

        detail.tradeCata = tradeCata;
        detail.serviceCata = serviceCata;

        if (detail.content) {
            detail.content = detail.content.replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
        }

        return {
            detail
        };
    }

    async getCaseRelate(id) {
        const {
            app
        } = this;

        const thisInfo = await app.mysql.get('cases', {
            id: id
        });

        const publishTime = moment(thisInfo.gmt_publish_time).format('YYYY-MM-DD HH:mm:ss');
        let prevItem = await await app.mysql.query(`select id,title,alias from cases where gmt_publish_time = (select max(gmt_publish_time) from cases where gmt_publish_time < '${publishTime}' AND is_delete=0 AND is_publish=1)`)
        let nextItem = await await app.mysql.query(`select id,title,alias from cases where gmt_publish_time = (select min(gmt_publish_time) from cases where gmt_publish_time > '${publishTime}' AND is_delete=0 AND is_publish=1)`)

        return {
            prevItem,
            nextItem
        }
    }

    async otherCase(id, cid) {
        const {
            app
        } = this;
        // const cata = await app.mysql.get('catalog_relation', {
        //     case_id: id
        // });
        // if (cata) {
        //     const catalog_id = cata.catalog_id;
        //     const list = await app.mysql.query('SELECT n.id,n.title,n.content,n.alias FROM cases n LEFT JOIN catalog_relation cr ON n.id=cr.case_id WHERE n.is_delete=0 AND n.is_publish=1 AND n.id!=' + id + ' AND cr.catalog_id=' + catalog_id + ' ORDER BY n.is_top DESC,n.gmt_publish_time DESC LIMIT 0,10');
        //
        //     return {
        //         list
        //     }
        // } else {
        //     return {
        //         list: []
        //     };
        // }


      const list = await app.mysql.query('SELECT n.id,n.title,n.content,n.alias FROM cases n JOIN catalog_relation cr ON n.id=cr.case_id JOIN catalog_relation c ON cr.catalog_id=c.catalog_id and c.case_id='+id+' WHERE n.is_delete=0 AND n.is_publish=1 AND n.id!='+id+' ORDER BY n.is_top DESC,n.gmt_publish_time DESC LIMIT 0,10');

      return {
        list
      }

    }

    // 获取首页资讯列表
    async getHomeHotList() {
        const {
            app
        } = this;


        const list = await app.mysql.query(`SELECT a.id, a.title, a.alias, a.catalog_id, b.name as catalog_name FROM cases a LEFT JOIN catalog b ON a.catalog_id=b.id WHERE a.is_delete='0' AND a.is_publish='1' AND a.is_hot='1' ORDER BY a.gmt_modify DESC`);
        const catalogs = await app.mysql.query(`SELECT * FROM catalog WHERE is_show='1' AND is_navi='1' AND is_delete='0' AND parent_id=5`);
        let seminarId = 0;
        catalogs.forEach(item => {
            if (item.name === '展会及研讨会' || item.name === '直播及研讨会') {
                seminarId = item.id
            }
        });

        let seminar = [], // 展会及研讨会
            other = []; // 除展会及研讨会

        list.forEach(item => {
            if (item.catalog_id === seminarId) {
                seminar.push(item)
            } else {
                other.push(item)
            }
        })

        return {
            seminar,
            other
        }
    }
}

module.exports = CaseService;
