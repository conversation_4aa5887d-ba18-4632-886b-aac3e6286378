const Controller = require('egg').Controller;
const util = require('./util');
const category = require("../public/js/oiq/category").category;
const project = require("../public/js/oiq/category").project;
const products = require("../public/js/oiq/category").products;
const {
  mallPid,
  mallPcode,
  env
} = require('../../config/info').siteInfo;

async function getCookie(ctx, key) {
  if (!ctx.request.header.cookie) {
    return '';
  }
  const carr = ctx.request.header.cookie.split('; ');
  let sjson = {};
  for (let item of carr) {
    let iarr = item.split('=');
    sjson[iarr[0]] = iarr[1];
  }

  if (sjson) {
    return sjson[key];
  } else {
    return '';
  }

}

class ActiveController extends Controller {
  async start() {
    const {
      ctx
    } = this;
    let detail = {
      name: 'z'
    };
    const source = ctx.query.source || '';
    const userInfo = await util.getUserInfo(ctx)
    console.log(userInfo)
    let token = await util.getCookie(ctx, 'SSO_TOKEN');
    const inquirynum = await ctx.service.external.GetStateNum({}, token + '==')
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('oiq/start', {
      isLogin,
      userInfo,
      inquirynum: inquirynum,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      indUrl: env[this.app.locals.env].indUrl,
      detail,
      source,
      csrf: ctx.csrf,
    })
  }

  async landingPage() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();
    const oiqList = await ctx.service.oiq.getOiqList(ctx.headers)
    let host = ''
    if (this.app.locals.env == 'prod') {
      host = 'https://www.sgsmall.com.cn';
    } else {
      host = 'http://uat.sgsmall.com.cn';
    }
    let outDetail = {};
    outDetail.name = 'SGS快速报价服务介绍';
    outDetail.page_keywords = 'SGS,检测,认证';
    outDetail.page_description = 'SGS是国际公认第三方检测认证机构，金属及高分子材料实验室全新发布在线询价工具，快速检测报价，工程师定制检测方案，现在询价立项优惠。';
    const source = ctx.query.source || '';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('oiq/landingPage', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      source,
      orderNum: Number(userInfo.orderNum.lv3) + Number(userInfo.orderNum.oiq),
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0,
      category,
      oiqList,
      project,
      products
    })
  }

}

module.exports = ActiveController;
