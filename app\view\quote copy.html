<% include header.html %>
  <% include ./components/header.html %>
    <div class="contact_v2">
      <div class="contact_main">
        <div class="contact_title">咨询报价</div>
        <div class="contact_des">全国业务就近分配，最快2小时联系您</div>
        <div class="contact_form" id="quote-box" v-loading="loading" v-cloak>
          <el-form ref="ruleForm" :model="form" :rules="rules">
            <el-form-item label="" prop="serviceIndex">
              <el-select v-model="form.serviceIndex" placeholder="请选择您需要的服务类型*" @change="serviceChange"
                style="width: 100%;;">
                <el-option v-for="(item, index) in serviceOptions" :key="item.id" :label="item.quoteAlias || item.name"
                  :value="index"></el-option>
              </el-select>
            </el-form-item>
            <template v-if="!showCountry">
              <div style="width:348px;" v-if="!tradeOptions || !tradeOptions.length"></div>
              <el-form-item label="" prop="tradeIndex" v-loading="tradeLoading"
                v-if="tradeOptions && tradeOptions.length">
                <el-select v-model="form.tradeIndex" placeholder="请选择您的产品类别*" @change="tradeChange"
                  style="width: 100%;;">
                  <el-option v-for="(item, index) in tradeOptions" :key="item.productId" :label="item.productName"
                    :value="index"></el-option>
                </el-select>
              </el-form-item>
            </template>
            <el-form-item label="" prop="destCountry" v-if="showCountry">
              <el-input v-model="form.destCountry" placeholder="请输入目的国*" maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="" prop="company">
              <el-input v-model="form.company" placeholder="请输入企业名称*" maxlength="200"></el-input>
            </el-form-item>
            <el-form-item label="" prop="provice">
              <el-select v-model="form.provice" placeholder="请选择所在省份或地区*" filterable style="width: 100%;">
                <el-option v-for="(item, index) in provice" :key="item.orgName" :label="item.orgName"
                  :value="item.orgName"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="content">
              <el-input type="textarea" v-model="form.content" resize="none" maxlength="500"
                :placeholder="productExplain || '请描述您的送测产品，若有明确的测试项目名称或测试标准号请提供*'"></el-input>
            </el-form-item>
            <el-form-item label="" prop="customer">
              <el-input v-model="form.customer" placeholder="请输入您的姓名" maxlength="50"></el-input>
            </el-form-item>
            <el-form-item class="contact_type">
              <div class="contact_radios">
                <span>您期望的联系方式<i>*</i></span>
                <div class="contact_type_swtich">
                  <em @click='handleChange(1)' :class="{ active: radio === 1, on: form.phone }">手机<label></label></em>
                  <em @click='handleChange(2)' :class="{ active: radio === 2, on: form.email }">邮箱<label></label></em>
                </div>
              </div>
              <div class="contact_type_line"></div>
            </el-form-item>
            <el-form-item label="   " v-if='radio == 1' prop="phone" class="contact_phone_form" style="margin-bottom: 0 !important;" >
              <el-input v-if='radio == 1' v-model="form.phone" placeholder="请输入您的手机号" ref="phone" maxlength="50" @input='handleInputEmail'
                :disabled='isLogin'></el-input>
            </el-form-item>
            <el-form-item label="   " v-if='radio == 2' prop="email" class="contact_email_form" style="margin-bottom: 0 !important;">
              <el-input v-if='radio == 2' v-model="form.email" placeholder="请输入您的电子邮箱" ref="email" maxlength="50" @input='handleInputEmail'>
              </el-input>
            </el-form-item>
            <el-form-item label="   "  class="contact_tips" v-if='!isLogin'>
              <p>*推荐填写手机号，自动注册会员，在线查看咨询进度</p>
            </el-form-item>
            <div class="contact_approve">
              <el-checkbox v-model="approve">
                <span style="color: #333333;">
                  <template v-if='contact_approve_show'>提交手机自动注册会员，获取后续信息，</template>
                  我已阅读并同意
                  <template v-if='contact_approve_show'><a @click='dialogVisibleReg = true'>注册条款</a>及</template>
                  <a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" target="_blank">隐私政策</a>
                </span>
              </el-checkbox>
            </div>
            <div class="contact_submit">
              <el-button type="primary" @click="handleSubmit">提 交</el-button>
            </div>
            <!-- <div style="opacity: 0; height: 0;">
              <button id="TencentCaptcha" data-appid="193600977" data-cbfn="callback" type="button">验证</button>
            </div> -->
          </el-form>
          <el-dialog :visible.sync="dialogVisible" :show-close="false" :close-on-click-modal="false"
            :close-on-press-escape="false" class="jump_dialog" v-cloak>
            <img
              :src="dialogData.jumpImg && dialogData.jumpImg.pc ? dialogData.jumpImg.pc : '../public/images/oiq_dialog.png'"
              alt="" class="jump_img">
            <img src="../public/images/closeDialog.png" alt="" class="close-btn" @click="closeDialog">
            <el-button type="primary" class="jump-btn" @click="jump">立即开始</el-button>
          </el-dialog>
          <% include ./pages/quote/modal.html %>
            <% include ./components/registionClause.html %>
        </div>
      </div>
      <div class="contact_ads"></div>
    </div>
    <script src="<%- locals.static %>/js/md5.js"></script>
    <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
    <script>
      var pid = 'pid.mall',
        pcode = 'Z0zCnRE3IaY9Kzem'
      var newVue = new Vue({
        name: 'quote',
        el: '#quote-box',
        data: function () {
          var validatorUserPhone = function (rule, value, callback) {
            var reg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/
            if (!value) {
              return callback(new Error('*请输入手机号'));
            } else if (!reg.test(value)) {
              return callback(new Error('*请输入正确的手机号码'));
            } else {
              return callback()
            }
          }
          var validatorEmail = function (rule, value, callback) {
            var reg =   /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/
            if (!value) {
              return callback(new Error('*请输入邮箱地址'));
            } else if (!reg.test(value)) {
              return callback(new Error('*请输入正确的邮箱地址'));
            } else {
              return callback()
            }
          }

          return {
            radio: 1,
            host: '<%- host %>',
            form: {
              type: '业务咨询',
              trade: '',
              tradeName: '其他',
              tradeIndex: '',
              serviceIndex: '',
              service: '',
              serviceName: '其他',
              company: '',
              provice: '',
              content: '',
              customer: '<%- userInfo.userName %>',
              email: '<%- userInfo.userEmail %>',
              phone: '<%- userInfo.userPhone %>',
              imageCode: '',
              frontUrl: window.location.href,
              destCountry: '',
              verifyCode: ''
            },
            rules: {
              tradeIndex: [{ required: true, message: '*请选择您需要的服务类型' }],
              serviceIndex: [{ required: true, message: '*请选择您的产品类别' }],
              company: [{ required: true, message: '*请输入企业名称' }],
              provice: [{ required: true, message: '*请选择所在省份或地区' }],
              content: [{ required: true, message: '*请描述您的需求' }],
              customer: [{ required: true, message: '*请输入您的姓名' }],
              destCountry: [{ required: true, message: '*请输入目的国' }],
              // email: [{ validator: validatorEmail }],
              // phone: [{ validator: validatorUserPhone }],
            },
            approve: false,
            provice: [],
            props: {
              value: 'orgName',
              label: 'orgName',
              children: 'citys'
            },
            loading: false,
            serviceOptions: [],
            tradeLoading: false,
            tradeOptions: [],
            productExplain: '',
            dialogData: {},
            dialogVisible: false,
            showCountry: false,
            showModal: false,
            seconds: 59,
            timer: null,
            isLogin: <%- isLogin %>,
            dialogVisibleReg: false,
            contact_approve_show: true
          }
        },
        created: function () {
          this.qryCity()
          this.qryByAlias()
          var that = this
          window.callback = function (res) {
            that.verifyCaptcha(res)
          }
          if (this.isLogin) this.contact_approve_show = false
        },
        methods: {
          handleInputEmail: function (){
            // if (!this.isLogin) {
            //   if (this.form.email && !this.form.phone) {
            //     this.contact_approve_show = false
            //   } else {
            //     this.contact_approve_show = true
            //   }
            // }
          },
          updateEmailProps: function () {
            this.propEmail = false
          },
          jump: function () {
            if (this.dialogData.jumpUrl && this.dialogData.jumpUrl.pc) {
              jumpOIQAddtionSource(this.dialogData.jumpUrl.pc, '?')
            }
          },
          closeDialog: function () {
            var that = this
            that.dialogVisible = false
            that.form.serviceIndex = ''
            that.form.service = ''
            that.form.serviceName = ''
            that.form.tradeIndex = ''
            that.form.trade = ''
            that.form.tradeName = '其他'
            that.form.productId = ''
            that.form.productName = ''
            that.tradeOptions = []
            this.$nextTick(function () {
              that.$refs.ruleForm.clearValidate()
            })
          },
          serviceChange: function (val) {
            var that = this
            var id = that.serviceOptions[val].id
            that.form.service = id
            that.form.serviceName = that.serviceOptions[val].name
            that.form.tradeIndex = ''
            that.form.trade = ''
            that.form.tradeName = '其他'
            that.form.productId = ''
            that.form.productName = ''
            that.productExplain = that.serviceOptions[val].productExplain
            that.showCountry = that.serviceOptions[val].alias === 'governments'
            that.destCountry = ''
            if (that.showCountry) {
              return
            }
            var param = { serviceId: id }
            if (!param.serviceId) {
              param.serviceId = 0
            }
            var timestamp = new Date().valueOf();
            that.tradeLoading = true
            $.ajax({
              type: 'POST',
              url: this.host + '/ticMall/business/api.v1.mgmt/product/qry',
              data: JSON.stringify(param),
              contentType: 'application/json',
              headers: {
                pid: pid,
                sign: that.sing(param, timestamp),
                 timestamp: timestamp,
            frontUrl: window.location.href
              },
              success: function (res) {
                that.tradeLoading = false
                if (res.resultCode === '0') {
                  that.tradeOptions = res.data.items || []
                  if (that.tradeOptions.length) {
                    that.tradeOptions.push({
                      industryId: '',
                      industryName: '其他',
                      productId: 0,
                      productName: '其他'
                    })
                  }
                } else {
                  that.tradeOptions = []
                  that.$message.error(res.resultMsg)
                }
              },
              fail: function (data) {
                that.tradeOptions = []
                that.tradeLoading = false
                that.$message.error(res.resultMsg)
              },
              complete: function (complete) {
              }
            })
          },
          tradeChange: function (val) {
            if (!val && val !== 0) {
              return
            }
            var that = this
            that.form.trade = that.tradeOptions[val].industryId
            that.form.tradeName = that.tradeOptions[val].industryName
            that.form.productId = that.tradeOptions[val].productId
            that.form.productName = that.tradeOptions[val].productName
            if (that.tradeOptions[val].jumpType === 1) {
              that.dialogData = that.tradeOptions[val]
              that.dialogVisible = true
            }
          },
          qryByAlias: function () {
            var param = { alias: 'service' }
            var timestamp = new Date().valueOf();
            var that = this
            that.loading = true
            $.ajax({
              type: 'POST',
              url: that.host + '/ticMall/business/api.v1.mall/catalog/qryByAlias',
              data: JSON.stringify(param),
              contentType: 'application/json',
              headers: {
                pid: pid,
                sign: this.sing(param, timestamp),
                 timestamp: timestamp,
            frontUrl: window.location.href
              },
              success: function (res) {
                that.loading = false
                if (res.resultCode === '0') {
                  that.serviceOptions = res.data.items || []
                  that.serviceOptions.push({
                    id: '',
                    name: '其他'
                  })
                } else {
                  that.$message.error(res.resultMsg)
                }
              },
              fail: function (data) {
                that.loading = false
                that.$message.error(res.resultMsg)
              },
              complete: function (complete) {
              }
            })
          },
          handleKeydown: function () {
            if (this.$refs.cascader.suggestions.length) {
              this.$refs.cascader.handleSuggestionClick(0)
            }
          },
          sing: function (param, timestamp) {
            var pmd5 = md5((pid + pcode).toUpperCase());
            var str = JSON.stringify(param) + pmd5.toUpperCase();
            return md5(str + timestamp);
          },
          qryCity: function () {
            var param = {}
            var timestamp = new Date().valueOf();
            var that = this
            $.ajax({
              type: 'POST',
              url: this.host + '/ticCenter/business/api.v1.center/org/qryCity',
              data: JSON.stringify(param),
              contentType: 'application/json',
              headers: {
                pid: pid,
                sign: this.sing(param, timestamp),
                 timestamp: timestamp,
            frontUrl: window.location.href
              },
              success: function (res) {
                if (res.resultCode === '0') {
                  for (var i = 0; i < res.data.length; i++) {
                    res.data[i].citys = null
                  }
                  that.provice = res.data || []
                } else {
                  that.$message.error(res.resultMsg)
                }
              },
              fail: function (data) {
                that.$message.error(res.resultMsg)
              },
              complete: function (complete) {
              }
            })
          },
          // 验证手机和邮箱
          verifyPhoneAndMail: function () {
            var regMail =   /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/;
            var regPhone = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;
            if (!this.form.phone && !this.form.email) {
              if (this.radio === 1) {
                this.$message.error('请输入手机号')
                this.radio = 1
              } else {
                this.$message.error('请输入邮箱地址')
                this.radio = 2
              }
            } else if (this.form.phone && !this.form.email) {
              if (!regPhone.test(this.form.phone)) {
                this.$message.error('请输入正确的手机号')
                this.radio = 1
              } else {
                this.radio = 1
                flag = true
              }
            } else if (!this.form.phone && this.form.email) {
              if (!regMail.test(this.form.email)) {
                this.$message.error('请输入正确的邮箱地址')
                this.radio = 2
              } else {
                this.radio = 2
                flag = true
              }
            } else {
              if (!regPhone.test(this.form.phone)) {
                this.$message.error('请输入正确的手机号')
                this.radio = 1
              } else if (!regMail.test(this.form.email)) {
                this.$message.error('请输入正确的邮箱地址')
                this.radio = 2
              } else {
                flag = true
              }
            }
            return flag;
          },
          handleSubmit: function () {
            const that = this
            if (!that.approve) {
              this.$message.error('请先阅读隐私政策并选择是否同意')
              return
            }
            this.$refs.ruleForm.validate(function (valid) {
              if (valid) {
                if (that.verifyPhoneAndMail()) {
                  if (that.form.phone) {
                    if (!that.isLogin) {
                      Cookies.remove('SSO_TOKEN', { domain: '.sgsmall.com.cn' })
                      that.showModal = true
                      that.sendCode()
                    } else {
                      that.handleModalSubmit()
                    }
                  } else {
                    $("#TencentCaptcha").click();
                  }
                }
              } else {
                that.loading = false
              }
            })
          },
          verifyCaptcha: function (res) {
            var that = this
            if (res.ret === 0) {
              // 验证成功
              that.loading = true
              that.handleModalSubmit()
            } else if (res.ret === 2) {
              // 用户主动关闭验证码
            }
          },
          handleClose: function () {
            this.showModal = false
            this.form.verifyCode = ''
          },
          // 切换手机和邮箱
          handleChange: function (val) {
            this.radio = val
            // setTimeout(() => {
            //   if (val === 1) {
            //     this.$refs['phone'].focus();
            //     this.$refs['phone'].blur();
            //   } else {
            //     this.$refs['email'].focus();
            //     this.$refs['email'].blur();
            //   }
            // }, 100);
          },
          sendCode: function () {
            if (!this.timer) {
              var param = {
                projectName: 'TIC_TEMPLATE',
                verifyMode: 1,
                verifyNumber: this.form.phone,
                verifyType: 2
              }
              var timestamp = new Date().valueOf();
              var that = this
              $.ajax({
                type: 'POST',
                url: this.host + '/ticCenter/business/api.v1.center/verify/getCode',
                data: JSON.stringify(param),
                contentType: 'application/json',
                headers: {
                  pid: pid,
                  sign: this.sing(param, timestamp),
                   timestamp: timestamp,
            frontUrl: window.location.href
                },
                success: function (res) {
                  if (res.resultCode === '0') {
                    that.timer = setInterval(function () {
                      if (that.seconds > 1) {
                        that.seconds -= 1
                      } else {
                        clearInterval(that.timer)
                        that.timer = null
                        that.seconds = 59
                      }
                    }, 1000)
                  } else {
                    that.$message.error(res.resultMsg)
                  }
                },
                fail: function (data) {
                  that.$message.error(res.resultMsg)
                },
                complete: function (complete) {
                }
              })
            }
          },
          handleModalSubmit: function () {
            var that = this
            var param = that.form
            var timestamp = new Date().valueOf();
            var url = '';
            if (param.phone) {
              url = '/submitTicket';
            } else {
              url = '/submitTicketByEmail'
            }
            that.loading = true
            $.ajax({
              type: 'POST',
              url: url,
              data: JSON.stringify(param),
              contentType: 'application/json',
              headers: {
                pid: pid,
                sign: that.sing(param, timestamp),
                 timestamp: timestamp,
            frontUrl: window.location.href
              },
              success: function (res) {
                that.loading = false
                if (res.resultCode === '0') {
                  // 96 新用户 1
                  // 97 已有用户 2  且登录 3
                  // 98 注册失败
                  // 邮箱提交 4
                  if (!that.form.phone && that.form.email) {
                    window.location = '/success?type=4';
                  } else {
                    if (res.data.isReg === 1) {
                      window.location = '/success?type=1&phone=' + that.form.phone;
                    } else if (res.data.isReg === 0) {
                      if (that.isLogin) {
                        window.location = '/success?type=3';
                      } else {
                        window.location = '/success?type=2&phone=' + that.form.phone;
                      }
                    }
                    Cookies.set("SSO_TOKEN", res.data.token, { domain: ".sgsmall.com.cn", expires: 7 });
                  }
                  that.showModal = false
                  that.loading = false
                } else if (res.resultCode === '9978') {
                  Cookies.remove('SSO_TOKEN', { domain: '.sgsmall.com.cn' })
                  that.isLogin = false
                  that.showModal = true
                  that.loading = false
                  that.sendCode()
                } else {
                  that.$message.error(res.resultMsg)
                }
              },
              fail: function (data) {
                that.loading = false
                that.$message.error(res.resultMsg)
              },
              complete: function (complete) {
              }
            })
          }
        }
      });
    </script>
    <% include footer.html %>
