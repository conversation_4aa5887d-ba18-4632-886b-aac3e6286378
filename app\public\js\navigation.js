$(function () {
  /*
    TIC-5198
    异步渲染首页导航
    异步渲染内页导航
  */
  asyncRenderNavication();
  function asyncRenderNavication() {
    $.ajax({
      type: 'POST',
      url: '/getNavication',
      data: {},
      headers: {
        frontUrl: window.location.href
      },
      success: function (res) {
        createHomeNavivation(res)
      },
      fail: function (data) {
      },
      complete: function (complete) {
      }
    })
  }

  // 创建首页导航
  function createHomeNavivation(res) {
    var serviceID = $("#serviceHome #serviceNav"),
      tradeID = $("#tradeHome #tradeNav"),
      serviceNavication = res.serviceNavication,
      tradeNavication = res.tradeNavication;
    if (serviceNavication.length) {
      renderNavicationHome(serviceNavication, serviceID, '/service/')
    }
    if (tradeNavication.length) {
      renderNavicationHome(tradeNavication, tradeID, '/industry/')
    }
  }

  function renderNavicationHome(data, boxId, path) {
    var mainDom = '<div class="main">',
      sideDom = '<div class="side"><ul>';
    data.forEach(function (v1, index) {
      sideDom += '<li><a data-id="' + v1.id + '" href="' + path + v1.alias + '">' + v1.name + `</a><span><svg class="right-icon" aria-hidden="true"><use xlink:href="#home_menu_icon"></use></svg></span></li>`;
    })
    sideDom += '</ul></div>';
    data.forEach(function (v1, index) {
      mainDom += '<div class="list clearfix ">';
      if (v1.lstSub && v1.lstSub.length >= 5) {
        mainDom += '<div class="second"><ul>';
        v1.lstSub.forEach(function (v2, index2) {
          if (!index2) {
            mainDom += '<li style="display: list-item;" class="active"><a href="javascript:void(0);" data-id="' + v2.id + '" data-link="' + path + v1.alias + '/#/' + v2.id + '">' + v2.name + '</a></li>';
          } else {
            mainDom += '<li><a href="javascript:void(0);" data-id="' + v2.id + '" data-link="' + path + v1.alias + '/#/' + v2.id + '">' + v2.name + '</a></li>';
          }
        })
        mainDom += '</ul></div>';
        mainDom += '<div class="secondList">';
        mainDom += '<div class="clearfix">';
        mainDom += '<ul class="secondListLi">';
        v1.lstSub.forEach(function (v2, index2) {
          // 3级菜单start
          mainDom += '<li>';
          mainDom += '<div class="main-list2">';
          mainDom += '<label>';
          v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
            if (v3.showType === 1) {
              if (v3.linkUrl === "") {
                mainDom += '<p class="cleafix" title="' + v3.showName + '">' + v3.showName + '</p>';
              } else {
                mainDom += '<a class="cleafix" href="' + v3.linkUrl + '" title="' + v3.showName + '">' + v3.showName + '</a>';
              }
            }
            if (v3.showType === 2) {
              mainDom += '<span>';
              if (v3.isHot) {
                // 是否热门
                mainDom += '<a class="hot" href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
              } else {
                mainDom += '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
              }
              mainDom += '</span>';
            }
            // 最后一条之后添加分隔符
            if (index3 === v2.lstNavi.length - 1) {
              mainDom += '<div class="dashedLine"></div>';
            }
          })
          mainDom += '</label>';
          mainDom += '</div>';
          // 3级菜单end
          // 广告start
          mainDom += '<div class="ads clearfix">';
          mainDom += '<ul class="clearfix">';
          v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
            if (v3.showType === 3) {
              mainDom += '<li>' +
                '<a href="' + v3.linkUrl + '" style="background-image:url(' + v3.imgUrl + ');" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '"></a>' +
                '<div class="info">' +
                '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
                '<h3 title="' + v3.showName + '">' + v3.showName + '</h3>' +
                '<p title="' + v3.showSummary + '">' + v3.showSummary + '</p>' +
                '</a>' +
                '</div>' +
                '</li>';
            }
          })
          mainDom += '</ul><div class="moreService"><a href="/service/' + v1.alias + '/service" class="moreServiceA">查看更多服务<i></i></a></div></div>';
          // 广告end
          mainDom += '</li>';
        })
        mainDom += '</ul>';
        mainDom += '</div>';
        mainDom += '</div>';
      } else {
        mainDom += '<div class="main-list">';
        v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
          mainDom += '<dl class="clearfix">';
          // 2级start
          mainDom += '<dt>' +
            '<a href="javascript:void(0);" data-id="' + v2.id + '" data-link="' + path + v1.alias + '/#/' + v2.id + '">' + v2.name + '</a>' +
            '</dt>';
          // 2级end
          v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
            if (v3.showType === 2) {
              // 3级start
              mainDom += '<dd>';
              if (v3.isHot) {
                // 是否热门
                mainDom += '<a class="hot" href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
              } else {
                mainDom += '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' + v3.showName + '</a>';
              }
              mainDom += '</dd>';
              // 3级end
            }
          })
          mainDom += '</dl><div class="main-list-line"></div>';

        })
        mainDom += '</div>';
        mainDom += '<div class="ads">';
        // 广告start
        mainDom += '<ul>';
        v1.lstSub && v1.lstSub.forEach(function (v2, index3) {
          v2.lstNavi && v2.lstNavi.forEach(function (v3, index3) {
            if (v3.showType === 3) {
              mainDom += '<li>' +
                '<a href="' + v3.linkUrl + '" style="background-image:url(' + v3.imgUrl + ');" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
                '</a>' +
                '<div class="info">' +
                '<a href="' + v3.linkUrl + '" data-collectCode="' + v3.collectCode + '"  data-id="' + v3.catalogId + '">' +
                '<h3 title="' + v3.showName + '">' + v3.showName + '</h3>' +
                '<p title="' + v3.showSummary + '">' + v3.showSummary + '</p>' +
                '</a>' +
                '</div>' +
                '</li>';
            }
          })
        })
        mainDom += '</ul>';
        // 广告end
        mainDom += '<div class="moreService">' +
          '<a href="/service/' + v1.alias + '/service">查看更多服务<i></i></a>' +
          '</div>';
        mainDom += '</div>';
      }
      mainDom += '</div>';
    })
    mainDom += '</div>';
    boxId.html(sideDom + mainDom)

    setTimeout(function () {
      asyncBuildEvent()
    }, 100)
  }

  function asyncBuildEvent() {
    var tradeNav = $('#tradeNav');
    $("#tradeHome").hover(function () {
      tradeNav.show();
      // 默认选中第一个
      tradeNav.find('.side li').eq(0).addClass('active').siblings().removeClass('active');
      tradeNav.find('.main').show();
      tradeNav.find('.main .list').eq(0).show().siblings().hide();
      $(this).find('.dragAngel').show();
    }, function () {
      tradeNav.hide();
      $(this).find('.dragAngel').hide();
    })
    var serviceNav = $('#serviceNav');
    $("#serviceHome").hover(function () {
      serviceNav.show();
      // 默认选中第一个
      serviceNav.find('.side li').eq(0).addClass('active').siblings().removeClass('active');
      serviceNav.find('.main').show();
      serviceNav.find('.main .list').eq(0).show().siblings().hide();
      $(this).find('.dragAngel').show();
      $(this).find('.dragAngel').show();
    }, function () {
      serviceNav.hide();
      $(this).find('.dragAngel').hide();
    });
    // 新增二级下拉查单
    $('.nav_first--li').hover(function () {
      $(this).find('.dragAngel').show();
      $(this).find('.nav_second').show();
    }, function () {
      $(this).find('.dragAngel').hide();
      $(this).find('.nav_second').hide();
    });
    // main nav click envet
    $("#n_nav").on('mouseenter', 'li', function () {
      var index = $(this).index();
      var item = $(this).data('item');
      if (item) {
        $('.n_category').find('#' + item).show().siblings().hide();
        $(this).addClass("active").siblings().removeClass('active');
      }
    })

    // first nav event
    $('.n_category').on('mouseenter', '.side li', function () {
      var index = $(this).index();
      $(this).addClass('active').siblings().removeClass('active');
      $(this).closest('.side').siblings('.main').show();
      var mainEl = $(this).closest('.side').siblings('.main').find('.list');
      mainEl.eq(index).show().siblings().hide();
    })

    $('.n_category').on('mouseenter', '.side', function () {
      $(this).siblings().show();
    })

    $('.n_category').on('mouseenter', '.second li', function () {
      var index = $(this).index();
      $(this).addClass('active').siblings().removeClass('active');
      $(this).closest('.second').siblings('.secondList').find('.secondListLi > li').eq(index).show().siblings().hide();
    })

    $("#tradeNav, #serviceNav").mouseleave(function () {
      $(this).find('.main').hide();
      $(this).find('.side li').removeClass("active")
    });

    /*
      TIC-5202
      数据模型： 站点.页面.模块.标识位.session
      实例化后：spm=EJHxGX9b.h.n-${id}.${collectCode}.${sessionId}&token=${token}
      a:EJHxGX9b 站点名称 ticView(EJHxGX9b) member(HNCO2i11) store(Y0tCOIDS)
      b:1YCEZc6a(home首页) M3nTPXTW(inner)内页 网站页面
      c:n-${id} (navication)导航的菜单服务或行业id
      d:${collectCode} 服务端唯一标识
      e:${sessionId} sessionid
      token: 登录后用户token
    */
    $("#serviceNav, #tradeNav").on("click", "a", function (e) {
      if (e && e.preventDefault) {
        e.preventDefault();
      } else {
        window.event.returnValue = false;
      }
      var href = $(this).attr('href')
      var link = $(this).data('link')
      var collectcode = '';
      var id = '';
      if ($(this).data('collectcode')) collectcode = $(this).data('collectcode')
      if ($(this).data('id')) id = '-' + $(this).data('id')
      if (link) {
        createSPM('1YCEZc6a', id, collectcode, 'sessionId')
        window.location.href = link
      } else {
        createSPM('1YCEZc6a', id, collectcode, 'sessionId')
        window.location.href = href
      }
    });

    function createSPM(b, c, d, e) {
      var website = 'EJHxGX9b';
      var origin = window.location.origin;
      if (origin.indexOf('member') > -1) {
        website = 'HNCO2i11';
      } else if (origin.indexOf('tic') > -1) {
        website = 'Y0tCOIDS';
      }
      var spm = [website, b, 'n' + c, d, e];
      var query = {
        spm: spm.join('.'),
        token: Cookies.get('SSO_TOKEN'),
        tx: ''
      }
      $.ajax({
        type: 'POST',
        url: '/sendSPM',
        // url: 'http://localhost:9501/sendSPM',
        data: query,
        headers: {
          frontUrl: window.location.href
        },
        success: function (res) { },
        fail: function (data) { },
        complete: function (complete) { }
      })
    }
  }
});
