module.exports = appInfo => {
  // 环境判断
  const isTestEnv = ['local', 'test'].includes(appInfo.env);

  return {
    mysql: {
      client: isTestEnv ? {
        /* test */
        host: 'sgsdbcne2mysqlcnappdevmaster.mysql.database.chinacloudapi.cn',
        port: '3306',
        user: 'ticcms',
        password: 'jkl;4vyTO1cr',
        database: 'ticcms',
        useSSL: false,
        charset: 'utf8mb4'
      } : {
        /* uat */
        host: 'sgsdbcne2flexiblemysqlticsnewuat.mysql.database.chinacloudapi.cn',
        port: '3306',
        user: 'ticcms@sgsdbcne2mysqlticsnewuat',
        password: '?s%^g>4B}p7-%Xv8F_h.a',
        database: 'ticcms',
        useSSL: false,
        charset: 'utf8mb4'
      },
      app: true,
      agent: false
    }
  };
};