const Controller = require('egg').Controller;
const moment = require('moment');
const crypto = require('crypto');
const request = require('request');
const { navList, hotServiceList, solutionIds, newsList } = require("../public/js/promotion/jiance-new");
const {
  url,
  solrPstr,
  solrPid,
  mallPid,
  mallPcode,
  env
} = require('../../config/info').siteInfo;
const util = require('./util');
const fuwuList = require("../public/js/promotion/jiance").fuwuList;
const captchaAppId = '193600977'
const phoneNum = '4000-558-581'

function subString(str, n) {
  var r = /[^\x00-\xff]/g;
  var m;

  if (str.replace(r, '**').length > n) {
    m = Math.floor(n / 2);

    for (var i = m, l = str.length; i < l; i++) {
      if (str.substr(0, i).replace(r, '**').length >= n) {
        var newStr = str.substr(0, i) + '...';
        return newStr;
      }
    }
  }
  return str;
}

/*  fix bug for sentry TIC-VIEW-28 */
async function isMobile(ctx) {
  let isMobile = false;
  const userAgent = ctx.request.headers['user-agent'];
  if (userAgent && userAgent.toLowerCase().indexOf('mobile') != -1) isMobile = true;
  return isMobile;
}

async function changeMobileHost(host) {
  let mobileHost = ''
  switch (host) {
    case 'localhost:7002':
      mobileHost = 'http://localhost:3000'
      break;
    case 'dev.sgsmall.com.cn':
      mobileHost = 'https://mdev.sgsmall.com.cn'
      break;
    case 'test.sgsmall.com.cn':
      mobileHost = 'https://mtest.sgsmall.com.cn'
      break;
    case 'uat.sgsmall.com.cn':
      mobileHost = 'https://muat.sgsmall.com.cn'
      break;
    case 'uatgray.sgsmall.com.cn':
      mobileHost = 'https://muatgray.sgsmall.com.cn'
      break;
    case 'www.sgsmall.com.cn':
      mobileHost = 'https://m.sgsmall.com.cn'
      break;
    case 'gray.sgsmall.com.cn':
      mobileHost = 'https://mgray.sgsmall.com.cn'
      break;
    default:
      mobileHost = 'https://m.sgsmall.com.cn'
      break;
  }
  return mobileHost
}

function getIP(req) {
  let ip = req.get('x-forwarded-for'); // 获取代理前的ip地址
  if (ip && ip.split(',').length > 0) {
    ip = ip.split(',')[0];
  } else {
    ip = req.ip;
  }
  const ipArr = ip.match(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/g);
  return ipArr && ipArr.length > 0 ? ipArr[0] : '127.0.0.1';
}

async function getCookie(ctx, key) {
  if (!ctx.request.header.cookie) {
    return '';
  }
  const carr = ctx.request.header.cookie.split('; ');
  let sjson = {};
  for (let item of carr) {
    let iarr = item.split('=');
    sjson[iarr[0]] = iarr[1];
  }

  if (sjson) {
    return sjson[key];
  } else {
    return '';
  }
}

class HomeController extends Controller {
  async sku() {
    const {
      ctx
    } = this;
    // let alias = '',
    //     id = 0;
    // const params = ctx.params[0].split('/');
    // if (params.length > 1) {
    //     alias = params[0]
    //     id = params[1]
    // } else {
    //     id = params[0]
    // }
    const {
      alias,
      id
    } = ctx.params

    // 防止sql注入
    if (isNaN(Number(id))) return;

    // 移动设备重定向
    const mobile = await isMobile(ctx);
    const mobileHost = await changeMobileHost(ctx.request.host);
    if (mobile) {
      // ctx.redirect(encodeURI(`${mobileHost}/service/detail?cataId=${id}`), 301)
      // return;
      const headers = {
        Location: encodeURI(`${mobileHost}/service/detail?cataId=${id}`)
      }
      ctx.status = 302;
      ctx.set(headers)
    }

    const detail = await ctx.service.sku.getInfo(id);
    // 调接口新增访问次数
    const res = await ctx.service.external.qrySkuDetail({
      id
    })
    // 别名验证失效重定向到404页面
    if (!detail.detail || detail.detail == '' || detail.detail.is_delete == 1 || !detail.detail.is_use || detail.detail.alias != alias) {
      const headers = {
        Location: '/404'
      }
      ctx.status = 302;
      ctx.set(headers)
    }
    if (detail.detail.is_use == 0) {
      // ctx.body = '很抱歉，您正在查看的内容在调整中，<a href="/">去首页</a>查找你需要的服务';
      ctx.status = 412;
      return;
    }
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();
    const recommends = await ctx.service.sku.getRecommend(id);
    const tagAdpt = await ctx.service.sku.tagAdpt(id, false);

    // 相关案例
    const skuCases = await ctx.service.sku.skuCases(id);
    // 相关资料
    const skuResource = await ctx.service.sku.skuResource(id);
    // 获取楼层排序
    const skuFloorSort = await ctx.service.sku.skuFloorSort(id);
    if (!skuFloorSort.length) {
      skuFloorSort.list = [{
        floor_mark: 'case',
        floor_sort: 1
      },
      {
        floor_mark: 'resource',
        floor_sort: 2
      },
      {
        floor_mark: 'qa',
        floor_sort: 3
      }
      ]
    }
    let defaultSort = [];
    skuFloorSort.list.forEach((item, index) => {
      const obj = {}
      if (item.floor_mark === 'case') {
        obj.name = 'case';
        obj.datas = skuCases.list;
      } else if (item.floor_mark === 'resource') {
        obj.name = 'resource';
        obj.datas = skuResource.list;
      } else {
        obj.name = 'qa';
        obj.datas = detail.detail.qa;
      }
      defaultSort.push(obj)
    })
    // 创建锚点列表
    let anchorList = [];
    detail.detail.content.forEach((item, index) => {
      if (item.anchor_show) {
        let obj = {}
        obj.name = item.anchor_title;
        obj.id = 'content' + index
        anchorList.push(obj);
      }

    })
    defaultSort.forEach((item, index) => {
      if (item.datas.length && item.datas[0].anchor_show) {
        let obj = {}
        obj.name = item.datas[0].anchor_title;
        obj.id = item.name + '0'
        anchorList.push(obj);
      }
    })
    let tokenUrl;
    if (this.app.locals.env == 'prod') {
      tokenUrl = 'https://academy.sgsonline.com.cn';
    } else {
      tokenUrl = 'http://*************';
    }
    const class_info = JSON.parse(detail.detail.class_info);

    const userInfo = await util.getUserInfo(ctx)
    const disablePhone = userInfo.userPhone ? true : false;
    const disableMail = userInfo.userEmail ? true : false;
    let isLogin = userInfo.uId ? true : false;
    if (class_info) {
      await ctx.render('detail_new', {
        captchaAppId,
        uac_busiType: 'sku',
        uac_busiSubType: 'sku-',
        uac_busiCode: detail.detail.id,
        isChatbot,
        isIchatbot,
        isZhiChi,
        userInfo,
        isLogin,
        disablePhone,
        disableMail,
        host: env[this.app.locals.env].ticMall,
        mallPid,
        mallPcode,
        hotKeyword: hotKeyword.list,
        detail: detail.detail,
        tradeNavi: tradeNavi.list,
        serviceNavi: serviceNavi.list,
        informationTypes: informationTypes.list,
        filesType: filesType.list,
        tradeList,
        serviceList,
        tagAdpt: recommends.list.concat(tagAdpt.list),
        skuCases: skuCases.list,
        skuResource: skuResource.list,
        defaultSort,
        anchorList,
        class_infoTime: class_info.time || '',
        class_infoArea: class_info.area || '',
        class_infoDay: class_info.days || '',
        storeUrl: env[this.app.locals.env].storeUrl,
        memberUrl: env[this.app.locals.env].memberUrl,
        portalHost: env[this.app.locals.env].portalUrl,
        clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
        id: id,
        csrf: ctx.csrf,
        uname: '',
        tokenUrl: tokenUrl,
        isLogin,
        url,
        domain: env[this.app.locals.env].domain,
        apihost: env[this.app.locals.env].ENV_API,
        env: env[this.app.locals.env].env,
      })
    } else {
      await ctx.render('detail_new', {
        captchaAppId,
        uac_busiType: 'sku',
        uac_busiSubType: 'sku-',
        uac_busiCode: detail.detail.id,
        isChatbot,
        isIchatbot,
        isZhiChi,
        userInfo,
        isLogin,
        disablePhone,
        disableMail,
        host: env[this.app.locals.env].ticMall,
        mallPid,
        mallPcode,
        hotKeyword: hotKeyword.list,
        detail: detail.detail,
        tradeNavi: tradeNavi.list,
        serviceNavi: serviceNavi.list,
        informationTypes: informationTypes.list,
        filesType: filesType.list,
        tradeList,
        serviceList,
        tagAdpt: recommends.list.concat(tagAdpt.list),
        skuCases: skuCases.list,
        skuResource: skuResource.list,
        defaultSort,
        anchorList,
        storeUrl: env[this.app.locals.env].storeUrl,
        memberUrl: env[this.app.locals.env].memberUrl,
        portalHost: env[this.app.locals.env].portalUrl,
        clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
        id: id,
        csrf: ctx.csrf,
        uname: '',
        isLogin,
        tokenUrl: tokenUrl,
        url,
        domain: env[this.app.locals.env].domain,
        apihost: env[this.app.locals.env].ENV_API,
        env: env[this.app.locals.env].env,
      })
    }
  }

  async newsList() {
    const {
      ctx
    } = this;
    const mobile = await isMobile(ctx);
    const mobileHost = await changeMobileHost(ctx.request.host)
    const params = {
      is_publish: 1,
      title: ctx.request.query.title,
      time: ctx.request.query.time,
      page: ctx.request.query.page,
      type: ctx.request.query.type // 新闻类型
    };
    const validateSQL = await util.filterSQL(params);
    let detail = {
      list: [],
      total: 0,
      page: 0
    };
    if (!validateSQL) detail = await ctx.service.news.getList(params);
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();
    const {
      newsTypeList
    } = await ctx.service.news.getNewsType(); //获取新闻分类列表
    if (mobile) {
      // ctx.redirect(encodeURI(`${mobileHost}/news/list`))
      // return;
      const headers = {
        Location: encodeURI(`${mobileHost}/news/list`)
      }
      ctx.status = 302;
      ctx.set(headers)
    }
    detail.list.forEach(item => {
      item.time = item.time.split(' ')[0];
    });

    let maxPage = Math.ceil(detail.total / 10);
    if (ctx.request.query.page > maxPage) {
      // ctx.redirect('/news');
      const headers = {
        Location: '/news'
      }
      ctx.status = 302;
      ctx.set(headers)
    }
    detail.name = '新闻中心'
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('newsList', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: detail,
      total: detail.total,
      page: detail.page,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      q: ctx.request.query.title,
      time: ctx.request.query.time,
      type: ctx.request.query.type,
      newsTypeList
    })

  }

  async seoList() {
    const {
      ctx
    } = this;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();

    const params = {
      page: ctx.request.query.page
    }
    const validateSQL = await util.filterSQL(params);
    let detail = {
      list: [],
      total: 0,
      page: 0
    };
    if (!validateSQL) detail = await ctx.service.seo.getSeoList(params);


    detail.list.forEach(item => {
      item.time = item.time.split(' ')[0];
    });
    detail.name = '服务推广'
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('seoList', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      detail: detail,
      tradeList,
      serviceList,
      total: detail.total['COUNT(distinct a.id)'] || 0,
      page: detail.page,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
    })
  }

  async newsDetail() {
    const {
      ctx
    } = this;
    const {
      id,
      alias
    } = ctx.params;
    // 防止sql注入
    if (isNaN(Number(id))) return;
    const mobile = await util.isMobile(ctx);
    const mobileHost = await changeMobileHost(ctx.request.host);
    if (mobile) {
      ctx.redirect(encodeURI(`${mobileHost}/news/detail?id=${id}`))
      return;
    }
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();

    const relate = await ctx.service.news.getNewsRelate(id);
    const otherNews = await ctx.service.news.otherNews(id);
    otherNews.list.some(item => {
      item.content = subString(item.content.replace(/<.*?>/g, ""), 80);
    });
    // 相关新闻
    const relatedNews = await ctx.service.news.relatedNews(id);
    const detail = await ctx.service.external.qryNewsDetail({
      id
    });
    // 别名不正确重定向到404页面
    const getDtail = await ctx.service.news.getDetail(id);
    if (detail.resultCode !== '0' || getDtail.detail.alias != alias) {
      // TIC-16897 主站下架/删除页面设置404
      ctx.body = '您访问的页面不存在，请访问https://www.sgsonline.com.cn'
      ctx.status = 404;
      return;
    }

    detail.data.page_title = detail.data.pageTitle
    const userInfo = await util.getUserInfo(ctx)
    const disablePhone = userInfo.userPhone ? true : false;
    const disableMail = userInfo.userEmail ? true : false;
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('news', {
      uac_busiType: 'news',
      uac_busiSubType: `news-${detail.data.catalogId}`,
      uac_busiCode: detail.data.id,
      captchaAppId,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      disablePhone,
      disableMail,
      host: env[this.app.locals.env].ticMall,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: detail.data,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      prevNews: relate.prevItem[0],
      nextNews: relate.nextItem[0],
      otherNews: otherNews.list,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      relatedNews: relatedNews.list
    })
  }

  async caseList() {
    const {
      ctx
    } = this;

    const params = {
      is_publish: 1,
      title: ctx.request.query.title,
      time: ctx.request.query.time,
      page: ctx.request.query.page
    };
    const validateSQL = await util.filterSQL(params);
    let detail = {
      list: [],
      total: 0,
      page: 0
    };
    if (!validateSQL) detail = await ctx.service.case.getList(params);
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();

    detail.list.forEach(item => {
      item.time = item.time.split(' ')[0];
    });
    detail.name = '资讯中心'
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('caseList', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: detail.list,
      total: detail.total,
      page: detail.page,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      q: ctx.request.query.title,
      time: ctx.request.query.time,
    })

  }

  async caseDetail() {
    const {
      ctx
    } = this;
    const {
      id,
      alias
    } = ctx.params;
    // 防止sql注入
    if (isNaN(Number(id))) return;
    const mobile = await isMobile(ctx);
    const mobileHost = await changeMobileHost(ctx.request.host)
    if (mobile) {
      // ctx.redirect(encodeURI(`${mobileHost}/case/detail?id=${id}`))
      // return;
      const headers = {
        Location: encodeURI(`${mobileHost}/case/detail?id=${id}`)
      }
      ctx.status = 302;
      ctx.set(headers)
    }
    const detail = await ctx.service.case.getDetail(id);
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();

    const relate = await ctx.service.case.getCaseRelate(id);
    // const otherCase = await ctx.service.case.otherCase(id, detail.detail.catalog_id);
    const otherCase = await ctx.service.external.casesQryRelate({
      caseId: detail.detail.id,
      pageRow: 10,
      pageNum: 1
    });
    detail.detail.gmt_publish_time = moment(detail.detail.gmt_publish_time).format('YYYY年MM月DD日');
    detail.detail.name = detail.detail.title;
    // otherCase.items.some(item => {
    //   item.content = subString(item.content.replace(/<.*?>/g, ""), 80);
    // });
    const detailMall = await ctx.service.external.qryCaseDetail({
      id
    })
    // 别名不正确重定向到404页面
    if (detailMall.resultCode !== '0' || detail.detail.alias != alias) {
      // ctx.redirect('/404')
      // return;
      const headers = {
        Location: '/404'
      }
      ctx.status = 302;
      ctx.set(headers)
    }

    const userInfo = await util.getUserInfo(ctx)
    const disablePhone = userInfo.userPhone ? true : false;
    const disableMail = userInfo.userEmail ? true : false;
    let isLogin = userInfo.uId ? true : false;
    console.log(detailMall);
    await ctx.render('case', {
      captchaAppId,
      uac_busiType: 'case',
      uac_busiSubType: `case-${detailMall.data.catalogCode}`,
      uac_busiCode: detail.detail.id,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      disablePhone,
      disableMail,
      host: env[this.app.locals.env].ticMall,
      domain: env[this.app.locals.env].domain,
      apihost: env[this.app.locals.env].ENV_API,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: detail.detail,
      detailMall: detailMall.data,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      prevCase: relate.prevItem[0],
      nextCase: relate.nextItem[0],
      otherCase: otherCase.data.items || [],
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      env: env[this.app.locals.env].env,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      recommends: detailMall.data.recommends  || [],
    })

  }

  //catalog // service or industry detail
  async tradeDetail() {
    const {
      ctx
    } = this;
    const mobile = await isMobile(ctx);
    const mobileHost = await changeMobileHost(ctx.request.host)
    const alias = ctx.params.id;

    // 防止sql注入
    function fillter(str) {
      let flag = false;
      if (str.includes('script') || str.includes('sleep') || str.includes('select') || str.includes('insert') || str.includes('update') || str.includes('delete') || str.includes('truncate') || str.includes('src')) {
        flag = true;
      }
      return flag;
    }

    if (fillter(alias.toLowerCase())) return;
    const detail = await ctx.service.catalog.getCatalogInfoByAlias(alias);
    if (detail.info.parent_id === 2 && ctx.request.url.includes('/industry/')) {
      ctx.body = '您访问的页面不存在，请访问https://www.sgsonline.com.cn'
      ctx.status = 404;
      return
    }

    if (detail.info.parent_id === 1 && ctx.request.url.includes('/service/')) {
      ctx.body = '您访问的页面不存在，请访问https://www.sgsonline.com.cn'
      ctx.status = 404;
      return
    }

    if (mobile) {
      if (detail.info.parent_id === 2) {
        // this is service
        // ctx.redirect(encodeURI(`${mobileHost}/service?cataId=${detail.info.id}`))

        const headers = {
          Location: encodeURI(`${mobileHost}/service?cataId=${detail.info.id}`)
        }
        ctx.status = 302;
        ctx.set(headers)
      } else {
        // this is industry
        // ctx.redirect(encodeURI(`${mobileHost}/industry/list?name=${detail.info.name}&alias=${detail.info.alias}&cataId=${detail.info.id}`))

        const headers = {
          Location: encodeURI(`${mobileHost}/industry/list?name=${detail.info.name}&alias=${detail.info.alias}&cataId=${detail.info.id}`)
        }
        ctx.status = 302;
        ctx.set(headers)
      }
      return;
    }
    if (!detail) {
      ctx.status = 500;
      return;
    }
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();

    if (detail.info.description) {
      detail.info.description = detail.info.description.replace(/\n/g, '<br>');
    }

    detail.resource.forEach(item => {
      var ft = item.path.split('.');
      ft = ft[ft.length - 1];
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].indexOf(ft) != -1) {
        item.type = 'img';
      } else if (['doc', 'docx'].indexOf(ft) != -1) {
        item.type = 'word';
      } else if (['xls', 'xlsx'].indexOf(ft) != -1) {
        item.type = 'excel';
      } else if (['ppt', 'pptx'].indexOf(ft) != -1) {
        item.type = 'ppt';
      } else if (['mp4', 'avi'].indexOf(ft) != -1) {
        item.type = 'video';
      } else if (ft == 'pdf') {
        item.type = 'pdf';
      } else if (['zip', '7z', 'rar', 'tar'].indexOf(ft) != -1) {
        item.type = 'zip';
      } else {
        item.type = 'other';
      }
    });

    informationTypes.list.forEach(item => {
      item.list = []
      detail.cases.forEach(caseItem => {
        if (item.id === caseItem.catalog_id) {
          caseItem.gmt_publish_time = moment(caseItem.gmt_publish_time).format('YYYY-MM-DD');
          const m1 = moment(caseItem.gmt_publish_time)
          const m2 = moment()
          const day = m2.diff(m1, 'days')
          if (day < 180) caseItem.isHot = true
          item.list.push(caseItem)
        }
      });
    });
    filesType.list.forEach(item => {
      item.list = []
      detail.resource.forEach(caseItem => {
        if (item.id === caseItem.catalog_id) {
          item.list.push(caseItem)
        }
      });
    });

    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('categoryDetail', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: detail.info,
      banner: detail.banner,
      children: detail.children,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      solution: detail.solution,
      cases: detail.cases,
      resource: detail.resource,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      alias: alias,
      csrf: ctx.csrf,
      url
    });
  }

  async tradeSkus() {
    const {
      ctx
    } = this;

    const alias = ctx.params.id;
    const params = ctx.request.query;
    const name = params.name || '',
      page = params.page || 1;
    let id = params.id;

    // 防止sql注入
    function fillter(str) {
      let flag = false;
      if (str.includes('script') || str.includes('sleep') || str.includes('select') || str.includes('insert') || str.includes('update') || str.includes('delete') || str.includes('truncate') || str.includes('src')) {
        flag = true;
      }
      return flag;
    }

    if (fillter(alias.toLowerCase())) return;
    let detail = {};
    if (alias == 'training') {
      detail = await ctx.service.catalog.getCatalogInfoByAliasTrain(alias);
    } else {
      detail = await ctx.service.catalog.getCatalogInfoByAlias(alias);
    }

    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();

    if (!id || id == '') {
      id = detail.info.id;
    }
    const skuList = await ctx.service.sku.getSkuByCata({
      id: id,
      page: page,
      limit: 1000,
      name: name
    });

    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    if (alias == 'training') {
      await ctx.render('classlist', {
        isChatbot,
        isIchatbot,
        isZhiChi,
        userInfo,
        isLogin,
        host: env[this.app.locals.env].ticMall,
        mallPid,
        mallPcode,
        hotKeyword: hotKeyword.list,
        detail: detail.info,
        banner: detail.banner,
        children: detail.children,
        solution: detail.solution,
        class_info: detail.class_info,
        page: page,
        csrf: ctx.csrf,
        id: id,
        name: name,
        storeUrl: env[this.app.locals.env].storeUrl,
        memberUrl: env[this.app.locals.env].memberUrl,
        portalHost: env[this.app.locals.env].portalUrl,
        clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
        tradeNavi: tradeNavi.list,
        serviceNavi: serviceNavi.list,
        informationTypes: informationTypes.list,
        filesType: filesType.list,
        tradeList,
        serviceList
      });
    } else {
      await ctx.render('serverlist', {
        isChatbot,
        isIchatbot,
        isZhiChi,
        userInfo,
        isLogin,
        host: env[this.app.locals.env].ticMall,
        mallPid,
        mallPcode,
        hotKeyword: hotKeyword.list,
        detail: detail.info,
        banner: detail.banner,
        children: detail.children,
        solution: detail.solution,
        page: page,
        total: skuList.total,
        csrf: ctx.csrf,
        id: id,
        name: name,
        storeUrl: env[this.app.locals.env].storeUrl,
        memberUrl: env[this.app.locals.env].memberUrl,
        portalHost: env[this.app.locals.env].portalUrl,
        clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
        tradeNavi: tradeNavi.list,
        serviceNavi: serviceNavi.list,
        informationTypes: informationTypes.list,
        filesType: filesType.list,
        tradeList,
        serviceList
      });
    }

    /*
await ctx.render('serverlist', {
    detail: detail.info,
    banner: detail.banner,
    children: detail.children,
    tradeNavi: tradeNavi.list,
    serviceNavi: serviceNavi.list,
    informationTypes: informationTypes.list,
    filesType: filesType.list,
    tradeList,
    serviceList,
    solution: detail.solution,
    //skuList: skuList.list,
    page: page,
    csrf: ctx.csrf,
    id: id,
    name: name,
    storeUrl: env[this.app.locals.env].storeUrl,
  memberUrl: env[this.app.locals.env].memberUrl,
});
*/
  }

  // async getCataSkus() {
  //   const {
  //     ctx
  //   } = this;
  //   const params = ctx.request.query;
  //   const id = params.id,
  //     page = params.page || 1,
  //     name = params.name || '',
  //     limit = params.limit || 10;

  //   const param = {
  //     id: id,
  //     page: page,
  //     limit: limit,
  //     name: name
  //   }
  //   let skuList = {}
  //   const validateSQL = await util.filterSQL(ctx.params);
  //   if (!validateSQL) {
  //     skuList = await ctx.service.sku.getSkuByCata(param);
  //   }

  //   ctx.body = {
  //     success: true,
  //     data: skuList.list,
  //     total: skuList.total,
  //     page: skuList.page
  //   };
  // }

  // async getTrainCataSkus() {
  //   const {
  //     ctx
  //   } = this;
  //   const alias = ctx.params.alias;
  //   const params = ctx.request.query;
  //   const page = params.page || 1,
  //     limit = params.limit || 10,
  //     name = params.name || '',
  //     id = params.id,
  //     area = params.classArea,
  //     time = params.classTime,
  //     days = params.classDays,
  //     is_buy = params.is_buy == 'true' ? 1 : '';

  //   const detail = await ctx.service.sku.getSkuByTrainCata({
  //     id: id,
  //     page: page,
  //     limit: limit,
  //     name: name,
  //     area: area,
  //     time: time,
  //     days: days,
  //     is_buy: is_buy
  //   });

  //   ctx.body = {
  //     success: true,
  //     data: detail.list,
  //     page: detail.page,
  //     total: detail.total
  //   }
  // }

  async randomTag() {
    const {
      ctx
    } = this;

    const id = ctx.request.query.id;
    // 防止sql注入
    if (isNaN(Number(id))) return;
    const tagAdpt = await ctx.service.sku.tagAdpt(id, true);
    const recommends = await ctx.service.sku.getRecommend(id);

    let newList = [];
    const ids = []
    tagAdpt.list.forEach(v => {
      if (!ids.includes(v.id)) {
        newList.push(v)
        ids.push(v.id)
      }
    })
    recommends.list.forEach(v => {
      if (!ids.includes(v.id)) {
        newList.push(v)
        ids.push(v.id)
      }
    })
    newList.sort(function () {
      return 0.5 - Math.random()
    });
    ctx.body = {
      code: 0,
      data: newList
    };
  }

  async other() {
    const {
      ctx
    } = this;
    const mobile = await isMobile(ctx);
    const mobileHost = await changeMobileHost(ctx.request.host)
    const alias = ctx.params.alias;

    // 防止sql注入
    function fillter(str) {
      let flag = false;
      if (str.includes('script') || str.includes('sleep') || str.includes('select') || str.includes('insert') || str.includes('update') || str.includes('delete') || str.includes('truncate') || str.includes('src')) {
        flag = true;
      }
      return flag;
    }

    if (fillter(alias.toLowerCase())) return;
    if (alias === 'order-online' || alias === 'order-online-m') {
      ctx.redirect(encodeURI(`${env[this.app.locals.env].portalUrl}/order-online`))
      return;
    }
    if (mobile) {
      const headers = {
        Location: encodeURI(`${mobileHost}/overview?page=${alias}`)
      }
      ctx.status = 302;
      ctx.set(headers)

      // ctx.redirect(encodeURI(`${mobileHost}/overview?page=${alias}`))
      // return;
    }
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();
    const detail = await ctx.service.other.getDetailByAlias(alias);
    if (!detail.detail) {
      ctx.status = 500;
      return;
    }

    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('article', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: detail.detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
    })
  }

  async homepage() {
    const {
      ctx
    } = this;
    const mobile = await util.isMobile(ctx);
    ctx.logger.info('ctx:'+JSON.stringify(ctx))
    ctx.logger.info('ctx.request.host:'+ctx.request.host)
    ctx.logger.info('ctx.headers.host:'+ctx.headers.host)
    ctx.logger.info('ctx.get(\'X-Forwarded-Host\'):'+ctx.get('X-Forwarded-Host'))
    const mobileHost = await changeMobileHost(ctx.request.host)
    if (mobile && ctx.request.path === '/') {
      ctx.redirect(mobileHost, 301)
      return;
    }
    let {
      phoneNum
    } = ctx.service.common.hotLine()
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();
    const detail = await ctx.service.index.getDetail();
    const oiqList = await ctx.service.oiq.getOiqList(ctx.headers)
    let outDetail = '';
    try {
      outDetail = JSON.parse(detail.detail.content);
    } catch (e) {
      outDetail = detail.detail.content;
    }
    let newsTypeList = detail.newsTypeList
    const informationList = await ctx.service.case.getHomeHotList();
    // oiq订单来源标识
    const source = ctx.query.source || '';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    const pageQryHome = await ctx.service.external.pageQryHome({ pageCode: 'Home' }, ctx.headers);
    const homeOnlineOrder = await ctx.service.external.homeOnlineOrder({}, ctx.headers);
    await ctx.render('index', {
      // sentry fix bug TIC-VIEW-10
      pageFloors: (pageQryHome && pageQryHome.data && pageQryHome.data.floors[0]) || {},
      // sentry fix bug TIC-VIEW-12
      pageOnlineOrder: (homeOnlineOrder && homeOnlineOrder.data && homeOnlineOrder.data.items) || [],
      isChatbot,
      isIchatbot,
      isZhiChi,
      source,
      orderNum: Number(userInfo.orderNum.lv3) + Number(userInfo.orderNum.oiq),
      enquiryNum: userInfo.orderNum.enquirynum,
      couponNum: userInfo.orderNum.couponNum,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: outDetail,
      newsTypeList,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      informationList,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      csrf: ctx.csrf,
      phoneNum,
      oiqList
    })
  }


  async hotline() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();
    const hotsku = await ctx.service.search.hotsku();
    let outDetail = {};
    outDetail.name = '客服电话';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('hotline', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0
    })
  }
  
  async quote() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const frontUrl = ctx.host + encodeURI(ctx.originalUrl)
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();
    const hotsku = await ctx.service.search.hotsku();
    let outDetail = {};
    outDetail.name = '留言咨询';
    const source = ctx.query.source || '';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    const spm = ctx.request.query.spm;
    if (spm) {
      let token = await util.getCookie(ctx, 'SSO_TOKEN');
      if (token) token = token + '=='
      const params = {
        token,
        spm,
        tx: ''
      }
      const query = []
      Object.keys(params).forEach(v => {
        query.push(`${v}=${params[v]}`)
      })
      await ctx.service.external.sendSPM(query.join('&'));
    }
    await ctx.render('quote', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      source,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      portalUrl: env[this.app.locals.env].portalUrl + '/quote?frontUrl=' + frontUrl,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0,
      noTab: false,
    })
  }

  async baojia() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const frontUrl = env[this.app.locals.env].url + encodeURI(ctx.originalUrl)

    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    let outDetail = {};
    outDetail.name = '业务咨询';
    const source = ctx.query.source || '';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    const spm = ctx.request.query.spm;
    if (spm) {
      let token = await util.getCookie(ctx, 'SSO_TOKEN');
      if (token) token = token + '=='
      const params = {
        token,
        spm,
        tx: ''
      }
      const query = []
      Object.keys(params).forEach(v => {
        query.push(`${v}=${params[v]}`)
      })
      await ctx.service.external.sendSPM(query.join('&'));
    }
    await ctx.render('quote', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      source,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      portalUrl: env[this.app.locals.env].portalUrl + '/quote?frontUrl=' + frontUrl,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0,
      noTab: true,
    })
  }

  async hlMedical() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const frontUrl = env[this.app.locals.env].url + encodeURI(ctx.originalUrl)

    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    let outDetail = {};
    outDetail.name = '无源医疗器械能力查询系统';
    outDetail.page_description = '输液/注射类、医用导管、呼吸麻醉管路、胰岛素注射笔、无针注射系统、医用小口径接头、医疗器械包装、模拟运输ASTM D4169、CMR/EDCs筛查';
    const source = ctx.query.source || '';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    const spm = ctx.request.query.spm;
    if (spm) {
      let token = await util.getCookie(ctx, 'SSO_TOKEN');
      ctx.logger.error(`---------------------token-----------------`, token);
      if (token) token = token + '=='
      const params = {
        token,
        spm,
        tx: ''
      }
      const query = []
      Object.keys(params).forEach(v => {
        query.push(`${v}=${params[v]}`)
      })
      await ctx.service.external.sendSPM(query.join('&'));
    }
    await ctx.render('hlMedical', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      source,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      portalUrl: env[this.app.locals.env].portalUrl + '/quote?frontUrl=' + frontUrl,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.routerPath,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0,
      noTab: true,
    })
  }

  async contact() {
    const {
      ctx
    } = this;
    ctx.redirect('/404')
  }

  async search() {
    const {
      ctx
    } = this;
    const mobile = await isMobile(ctx);
    const mobileHost = await changeMobileHost(ctx.request.host)
    const params = ctx.request.query;
    params.q = params.q ? params.q.replace(/%/g, '') : ''
    const validateSQL = await util.filterSQL(params);
    if (validateSQL) return false
    if (mobile) {
      // ctx.redirect(encodeURI(`${mobileHost}/search/result?q=${params.q}`))
      // return;
      const headers = {
        Location: encencodeURI(`${mobileHost}/search/result?q=${params.q}`)
      }
      ctx.status = 302;
      ctx.set(headers)
    }
    const goodsRecommend = await ctx.service.external.goodsQryRecommend({ keywords: params.q, pageRow: 1, pageNum: 1 });
    let goodsData = {
      price: 0,
      img: '',
      totalNum: 0
    }
    if (goodsRecommend.data && goodsRecommend.data.items.length) {
      goodsData = {
        price: goodsRecommend.data.items[0].price.includes('起') ? goodsRecommend.data.items[0].price : goodsRecommend.data.items[0].price + ' 起',
        img: goodsRecommend.data.items[0].goodsImage,
        totalNum: goodsRecommend.data.totalNum
      }
    }


    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    tradeNavi.list.forEach(item => {
      item.isCheck = false
    })
    serviceNavi.list.forEach(item => {
      item.isCheck = false
    })
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('search', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      keyword: decodeURIComponent(params.q),
      pageNum: params.pageNum || 1,
      pageRow: params.pageRow || 10,
      industry: params.industry || '',
      service: params.service || '',
      type: params.type || '',
      detail: {
        keyword: decodeURIComponent(params.q),
      },
      hot: hotsku.list,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      csrf: ctx.csrf,
      goodsData
    })
  }

  async searchResult() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;

    await ctx.service.external.addKeywordNum({
      keyword: params.title
    })

    // ctx.body = result;

    let options = {
      url: env[this.app.locals.env].solrUrl + '/qry',
      method: 'POST',
      headers: {
        pid: solrPid,
        timestamp: new Date().getTime(),
      },
      json: true,
      body: {
        title: params.title,
        type: params.type,
        industry: params.industry,
        service: params.service,
        pageNum: params.pageNum,
        pageRow: params.pageRow
      }
    }

    let parr = JSON.stringify(options.body);
    parr += solrPstr;
    parr = crypto.createHash('md5').update(parr).digest("hex");
    options.headers.sign = parr;

    function doRequest() {
      return new Promise(function (resolve, reject) {
        request(options, function (error, res, body) {
          if (!error && res.statusCode == 200) {
            resolve(body);
          } else {
            reject(error);
          }
        });
      });
    }

    async function getResult() {
      let res = await doRequest(options);
      return res;
    }

    let resultData = await getResult();

    for (let item of resultData.data.data) {
      let scata = '';
      if (item.industry) {
        let sarr = item.industry.split(',');
        scata = sarr[sarr.length - 1];
        item.ctype = 'industry';
      } else if (item.service) {
        let sarr = item.service.split(',');
        scata = sarr[sarr.length - 1];
        item.ctype = 'service';
      } else {
        item.ctype = '';
      }

      if (scata && item.ctype) {
        let rt = await ctx.service.catalog.getInfoByName(scata, item.ctype);
        if (rt.info && rt.info.alias) {
          item.calias = rt.info.alias;
        } else {
          item.calias = '';
        }

      } else {
        item.calias = '';
      }

    }

    ctx.body = resultData;
  }

  async ticketpost() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const phone = params.phone,
      email = params.email;

    let check = false;
    // const emailReg = /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
    var emailReg = /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/;
    if (email == '' || !emailReg.test(email)) {
      check = true;
    }

    if (phone == '' || !/^1[2|3|4|5|6|7|8|9]\d{9}$/.test(phone)) {
      check = true;
    }

    if (check) {
      ctx.body = {
        fail: true,
        data: '提交数据校验错误.'
      }
      return;
    }

    const customerIp = getIP(ctx.request);
    params.ip = customerIp;
    params.serviceId = params.service
    params.tradeId = params.trade

    // 英文转中文
    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();
    tradeNavi.list.forEach(item => {
      if (params.tradeName === item.en_name) {
        params.tradeName = item.name
      }
    })
    serviceNavi.list.forEach(item => {
      if (params.serviceName === item.en_name) {
        params.serviceName = item.name
      }
    })

    const result = await ctx.service.ticket.add(params);

    if (result.success) {
      ctx.body = {
        success: true,
        data: null
      }
    } else {
      ctx.body = {
        success: false,
        data: result.resultMsg
      }
    }

    // const id = result.id;

    // 创建客户账号

    // let needSend = true;
    // let userOptions = {
    //     url: 'https://sgs.kf5.com/apiv2/users.json',
    //     method: 'POST',
    //     headers: {
    //         "Authorization": "Basic " + new Buffer("<EMAIL>/token:6fc8f4ae566b2590a2320601560066").toString("base64")
    //     },
    //     json: true,
    //     body: {
    //         "user": {
    //             "name": params.customer,
    //             "email": params.email,
    //             "phone": params.phone
    //         }
    //     }
    // }

    // let sendUser, sendBy = 'email';
    // request(userOptions, function (error, response, body) {

    //     if (error || (response && response.statusCode > 400)) {
    //         let result_text = [];
    //         if (response && response.statusCode) {
    //             result_text.push('CODE:' + response.statusCode);
    //         }

    //         if (error) {
    //             result_text.push('ERROR:' + JSON.stringify(error));
    //         }

    //         if (body) {
    //             result_text.push('BODY:' + JSON.stringify(body));
    //         }

    //         if (body && (response.statusCode == 409 || body.message.indexOf('已经存在') >= 0)) {
    //             // 该用户已经存在
    //             const {
    //                 user
    //             } = body
    //             if (user.email === params.email) {
    //                 sendUser = params.email;
    //                 sendBy = 'email';
    //             } else if (user.phone === params.phone) {
    //                 sendUser = params.phone;
    //                 sendBy = 'phone';
    //             }
    //         } else {
    //             ctx.logger.error(moment(new Date()).format('YYYY-MM-DD HH:mm:ss') + ': ' + result_text);
    //             ctx.service.ticket.update({
    //                 id: id,
    //                 result_code: 0,
    //                 result_text: result_text.join(';'),
    //                 sendBy: sendBy
    //             });
    //             needSend = false;
    //         }
    //     } else {
    //         sendUser = params.email;
    //     }

    //     if (!needSend) {
    //         return;
    //     }

    //     // 同步至工单kf5.com
    //     const api = {
    //         domain: 'sgs.kf5.com/apiv2/requests.json',
    //         token: '6fc8f4ae566b2590a2320601560066',
    //         // email: '<EMAIL>',
    //         email: sendUser
    //     }
    //     const auth = "Basic " + new Buffer(api.email + "/token:" + api.token).toString("base64");
    //     let options = {
    //         url: 'https://' + api.domain,
    //         method: 'POST',
    //         headers: {
    //             "Authorization": auth
    //         },
    //         json: true,
    //         body: {
    //             "request": {
    //                 "title": "来自" + params.customer + "的" + params.type,
    //                 "comment": {
    //                     "content": params.content,
    //                 },
    //                 "custom_fields": [{
    //                         "name": "field_1006481",
    //                         "value": params.type
    //                     }, //类型
    //                     {
    //                         "name": "field_1005362",
    //                         "value": params.serviceName
    //                     }, //服务
    //                     {
    //                         "name": "field_1005361",
    //                         "value": params.tradeName
    //                     }, //行业
    //                     // {"name":"field_1006438","value":params.provice},//省份
    //                     {
    //                         "name": "field_1006482",
    //                         "value": params.provice
    //                     }, //省份
    //                     {
    //                         "name": "field_1006486",
    //                         "value": params.company
    //                     }, //企业
    //                     {
    //                         "name": "field_1005365",
    //                         "value": params.email
    //                     }, // 邮箱
    //                     {
    //                         "name": "field_1006533",
    //                         "value": params.phone
    //                     }, // 电话
    //                     {
    //                         "name": "field_1007098",
    //                         "value": customerIp
    //                     }, //ip
    //                     {
    //                         "name": "field_1007155",
    //                         "value": params.city
    //                     }, //城市
    //                     {
    //                         "name": "field_1007196",
    //                         "value": params.os_type == 'mobile' ? '移动端' : 'PC'
    //                     } //来源
    //                 ],
    //                 // requester: {
    //                 //     "email": params.email,
    //                 //     "phone": params.phone,
    //                 //     "name": params.customer,
    //                 // },
    //             }
    //         }
    //     }

    //     const {
    //         app
    //     } = this;
    //     request(options, function (error, response, body) {
    //         if (error || (response && response.statusCode > 400)) {
    //             let result_text = [];
    //             if (response && response.statusCode) {
    //                 result_text.push('CODE:' + response.statusCode);
    //             }

    //             if (error) {
    //                 result_text.push('ERROR:' + JSON.stringify(error));
    //             }

    //             if (body) {
    //                 result_text.push('BODY:' + JSON.stringify(body));
    //             }

    //             ctx.logger.error(moment(new Date()).format('YYYY-MM-DD HH:mm:ss') + ': ' + result_text)
    //             ctx.service.ticket.update({
    //                 id: id,
    //                 result_code: 0,
    //                 result_text: result_text.join(';'),
    //                 sendBy: sendBy
    //             });
    //             return;
    //         }

    //         ctx.service.ticket.update({
    //             id: id,
    //             result_code: 1,
    //             result_text: JSON.stringify(body),
    //             sendBy: sendBy
    //         });
    //     });
    // });
  }

  async setPlatform() {
    const {
      ctx
    } = this;

    ctx.cookies.set('platform', 'pc', {
      maxAge: 900000000
    });

    ctx.redirect(ctx.session.MoblileUrl);
  }

  async clickrecord() {
    const {
      ctx
    } = this;
    const url = ctx.request.query.url;
    // const time = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
    const customerIp = getIP(ctx.request);
    const type = 1;
    await ctx.service.record.new({
      url: url,
      ip: customerIp,
      type: type
    })
    ctx.body = 'done';
  }

  async success() {
    const {
      ctx
    } = this;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    const { type, account } = ctx.query;
    await ctx.render('success', {
      type,
      account,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      hot: hotsku.list,
      detail: {
        title: '业务咨询提交成功'
      },
      csrf: ctx.csrf,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
    })
  }

  async successRecord() {
    const {
      ctx
    } = this;
    const params = ctx.request.query;
    const customerIp = getIP(ctx.request);

    const result = await ctx.service.record.successRecord({
      url: params.url,
      ip: customerIp
    });

    ctx.body = {
      code: 0,
      data: {}
    }
  }

  async getCode() {
    const {
      ctx,
      app
    } = this;
    const phone = ctx.request.body.phone;

    let apiUrl, appId, elpid, elpcode;
    let uname = await getCookie(ctx, 'SGS_USER_ACCOUNT');

    if (app.config.env == 'prod') {
      apiUrl = 'https://gate.sgsonline.com.cn/ticSso/business/api.v1.sso/UserAuthorizeAction/getCode';
      appId = '********';
      elpid = 'pid.bbc';
      elpcode = 'aq2s2ASdsqoa9U80';
    } else {
      apiUrl = 'https://gateuat.sgsonline.com.cn/ticSso/business/api.v1.sso/UserAuthorizeAction/getCode';
      appId = '********';
      elpid = 'pid.bbc';
      elpcode = 'aq2s2ASdsqoa9U80';
    }

    const timestamp = (new Date().getTime()).toString();

    let options = {
      url: apiUrl,
      method: 'POST',
      headers: {
        pid: elpid,
        timestamp: timestamp,
      },
      json: true,
      body: {
        userPhone: uname,
        appId: appId
      }
    }

    let parr = JSON.stringify(options.body);

    let elPstr = crypto.createHash('md5').update((elpid + elpcode).toUpperCase()).digest("hex");
    parr += elPstr.toUpperCase();
    parr += timestamp;
    parr = crypto.createHash('md5').update(parr).digest("hex");
    options.headers.sign = parr;

    function doRequest() {
      return new Promise(function (resolve, reject) {
        request(options, function (error, res, body) {
          if (!error && res.statusCode == 200) {
            resolve(body);
          } else {
            reject(error);
          }
        });
      });
    }

    async function getResult() {
      let res = await doRequest(options);
      return res;
    }

    let resultData = await getResult();

    ctx.body = resultData;
  }

  async order() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    let outDetail = {};
    outDetail.name = '实验室测试订单查询';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('order', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0
    })
  }

  // 报告真伪
  async DocCheck() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    let outDetail = {};
    const tabIndex = ctx.request.url.includes('/certified-client-directory') ? 1 : 0
    outDetail.name = tabIndex ? '体系认证报告查验' : 'SGS报告真伪验证';
    outDetail.page_keywords = '';
    outDetail.page_description = '在线快速验证：针对PDF数字签章报告，或带有验真二维码的报告，一次提交验一份报告，在线即时反馈。 人工查验反馈：针对多份报告，或无法在线快速查验的报告，将提交人工查验';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('DocCheck', {
      tabIndex,
      captchaAppId,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      pid: 'pid.member',
      pcode: 'CGz2RFHATB3XAsvg',
      host: env[this.app.locals.env].ticMall,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0,
      domain: env[this.app.locals.env].domain,
      env: env[this.app.locals.env].env
    })
  }

  // 报告真伪
  async DocCheckEn() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    let outDetail = {};
    const tabIndex = ctx.request.url.includes('/certified-client-directory') ? 1 : 0
    outDetail.name = tabIndex ? '体系认证报告查验' : 'SGS报告真伪验证';
    outDetail.page_keywords = '';
    outDetail.page_description = '在线快速验证：针对PDF数字签章报告，或带有验真二维码的报告，一次提交验一份报告，在线即时反馈。 人工查验反馈：针对多份报告，或无法在线快速查验的报告，将提交人工查验';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('DocCheckEn', {
      tabIndex,
      captchaAppId,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      pid: 'pid.member',
      pcode: 'CGz2RFHATB3XAsvg',
      host: env[this.app.locals.env].ticMall,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0,
      domain: env[this.app.locals.env].domain,
      env: env[this.app.locals.env].env
    })
  }

  // 核心供应商
  async coreSupplier() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    const detail = await ctx.service.external.getCoreSupplier({ pageCode: 'U8e87Bfu' });
    let outDetail = {};
    outDetail.name = detail.data.basic.pageTitle || 'SGS核心合作供应商';
    outDetail.page_keywords = detail.data.basic.pageKeywords;
    outDetail.page_description = detail.data.basic.pageDescription;
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('core_supplier', {
      // coreSupplier: detail.detail.content,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      host: env[this.app.locals.env].ticMall,
      hotKeyword: hotKeyword.list,
      pid: 'pid.member',
      pcode: 'CGz2RFHATB3XAsvg',
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      coreSupplier: env[this.app.locals.env].portalUrl + '/coreSupplier',
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0
    })
  }

  // 班组长活动
  async banZuZhang() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    let tokenUrl;
    if (this.app.locals.env == 'prod') {
      tokenUrl = 'https://academy.sgsonline.com.cn';
    } else {
      tokenUrl = 'http://*************';
    }
    let outDetail = {};
    outDetail.name = '杰出班组长培养系列课程';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('promotion/banZuZhang', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      hotKeyword,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      tokenUrl,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0
    })
  }

  // 高分子活动
  async gaoFenZi() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    let outDetail = {};
    outDetail.name = '成分分析';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('promotion/gaoFenZi', {
      captchaAppId,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0
    })
  }

  // 汽车产业链活动
  async qichechanye() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();
    let outDetail = {
      name: 'IATF16949汽车行业质量管理体系审核认证与培训-SGS认证',
      page_keywords: 'IATF16949,iso26262,vda6.3',
      page_description: 'SGS致力于向汽车行业提供贯穿全价值链的质量保障服务，从零部件品质到整车安全，覆盖汽车全产业链各个细分领域；SGS是少数几家被国内汽车主机厂都认可的第三方认证机构之一，拥有100多名IATF16949审核员。'
    };
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('promotion/qichechanye', {
      captchaAppId,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0
    })
  }

  // ISO 9001质量管理体系
  async iso9001() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();
    let detail = {
      name: 'ISO 9001质量管理体系'
    };
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('promotion/iso9001', {
      captchaAppId,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0
    })
  }

  // 检测认证中心
  async jiance() {
    const {
      ctx
    } = this;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();
    const sku = await ctx.service.sku.querySkuById({ ids: solutionIds.join(',') });
    const skuList = sku.list
    const skuListById = {}
    skuList.forEach(v => {
      skuListById[v.id] = v
    })
    const sortSkuList = []
    solutionIds.forEach(v => {
      sortSkuList.push(skuListById[v] || {})
    })
    const solutionList = []
    if (sortSkuList && sortSkuList.length === 24) {
      solutionList[0] = sortSkuList.slice(0, 4)
      solutionList[1] = sortSkuList.slice(4, 8)
      solutionList[2] = sortSkuList.slice(8, 12)
      solutionList[3] = sortSkuList.slice(12, 16)
      solutionList[4] = sortSkuList.slice(16, 20)
      solutionList[5] = sortSkuList.slice(20)
    }
    let outDetail = {
      name: '国内第三方检测中心-产品检测认证机构-质量认证-SGS中国',
      page_keywords: '第三方检测认证中心',
      page_description: 'SGS是国际公认的第三方权威检测认证机构。 国内30年检测认证经验，提供工业材料、家居百货、农产食品、纺织服装、电子电气、汽车、日化、石化等产品检测、测试、认证等服务，专业并快速提供检测报告认证报告'
    };
    const userInfo = await util.getUserInfo(ctx)
    const disablePhone = userInfo.userPhone ? true : false;
    const disableMail = userInfo.userEmail ? true : false;
    let isLogin = userInfo.uId ? true : false;
    // 标识是原有的检测认证中心还是新的SGS TUV SAAR
    const isTest = ctx.routerPath === '/Testing-and-Certification-Center' ? true : false;
    outDetail.isTestingCertification = true
    await ctx.render('promotion/jiance', {
      isTestingCertification: true,
      phoneNum,
      captchaAppId,
      isTest,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      disablePhone,
      disableMail,
      mallPid,
      mallPcode,
      host: env[this.app.locals.env].ticMall,
      detail: outDetail,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      csrf: ctx.csrf,
      fuwuList,
      bannerName: 'banner-A.jpg',
      navList,
      hotServiceList,
      solutionList,
      newsList,
    })
  }

  // 检测认证中心-b页面
  async jianceB() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();
    let outDetail = {
      name: '检测认证中心'
    };
    const userInfo = await util.getUserInfo(ctx)
    const disablePhone = userInfo.userPhone ? true : false;
    const disableMail = userInfo.userEmail ? true : false;
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('promotion/jiance', {
      captchaAppId,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      disablePhone,
      disableMail,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0,
      fuwuList,
      bannerName: 'banner-B.jpg'
    })
  }

  // lab实验室
  async labPage() {
    const {
      ctx
    } = this;
    const {
      alias
    } = ctx.params;
    const {
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();
    let labDetail = {}
    const oiqList = await ctx.service.oiq.getOiqList(ctx.headers)
    const validateSQL = await util.filterSQL(ctx.params);
    if (!validateSQL) {
      labDetail = await ctx.service.lab.queryLabByAlais({
        alias
      })
    } else {
      return
    }
    if (labDetail.detail.is_delete) {
      // ctx.redirect('/')
      const headers = {
        Location: '/'
      }
      ctx.status = 302;
      ctx.set(headers)
    }
    /*  每4个分为一组 */
    const solutionList = JSON.parse(labDetail.detail.content).floors.filter(v => {
      return v.type == 3
    })[0]
    const solution = []
    for (var i = 0, len = solutionList.list.length; i < len; i += 4) {
      solution.push(solutionList.list.slice(i, i + 4));
    }
    /*  每3个分为一组 */
    const caseList = JSON.parse(labDetail.detail.content).floors.filter(v => {
      return v.type == 8
    })[0]
    const cases = [];
    for (var i = 0, len = caseList.list.length; i < len; i += 3) {
      cases.push(caseList.list.slice(i, i + 3));
    }
    let oiqCategorys = JSON.parse(labDetail.detail.content).oiqCategorys
    if (oiqCategorys && oiqCategorys.length) {
      oiqCategorys.forEach(v => {
        v.categoryValuesStr = v.categoryValues.join(',')
      })
    }
    let outDetail = {
      name: JSON.parse(labDetail.detail.seo).title,
      page_keywords: JSON.parse(labDetail.detail.seo).keywords,
      page_description: JSON.parse(labDetail.detail.seo).description,
    };
    const userInfo = await util.getUserInfo(ctx)
    const disablePhone = userInfo.userPhone ? true : false;
    const disableMail = userInfo.userEmail ? true : false;
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('promotion/lab', {
      captchaAppId,
      oiqList,
      labDetail: labDetail.detail || {},
      oiqCategorys,
      cases: JSON.stringify(cases),
      solution: JSON.stringify(solution),
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      disablePhone,
      disableMail,
      mallPid,
      mallPcode,
      host: env[this.app.locals.env].ticMall,
      detail: outDetail,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      csrf: ctx.csrf,
      fuwuList,
      bannerName: 'banner-A.jpg',
      navList,
      hotServiceList,
      // solutionList,
      newsList,
    })
  }

  // NGO 表单
  async NGOform() {
    const {
      ctx
    } = this;
    const {
      orderId,
      tab
    } = ctx.request.query
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();
    let host = '',
      host1 = '';
    if (this.app.locals.env == 'prod') {
      host = 'https://gate.sgsonline.com.cn';
      host1 = 'http://**************:8090/api/v1';
    } else {
      host = 'https://gateuat.sgsonline.com.cn';
      host1 = 'https://ticsvcuat.sgsonline.com.cn/api/v1';
    }
    let detail = {
      name: 'NGO 表单'
    };
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('event/NGOform', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      orderId,
      isLogin,
      mallPid,
      mallPcode,
      pid: 'pid.member',
      pcode: 'CGz2RFHATB3XAsvg',
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      host1,
      detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0
    })
  }

  // NGO 表单
  async NGOformQwasdTest() {
    const {
      ctx
    } = this;
    const {
      orderId,
      tab
    } = ctx.request.query
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();
    let host = '',
      host1 = '';
    if (this.app.locals.env == 'prod') {
      host = 'https://gate.sgsonline.com.cn';
      host1 = 'http://**************:8090/api/v1';
    } else {
      host = 'https://gateuat.sgsonline.com.cn';
      host1 = 'https://ticsvcuat.sgsonline.com.cn/api/v1';
    }
    let detail = {
      name: 'NGO 表单'
    };
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('event/NGOformQwasdTest', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      orderId,
      isLogin,
      mallPid,
      mallPcode,
      pid: 'pid.member',
      pcode: 'CGz2RFHATB3XAsvg',
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      host1,
      detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0
    })
  }

  // 资料下载
  async files() {
    const {
      ctx
    } = this;
    const params = {
      type: '',
      service: "",
      trade: "",
      title: "",
      page: 1,
      time: '',
      is_publish: 1
    }
    const filesList = await ctx.service.resource.getList(params);
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    let host = '',
      host1 = '';
    if (this.app.locals.env == 'prod') {
      host = 'https://gate.sgsonline.com.cn';
      host1 = 'http://**************:8090/api/v1';
    } else {
      host = 'https://gateuat.sgsonline.com.cn';
      host1 = 'https://ticsvcuat.sgsonline.com.cn/api/v1';
    }
    let detail = {
      name: '资料下载'
    };
    const userInfo = await util.getUserInfo(ctx)
    const disablePhone = userInfo.userPhone ? true : false;
    const disableMail = userInfo.userEmail ? true : false;
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('files', {
      disablePhone,
      disableMail,
      captchaAppId,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      userInfo,
      mallPid,
      mallPcode,
      pid: 'pid.member',
      pcode: 'CGz2RFHATB3XAsvg',
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      domain: env[this.app.locals.env].domain,
      apihost: env[this.app.locals.env].ENV_API,
      host1,
      detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      filesList: filesList.list,
      total: Math.ceil(filesList.total / 10),
      page: filesList.page,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      env: env[this.app.locals.env].env,
      url,
      hot: hotsku.list,
      csrf: ctx.csrf,
      time: ctx.request.query.time,
    })
  }

  async getFilePath() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const detail = await ctx.service.resource.download(params);
    const {
      fileType
    } = await ctx.service.resource.getFileType(detail.path.catalog_id);

    // const SGS_INFO = await getCookie(ctx, 'SGS_INFO');
    // if (SGS_INFO) {
    //   const str = JSON.parse(new Buffer(unescape(SGS_INFO), 'base64').toString())
    //   params.userName = str.user_nick
    //   params.userPhone = str.mobile
    //   params.userEmail = str.email
    // } else {
    //   params.userName = ''
    //   params.userPhone = ''
    //   params.userEmail = ''
    // }
    params.userName = ''
    params.userPhone = ''
    params.userEmail = ''
    params.fileName = detail.path.title
    params.fileType = fileType

    await ctx.service.external.addDownloadNum(params);
    ctx.body = {
      success: true,
      path: detail.path.path
    };
  }

  async getFileList() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    let filesList = []
    const validateSQL = await util.filterSQL(params);
    if (!validateSQL) {
      filesList = await ctx.service.resource.getList(params);
    } else {
      filesList = {
        resultMsg: '系统错误'
      }
    }

    ctx.body = filesList;
  }

  // 咨询中心
  async information() {
    const {
      ctx
    } = this;
    const {
      type
    } = ctx.request.query
    // 防止sql注入
    if (isNaN(Number(type)) && type) return;
    const params = {
      cid: type,
      cataId: "",
      title: "",
      pageRow: 10,
      pageNum: 1,
      gmtPublishTime: '',
    }
    const informationList = await ctx.service.external.qryCaseList(params);
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();
    let host = '',
      host1 = '';
    if (this.app.locals.env == 'prod') {
      host = 'https://gate.sgsonline.com.cn';
      host1 = 'http://**************:8090/api/v1';
    } else {
      host = 'https://gateuat.sgsonline.com.cn';
      host1 = 'https://ticsvcuat.sgsonline.com.cn/api/v1';
    }
    let detail = {
      name: '资讯中心'
    };
    const userInfo = await util.getUserInfo(ctx)
    const disablePhone = userInfo.userPhone ? true : false;
    const disableMail = userInfo.userEmail ? true : false;
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('information', {
      captchaAppId,
      disablePhone,
      disableMail,
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      isLogin,
      mallPid,
      mallPcode,
      pid: 'pid.member',
      pcode: 'CGz2RFHATB3XAsvg',
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      host1,
      detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      informationList: informationList.data && informationList.data.items,
      total: informationList.data && informationList.data.totalNum,
      page: informationList.data && informationList.data.pageNo,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      time: ctx.request.query.time,
      type: Number(type) || 0,
      env: env[this.app.locals.env].env,
    })
  }

  async getInformationList() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;
    const informationList = await ctx.service.information.getList(params);
    ctx.body = informationList;
  }

  // 资讯详情
  async informationDetail() {
    const {
      ctx
    } = this;
    // const mobile = await isMobile(ctx);
    // const mobileHost = await changeMobileHost(ctx.request.host)
    const id = ctx.params.id;
    // 防止sql注入
    if (isNaN(Number(id))) return;
    const detail = await ctx.service.news.getDetail(id);
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();
    if (detail.detail.is_publish == 0) {
      // ctx.body = '很抱歉，您正在查看的内容在调整中，<a href="/">去首页</a>查找你需要的服务';
      ctx.status = 412;
      return;
    }

    const relate = await ctx.service.news.getNewsRelate(id);
    const otherNews = await ctx.service.news.otherNews(id);
    // 相关新闻
    const relatedNews = await ctx.service.news.relatedNews(id);
    detail.detail.gmt_publish_time = moment(detail.detail.gmt_publish_time).format('YYYY年MM月DD日');
    detail.detail.catalog_name = await ctx.service.catalog.getCataName(detail.detail.catalog_id);
    detail.detail.name = detail.detail.title;

    otherNews.list.some(item => {
      item.content = subString(item.content.replace(/<.*?>/g, ""), 80);
    });
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('information_detail', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      host: env[this.app.locals.env].ticMall,
      mallPid,
      mallPcode,
      hotKeyword: hotKeyword.list,
      detail: detail.detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      prevNews: relate.prevItem[0],
      nextNews: relate.nextItem[0],
      otherNews: otherNews.list,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      relatedNews: relatedNews.list,
      hot: hotsku.list,
    })
  }

  // 404页面
  async page404() {
    const {
      ctx
    } = this;

    const tokenUrl = await util.getTokenUrl(this.app.locals.env);
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();
    let detail = {
      name: '错误页面'
    };
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('404', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      hotKeyword,
      hotKeyword: hotKeyword.list,
      host: env[this.app.locals.env].ticMall,
      tokenUrl,
      detail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      csrf: ctx.csrf,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
    })
  }

  /* TIC-5482 历史错误页面重定向到404 */
  async redirect404() {
    const { ctx } = this
    /* TIC-6554 历史错误页面重定向到404 */
    ctx.body = '您访问的页面不存在，请访问https://www.sgsonline.com.cn'
    ctx.status = 404;
    // ctx.redirect('/404', 404)
  }

  // 网站地图
  async sitemap() {
    const {
      ctx
    } = this;

    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
    } = await ctx.service.common.navigation();
    const hotsku = await ctx.service.search.hotsku();
    const sitemap = [{
      name: 'service',
      title: '我们的服务',
      list: []
    },
    {
      name: 'trade',
      title: '行业解决方案',
      list: []
    },
    {
      title: '特色解决方案',
      list: [{
        subTitle: 'SGS独立慧鉴认证标识服务',
        link: '/sku/product/90'
      },
      {
        subTitle: '室内空气甲醛检测服务',
        link: '/sku/product/88'
      },
      {
        subTitle: '绿色产品服务',
        link: '/sku/product/89'
      },
      {
        subTitle: '生活饮用水金属指标检测',
        link: '/sku/product/813'
      },
      {
        subTitle: 'SGS实验室建设技术咨询服务',
        link: '/sku/product/1026'
      },
      {
        subTitle: '民宿认证',
        link: '/sku/product/28'
      },
      {
        subTitle: '通测云实验室信息管理系统（LIMS）',
        link: '/sku/product/901059'
      },
      {
        subTitle: '成分分析',
        link: '/promotion/gaofenzi'
      },
      {
        subTitle: 'IATF16949汽车行业质量管理体系审核认证与培训-SGS认证',
        link: '/promotion/qichechanye'
      },
      {
        subTitle: 'ISO 9001质量管理体系',
        link: '/promotion/iso9001'
      },
      {
        subTitle: '疫情防护用品检测认证',
        link: '/promotion/fangyi'
      },
      {
        subTitle: 'SGS在线询价-测试方案定制',
        link: '/oiq'
      }
      ]
    },
    {
      title: '新闻，媒体和资料库',
      list: [{
        subTitle: '新闻中心',
        link: '/news'
      },
      {
        subTitle: '资讯中心',
        link: '/information'
      },
      {
        subTitle: '资料下载',
        link: '/files'
      }
      ]
    },
    {
      title: '联系我们',
      list: [{
        subTitle: '业务咨询',
        link: '/quote'
      },
      {
        subTitle: '客服电话',
        link: '/hotline'
      },
      {
        subTitle: '服务网点',
        link: '/locatin'
      },
      {
        subTitle: 'Contact Us',
        link: '/ticket/en'
      },
      {
        subTitle: '关于SGS',
        link: '/overview/aboutSGS'
      },
      {
        subTitle: '认可资质',
        link: 'https://www.sgsonline.com.cn/overview/qualifications'
      },
      {
        subTitle: '新手指南',
        link: '/overview/process'
      },
      {
        subTitle: '订购流程',
        link: '/overview/process'
      },
      {
        subTitle: '服务条款',
        link: 'https://www.sgsonline.com.cn/overview/terms-and-conditions'
      },
      {
        subTitle: '核心供应商',
        link: 'https://www.sgsonline.com.cn/coreSupplier'
      }
      ]
    },
    {
      title: '服务和工具',
      list: [{
        subTitle: '订单报告进度查询',
        link: '/order'
      },
      {
        subTitle: '报告真伪',
        link: '/DocCheck'
      },
      {
        subTitle: '服务推广',
        link: '/seoList'
      },
      {
        subTitle: '农残快速查询',
        link: '/foodsafeguard'
      }
      ]
    },
    {
      title: '热门服务',
      list: [
        { "subTitle": "机械产品认证", "link": "/sku/product/890" },
        { "subTitle": "锅炉压力容器认证 ", "link": "/sku/product/889" },
        { "subTitle": "相控阵超声检测（PAUT） ", "link": "/sku/product/305" },
        { "subTitle": "门窗幕墙检测", "link": "/sku/product/140" },
        { "subTitle": "高分子材料成分测试 ", "link": "/sku/product/104" },
        { "subTitle": "金属材料测试 ", "link": "/sku/product/106" },
        { "subTitle": "建筑材料测试 ", "link": "/sku/product/139" },
        { "subTitle": "工业产品检验 ", "link": "/sku/product/917" },
        { "subTitle": "材料及构件声学检测 ", "link": "/sku/product/901248" },
        { "subTitle": "涂料 油墨 胶粘剂 清洗剂 VOC新国标检测服务 ", "link": "/sku/product/901157" },
        { "subTitle": "高分子材料测试 ", "link": "/sku/product/141" },
        { "subTitle": "金属材料失效分析 ", "link": "/sku/product/109" },
        { "subTitle": "可降解材料降解、成分分析测试及降解认证 ", "link": "/sku/product/901167" },
        { "subTitle": "金属材料成分分析 ", "link": "/sku/product/193" },
        { "subTitle": "高分子材料失效分析", "link": "/sku/product/103" },
        { "subTitle": "金属材料尺寸测量", "link": "/sku/product/188" },
        { "subTitle": "涂料和油墨测试", "link": "/sku/product/286" },
        { "subTitle": "材料化学环保测试", "link": "/sku/product/216" },
        { "subTitle": "金属材料金相分析", "link": "/sku/product/194" },
        { "subTitle": "高分子材料物理性能测试", "link": "/sku/product/287" },
        { "subTitle": "塑料材料测试", "link": "/sku/product/285" },
        { "subTitle": "安全玻璃及制品检测", "link": "/sku/product/312" },
        { "subTitle": "木塑产品测试服务", "link": "/sku/product/323" },
        { "subTitle": "包装材料测试", "link": "/sku/product/271" },
        { "subTitle": "橡胶材料测试", "link": "/sku/product/212" },
        { "subTitle": "电线线缆测试", "link": "/sku/product/210" },
        { "subTitle": "脚手架系统及附件检测", "link": "/sku/product/114" },
        { "subTitle": "五金建筑材料检测", "link": "/sku/product/311" },
        { "subTitle": "弹性地材检测", "link": "/sku/product/315" },
        { "subTitle": "强化复合地材检测", "link": "/sku/product/325" },
        { "subTitle": "涂料测试", "link": "/sku/product/115" },
        { "subTitle": "金属材料力学性能测试", "link": "/sku/product/195" },
        { "subTitle": "发泡材料测试", "link": "/sku/product/288" },
        { "subTitle": "实木地板测试", "link": "/sku/product/333" },
        { "subTitle": "胶粘剂测试", "link": "/sku/product/281" },
        { "subTitle": "高分子材料老化测试", "link": "/sku/product/116" },
        { "subTitle": "复合材料类测试", "link": "/sku/product/273" },
        { "subTitle": "可靠性测试", "link": "/sku/product/283" },
        { "subTitle": "管道管件类产品检测", "link": "/sku/product/319" },
        { "subTitle": "墙纸测试", "link": "/sku/product/327" },
        { "subTitle": "高分子材料热性能测试", "link": "/sku/product/284" },
        { "subTitle": "口罩原材料PP熔喷料测试", "link": "/sku/product/901078" },
        { "subTitle": "管材产品测试", "link": "/sku/product/274" },
        { "subTitle": "建材生物化学检测", "link": "/sku/product/320" },
        { "subTitle": "燃烧测试", "link": "/sku/product/289" },
        { "subTitle": "化工品测试", "link": "/sku/product/276" },
        { "subTitle": "五金配件测试", "link": "/sku/product/901280" },
        { "subTitle": "IP防护等级测试", "link": "/sku/product/270" },
        { "subTitle": "人造板测试", "link": "/sku/product/329" },
        { "subTitle": "环境测试", "link": "/sku/product/278" },
      ]
    },
    ];

    sitemap.forEach(sitemapItem => {
      tradeNavi.list.forEach(navigationItem => {
        if (sitemapItem.name && sitemapItem.name === 'trade') {
          let item = {
            subTitle: navigationItem.name,
            link: `/industry/${navigationItem.alias}`
          }
          sitemapItem.list.push(item);
        }
      })
      serviceNavi.list.forEach(navigationItem => {
        if (sitemapItem.name && sitemapItem.name === 'service') {
          let item = {
            subTitle: navigationItem.name,
            link: `/service/${navigationItem.alias}`
          }
          sitemapItem.list.push(item);
        }
      })
    })

    let detail = {
      name: '网站地图',
      sitemap
    };
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('sitemap', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      tradeList, // header头
      serviceList, // header头
      host: env[this.app.locals.env].host,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl, // header头
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hotKeyword: hotKeyword.list, // 热搜词
      tradeNavi: tradeNavi.list, // 菜单
      serviceNavi: serviceNavi.list, // 菜单
      informationTypes: informationTypes.list, // 菜单
      filesType: filesType.list, // 菜单
      hot: hotsku.list, // 右侧热点服务
      detail
    })
  }

  async queryNavigation() {
    const {
      ctx
    } = this;

    const params = ctx.request.body;

    const navigationList = await ctx.service.catalog.getHeadNav();
    const informationTypes = await ctx.service.information.getType();
    const resource = {
      resource: informationTypes.list
    }
    ctx.body = Object.assign(navigationList, resource)
  }

  async querySkuById() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const skuList = await ctx.service.sku.querySkuById(params);
    ctx.body = {
      success: skuList.length ? true : false,
      skuList: skuList.list
    }
  }

  async foodsafeguard() {
    const {
      ctx
    } = this;
    const tab = ctx.request.query.tab;
    const {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    } = await ctx.service.common.navigation();

    let outDetail = {};
    outDetail.name = '食品与农产品数据检索';
    const userInfo = await util.getUserInfo(ctx)
    let isLogin = userInfo.uId ? true : false;
    await ctx.render('foodsafeguard', {
      isChatbot,
      isIchatbot,
      isZhiChi,
      userInfo,
      isLogin,
      mallPid,
      mallPcode,
      host: env[this.app.locals.env].ticMall,
      hotKeyword: hotKeyword.list,
      detail: outDetail,
      tradeNavi: tradeNavi.list,
      serviceNavi: serviceNavi.list,
      informationTypes: informationTypes.list,
      filesType: filesType.list,
      tradeList,
      serviceList,
      storeUrl: env[this.app.locals.env].storeUrl,
      memberUrl: env[this.app.locals.env].memberUrl,
      portalHost: env[this.app.locals.env].portalUrl,
      clientUrl: env[this.app.locals.env].url + ctx.originalUrl,
      hot: hotsku.list,
      csrf: ctx.csrf,
      tab: tab || 0
    })
  }
}

module.exports = HomeController;
