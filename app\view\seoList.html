<% include header.html %>
<% include ./components/header.html %>
<div class="bgBanner">
    <img src="<%- locals.static %>/images/bgbanner.png" alt="">
    <div class="bgBannerW">
        <div class="bgBannerW1">SGS 服务推广</div>
        <% /*<div class="bgBannerW2">全球领先的检验、检测、鉴定和认证机构</div>*/ %>
    </div>
</div>
<div class="location">
    <ul>
        <li class="locationLi">
            <a href="/">第三方检测机构</a>
        </li>
        <li class="locationLi icon"></li>
        <li class="locationLi">
            <a href="javascrpit:void(0);">服务推广</a>
        </li>

    </ul>
</div>

<div class="nlBox">
    <div class="nlLists">
        <ul class="nlListsUl">
            <% if(detail.list.length > 0){ %>
            <% for(var item of detail.list){ %>
            <li class="nlListsLi">
                <span class="nlListsI"></span>
                <a href="/sku/<%- item.alias %>/<%- item.id %>" class="nlListsA"><%- item.name %></a>
                <span class="nlListsT"><%- item.time %></span>
            </li>
            <% } %>
            <% }else{ %>
            <p>暂无结果</p>
            <% } %>
        </ul>

        <!-- <ul class="serverpages">
            <% for(var i=1;i<=total;i++){ %>
                <li class="serverpage<% if(i==page){ %> pageActive<% } %>"><a href="/seoList/?page=<%- i %>"><%- i %></a></li>
            <% } %>
        </ul> -->
    </div>
    <div class="pagination" id='seoListList'>
        <el-pagination @current-change="handleCurrentChange" :current-page.sync="currPage" :page-size="50"
            @size-change="handleSizeChange" layout="prev, pager, next, jumper" :total="totalNum">
        </el-pagination>
    </div>
</div>
<script>
    var newVue = new Vue({
        name: 'seoListList',
        el: '#seoListList',
        data: {
            totalNum: <%- total %>,
            currPage: <%- page %>
        },
        methods: {
            handleCurrentChange: function (val) {
                window.location.href = '/seoList/?page='+ val;
            },
            handleSizeChange: function (val) {
               window.location.href = '/seoList/?page=' + val ;
            },
        }
    });
</script>
<% include footer.html %>
