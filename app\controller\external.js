const Controller = require('egg').Controller;
const util = require('./util');

function getIP(req) {
  let ip = req.get('x-forwarded-for'); // 获取代理前的ip地址
  if (ip && ip.split(',').length > 0) {
    ip = ip.split(',')[0];
  } else {
    ip = req.ip;
  }
  const ipArr = ip.match(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/g);
  return ipArr && ipArr.length > 0 ? ipArr[0] : '127.0.0.1';
}

class externalControll extends Controller {
  // 工单发送验证码
  async sendSms() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.sendSms(Object.assign({
      businessType: 700000,
      projectName: 'TIC_VERIFY',
      buType: 'All'
    }, params), ctx.headers);

    ctx.body = result;
  }

  // 工单提交带有验证码
  async submitTicket() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    params.ip = getIP(ctx.request);
    let token = await util.getCookie(ctx, 'SSO_TOKEN');
    if (token) token = token + '=='
    const result = await ctx.service.external.submitTicket(params, token, ctx.headers);

    ctx.body = result;
  }

  // 工单提交邮箱提交
  async submitTicketByEmail() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    params.ip = getIP(ctx.request);
    if (!params.email && !params.phone) {
      ctx.body = {
        data: null,
        resultCode: 502,
        resultMsg: "提交数据异常，请刷新后重试。",
      };
    } else {
      let token = await util.getCookie(ctx, 'SSO_TOKEN');
      if (token) token = token + '=='
      const result = await ctx.service.external.submitTicketByEmail(params, token, ctx.headers);
      ctx.body = result;
    }
  }

  // 包裹查询
  async qryPackage() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qryPackage(params, ctx.headers);

    ctx.body = result;
  }

  // 热搜词查询
  async qryHotWord() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qryHotWord(params, ctx.headers);

    ctx.body = result;
  }

  // 热搜词点击
  async clickHotWord() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.clickHotWord(params, ctx.headers);
    await ctx.service.external.addKeywordNum({
      keyword: params.keyword
    });
    ctx.body = result;
  }

  // 查询新闻列表
  async qryNewsList() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qryNewsList(params, ctx.headers);

    ctx.body = result;
  }

  // 查询案例列表
  async qryCaseList() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qryCaseList(params, ctx.headers);

    ctx.body = result;
  }

  // 查询案例列表
  async qryClassList() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.qryClassList(params, ctx.headers);

    ctx.body = result;
  }

  // 400咨询热线点击
  async click400() {
    const {
      ctx
    } = this;
    const url = ctx.request.query.url;
    const customerIp = util.getIP(ctx.request);

    const result = await ctx.service.external.click400({
      url,
      ip: customerIp,
      type: 1
    }, ctx.headers);

    ctx.body = result;
  }

  // 搜索接口
  async searchSolr() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.searchSolr(params, ctx.headers);

    ctx.body = result;
  }

  // 悬浮窗的意见反馈提交
  async tictetOpinion() {
    const {
      ctx
    } = this;
    let params = ctx.request.body;
    //content  phone email frontUrl 客户端传入的字段
    let token = await util.getCookie(ctx, 'SSO_TOKEN');
    if (token) token = token + '=='
    params = Object.assign(params, {
      ip: getIP(ctx.request),
      type: '其他',
      trade: '',
      tradeName: '其他',
      service: '',
      serviceName: '其他',
      provice: '浙江省',
      company: '',
      os_type: 'pc',
    })
    const result = await ctx.service.ticket.add(params, token, ctx.headers);
    ctx.body = result;
  }

  // sku悬浮窗的资讯信息
  async tictetSKU() {
    const {
      ctx
    } = this;
    let params = ctx.request.body;
    //content  phone email frontUrl provice customer 客户端传入的字段
    params = Object.assign(params, {
      ip: getIP(ctx.request),
      type: '其他',
      trade: '',
      tradeName: '其他',
      service: '',
      serviceName: '其他',
      os_type: 'pc',
    })
    const result = await ctx.service.ticket.add(params, ctx.headers);
    ctx.body = result;
  }

  // 点击我的订单去对应订单列表
  async getOrderListUrl() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    let token = await util.getCookie(ctx, 'SSO_TOKEN');
    if (token) token = token + '=='
    let result = { isStoreList: true }
    if (token) {
      result = await ctx.service.index.getListUrl(token, ctx.headers);
    }
    ctx.body = result;
  }

  // 获取着陆页列表
  async getOiqList() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.oiq.getOiqList(ctx.headers);
    ctx.body = result;
  }

  // 登出接口
  async logout() {
    const {
      ctx
    } = this;
    let token = await util.getCookie(ctx, 'SSO_TOKEN');
    if (token) token = token + '=='
    const result = await ctx.service.external.logout({}, token, ctx.headers);
    // 退出成功清空session中的userInfo
    if (result) ctx.session.userInfo = null
    ctx.body = {
      status: result
    };
  }

  // get userinfo
  async getUserInfo() {
    const {
      ctx
    } = this;
    const result = await util.getUserInfo(ctx)
    ctx.body = result;
  }

  // get IM tool
  async IMtool() {
    const {
      ctx
    } = this;
    const result = await ctx.service.external.getIMtool({
      paraName: 'CHAT_SET'
    }, ctx.headers);
    ctx.body = result;
  }

  async getNavication() {
    const {
      ctx
    } = this;
    const serviceNavication = await ctx.service.external.getNavication({
      catalogId: 2,
    }, ctx.headers);
    const tradeNavication = await ctx.service.external.getNavication({
      catalogId: 1,
    }, ctx.headers);
    ctx.body = {
      serviceNavication,
      tradeNavication
    };
  }

  async sendSPM() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const query = []
    Object.keys(params).forEach(v => {
      query.push(`${v}=${params[v]}`)
    })
    const result = await ctx.service.external.sendSPM(query.join('&'), ctx.headers);
    ctx.body = result;
  }

  // 获取短信验证码
  async getCodeQuote() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.getCodeQuote(params, ctx.headers);
    ctx.body = result;
  }

  async getFileListApi() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.getFileListApi(params, ctx.headers);
    if (result.data && result.data.items.length) {
      result.data.items.forEach(item => {
        item.isActive = false;
        // 文件类型
        let suffix = item.path.substr(item.path.lastIndexOf('.') + 1, item.path.length).toLocaleLowerCase();
        if (suffix == 'jpg' || suffix === 'jpeg' || suffix === 'png' || suffix === 'gif' || suffix === 'bmp' || suffix === 'wbmp' || suffix === 'webp' || suffix === 'tif' || suffix === 'psd') {
          item.fileType = 'image'
        } else if (suffix === 'pdf') {
          item.fileType = 'pdf'
        } else if (suffix === 'gz' || suffix === 'tgz' || suffix === 'gzip' || suffix === '7z' || suffix === 'zip') {
          item.fileType = 'tar'
        } else if (suffix === 'doc' || suffix === 'docx') {
          item.fileType = 'word'
        } else if (suffix === 'xls' || suffix === 'xlsx') {
          item.fileType = 'excel'
        } else if (suffix === 'ppt' || suffix === 'pptx') {
          item.fileType = 'ppt'
        } else if (suffix === 'mp3' || suffix === 'mp4' || suffix === 'avi') {
          item.fileType = 'video'
        } else if (suffix === 'js' || suffix === 'jsx' || suffix === 'json' || suffix === 'css' || suffix === 'less' || suffix === 'html' || suffix === 'htm' || suffix === 'xml') {
          item.fileType = 'code'
        } else if (suffix === 'txt') {
          item.fileType = 'text'
        } else if (suffix === 'cn' || suffix === 'cn/' || suffix === 'com' || suffix === 'com/' || suffix === 'net' || suffix === 'net/' || suffix === 'top' || suffix === 'vip') {
          item.fileType = 'link'
        } else {
          item.fileType = 'other'
        }
      })
    }

    ctx.body = result;
  }

  async getSkuCataApi() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.getSkuCataApi(params, ctx.headers);
    ctx.body = result;
  }

  async getSkuTagApi() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.getSkuTagApi(params, ctx.headers);
    ctx.body = result;
  }

  // 点赞
  async collection() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    let token = await util.getCookie(ctx, 'SSO_TOKEN');
    if (token) token = token + '=='
    const result = await ctx.service.external.collection(params, token, ctx.headers);
    ctx.body = result;
  }

  async successCallback() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    let token = await util.getCookie(ctx, 'SSO_TOKEN');
    if (token) token = token + '=='
    const result = await ctx.service.external.successCallback(params, token, ctx.headers);
    ctx.body = result;
  }

  //邮件分享
  async emailSharing() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    let token = await util.getCookie(ctx, 'SSO_TOKEN');
    if (token) token = token + '=='
    const result = await ctx.service.external.emailSharing(params, token, ctx.headers);
    ctx.body = result;
  }

  //校验token
  async checkToken() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    let token = await util.getCookie(ctx, 'SSO_TOKEN');
    if (token) token = token + '=='
    const result = await ctx.service.external.checkToken(params, token, ctx.headers);
    ctx.body = result;
  }

  //校验token
  async statByBusi() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    let token = await util.getCookie(ctx, 'SSO_TOKEN');
    if (token) token = token + '=='
    const result = await ctx.service.external.statByBusi(params, token, ctx.headers);
    ctx.body = result;
  }

  // 滑动验证码验证
  async tencentCaptchaApi() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.tencentCaptchaApi(params, ctx.headers);
    ctx.body = result;
  }

  async loginReg() {
    const {
      ctx
    } = this;
    const params = ctx.request.body;
    const result = await ctx.service.external.loginReg(params, ctx.headers);
    ctx.body = result;
  }
}

module.exports = externalControll;
