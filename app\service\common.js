'use strict';

const Service = require('egg').Service;
const moment = require('moment');


class CommonService extends Service {
  // 获取资料中心分类
  async getFilesType() {
    const list = await this.app.mysql.select('catalog', {
      columns: ['id', 'name', 'alias', 'is_show'],
      where: {
        is_delete: 0,
        parent_id: 4,
        is_navi: 1,
        is_show: 1
      },
      orders: [
        ['sort_num', 'desc'],
      ]
    });

    return {
      list
    };
  }

  async navigation() {
    const {
      ctx
    } = this
    const tradeNavi = await ctx.service.catalog.getNaviTrade();
    const serviceNavi = await ctx.service.catalog.getNaviService();
    const informationTypes = await ctx.service.information.getType();
    const filesType = await ctx.service.common.getFilesType();
    const IMtool = await ctx.service.external.getIMtool({ paraName: 'CHAT_SET' }, ctx.headers);
    const host = ctx.host;
    const requestHost = ctx.request.host;
    let router = ctx._matchedRoute;
    let isChatbot = false;
    let isIchatbot = false;
    let isZhiChi = false;
    if (IMtool.length) {
      const ticViewList = IMtool.filter(v => {
        return v.groupName === 'MALL'
      })
      ticViewList.forEach(v => {
        if (v.paraValue === "1") {
          if (v.paraCode.includes("CHATBOT")) isChatbot = true
          if (v.paraCode.includes("SOBOT")) isZhiChi = true
          if (v.paraCode.includes("ICHATBOT")) isIchatbot = true
        }
      })
      // oiq引导和实验室页面禁用leadoo
      if (router.includes('/oiq') || router.includes('/lab')) isChatbot = false
    }
    const {
      tradeList,
      serviceList
    } = await ctx.service.catalog.getHeadNav();
    const hotKeyword = await ctx.service.catalog.getHotKeyword();
    const hotsku = await ctx.service.search.hotsku();

    return {
      tradeNavi,
      serviceNavi,
      informationTypes,
      filesType,
      tradeList,
      serviceList,
      hotKeyword,
      isChatbot,
      isIchatbot,
      isZhiChi,
      hotsku
    }
  }

  // 根据域名不同，生成不同电话号码
  async hotLine() {
    const {
      ctx
    } = this;
    let phoneNum = '4000-558-581';
    return {
      phoneNum
    }
  }

  // 
}

module.exports = CommonService;