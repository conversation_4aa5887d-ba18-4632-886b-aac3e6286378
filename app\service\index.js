'use strict';

const Service = require('egg').Service
const axios = require('axios');
const crypto = require('crypto');
const appKey = 'TICJAVA';
const pcode = '07cc5a890687c310'
const {
  env
} = require('../../config/info').siteInfo;

const ticMember = {
  pid: 'pid.member',
  pcode: 'CGz2RFHATB3XAsvg',
  appId: '99999999'
}

function getCookie(ctx, key) {
  if (!ctx.request.header.cookie) {
    return '';
  }
  const carr = ctx.request.header.cookie.split('; ');
  let sjson = {};
  for (let item of carr) {
    let iarr = item.split('=');
    sjson[iarr[0]] = iarr[1];
  }

  if (sjson) {
    return sjson[key];
  } else {
    return '';
  }
}


/*
  随机生成字符串
  length: 生成的字符串长度
*/
var randomStr = function (length) {
  var source = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  var result = '';
  for (var i = length; i > 0; --i)
    result += source[Math.floor(Math.random() * source.length)];
  return result;
};

function createHeader(type, params, header, that) {
  const ctx = that && that.ctx
  let frontUrl = '', si = ''
  if (ctx) {
    frontUrl = env[that.app.locals.env].url+ctx.request.url
    si = getCookie(ctx, 'sessionId') || '';
    if (!si) {
      ctx.cookies.set('sessionId', randomStr(32), {
        httpOnly: false
      })
    }
  }
  header = header || {}
  const pmd5 = crypto.createHash('md5').update((type.pid + type.pcode).toUpperCase()).digest('hex')
  const param = JSON.stringify(params) + pmd5.toUpperCase()
  const timestamp = new Date().getTime();
  const sign = crypto.createHash('md5').update(param + timestamp).digest('hex')
  const headers = {
    'Content-Type': 'application/json',
    pid: type.pid,
    pcode: type.pcode,
    timestamp,
    sign,
    appId: type.appId,
    appKey: type.pid,
    frontUrl: header.frontUrl || frontUrl,
    si: header.si || si,
    ia: header.ia || '',
  }
  for (var item in headers) {
    if (!headers[item]) delete headers[item]
  }
  return headers
}

class IndexService extends Service {
  async getDetail() {
    const {
      app
    } = this;
    const detail = await app.mysql.get('pages', {
      id: 1
    });
    const newsTypeList = await app.mysql.query(`select * from catalog WHERE parent_id=3 AND is_delete=0 AND is_show=1 AND is_navi=1`)

    return {
      detail,
      newsTypeList
    };
  }

  async getListUrl(token, header) {
    // const params = {
    //   flg: 'lv'
    // }
    // const timestamp = new Date().valueOf()
    // const sign = crypto.createHash('md5').update(timestamp + appKey + pcode).digest('hex')

    // const headers = {
    //   'Content-Type': 'application/json',
    //   sign,
    //   timestamp,
    //   appKey,
    //   pid: 'pid.bbc',
    //   appid: 99999999,
    //   accessToken: token
    // }
    let params = {}
    let headers = createHeader(ticMember, params, header, this)
    headers.accessToken = token

    let isStoreList = true
    let storeNum = 0
    let oiqNum = 0
    return axios({
      url: env[this.app.locals.env].ticMall + '/ticMember/business/api.v2.order/order/getTotalNum',
      method: 'post',
      data: params,
      headers: headers,
    }).then(res => {
      res.data.data.forEach(v => {
        if (v.orderType === '100000') {
          storeNum = v.num
        }
        if (v.orderType === '210000') {
          oiqNum = v.num
        }
      })
      if (storeNum === 0 && oiqNum !== 0) {
        isStoreList = false
      }
      return {
        isStoreList
      }
    }).catch(e => {
      return {
        isStoreList
      }
    })
  }

  // 获取lv3的订单数量和OIQ订单数量
  async getOrderNum(token, header) {
    const {
      app,
      ctx
    } = this;
    const params = {
      flg: 'lv'
    }
    const timestamp = new Date().valueOf()
    const sign = crypto.createHash('md5').update(timestamp + appKey + pcode).digest('hex')

    const headers = {
      'Content-Type': 'application/json',
      sign,
      timestamp,
      appKey,
      pid: 'pid.bbc',
      appid: 99999999,
      accessToken: token
    }
    let params2 = {}
    let headers2 = createHeader(ticMember, params2, header, this)
    headers2.accessToken = token
    const result = {
      lv3: 0,
      oiq: 0
    }
    let res = await Promise.all([
      axios({
        url: env[this.app.locals.env].ticMall + '/ticMember/business/api.v2.order/order/getTotalNum',
        method: 'post',
        data: params2,
        headers: headers2,
      }),
      axios({
        url: env[this.app.locals.env].ticMall + '/ticMember/business/api.v2.user/coupon/getUseNum',
        method: 'post',
        data: params2,
        headers: headers2,
      })
    ])
    if (res[0].status === 200 && res[0].data && res[0].data.resultCode === '0') {
      res[0].data.data.forEach(v => {
        if (v.orderType === '210000') {
          result.oiq = v.num
        } else if (v.orderType === '100000') {
          result.lv3 = v.num
        } else if (v.orderType === '200000') {
          if (v.num > 999) {
            result.enquirynum = '999+'
          } else {
            result.enquirynum = v.num
          }
        }
      })
    }
    if (res[1].status === 200 && res[1].data.resultCode === '0') {
      result.couponNum = res[1].data.data
    }
    return {
      ...result
    }
  }

  // 查询核心供应商详情
  async getCoreSupplier() {
    const {
      app
    } = this;
    const detail = await app.mysql.get('pages', {
      id: 3
    });
    return {
      detail
    };
  }
}

module.exports = IndexService;
