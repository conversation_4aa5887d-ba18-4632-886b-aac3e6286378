.iso9001 {
  margin: 0 auto;
  padding: 0;
}

.iso9001 * {
  margin: 0 auto;
  padding: 0;
}

ul,
li {
  list-style: none;
}

img {
  border: 0;
}

button {
  border: 0;
  outline: none;
  cursor: pointer;
}

em,
i {
  font-weight: normal;
}

.wrap {
  width: 1226px;
  margin: 0 auto;
}

.bg-white {
  background: #fff;
}

.bg-gray {
  background: #f9f9f9;
}

.iso9001 {
  overflow: hidden;
}

.phone {
  color: #ca4300;
  text-align: center;
}

.phone-label {
  font-size: 20px;
  font-weight: 600;
}

.phone-content {
  font-size: 30px;
  font-style: italic;
  font-weight: bold;
}

.iso9001 .banner {
  width: 100%;
  height: 425px;
  background: url("../../images/promotion/iso9001/banner.jpg") no-repeat center center;
  text-align: left;
}

.iso9001 .banner p,
.iso9001 .banner .wrap div {
  margin-left: 350px;
}

.iso9001 .banner .p1 {
  height: 47px;
  font-size: 48px;
  font-family: Arial;
  font-weight: bold;
  color: #000000;
  line-height: 75px;
  margin-top: 50px;
}

.iso9001 .banner .p2 {
  margin-top: 35px;
  width: 748px;
  height: 45px;
  line-height: 45px;
  font-size: 18px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #000000;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.iso9001 .banner .p2 span {
  margin-right: 15px;
  position: relative;
}

.iso9001 .banner .p2 i {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #CA4300;
  margin-right: 5px;
  position: absolute;
  left: 0;
  top: 7px;
}

.iso9001 .banner .p2 b {
  padding-left: 15px;
}

.iso9001 .banner .p3 {
  font-size: 18px;
  color: #000;
  line-height: 36px;
  opacity: 0.9;
  padding-top: 8px;
}


.iso9001 .banner div {
  padding-top: 40px;
}

.iso9001 .banner button {
  width: 160px;
  text-align: center;
  height: 50px;
  line-height: 50px;
  background: #CA4300;
  border-radius: 6px;
  color: #FFFFFF;
  font-size: 18px;
  float: left;
}

.iso9001 .banner .p4 {
  width: 346px;
  height: 41px;
  font-size: 15px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-style: italic;
  color: #CA4300;
  line-height: 26px;
  text-shadow: 2px 1px 5px #FFFFFF;
  float: left;
  margin-left: 30px;
}


/* .iso9001 .banner .phone {
  margin: 20px 195px 0 0;
  text-align: right;
}

.iso9001 .banner h2 {
  font-size: 50px;
  color: #000;
  padding: 100px 98px 0 0;
}

.iso9001 .banner h3 {
  font-style: normal;
  font-size: 28px;
  color: #000;
  margin-right: 195px;
  padding: 30px;
}

.iso9001 .banner button {
  background: #ca4300;
  color: #fff;
  width: 162px;
  color: #fff;
  margin-left: 20px;
  height: 43px;
  line-height: 43px;
  font-size: 18px;
}


.iso9001 .banner button:last-child {
  margin-right: 190px;
}
*/
.iso9001 .banner-bottom {
  width: 100%;
  height: 56px;
  background: url("../../images/promotion/iso9001/banner-bottm.jpg") no-repeat center center #000;
}

.iso9001 .title {
  text-align: center;
  margin-bottom: 50px;
  padding-top: 60px;
}

.iso9001 .title h2 {
  font-size: 30px;
  font-weight: normal;
  margin-bottom: 20px;
}

.iso9001 .title p {
  font-size: 14px;
  margin-bottom: 30px;
  margin: 0 0 10px 0;
  color: #999;
}

.iso9001 .title span {
  width: 120px;
  height: 3px;
  display: inline-block;
  background: #e5e5e5;
}

.iso9001 .title i {
  color: #ca4300;
  font-style: normal;
}

.iso9001 #floor-1 {
  height: 317px;
  position: relative;
  border-bottom: 1px dashed #dedede;
}

.iso9001 #floor-1 .left {
  position: absolute;
  left: 0;
}

.iso9001 #floor-1 .right {
  position: absolute;
  right: -62px;
}

.iso9001 #floor-1 ul {
  font-size: 14px;
}

.iso9001 #floor-1 li {
  margin-bottom: 14px;
  list-style-type: square;
  color: #ca4300;
}

.iso9001 #floor-1 li p {
  color: #232323;
  font-size: 16px;
  line-height: 16px;
}

.iso9001 #floor-1 li .num {
  color: #ca4300;
}


.iso9001 .box1 img {
  float: left;
  width: 280px;
  margin-top: 70px;
  margin-left: 100px;
}

.iso9001 .box1 div {
  float: right;
  width: 660px;
  margin-right: 50px;
}

.iso9001 .box1 h3 {
  font-size: 30px;
  margin-top: 80px;
  margin-bottom: 30px;
}

.iso9001 .box1 p {
  font-size: 18px;
  line-height: 30px;
}

.iso9001 .box1 i {
  color: #ca4300;
  font-size: 30px;
  font-style: normal;
}

.iso9001 .box2 .p2 {
  color: #CA3200 !important;
  font-size: 16px !important;
  font-weight: bold;
  font-style: oblique;
  padding-top: 20px !important;
}

.iso9001 .box2 .p3 {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  line-height: 28px;
}

.iso9001 .box2 .p4 {
  padding-top: 20px;
  font-size: 14px !important;
}

.iso9001 .box2 .p4 span {
  color: #ca4300;
}

.iso9001 .bg-half {
  width: 100%;
  position: relative;
  height: 500px;
  margin: 0;
  border-bottom: 1px dashed #dedede;
}

.iso9001 .bg-half .bg {
  width: 50%;
  background: #f2f2f2;
  height: 370px;
  position: absolute;
  top: 0;
  z-index: -1;
}

.iso9001 .bg-half .wrap {
  position: relative;
}

.iso9001 .bg-half .bg.left {
  left: 0;
}

.iso9001 .bg-half .bg.right {
  right: 0;
}

.iso9001 .bg-half .info {
  width: 500px;
  position: absolute;
  top: 100px;
  text-align: left;
  /* padding-left: 50px; */
}

.iso9001 .bg-half .info.left {
  left: 0;
}

.iso9001 .bg-half img.left {
  opacity: 0;
}

.iso9001 .bg-half .info h3 {
  color: #000;
  font-size: 24px;
}

.iso9001 .bg-half .info p {
  line-height: 30px;
  font-size: 16px;
  color: #232323;
  padding-top: 25px;
}

.iso9001 .bg-half .info p i {
  font-size: 50px;
  color: #ca4300;
  font-style: normal;
}

.iso9001 .bg-half .info a {
  color: #ca4300;
}

.iso9001 .bg-half .info button {
  width: 160px;
  text-align: center;
  height: 44px;
  line-height: 44px;
  background: #CA4300;
  border-radius: 6px;
  color: #fff;
  font-size: 16px;
  margin-top: 25px;
}

.iso9001 .bg-half img {
  /*width: 676px;*/
  /*height: 372px;*/
  /*position: absolute;*/
  /*top: -30px;*/
  /*box-shadow: 10px 10px 10px #ddd;*/
}

.iso9001 .bg-half .img-right,
.iso9001 .bg-half .img-left {
  position: absolute;
  width: 660px;
  height: 500px;
  top: 0;
  overflow: hidden;
}

.iso9001 .bg-half .img-right {
  right: 59px;
}

.iso9001 .bg-half .img-left {
  left: 0;
  opacity: 0;
}

.iso9001 .bg-half .img-content {
  width: 500px;
  height: 245px;
  border: 5px solid #eee;
  margin-top: 100px;
}

.iso9001 .bg-half .img-left .img-content {
  position: absolute;
  width: 400px;
  height: 245px;
  border: 5px solid #eee;
  margin-top: 100px;
}

.iso9001 .bg-half #box6 .img-content {
  margin-top: 150px;
}

.iso9001 .bg-half .img-content img {
  position: absolute;
}

.iso9001 .bg-half .img-content .img-1 {
  /*left: -600px;*/
  left: 108px;
  top: 64px;
}

.iso9001 .bg-half .img-content .img-2 {
  /*right: -600px;*/
  right: -25px;
  top: 195px;
}

.iso9001 .bg-half .img-content .img-3 {
  left: 92px;
  /*top: -600px;*/
  top: -33px;
}

.iso9001 .bg-half .img-content .img-4 {
  left: 22px;
  /*bottom: -600px;*/
  bottom: -92px;
}

.iso9001 .bg-half .img-content .img-5 {
  /*right: -600px;*/
  right: -190px;
  top: -33px;
}

.iso9001 .bg-half .img-content .img-6 {
  left: 89px;
  /*top: -600px;*/
  top: 45px;
}

.iso9001 .bg-half .img-content .img-7 {
  /*left: -600px;*/
  left: 124px;
  bottom: 52px;
}

.iso9001 .bg-half .img-content .img-8 {
  /*right: -600px;*/
  right: 68px;
  bottom: 0;
}

.iso9001 .bg-half .info .content p {
  margin: 0 0 10px 0;
  padding: 0;
  font-size: 16px;
  line-height: 16px;
  color: #232323;
}

.iso9001 .bg-half .info .content p:first-child {
  padding-top: 25px;
}

.iso9001 .cases {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 15px;
}

.iso9001 .cases .img-item {
  width: 150px;
}

.iso9001 .cases .img-item a {
  display: block;
}

.iso9001 .cases img {
  width: 150px;
  height: 150px;
}

.iso9001 .cases img.hide {
  display: none;
}

.iso9001 .cases .p1 {
  font-size: 18px;
  font-weight: bold;
  margin-top: 15px;
  padding: 21px 0;
  display: block;
  text-align: center;
  color: #000;
}

.iso9001 .cases .p2 {
  height: 38px;
  line-height: 19px;
  font-size: 14px;
  display: block;
  text-align: center;
  color: #878787;
}

.iso9001 .img-item.active img {
  display: none;
}

.iso9001 .img-item.active img.hide {
  display: inline-block;
}

.iso9001 .img-item:hover .p1 {
  color: #ca4300;
}

.iso9001 .img-item:hover .p2 {
  color: #333;
}

#box3 {
  padding-bottom: 90px;
}

.iso9001 .time-step {
  position: relative;
  top: 40px;
  opacity: 0;
}

.iso9001 .time-step .axis {
  position: absolute;
  height: 6px;
  width: 100%;
  top: 160px;
}

.iso9001 .time-step .step-top,
.iso9001 .time-step .step-bottom {
  position: absolute;
  width: 44px;
  height: 43px;
  top: -18px;
}

.iso9001 .time-step .step-top img,
.iso9001 .time-step .step-bottom img {
  position: absolute;
  width: 44px;
  height: 43px;
}

.iso9001 .time-step .step-top i,
.iso9001 .time-step .step-bottom i {
  width: 1px;
  height: 20px;
  background: #ca4300;
  position: absolute;
  z-index: 1;
}

.iso9001 .time-step .step-top i {
  top: -14px;
  left: 22px;
}

.iso9001 .time-step .step-bottom i {
  top: 37px;
  left: 22px;
}

.iso9001 .time-step .step-1 {
  left: 30px;
}

.iso9001 .time-step .step-2 {
  left: 180px;
}

.iso9001 .time-step .step-3 {
  left: 340px;
}

.iso9001 .time-step .step-4 {
  left: 697px;
}

.iso9001 .time-step .step-5 {
  left: 835px;
}

.iso9001 .time-step .step-6 {
  left: 1041px;
}

.iso9001 .step-content {
  position: absolute;
  border-radius: 15px;
  box-shadow: 4.596px 3.857px 7px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  border: 1px solid #ca4300;
  padding: 20px;
  box-sizing: border-box;
}

.iso9001 .step-content .label {
  font-size: 18px;
  line-height: 24px;
  color: #000000;
  margin-bottom: 10px;
}

.iso9001 .step-content .content {
  font-size: 15px;
  line-height: 24px;
  color: #888888;
  font-weight: 300;
}

.iso9001 .step-content .color-label {
  font-size: 20px;
  line-height: 28px;
  color: #ca4300;
  font-weight: 500;
  padding: 10px 0;
}

.iso9001 .step-content .sign-img {
  position: absolute;
  top: 9px;
  right: 33px;
  width: 98px;
  height: 95px;
}

.iso9001 .step-content-1 {
  width: 285px;
  height: 144px;
}

.iso9001 .step-content-2 {
  width: 285px;
  height: 124px;
  left: 130px;
}

.iso9001 .step-content-3 {
  width: 384px;
  height: 185px;
  top: -57px !important;
  left: 329px;
}

.iso9001 .step-content-4 {
  width: 270px;
  height: 120px;
  left: 584px;
}

.iso9001 .step-content-5 {
  width: 278px;
  height: 124px;
  left: 774px;
  top: 5px !important
}

.iso9001 .step-content-6 {
  width: 192px;
  height: 95px;
  left: 977px;
}

.iso9001 .step-content.top {
  top: -16px;
}

.iso9001 .step-content.bottom {
  top: 198px;
}

.iso9001 .phone1 {
  color: #ca4300;
  margin-top: 10px;
  font-size: 20px;
  font-weight: bold;
  font-style: italic;
}

.iso9001 .bg-half img.right,
.iso9001 .bg-half .info.right {
  /*right: -600px;*/
  right: 0;
  opacity: 0;
}

.iso9001 .class-list {
  /* margin-bottom: 50px; */
}

.iso9001 .class-list-phone {
  position: relative;
  top: 45px;
}

.iso9001 .class-list li {
  float: left;
  width: 284px;
  margin-right: 30px;
  /* margin-bottom: 60px; */
  background: #fff;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.iso9001 .class-list li:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transition: .4s;
  transform: translate3d(0, -3px, 0);
}

.iso9001 .class-list li:nth-child(4n) {
  margin-right: 0;
}

.iso9001 .class-listbox,
.iso9001 .class-list img {
  width: 284px;
  height: 190px;
  ;
}

.iso9001 .class-list img {
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
}

.iso9001 .class-listbox {
  position: relative;
}

.iso9001 .class-listbox em {
  display: inline-block;
  width: 83px;
  height: 28px;
  line-height: 28px;
  color: #fff;
  z-index: 2;
  position: absolute;
  left: 0;
  top: 0;
  background: url('../../images/promotion/iso.iso9001/list/icon.png') no-repeat;
  font-style: normal;
  font-size: 14px;
  padding-left: 5px;
}

.iso9001 .class-listbox a {
  position: absolute;
  bottom: 0;
  left: 0;
  display: inline-block;
  width: 274px;
  height: 27px;
  line-height: 27px;
  color: #fefefe;
  font-size: 12px;
  z-index: 2;
  background: rgba(0, 0, 0, .7);
  text-align: right;
  padding-right: 10px;
}

.iso9001 .class-list-box {
  text-align: center;
  padding-bottom: 22px;
}

.iso9001 .class-list-box h2 {
  font-size: 18px;
  margin-top: 24px;
}

.iso9001 .class-list-box em {
  display: block;
  width: 72px;
  height: 3px;
  background: #e5e5e5;
  margin-top: 10px;
  margin-bottom: 10px;
}

.iso9001 .class-list-box p {
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  color: #999;
}

.iso9001 .class-list-box span,
.iso9001 .class-list-box a {
  display: inline-block;
  width: 120px;
  height: 27px;
  line-height: 27px;
  border: 1px solid #ca4300;
  text-align: center;
  margin-top: 20px;
  color: #ca4300;
  font-size: 16px;
  cursor: pointer;
}

.iso9001 .class-list-box span:hover,
.iso9001 .class-list-box a:hover {
  color: #fff;
  background: #ca4300;
  transition: .4s;
}

/* 案例集锦 */
.iso9001 .case {
  padding-bottom: 100px;
}

.iso9001 .case .tab {
  float: left;
  width: 220px;
}

.iso9001 .case .tab li {
  width: 220px;
  height: 115px;
  background: #aeaeae;
  border-bottom: 3px solid #f9f9f9;
  color: #fff;
  font-size: 16px;
  text-align: center;
  line-height: 115px;
  cursor: pointer;
}

.iso9001 .case .tab li.active {
  background: #ca4300;
  font-weight: normal;
}

.iso9001 .case .tab li.active span {
  border-bottom: 2px solid #fff;
  padding-bottom: 5px;
}

.iso9001 .case .card {
  float: left;
  border: 2px solid #ca4300;
  width: 942px;
  padding: 30px;
  height: 405px;
  overflow: hidden;
  position: relative;
}

.iso9001 .case .card .box {
  height: 1212px;
  width: 942px;
  position: absolute;
  top: 0;
  left: 0;
}

.iso9001 .case .card .item {
  height: 405px;
  padding-top: 30px;
  padding-bottom: 32px;
}

.iso9001 .case .card .left {
  float: left;
  width: 250px;
  height: 410px;
  padding-right: 30px;
  margin-right: 30px;
  border-right: 1px solid #ddd;
  text-align: center;
}

.iso9001 .case .card .left p {
  text-align: center;
  font-size: 18px;
  color: #000;
  line-height: 24px;
}

.iso9001 .case .card .left img {
  margin: 30px 0 20px 0;
  display: inline-block;
  width: 250px;
  height: 280px;
}

.iso9001 .case .card .left a,
.iso9001 .case .card .left button {
  display: inline-block;
  width: 125px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #ca4300;
  border: 1px solid #ca4300;
  font-size: 14px;
  background: transparent;
}

.iso9001 .case .card .left a:hover,
.iso9001 .case .card .left button:hover {
  background: #ca4300;
  color: #fff;
}

.iso9001 .case .card .right {
  float: left;
  width: 630px;
}

.iso9001 .case .card .right dl {
  padding-bottom: 15px;
}

.iso9001 .case .card .right dt {
  color: #ca4300;
  font-size: 14px;
  padding-bottom: 15px;
}

.iso9001 .case .card .right dd {
  color: #7f7f7f;
  font-size: 15px;
  line-height: 25px;
  padding-top: 10px;
  text-indent: 2em;
}

.iso9001 .case .card .right dd a {
  color: #ca4300;
  display: inline-block;
}

.iso9001 .advantage {
  padding-bottom: 50px;
  height: 270px;
}

.iso9001 .advantage li {
  float: left;
  border-left: 1px solid #ddd;
  padding: 0 30px;
  height: 270px;
  width: 245px;
}

.iso9001 .advantage li:last-child {
  border-right: 1px solid #ddd;
}

.iso9001 .advantage h3 {
  color: #ca4300;
  font-size: 40px;
  height: 55px;
  line-height: 55px;
}

.iso9001 .advantage h3 em {
  font-family: Arail, Roboto, "Helvetica Neue";
  font-style: normal;
  font-weight: bold;
}

.iso9001 .advantage span {
  color: #333;
  font-size: 20px;
  display: block;
  padding: 25px 0;
  font-weight: bold;
}

.iso9001 .advantage p {
  color: #666;
  line-height: 24px;
  font-size: 16px;
}

.iso9001 .valueService {
  padding-bottom: 100px;
}

.iso9001 .valueService li {
  float: left;
  margin-right: 37px;
}

.iso9001 .valueService li:last-child {
  margin-right: 0;
}

.iso9001 .valueService .info:hover {
  background: url("../../images/promotion/iso9001/valueService/bg-a.png") no-repeat center center;
}

.iso9001 .valueService .info {
  width: 215px;
  height: 215px;
  background: url("../../images/promotion/iso9001/valueService/bg.png") no-repeat center center;
  text-align: center;
}

.iso9001 .valueService i {
  display: inline-block;
  width: 83px;
  height: 60px;
  margin: 40px 0 15px 0;
}

.iso9001 .valueService i.icon1 {
  background: url("../../images/promotion/iso9001/valueService/icon-1.png") no-repeat center center;
}

.iso9001 .valueService i.icon2 {
  background: url("../../images/promotion/iso9001/valueService/icon-2.png") no-repeat center center;
}

.iso9001 .valueService i.icon3 {
  background: url("../../images/promotion/iso9001/valueService/icon-3.png") no-repeat center center;
}

.iso9001 .valueService i.icon4 {
  background: url("../../images/promotion/iso9001/valueService/icon-4.png") no-repeat center center;
}

.iso9001 .valueService i.icon5 {
  background: url("../../images/promotion/iso9001/valueService/icon-5.png") no-repeat center center;
}

.iso9001 .valueService p {
  color: #fff;
  line-height: 24px;
  font-size: 16px;
}

.iso9001 .valueService .shadow {
  width: 215px;
  height: 22px;
  background: url("../../images/promotion/iso9001/valueService/shadow.png") no-repeat center center;
  margin-top: 20px;
}

.iso9001 .moreSevice ul {
  height: 145px;
}

.iso9001 .moreSevice li {
  width: 201px;
  height: 145px;
  float: left;
  text-align: center;
}

.iso9001 .moreSevice li:hover span {
  color: #ca4300;
}

/* .iso9001 .moreSevice li:last-child {
  border-right: 1px solid #ddd;
} */

.iso9001 .moreSevice i {
  width: 145px;
  height: 78px;
  display: inline-block;
  margin-top: 15px;
}

.iso9001 .moreSevice .icon1 i {
  background: url("../../images/promotion/iso9001/moreSevice/icon1.png") no-repeat center center;
}

.iso9001 .moreSevice li.icon1:hover i {
  background: url("../../images/promotion/iso9001/moreSevice/icon1-a.png") no-repeat center center;
}

.iso9001 .moreSevice .icon2 i {
  background: url("../../images/promotion/iso9001/moreSevice/icon2.png") no-repeat center center;
}

.iso9001 .moreSevice li.icon2:hover i {
  background: url("../../images/promotion/iso9001/moreSevice/icon2-a.png") no-repeat center center;
}

.iso9001 .moreSevice .icon3 i {
  background: url("../../images/promotion/iso9001/moreSevice/icon3.png") no-repeat center center;
}

.iso9001 .moreSevice li.icon3:hover i {
  background: url("../../images/promotion/iso9001/moreSevice/icon3-a.png") no-repeat center center;
}

.iso9001 .moreSevice .icon4 i {
  background: url("../../images/promotion/iso9001/moreSevice/icon4.png") no-repeat center center;
}

.iso9001 .moreSevice li.icon4:hover i {
  background: url("../../images/promotion/iso9001/moreSevice/icon4-a.png") no-repeat center center;
}

.iso9001 .moreSevice .icon5 i {
  background: url("../../images/promotion/iso9001/moreSevice/icon5.png") no-repeat center center;
}

.iso9001 .moreSevice li.icon5:hover i {
  background: url("../../images/promotion/iso9001/moreSevice/icon5-a.png") no-repeat center center;
}

.iso9001 .moreSevice .icon6 i {
  background: url("../../images/promotion/iso9001/moreSevice/icon6.png") no-repeat center center;
}

.iso9001 .moreSevice li.icon6:hover i {
  background: url("../../images/promotion/iso9001/moreSevice/icon6-a.png") no-repeat center center;
}

.iso9001 .moreSevice span {
  width: 200px;
  display: inline-block;
  padding: 20px 0;
  text-align: center;
  color: #787878;
}

.iso9001 .moreSevice .more {
  padding: 55px 0 90px 0;
  text-align: center;
}

.iso9001 .moreSevice .more a {
  border: 1px solid #ca4300;
  color: #ca4300;
  font-size: 16px;
  display: inline-block;
  width: 185px;
  height: 45px;
  line-height: 45px;

  border-radius: 6px;
}

.iso9001 .moreSevice .more a:hover {
  background: #ca4300;
  color: #fff;
}

.iso9001 .mews {
  padding-bottom: 60px;
}

.iso9001 .mews h3 {
  border-bottom: 2px solid #ddd;
  height: 40px;
  line-height: 40px;
  margin-bottom: 20px;
}

.iso9001 .mews .left {
  width: 815px;
  float: left;
}

.iso9001 .mews .left li {
  float: left;
  width: 390px;
  border-bottom: 1px dashed #ddd;
  height: 36px;
  line-height: 36px;
  margin-right: 35px;
}

.iso9001 .mews .left li:nth-child(even) {
  margin-right: 0;
}

.iso9001 .mews .left li a {
  color: #000;
  font-size: 16px;
}

.iso9001 .mews .left li a:hover {
  color: #ca4300;
}

.iso9001 .mews .left li.more {
  text-align: right;
  border-bottom: 0;
  width: 100%;
}

.iso9001 .mews .left li.more a {
  color: #ca4300;
  font-size: 14px;
}

.iso9001 .mews .right {
  float: right;
  width: 360px;
}

.iso9001 .mews .right li {
  height: 35px;
  line-height: 35px;
}

.iso9001 .mews .right li a {
  color: #666;
  background: url("../../images/promotion/iso9001/pdf.png ") no-repeat left center;
  padding-left: 40px;
}

.iso9001 .consult {
  padding-bottom: 100px;
  height: 45px;
}

.iso9001 .consult label {
  float: left;
}

.iso9001 .consult label:first-child {
  margin-left: 50px;
}

.iso9001 .consult input {
  height: 45px;
  line-height: 45px;
  padding-left: 10px;
  border: 1px solid #ddd;
  background: #fff;
  margin-right: 20px;
  width: 179px;
  border-radius: 6px;
}

.iso9001 .consult input:last-child {
  margin-right: 10px;
}

.iso9001 .consult input.input-big {
  width: 330px;
}

.iso9001 .consult input:focus {
  border: 1px solid #ca4300;
  outline: none;
}

.iso9001 .consult input.focus {
  border: 1px solid #ca4300;
}

.iso9001 .consult button {
  background: #ca4300;
  color: #fff;
  height: 45px;
  line-height: 45px;
  text-align: center;
  width: 130px;
  border: 0;
  cursor: pointer;
  margin-left: 20px;
  border-radius: 6px;
}

.iso9001 .consult p {
  text-align: center;
  font-size: 16px;
  padding-top: 20px;
  padding-bottom: 50px;
  color: rgba(0, 0, 0, .6);
}

.iso9001 .list {
  margin-bottom: 50px;
}

.iso9001 .list li {
  float: left;
  width: 284px;
  margin-right: 30px;
  margin-bottom: 60px;
  background: #fff;
}

.iso9001 .list li:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transition: .4s;
  transform: translate3d(0, -3px, 0);
}

.iso9001 .list li:nth-child(4n) {
  margin-right: 0;
}

.iso9001 .img-box,
.iso9001 .list img {
  width: 284px;
  height: 190px;
  ;
}

.iso9001 .list img {
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
}

.iso9001 .img-box {
  position: relative;
}

.iso9001 .img-box em {
  display: inline-block;
  width: 83px;
  height: 28px;
  line-height: 28px;
  color: #fff;
  z-index: 2;
  position: absolute;
  left: 0;
  top: 0;
  background: url('../../images/promotion/iso9001/icon.png') no-repeat;
  font-style: normal;
  font-size: 14px;
  padding-left: 5px;
}

.iso9001 .img-box a {
  position: absolute;
  bottom: 0;
  left: 0;
  display: inline-block;
  width: 274px;
  height: 27px;
  line-height: 27px;
  color: #fefefe;
  font-size: 12px;
  z-index: 2;
  background: rgba(0, 0, 0, .7);
  text-align: right;
  padding-right: 10px;
}

.iso9001 .info-box {
  text-align: center;
  padding-bottom: 30px;
  padding-top: 25px;
}


.iso9001 .info-box p {
  font-size: 18px;
  height: 25px;
  line-height: 25px;
  color: #000;
}

.iso9001 .info-box span,
.iso9001 .info-box a {
  display: inline-block;
  width: 120px;
  height: 27px;
  line-height: 27px;
  border: 1px solid #ca4300;
  text-align: center;
  margin-top: 20px;
  color: #ca4300;
  font-size: 16px;
  cursor: pointer;
}

.iso9001 .info-box span:hover,
.iso9001 .info-box a:hover {
  color: #fff;
  background: #ca4300;
  transition: .4s;
}

.iso9001 .list .more {
  background: #ca4300;
  width: 300px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  cursor: pointer;
}

.iso9001 .list .more a {
  color: #fff;
}

.iso9001 .list .more:hover {
  background: #cc5500;
  transition: .4s;
}

.pop {
  display: none;
}

.pop .layout {
  background: rgba(0, 0, 0, .7);
  width: 1000px;
  height: 500px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
}

.pop .popBox {
  width: 300px;
  height: 160px;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999999;
  background: #fff;
  border-radius: 5px;
  margin: auto;
}

.pop .popBox .tit {
  text-align: left;
  background: #f5f5f5;
  padding-left: 20px;
  font-size: 14px;
  border-bottom: 1px solid #ddd;
  height: 35px;
  line-height: 35px;
  margin: 0;
  border-radius: 3px 3px 0 0;
}

.pop .popBox .cont {
  text-indent: 2em;
  font-size: 14px;
  padding: 20px;
}

.pop .popBox .btn {
  text-align: center;
  margin-top: 5px;
}

.pop .popBox .btn span {
  width: 50px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  border-radius: 3px;
  background: #ca4300;
  color: #fff;
  font-size: 14px;
  display: inline-block;
  cursor: pointer;
}

.consult-phone {
  position: relative;
  top: -37px;
}

.consult-pop {
  display: none;
}

.consult-pop .layout {
  background: rgba(0, 0, 0, .7);
  width: 1000px;
  height: 500px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
}

.consult-pop .popBox {
  width: 0;
  height: 0;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999999;
  background: #fff;
  border-radius: 5px;
  margin: auto;
  text-align: center;
  padding: 30px;
  opacity: 0;
}

.consult-pop .popBox b {
  font-size: 30px;
  border-bottom: 2px solid #aeaeae;
  display: inline;
  padding-bottom: 20px;
  font-weight: normal;
}

.consult-pop .popBox p {
  color: #666;
  padding: 20px 0 0 0;
  font-size: 14px;
  margin: 20px 0 0 0;
}

.consult-pop .popBox em {
  color: #ca4300;
  font-size: 12px;
  font-style: normal;
}

.consult-pop .popBox input {
  width: 185px;
  height: 45px;
  margin-top: 20px;
  margin-right: 20px;
  outline: none;
  border: 1px solid #666;
  color: #888;
  padding-left: 10px;
}

.consult-pop .popBox input#phone,
.consult-pop .popBox input#city {
  margin-right: 0;
}

.consult-pop .popBox input:focus {
  border: 1px solid #ca4300;
}

.consult-pop .popBox input.focus {
  border: 1px solid #ca4300;
}

.consult-pop .popBox textarea {
  border: 1px solid #666;
  width: 405px;
  height: 110px;
  margin-top: 20px;
  padding: 5px;
}

.consult-pop .popBox .content {
  display: none;
}

.consult-pop .popBox button {
  background: #ca4300;
  color: #fff;
  height: 45px;
  line-height: 45px;
  text-align: center;
  width: 415px;
  border: 0;
  cursor: pointer;
  margin-top: 20px;
}

.consult-pop .popBox .close {
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  right: 10px;
  font-style: normal;
}

.iso9001 .bg-half .info button:hover,
.iso9001 .consult button:hover {
  background: #cc5500;
  transition: .4s;
}

.iso9001 button.darkBtn {
  background-color: #7d7d7d !important;
}

.iso9001 button.darkBtn:hover {
  background-color: #505050 !important;
}

.animated {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.rollInLeft {
  animation-name: rollInLeft;
}

@keyframes rollInLeft {
  0% {
    opacity: 0;
    /*transform: translateX(-100%) rotateY(-180deg);*/
    transform: translateX(-100%);
  }

  100% {
    opacity: 1;
    transform: translateX(0) rotateY(0deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 10px, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.fadeInUp {
  animation-name: fadeInUp;
}