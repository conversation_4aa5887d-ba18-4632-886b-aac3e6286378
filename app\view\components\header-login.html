<% include ../common/topbar_new.html %>
<div class="navwrap">
  <div class="nav detailNav">
    <div class="navBox" id='n_nav'>
      <div>
        <div class="navImg">
          <a href="/"><img src="<%- locals.static %>/images/logo.png" alt="第三方认证检测机构"></a>
        </div>
        <div class="navList">
          <ul class="mainNav ">
            <li id="serviceHome" class="n_category">
              <span>我们的服务</span>
              <div class="dragAngel"></div>
              <div class="allNav1 clearfix" id='serviceNav'></div>
            </li>
            <li id='tradeHome' class="n_category">
              <span>您的行业</span>
              <div class="dragAngel"></div>
              <div class="allNav1" id='tradeNav'></div>
            </li>
            <li class='nav_first--li'>
              <a href="/information" rel='nofollow'>资源中心</a>
              <div class="dragAngel"></div>
              <% if(informationTypes && informationTypes.length > 0){ %>
              <dl class="nav_second">
                <% for (var item of informationTypes){ %>
                <dd>
                  <a href='/information?type=<%- item.id %>'><%- item.name %></a>
                </dd>
                <% } %>
                <dd>
                  <a href="/news">集团新闻</a>
                </dd>
                <dd>
                  <a href='/files'>资料下载</a>
                </dd>
              </dl>
              <% } %>
            </li>
            <li>
              <div class="srcoll">
                <div class="srcolling">
                  <div class="srcolling-child">
                    <span>名师讲堂</span>
                    <span>海量课程</span>
                    <span>持续上新</span>
                    <span>免费畅学</span>
                  </div>
                </div>
              </div>
              <a href="https://a.sgsonline.com.cn/" rel='nofollow'>在线讲堂</a>
            </li>
            <li>
              <a href="<%- memberUrl %>/questionnaire/step1?fromSource=navigation&fromUrl=<%- clientUrl %>" rel='nofollow' target="_blank">快速报价</a>
            </li>
            <li class="order-online">
              <a href="<%- portalHost %>/order-online" rel='nofollow' target="_blank">检测超市</a>
            </li>
            <li class='nav_first--li'>
              <a href="/overview/aboutSGS" rel='nofollow'>关于SGS</a>
              <div class="dragAngel"></div>
              <dl class="nav_second">
                <dd>
                  <a href='/overview/aboutSGS'>SGS简介</a>
                </dd>
              </dl>
            </li>
          </ul>
        </div>
      </div>
      <div class="navSearchBox" id="navSearchBox" data-path="<% if(locals.env != 'prod'){ %>http://cmsuat.sgsonline.com.cn<% }else{ %>https://www.sgsonline.com.cn<% } %>">
        <button id='searchBtn'><i class="navSearchIconIcon"></i></button>
        <input type="text" autocomplete="off" class="navSearch" id="keyWord" name="q" placeholder="" maxlength="100" />
        <div class="hotKeyword">
          <% if(hotKeyword && hotKeyword.length > 0){ %>
          <% for (var index in hotKeyword){ %>
          <% if (index < 3) { %>
          <span><%- hotKeyword[index].name %></span>
          <% } %>
          <% } %>
          <% } %>
        </div>
        <div class="linkageWord">
          <ul></ul>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="<%- locals.static %>/js/extend.js"></script>
<script>
  $(function () {
    window.addEventListener("hashchange", myFunction, false)
    function myFunction() {
      // window.location.reload()
    }
  })
</script>
