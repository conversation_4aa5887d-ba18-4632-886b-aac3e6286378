'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class NewsService extends Service {
    async getList(params) {
        if (!params) {
            params = {};
        }
        const {
            app
        } = this;
        let query = ['n.is_delete=0'];

        if (params && params.type && params.type > 0) {
            query.push('c.id=' + params.type);
        }

        if (params && params.title) {
            query.push('n.title LIKE "%' + params.title + '%"');
        }

        if (params && typeof params.is_publish == 'number') {
            query.push('n.is_publish=' + params.is_publish);
        }

        if (params.time) {
            let time = params.time.split(' - ');
            query.push('n.gmt_publish_time>="' + time[0] + '"');
            query.push('n.gmt_publish_time<"' + time[1] + '"');
        }

        let page = params.page || 1,
            limit = params.limit || 10;
        let start = (page - 1) * limit;

        const total = await app.mysql.query('SELECT n.id FROM news n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.news_id=n.id WHERE ' + query.join(' AND ') + ' GROUP BY n.id');

        const list = await app.mysql.query('SELECT n.id,n.title,n.is_top,n.is_publish,n.gmt_publish_time AS time,c.name AS typeName FROM news n LEFT JOIN catalog c ON n.catalog_id=c.id LEFT JOIN catalog_relation cr ON cr.news_id=n.id WHERE ' + query.join(' AND ') + ' GROUP BY n.id ORDER BY n.is_top DESC,n.gmt_publish_time DESC LIMIT ' + start + ',' + limit);

        for (let item of list) {
            item.time = moment(item.time).format('YYYY-MM-DD HH:mm:ss');

            let tradeList = [],
                serviceList = [];
            let tradeCata = await this.app.mysql.select('catalog_relation', {
                where: {
                    news_id: item.id,
                    catalog_type: 1
                }
            });
            let serviceCate = await this.app.mysql.select('catalog_relation', {
                where: {
                    news_id: item.id,
                    catalog_type: 2
                }
            });

            if (tradeCata && tradeCata.length > 0) {
                for (let ti of tradeCata) {
                    let tradeInfo = await this.app.mysql.get('catalog', {
                        id: ti.catalog_id
                    });
                    let tradeParantInfo = await this.app.mysql.get('catalog', {
                        id: tradeInfo.parent_id
                    });
                    //tradeList.push(tradeParantInfo.name + '-' + tradeInfo.name);
                    tradeList.push(tradeInfo.name);
                }
            }

            if (serviceCate && serviceCate.length > 0) {
                for (let si of serviceCate) {
                    let serviceInfo = await this.app.mysql.get('catalog', {
                        id: si.catalog_id
                    });
                    let serviceParantInfo = await this.app.mysql.get('catalog', {
                        id: serviceInfo.parent_id
                    });
                    //serviceList.push(serviceParantInfo.name + '-' + serviceInfo.name);
                    serviceList.push(serviceInfo.name);
                }
            }

            item.tradeList = tradeList;
            item.serviceList = serviceList;
        }

        return {
            list: list,
            total: total.length,
            page: page,
            limit: limit,
            q: params.title
        }
    }

    async getDetail(id) {
        const {
            app
        } = this;

        const detail = await app.mysql.get('news', {
            id: id
        });
        // 获取更新访问次数
        let visitsNum = detail.visits_num
        if (visitsNum) {
            visitsNum += 1
        } else {
            visitsNum = 1
        }
        // detail.visits_num = visitsNum > 999 ? '999+' : visitsNum
        const row = {
            id,
            visits_num: visitsNum
        }
        // await app.mysql.update('news', row)

        const tag = await app.mysql.select('tag_relation', {
            columns: ['id', 'sku_id', 'tag_name'],
            where: {
                'sku_id': detail.id
            },
        });

        const tradeCata = await app.mysql.select('catalog_relation', {
            where: {
                news_id: id,
                catalog_type: 1
            }
        });
        const serviceCata = await app.mysql.select('catalog_relation', {
            where: {
                news_id: id,
                catalog_type: 2
            }
        });
        const tagList = await app.mysql.select('tag_relation', {
            where: {
                news_id: id
            }
        });

        if (tradeCata && tradeCata.length > 0) {
            for (let ti of tradeCata) {
                let tradeInfo = await app.mysql.get('catalog', {
                    id: ti.catalog_id
                });
                let tradeParantInfo = await app.mysql.get('catalog', {
                    id: tradeInfo.parent_id
                });
                ti.parentName = tradeParantInfo.name;
                ti.name = tradeInfo.name;
                ti.id = tradeInfo.id;
            }
        }

        if (serviceCata && serviceCata.length > 0) {
            for (let si of serviceCata) {
                let serviceInfo = await app.mysql.get('catalog', {
                    id: si.catalog_id
                });
                let serviceParantInfo = await app.mysql.get('catalog', {
                    id: serviceInfo.parent_id
                });
                si.parentName = serviceParantInfo.name;
                si.name = serviceInfo.name;
                si.id = serviceInfo.id;
            }
        }

        if (detail.is_publish == 1) {
            detail.is_publish = true;
        } else {
            detail.is_publish = false;
        }

        if (detail.is_top == 1) {
            detail.is_top = true;
        } else {
            detail.is_top = false;
        }

        detail.tag = [];
        for (let item of tagList) {
            detail.tag.push(item.tag_name);
        }

        detail.tradeCata = tradeCata;
        detail.serviceCata = serviceCata;

        if (detail.content) {
            detail.content = detail.content.replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
        }

        return {
            detail
        };
    }

    async getNewsRelate(id) {
        const {
            app
        } = this;

        const thisInfo = await app.mysql.get('news', {
            id: id
        });

        const publishTime = moment(thisInfo.gmt_publish_time).format('YYYY-MM-DD HH:mm:ss');

        // let prevSameTimeItem = await await app.mysql.query('SELECT id,title FROM news WHERE unix_timestamp(gmt_publish_time)=unix_timestamp(?) AND is_publish=1 AND is_delete=0 AND id>? LIMIT 1', [publishTime, id]);
        // let nextSameTimeItem = await await app.mysql.query('SELECT id,title FROM news WHERE unix_timestamp(gmt_publish_time)=unix_timestamp(?) AND is_publish=1 AND is_delete=0 AND id<? LIMIT 1', [publishTime, id]);

        // let prevItem, nextItem;
        // if (prevSameTimeItem.length == 0) {
        //     prevItem = await app.mysql.query('SELECT id,title FROM news WHERE unix_timestamp(gmt_publish_time)<unix_timestamp(?) AND is_publish=1 AND is_delete=0 ORDER BY gmt_publish_time DESC,id DESC LIMIT 1', publishTime);
        // } else {
        //     prevItem = prevSameTimeItem;
        // }

        // if (nextSameTimeItem.length == 0) {
        //     nextItem = await app.mysql.query('SELECT id,title FROM news WHERE unix_timestamp(gmt_publish_time)>unix_timestamp(?) AND is_publish=1 AND is_delete=0 ORDER BY gmt_publish_time,id LIMIT 1', publishTime);
        // } else {
        //     nextItem = nextSameTimeItem;
        // }
        let prevItem = await await app.mysql.query(`select id,title, alias from news where gmt_publish_time = (select max(gmt_publish_time) from news where gmt_publish_time < '${publishTime}' AND is_delete=0 AND is_publish=1)`)
        let nextItem = await await app.mysql.query(`select id,title, alias from news where gmt_publish_time = (select min(gmt_publish_time) from news where gmt_publish_time > '${publishTime}' AND is_delete=0 AND is_publish=1)`)

        return {
            prevItem,
            nextItem
        }
    }

    async otherNews(id) {
        const {
            app
        } = this;
        const cata = await app.mysql.get('news', {
            id: id
        });
        const catalog_id = cata.catalog_id;
        const list = await app.mysql.query('SELECT id,title,content,alias FROM news WHERE is_delete=0 AND is_publish=1 AND id!=' + id + ' AND catalog_id=' + catalog_id + ' ORDER BY is_top DESC,gmt_publish_time DESC LIMIT 0,10');

        return {
            list
        }
    }

    async relatedNews(id) {
        const {
            app
        } = this;
        const cata = await app.mysql.get('news', {
            id: id
        });
        let page_keywords = ''
        if (cata.page_keywords) { // 不考虑多个关键词的情况
            // page_keywords = cata.page_keywords.split(',').map(v => {
            //     return `page_keywords like \'%${v}%\'`
            // })
            page_keywords = ' AND ' + `page_keywords like \'%${cata.page_keywords}%\'`
        } else {
            return {
                list: null
            }
        }
        const list = await app.mysql.query('SELECT id,title, alias FROM news WHERE is_delete=0 AND is_publish=1 AND id!=' + id + page_keywords + ' ORDER BY gmt_publish_time DESC LIMIT 0,6');
        return {
            list
        }
    }

    // 获取新闻分类
    async getNewsType() {
        const {
            app
        } = this
        const newsTypeList = await app.mysql.query(`select * from catalog WHERE parent_id=3 AND is_delete=0 AND is_show=1 AND is_navi=1`)
        return {
            newsTypeList
        }
    }
}

module.exports = NewsService;
