<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta http-equiv="Content-Security-Policy">
  <% if(locals.env !='prod' ){ %>
    <meta name="baidu-site-verification" content="rVqh4kcxit" />
  <% }else{ %>
    <meta name="baidu-site-verification" content="LAo7KbfttB" />
  <% } %>
  <meta name="format-detection" content="telephone=no">
  <link rel="icon" href="<%- locals.static %>/favicon.ico" type="image/x-icon" />
  <link rel="shortcut icon" href="<%- locals.static %>/favicon.ico" type="image/x-icon" />
  <title>快速报价-工业制造与材料</title>
  <meta name="keywords" content="材料检测，金属材料测试，高分子材料测试" />
  <meta name="description"
    content="SGS是国际公认的检验、鉴定、测试和认证机构，已在全国建成78个分支机构和150多间实验室，拥有15000多名训练有素的专业人员。使用SGS在线询价系统，您可3分钟提交测试需求，快速获取测试方案，实时与工程师沟通交流。" />
  <link rel="stylesheet" href="<%- locals.static %>/css/style_less.css">
  <link rel="stylesheet" href="<%- locals.static %>/css/oiq.css">
  <script>
    var STORE_URL = '<%- storeUrl %>';
    var MEMBER_URL = '<%- memberUrl %>';
    var IND_URL = '<%- indUrl %>';
  </script>
</head>

<body>
  <div class="oiq ">
    <div class="start">
      <div class="wrap">
        <header style="position: relative">
          <div class="logo">
            <img src='./../../public/images/oiq/start/logo.png' />
            <img style="margin-left: 28px;" src="./../../public/images/oiq/start/slogan.png" alt="">
          </div>

          <div class="menu" style="position: absolute; right: 0;">
            <div style="float: left;">
              <span class="user_name">您好！
                <% if(!isLogin){ %>
                  <img style="width: 24px;height: 24px;vertical-align: middle;"
                    src="./../../public/images/oiq/start/user.png" />
                  <a title="登录" href="<%- memberUrl %>/login?next_origin=www&next_path=/oiq/start"
                    rel="nofollow">请登录</a>
                  <% } else { %>
                    <a title="<%- userInfo.userNick || userInfo.userPhone || userInfo.userEmail %>" style="margin-right: 10px;"
                      href='<%- memberUrl %>/member/center' rel="nofollow">
                      <%- userInfo.userNick || userInfo.userPhone || userInfo.userEmailCopy || userInfo.userEmail %>
                    </a>
                    <% } %>
              </span>
              <span>
                <% if(!isLogin){ %>
                  <a onclick="signup('<%- memberUrl %>')" title="注册" rel="nofollow"
                    style="margin-left:10px;cursor: pointer;">注册</a>
                  <% } else { %>
                    <a id='logout' title="退出登录" rel="nofollow" style="cursor: pointer;">退出</a>
                    <% } %>
              </span>
              <span>
                <img style="width: 24px;height: 24px;vertical-align: middle;"
                  src="./../../public/images/oiq/start/quotation.png" />
                <a href="<%- memberUrl %>/quote/list" rel="nofollow">我的咨询(<span style="color: red;">
                    <%- inquirynum || 0 %>
                  </span>)</a>
              </span>
            </div>
            <em></em>
            <!-- <a href='/' target="_blank">在线商城</a>
            <em></em> -->
            <!-- <a href='/overview/aboutSGS' target="_blank">关于SGS</a>
            <em></em> -->
            <a href='javascrpit:void(0);' id='addFavorite'>收藏本站</a>
          </div>
        </header>
        <div class="content">
          <div class="step">
            <h2>快速报价-工业制造与材料</h2>
            <ul id='step'>
              <li class="active">
                <i class="icon1"></i>
                <span>简单三个步骤，3分钟提交需求</span>
              </li>
              <li>
                <i class="icon2"></i>
                <span>专业工程师服务，2小时获取方案</span>
              </li>
              <li>
                <i class="icon3"></i>
                <span>一键生成订单，进度一目了然</span>
              </li>
            </ul>
            <a href='javascrpit:void(0);'
              onclick="jumpOIQAddtionSource('<%- indUrl %>/questionnaire/step1', '?')">立即开始</a>
          </div>
          <div class="img">
            <div class="img_box" id='img_box'>
              <img src='./../../public/images/oiq/start/pic-1.png' style="opacity: 1;" />
              <img src='./../../public/images/oiq/start/pic-2.png' />
              <img src='./../../public/images/oiq/start/pic-3.png' />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="<%- locals.static %>/js/vue/axios.min.js"></script>
  <script src="<%- locals.static %>/js/jquery.min.js"></script>
  <script src="<%- locals.static %>/js/js.cookie.min.js"></script>
  <script src="<%- locals.static %>/js/common.js"></script>
  <script type="text/javascript" async src="https://bot.leadoo.com/bot/dynamic/chat.js?company=tl08dv7a"></script>
  <script type="text/javascript" async src="https://bot.leadoo.com/bot/chat.js?code=rpgbfixQ"></script>
  <script>
    $(function () {
      $("#addFavorite").on('click', function () {
        if (window.sidebar && window.sidebar.addPanel) {
          window.sidebar.addPanel(title, url, "")
        } else {
          if (window.external && ("AddFavorite" in window.external)) {
            window.external.AddFavorite(url, title)
          } else {
            if (window.opera && window.print) {
              this.title = title;
              return true
            } else {
              alert("请使用 " + (navigator.userAgent.toLowerCase().indexOf("mac") != -1 ? "Command" : "CTRL") + " + D 加入收藏")
            }
          }
        }
      });


      stepFun()
      function stepFun() {
        var step = $('#step');
        var imgBox = $("#img_box")
        var num = 0;
        step.find('li').on("click", function () {
          var index = $(this).index();
          num = index
          publicAnimate()
        });
        var sildeAnimateInterval = setInterval(function () {
          if (num < 2) {
            num++
          } else {
            num = 0
          }
          publicAnimate()
        }, 3000);

        // 移入停止动画，移出开始动画
        step.hover(function () {
          clearInterval(sildeAnimateInterval)
        }, function () {
          sildeAnimateInterval = setInterval(function () {
            if (num < 2) {
              num++
            } else {
              num = 0
            }
            publicAnimate()
          }, 3000);
        });
        function publicAnimate() {
          step.find('li').eq(num).addClass('active').siblings().removeClass('active');
          imgBox.find('img').eq(num).animate({
            opacity: 1
          }, 1000, function () {
          }).siblings().animate({
            opacity: 0
          }, 500)
        }
      }
    });
  </script>
</body>

</html>
