.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.clearfix {
  *+height: 1%;
}

ul,
li {
  list-style: none;
}

button {
  border: 0;
  outline: 0;
  cursor: pointer;
}

img {
  border: 0;
}

.wrap {
  width: 1226px;
  margin: 0 auto;
}

.bg-white {
  background: #fff;
}

.bg-gray {
  background: #f9f9f9;
}

.bg-orange {
  background: #ca4300;
}

.qichechanye * {
  margin: 0 auto;
  padding: 0;
  font-style: normal;
}

.phone {
  color: #ca4300;
  text-align: center;
}
.phone-label {
  font-size: 20px;
  /*font-weight: 600;*/
}
.phone-content {
  font-size: 30px;
  font-style: italic;
  /*font-weight: bold;*/
}

.qichechanye .banner {
  width: 100%;
  height: 374px;
  background: url("../../images/promotion/qichechanye/banner.jpg") no-repeat center center;
}

.qichechanye .banner .download-btn {
  width: 167px;
  margin: 260px 0 0 530px;
}
.qichechanye .banner .download-btn2 {
  width: 167px;
  margin: 260px 0 0 530px;
}

.qichechanye .banner .download-btn:hover {
  background: #cc5500;
}

.qichechanye .banner .from {
  height: 42px;
  line-height: 42px;
  overflow: hidden;
  width: 1100px;
}

.qichechanye .banner input,
.qichechanye .banner select,
.qichechanye .banner button {
  height: 40px;
  line-height: 40px;
  float: left;
}

.qichechanye .banner select {
  height: 42px;
  line-height: 42px;
}

.qichechanye .banner input {
  padding-left: 10px;
  border: 1px solid #ddd;
}

.qichechanye .banner input.input-big {
  width: 284px;
}

.qichechanye .banner input,
.qichechanye .banner select {
  width: 135px;
  margin-right: 14px;
}

.qichechanye .banner input:focus {
  border: 1px solid #ca4300;
  outline: none;
}

.qichechanye .banner input.focus {
  border: 1px solid #ca4300;
}

.qichechanye .banner button {
  width: 135px;
  background: #ca4300;
  color: #fff;
  border: 0;
  outline: 0;
  cursor: pointer;
  height: 42px;
  line-height: 42px;
}

.qichechanye .banner .p1 {
  text-align: center;
  color: #fff;
  font-size: 48px;
  padding-top: 100px;
}

.qichechanye .banner .phone {
  padding-top: 35px;
  padding-bottom: 35px;
}

.qichechanye .banner .p2 {
  text-align: center;
  color: #ca4300;
  font-size: 21px;
  padding-top: 35px;
  padding-bottom: 35px;
}

.qichechanye .banner .p3 {
  text-align: center;
  color: #ddd;
  font-size: 18px;
  padding-top: 30px;
}

.qichechanye .title {
  text-align: center;
  margin-bottom: 50px;
  padding-top: 60px;
}

.qichechanye .title h2 {
  font-size: 30px;
  font-weight: normal;
  margin-bottom: 20px;
  color: #000;
}

.qichechanye .title p {
  font-size: 14px;
  margin-bottom: 30px;
  margin: 0 50px 10px 50px;
  /* color: #999; */
  color: #a6a6a6;
}

.qichechanye .title span {
  width: 120px;
  height: 3px;
  display: inline-block;
  background: #e5e5e5;
}

.hotService {
  padding: 30px 0 50px 0;
}

.hotService .item {
  width: 600px;
  height: 379px;
  border: 1px solid #ddd;
  background: #fff;
}

.hotService .item.activity {
  border: 1px solid #ca4300;
}

.leftBox {
  float: left;
}

.rightBox {
  float: right;
}

.hotService .left {
  float: left;
  width: 350px;
  text-align: center;
  padding: 0 30px;
}

.hotService .left i {
  display: inline-block;
  width: 190px;
  height: 98px;
  margin: 25px 0;
}

.hotService .leftBox i {
  background: url("../../images/promotion/qichechanye/hotService/left-icon.png") no-repeat center center;
}

.hotService .rightBox i {
  background: url("../../images/promotion/qichechanye/hotService/right-icon.png") no-repeat center center;
}

.hotService .leftBox.activity i {
  background: url("../../images/promotion/qichechanye/hotService/left-icon-a.png") no-repeat center center;
}

.hotService .rightBox.activity i {
  background: url("../../images/promotion/qichechanye/hotService/right-icon-a.png") no-repeat center center;
}

.hotService .right {
  float: right;
  width: 154px;
  height: 379px;

}

.hotService .leftBox .right {
  background: url("../../images/promotion/qichechanye/hotService/left-pic.jpg") no-repeat center center;
}

.hotService .rightBox .right {
  background: url("../../images/promotion/qichechanye/hotService/right-pic.jpg") no-repeat center center;
}


.hotService h2 {
  font-weight: normal;
  color: #000;
}

.hotService button {
  background: #ca4300;
  color: #fff;
  width: 170px;
  height: 40px;
  line-height: 40px;
  margin-top: 30px;
}

.hotService button:hover {
  background: #cc5500;
}

.hotService p {
  padding: 20px 0;
  text-align: left;
  line-height: 25px;
  color: #333;
}

.hotService a {
  color: #ca4300;
}

.value {
  padding: 50px 0 0 0;
  margin-bottom: 50px;
}

.value .card {
  padding-top: 35px;
}

.value h3 {
  color: #fff;
  text-align: center;
  font-size: 30px;
  font-weight: normal;
}

.value li {
  float: left;
  background: #fff;
  padding: 30px 45px;
  width: 200px;
  margin-right: 20px;
  box-shadow: 3px 0 0 #cc5500;
  position: relative;
}

.value li:last-child {
  margin-right: 0;
}

.value .value-tit {
  border-bottom: 1px solid #ddd;
  height: 80px;
  line-height: 80px;
}

.value .value-tit b {
  color: #ca4300;
  font-size: 36px;
  font-family: Arail, Roboto, "Helvetica Neue";
}

.value .value-tit span {
  color: #000;
  font-size: 20px;
  padding-left: 15px;
}

.value p {
  font-size: 16px;
  color: #666;
  height: 60px;
  line-height: 24px;
  overflow: hidden;
  padding: 40px 0;
}

.value i {
  display: inline-block;
  position: absolute;
  width: 41px;
  height: 20px;
  background: url("../../images/promotion/qichechanye/arrow.png") no-repeat center center;
  left: 125px;
  bottom: -20px;
  z-index: 2;
  animation: value 1s infinite linear;
  opacity: 0;
}

@keyframes value {
  from {
    bottom: -20px;
    opacity: 0;
  }

  to {
    bottom: -5px;
    opacity: 1;
  }
}

.trainingCourse {
  padding-bottom: 80px;
}

.trainingCourse .img {
  float: left;
  position: relative;
}

.trainingCourse .pic {
  box-shadow: 2px 2px 2px #ddd;
  position: absolute;
  z-index: 2;
}

.trainingCourse .pic1 {
  width: 290px;
  height: 195px;
  background: url("../../images/promotion/qichechanye/trainingCourse/pic1.jpg") no-repeat center center;
  left: 30px;
  top: 200px;
}

.trainingCourse .pic2 {
  width: 250px;
  height: 177px;
  background: url("../../images/promotion/qichechanye/trainingCourse/pic2.jpg") no-repeat center center;
  left: 335px;
  top: 170px;
}

.trainingCourse .pic3 {
  width: 203px;
  height: 168px;
  background: url("../../images/promotion/qichechanye/trainingCourse/pic3.jpg") no-repeat center center;
  left: 118px;
  top: 410px;
}

.trainingCourse .pic4 {
  width: 250px;
  height: 177px;
  background: url("../../images/promotion/qichechanye/trainingCourse/pic4.jpg") no-repeat center center;
  left: 335px;
  top: 357px;
}

.trainingCourse .lists {
  float: right;
  width: 540px;
  margin-top: 30px;
}

.trainingCourse .lists li {
  border-bottom: 1px dashed #ddd;
  height: 42px;
  line-height: 42px;
}

.trainingCourse .lists span {
  display: inline-block;
  width: 7px;
  height: 7px;
  background: url("../../images/promotion/qichechanye/trainingCourse/dot.jpg") no-repeat center center;
}

.trainingCourse .lists .phone {
  position: relative;
  left: -79px;
  top: 10px;
}

.trainingCourse .lists .phone-label {
  font-size: 20px;
  /*font-weight: 600;*/
  display: inline;
  color: #ca4300;
}
.trainingCourse .lists .phone-content {
  display: inline;
  font-size: 30px;
  font-style: italic;
  color: #ca4300;
  /*font-weight: bold;*/
}

.trainingCourse .lists i {
  display: inline-block;
  width: 37px;
  height: 26px;
  background: url("../../images/promotion/qichechanye/trainingCourse/hot.gif") no-repeat center center;
  margin-left: 10px;
}

.trainingCourse a {
  padding-left: 15px;
  color: #333;
  font-size: 18px;
}

.trainingCourse a:hover {
  color: #ca4300;
}

.case {
  position: relative;
  height: 500px;
}

.case>div {
  position: absolute;
  /* border: 1px solid #d00; */
  z-index: 2;
}

.case .round {
  width: 314px;
  height: 314px;
  background: url("../../images/promotion/qichechanye/case/round.png") no-repeat center center;
  left: 456px;
  top: 50px;
  z-index: 1;
}

.case .line1 {
  width: 157px;
  height: 36px;
  background: url("../../images/promotion/qichechanye/case/line1.png") no-repeat center center;
  left: 390px;
  top: 80px;
}

.case .line2 {
  width: 157px;
  height: 36px;
  background: url("../../images/promotion/qichechanye/case/line2.png") no-repeat center center;
  right: 375px;
  top: 140px;
}

.case .line3 {
  width: 157px;
  height: 36px;
  background: url("../../images/promotion/qichechanye/case/line3.png") no-repeat center center;
  left: 380px;
  top: 255px;
}

.case .line4 {
  width: 157px;
  height: 36px;
  background: url("../../images/promotion/qichechanye/case/line4.png") no-repeat center center;
  right: 415px;
  top: 310px;
}

.case .text1 {
  left: 110px;
  top: 30px;
}

.case .text2 {
  right: 90px;
  top: 85px;
}

.case .text3 {
  left: 80px;
  top: 250px;
}

.case .text4 {
  right: 100px;
  top: 270px;
}

.case li {
  height: 30px;
  line-height: 30px;
}

.case span {
  display: inline-block;
  width: 4px;
  height: 4px;
  background: #ca4300;
}

.case a {
  color: #666;
  padding-left: 5px;
  cursor: pointer;
  font-size: 18px;
  color: #333;
}

.case a:hover {
  color: #ca4300;
}

.auditServices {
  position: relative;
  height: 500px;
}

.auditServices .phone {
  text-align: left;
  position: absolute;
  bottom: 66px;
  left: 224px;
}

.auditServices>div {
  position: absolute;
}

.auditServices .a {
  width: 208px;
  height: 167px;
  text-align: center;
}

.auditServices .b {
  width: 196px;
  height: 159px;
}

.auditServices .c {
  width: 104px;
  height: 84px;
}

.auditServices .d {
  width: 57px;
  height: 45px;
}

.auditServices .e {
  width: 44px;
  height: 36px;
}

.auditServices a {
  color: #ca4300;
}

.auditServices .active a {
  color: #fff;
}

.auditServices .a.active {
  background: url("../../images/promotion/qichechanye/auditServices/pic1.png") no-repeat center center;
}

.auditServices .box1 {
  background: url("../../images/promotion/qichechanye/auditServices/pic4.png") no-repeat center center;
  left: 270px;
  top: 0;
}

.auditServices .box2 {
  background: url("../../images/promotion/qichechanye/auditServices/pic2.png") no-repeat center center;
  left: 600px;
  top: 0;
}

.auditServices .box3 {
  background: url("../../images/promotion/qichechanye/auditServices/pic3.png") no-repeat center center;
  left: 435px;
  top: 84px;
}

.auditServices .box4 {
  background: url("../../images/promotion/qichechanye/auditServices/pic3.png") no-repeat center center;
  left: 765px;
  top: 84px;
}

.auditServices .box5 {
  background: url("../../images/promotion/qichechanye/auditServices/pic2.png") no-repeat center center;
  left: 270px;
  top: 175px;
}

.auditServices .box6 {
  background: url("../../images/promotion/qichechanye/auditServices/pic4.png") no-repeat center center;
  left: 600px;
  top: 175px;
}

.auditServices .box7 {
  background: url("../../images/promotion/qichechanye/auditServices/pic4.png") no-repeat center center;
  left: 435px;
  top: 260px;
}

.auditServices .box8 {
  background: url("../../images/promotion/qichechanye/auditServices/pic2.png") no-repeat center center;
  left: 765px;
  top: 260px;
}

.auditServices .box9 {
  background: url("../../images/promotion/qichechanye/auditServices/pic10.png") no-repeat center center;
  left: 560px;
  top: 42px;
}

.auditServices .box10 {
  background: url("../../images/promotion/qichechanye/auditServices/pic7.png") no-repeat center center;
  left: 210px;
  top: 130px;
}

.auditServices .box11 {
  background: url("../../images/promotion/qichechanye/auditServices/pic5.png") no-repeat center center;
  left: 70px;
  top: 150px;
}

.auditServices .box12 {
  background: url("../../images/promotion/qichechanye/auditServices/pic9.png") no-repeat center center;
  left: 927px;
  top: 233px;
}

.auditServices .box13 {
  background: url("../../images/promotion/qichechanye/auditServices/pic6.png") no-repeat center center;
  left: 975px;
  top: 147px;
}

.auditServices .box14 {
  background: url("../../images/promotion/qichechanye/auditServices/pic8.png") no-repeat center center;
  left: 388px;
  top: 348px;
}

.auditServices .a.active span,
.auditServices .a.active p {
  color: #fff;
}

.auditServices span {
  display: inline-block;
  color: #333;
  font-size: 16px;
  padding: 30px 0 20px 0;
  font-weight: 700;
}

.auditServices p {
  line-height: 20px;
  font-size: 14px;
  color: #666;
}

.bg-bg {
  background: url("../../images/promotion/qichechanye/manageService/bg.jpg") no-repeat center center;
  height: 700px;
}

.bg-bg .title h2 {
  color: #fff;
}

.bg-bg .title p {
  color: #ddd;
}

.manageService .card {
  float: left;
  width: 390px;
  background: rgba(0, 0, 0, .4);
  color: #fff;
  text-align: center;
  padding-bottom: 40px;
  margin-right: 20px;
}

.manageService .card:hover {
  background: rgba(0, 0, 0, .2);
}

.manageService .card:last-child {
  margin-right: 0;
}


.manageService i {
  display: inline-block;
  width: 69px;
  height: 61px;
  margin-top: 50px;
}

.manageService .card1 i {
  background: url("../../images/promotion/qichechanye/manageService/pic1.png") no-repeat center center;
}

.manageService .card2 i {
  background: url("../../images/promotion/qichechanye/manageService/pic2.png") no-repeat center center;
}

.manageService .card3 i {
  background: url("../../images/promotion/qichechanye/manageService/pic3.png") no-repeat center center;
}

.manageService .card1:hover i {
  background: url("../../images/promotion/qichechanye/manageService/pic1-a.png") no-repeat center center;
}

.manageService .card2:hover i {
  background: url("../../images/promotion/qichechanye/manageService/pic2-a.png") no-repeat center center;
}

.manageService .card3:hover i {
  background: url("../../images/promotion/qichechanye/manageService/pic3-a.png") no-repeat center center;
}

.manageService h3 {
  margin-top: 40px;
  font-weight: normal;
  font-size: 20px;
  border-bottom: 2px solid #fff;
  padding-bottom: 20px;
  display: inline-block;
}

.manageService p {
  line-height: 24px;
  padding: 30px 0;
  font-size: 16px;
}

.manageService button {
  border: 1px solid #ca4300;
  color: #ca4300;
  background: transparent;
  font-size: 16px;
  width: 150px;
  height: 45px;
  line-height: 45px;
}

.manageService button:hover {
  background: #ca4300;
  color: #fff;
}

.carService {
  padding-bottom: 80px;
}

.carService .list {
  float: left;
  padding-left: 50px;
}

.carService .list li {
  border-bottom: 1px dashed #ddd;
  height: 42px;
  line-height: 42px;
  width: 300px;
}

.carService .list span {
  float: left;
  display: block;
  width: 7px;
  height: 42px;
  background: url("../../images/promotion/qichechanye/trainingCourse/dot.jpg") no-repeat center center;
}

.carService .list a {
  display: block;
  float: left;
  color: #333;
  padding-left: 10px;
  width: 250px;
  height: 30px;
  overflow: hidden;
  font-size: 18px;
}

.carService .list a:hover {
  color: #ca4300;
}

.carService .list em {
  display: block;
  float: right;
  color: #ca4300;
}

.carService button {
  background: #ca4300;
  color: #fff;
  width: 150px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  margin: 50px 0 0 20px;
  font-size: 16px;
}

.carService .img {
  float: right;
  position: relative;
}

.carService .img div {
  box-shadow: 3px 3px 3px #ddd;
}

.carService .box1 {
  width: 452px;
  height: 358px;
  background: url("../../images/promotion/qichechanye/carService/pic1.jpg") no-repeat center center;
  position: absolute;
  right: 100px;
  top: 0;
}

.carService .box2 {
  width: 241px;
  height: 259px;
  background: url("../../images/promotion/qichechanye/carService/pic2.jpg") no-repeat center center;
  position: absolute;
  right: 365px;
  top: 70px;
}

.advantage {
  padding-bottom: 50px;
  height: 270px;
}

.advantage li {
  float: left;
  border-left: 1px solid #ddd;
  padding: 0 30px;
  height: 270px;
  width: 245px;
}

.advantage li:last-child {
  border-right: 1px solid #ddd;
}

.advantage h3 {
  color: #ca4300;
  font-size: 40px;
  height: 55px;
  line-height: 55px;
}

.advantage h3 em {
  font-family: Arail, Roboto, "Helvetica Neue";
}

.advantage span {
  color: #333;
  font-size: 20px;
  display: block;
  padding: 25px 0;
  font-weight: bold;
}

.advantage p {
  color: #666;
  line-height: 24px;
  font-size: 16px;
}

.valueService {
  padding-bottom: 100px;
}

.valueService li {
  float: left;
  margin-right: 37px;
}

.valueService li:last-child {
  margin-right: 0;
}

.valueService .info:hover {
  background: url("../../images/promotion/qichechanye/valueService/bg-a.png") no-repeat center center;
}

.valueService .info {
  width: 215px;
  height: 215px;
  background: url("../../images/promotion/qichechanye/valueService/bg.png") no-repeat center center;
  text-align: center;
}

.valueService i {
  display: inline-block;
  width: 83px;
  height: 60px;
  margin: 40px 0 15px 0;
}

.valueService i.icon1 {
  background: url("../../images/promotion/qichechanye/valueService/icon-1.png") no-repeat center center;
}

.valueService i.icon2 {
  background: url("../../images/promotion/qichechanye/valueService/icon-2.png") no-repeat center center;
}

.valueService i.icon3 {
  background: url("../../images/promotion/qichechanye/valueService/icon-3.png") no-repeat center center;
}

.valueService i.icon4 {
  background: url("../../images/promotion/qichechanye/valueService/icon-4.png") no-repeat center center;
}

.valueService i.icon5 {
  background: url("../../images/promotion/qichechanye/valueService/icon-5.png") no-repeat center center;
}

.valueService p {
  color: #fff;
  line-height: 24px;
  font-size: 16px;
}

.valueService .shadow {
  width: 215px;
  height: 22px;
  background: url("../../images/promotion/qichechanye/valueService/shadow.png") no-repeat center center;
  margin-top: 20px;
}

.moreService {
  padding-bottom: 50px;
}

.moreService li {
  width: 285px;
  margin-right: 25px;
  float: left;
}

.moreService li:last-child {
  margin-right: 0;
}

.moreService dt {
  color: #000;
  border-bottom: 2px solid #ca4300;
  height: 35px;
  line-height: 35px;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 700;
}

.moreService dd {
  border-bottom: 1px dashed #ddd;
  height: 35px;
  line-height: 35px;
  font-size: 15px;
}

.moreService dd a {
  color: #333;
  display: block;
  width: 100%;
  height: 36px;
  line-height: 36px;
  overflow: hidden;
}

.moreService a:hover {
  color: #ca4300;
}

.moreService .more {
  color: #ca4300;
  float: right;
  margin-top: 15px;
}

.dwon {
  padding-bottom: 100px;
}

.dwon li {
  float: left;
  background: url("../../images/promotion/qichechanye/down.png") no-repeat left center;
  width: 25%;
}

.dwon a {
  color: #666;
  padding-left: 30px;
  font-size: 16px;
  line-height: 36px;
}

.phone-message {
  position: relative;
  top: -30px;
}

.pageMessage {
  padding-bottom: 50px;
  text-align: center;
}

.pageMessage p {
  color: #ca4300;
  font-size: 16px;
  padding: 0 0 20px 0;
}

.pageMessage .from {
  padding-bottom: 20px;
  height: 42px;
  line-height: 42px;
  overflow: hidden;
  width: 1100px;
}

.pageMessage input,
.pageMessage select,
.pageMessage button {
  height: 40px;
  line-height: 40px;
  float: left;
  border: 1px solid #ddd;
}

.pageMessage input {
  padding-left: 10px;
}

.pageMessage input.input-big {
  width: 284px;
}

.pageMessage input,
.pageMessage select {
  width: 135px;
  margin-right: 14px;
}

.pageMessage select {
  height: 42px;
  line-height: 42px;
}

.pageMessage input:focus {
  border: 1px solid #ca4300;
  outline: none;
}

.pageMessage input.focus {
  border: 1px solid #ca4300;
}

.pageMessage button {
  width: 135px;
  background: #ca4300;
  color: #fff;
  border: 0;
  outline: 0;
  cursor: pointer;
  height: 42px;
  line-height: 42px;
}

.pageMessage span {
  font-size: 14px;
  color: #666;
  padding: 20px;
}

/* 弹窗 */
.pop {
  display: none;
}

.pop .layout {
  background: rgba(0, 0, 0, .7);
  width: 1000px;
  height: 500px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 11;
}

.pop .popBox {
  width: 300px;
  height: 160px;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 12;
  background: #fff;
  border-radius: 5px;
  margin: auto;
}

.pop .popBox .tit {
  text-align: left;
  background: #f5f5f5;
  padding-left: 20px;
  font-size: 14px;
  border-bottom: 1px solid #ddd;
  height: 35px;
  line-height: 35px;
  margin: 0;
  border-radius: 3px 3px 0 0;
}

.pop .popBox .cont {
  text-indent: 2em;
  font-size: 14px;
  padding: 20px;
}

.pop .popBox .btn {
  text-align: center;
  margin-top: 5px;
}

.pop .popBox .btn span {
  width: 50px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  border-radius: 3px;
  background: #ca4300;
  color: #fff;
  font-size: 14px;
  display: inline-block;
  cursor: pointer;
}

.pop .imgBox {
  width: 80%;
  height: 80%;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 13;
  margin: auto;
  text-align: center;
}

.pop .imgBox img {
  max-width: 100%;
  max-height: 100%;
}

.pop .close {
  position: fixed;
  right: 20px;
  top: 20px;
  color: #fff;
  z-index: 14;
  font-size: 15px;
  cursor: pointer;
  border: 1px solid #fff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
}

.down-load-pop {
  display: none;
}
.down-load-pop .layout {
  background: rgba(0, 0, 0, .7);
  width: 1000px;
  height: 500px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
}
.down-load-pop .popBox {
  width: 0;
  height: 0;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999999;
  background: #fff;
  border-radius: 5px;
  margin: auto;
  text-align: center;
  padding: 30px;
  opacity: 0;
}
.down-load-pop .popBox b {
  font-size: 30px;
  border-bottom: 2px solid #aeaeae;
  display: inline;
  padding-bottom: 20px;
  font-weight: normal;
}
.down-load-pop .popBox p {
  color: #666;
  padding: 20px 0 0 0;
  font-size: 14px;
  margin: 20px 0 0 0;
}
.down-load-pop .popBox em {
  color: #ca4300;
  font-size: 12px;
  font-style: normal;
}
.down-load-pop .popBox input:not([type="checkbox"]) {
  width: 348px;
  height: 48px;
  margin-top: 20px;
  outline: none;
  border: 1px solid #666;
  color: #888;
  padding-left: 10px;
  box-sizing: border-box;
}
.down-load-pop .popBox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  vertical-align: bottom;
}
.down-load-pop .popBox input#phone, .down-load-pop .popBox input#city {
  margin-right: 0;
}
.down-load-pop .popBox input:focus {
  border: 1px solid #ca4300;
}
.down-load-pop .popBox input.focus {
  border: 1px solid #ca4300;
}
.down-load-pop .popBox textarea {
  border: 1px solid #666;
  width: 405px;
  height: 110px;
  margin-top: 20px;
  padding: 5px;
}
.down-load-pop .popBox .content {
  display: none;
}
.down-load-pop .popBox button {
  background: #ca4300;
  color: #fff;
  height: 48px;
  line-height: 48px;
  text-align: center;
  width: 348px;
  border: 0;
  cursor: pointer;
  margin-top: 20px;
  font-size: 16px;
}
.down-load-pop .popBox .close {
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  right: 10px;
  font-style: normal;
}


