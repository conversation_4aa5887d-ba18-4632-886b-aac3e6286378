$(function () {

    var $_searchAll = $('.searchAll');
    $_searchAll.click(function () {
        $(this).addClass("clickActive").siblings(".searchAll").removeClass("clickActive");
        $(this).children(".angle").addClass("angleShow").end().siblings(".searchAll").children(".angle").removeClass("angleShow");
    })

    var $_searchRelativeC = $(".searchRelativeC");
    $_searchRelativeC.click(function () {
        $(this).addClass("clickActive").siblings(".searchRelativeC").removeClass("clickActive");
    })

    var $_drag = $('.dragbut-icon');
    var $_yours = $(".yours");

    $_yours.click(function () {

        var $_con = $(this).siblings(".your-cons");

        // for(var i=0;i<$_drag.length;i++){
        //     if($_drag.eq(i).attr("src") == "./images/"){
        //
        //     }
        // }

        if($_con.css("height")=="0px"){
            $_con.animate({"height":486}).end().parents(".your").siblings(".your").children(".your-cons").animate({"height":0});
            $(this).find(".dragbut-icon").attr("src",'./images/jianhao.png');
            $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(0).attr("src","./images/jiahao.png");
            $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(1).attr("src","./images/jiahao.png")
        }else{
            $(this).find(".dragbut-icon").attr("src",'./images/jiahao.png');
            $_con.animate({"height":0});
        }
    })
})
