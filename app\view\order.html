<% include header.html %>
<!-- 面包屑 -->
<style>
  body,
  html {
    background: #f5f5f5;
  }
</style>
<div class="myLocation">
  <div class="myLocationBox">
    <div class="myLocation-word1"><a href="/">第三方检测机构</a></div>
    <div class="myLocation-icon1"></div>
    <div class="myLocation-word1"><a href="javascrpit:void(0);">订单查询</a></div>
    <div class="myLocation-icon1"></div>
    <div class="myLocation-word1"><a href="javascrpit:void(0);"><%- detail.name %></a></div>
  </div>
</div>
<div class="SearchBox">
  <div class="searchleftBox orderList">
    <header class='orderList__header'>
      <h2>
        <%- detail.name %>
      </h2>
      <p>（仅支持SGS全国纺织品鞋类实验室测试订单查询）</p>
      <div class='orderList__header__search'>
        <input placeholder="请输入您的运单号或订单号" id='packageNo' />
        <button id='handleSearch'>点击查询</button>
      </div>
      <div class='orderList__header__link'>
        <span>
          您可以访问<a href='/industry/textile/' target="_blank">纺织行业板块</a>，了解更多服务咨询。
        </span>
        <span>
          其他实验室订单查询，敬请期待。
        </span>
      </div>
    </header>
    <section class="orderList__status">
    </section>
    <div id="itemBox"></div>
  </div>
  <!-- 右侧边栏 -->
  <div class="searchrightBox" style="background: #fff;">
    <div class="searchrightBox1">
      <div class="your">
        <div class="yours">
          <span class="your-word">行业解决方案</span>
          <span class="dragbut">
            <img src="<%- locals.static %>/images/jianhao.png" alt="" class="dragbut-icon">
          </span>
        </div>
        <div class="your-cons first">
          <div class="yourcon">
            <ul>
              <% if(tradeNavi && tradeNavi.length > 0){ %>
              <% for (var item of tradeNavi){ %>
              <li class="yourconLi">
                <a href="/industry/<%- item.alias %>/">
                  <%- item.name %></a>
              </li>
              <% } %>
              <% } %>
            </ul>
          </div>
        </div>
      </div>
      <div class="your">
        <div class="yours">
          <span class="your-word">我们的服务</span>
          <span class="dragbut">
            <img src="<%- locals.static %>/images/jiahao.png" alt="" class="dragbut-icon">
          </span>
        </div>
        <div class="your-cons">
          <div class="yourcon">
            <ul>
              <% if(serviceNavi && serviceNavi.length > 0){ %>
              <% for (var item of serviceNavi){ %>
              <li class="yourconLi">
                <a href="/service/<%- item.alias %>/">
                  <%- item.name %></a>
              </li>
              <% } %>
              <% } %>
            </ul>
          </div>
        </div>
      </div>
      <div class="your">
        <div class="yours">
          <span class="your-word">热点服务</span>
          <span class="dragbut">
            <img src="<%- locals.static %>/images/jiahao.png" alt="" class="dragbut-icon">
          </span>
        </div>
        <div class="your-cons">
          <div class="yourcon">
            <ul>
              <% if(hot && hot.length > 0){ %>
              <% for (var item of hot){ %>
              <li class="yourconLi">
                <a href="/sku/<%- item.alias %>/<%- item.id %>/">
                  <%- item.name %></a>
              </li>
              <% } %>
              <% } %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  $(function () {
   
    $("#handleSearch").on("click", function () {
      var packageNo = $("#packageNo").val().trim()
      $(this).prop('disabled', true)
      $(this).addClass('disabled')
      if (!packageNo) {
        alert('请输入运单号或订单号')
      } else {
        getList(packageNo)
      }
    })

    // 获取订单数据
    function getList(packageNo) {
      var param = {
        packageNo: packageNo
      };
      $.ajax({
        type: 'POST',
        url: '/qryPackage',
        data: param,
        success: function (res) {
          var data = {};
          if (typeof res !== 'object') {
            data = JSON.parse(res)
          } else {
            data = res
          }
          if (data.resultCode == '0') {
            createDom(data.data)
          } else {
            alert(data.resultMsg)
          }
        },
        fail: function (data) {
          alert(data.resultMsg)
        },
        complete: function (complete) {
          $('#handleSearch').removeClass('disabled')
          $('#handleSearch').prop('disabled', false)
        }
      })

    }

    function createDom(datas) {
      var statusDemo = ''
      var itemDemo = '';
      var packageNo = $("#packageNo").val().trim()
      if (datas.type == 2) {
        statusDemo =
          '<ul>' +
          '<li>' +
          '<span>运单号：</span>' +
          '<em>' + packageNo + '</em>' +
          '</li>' +
          '<li>' +
          '<span>包裹状态：</span>' +
          '<em>' + datas.packageState + '</em>' +
          '</li>' +
          '<li>' +
          '<span>包裹签收人：</span>' +
          '<em>' + datas.receiveName + '</em>' +
          '</li>' +
          '<li>' +
          '<span>联系方式：</span>' +
          '<em>' + datas.receivePhone + '</em>' +
          '</li>' +
          '</ul>';
        if (datas.packageState === '已签收') {
          statusDemo += '<div id="orderList__status-icon"></div>';
        } else {
          statusDemo += '<div id="orderList__status-icon" class="disabled"></div>';
        }
        $('.orderList__status').html(statusDemo);
        $('.orderList__status').show();
      } else {
        $('.orderList__status').html('');
        $('.orderList__status').hide();
      }
      if (datas.items.length) {
        var isFinal = true
        datas.items.forEach(function (item) {
          if (!item.orderNo) isFinal = false
          itemDemo +=
            "<section class='orderList__item'>" +
            "<header class='orderList__item__header'>" +
            "<ul>" +
            "<li>";
          if (!item.caseNo) {
            itemDemo += "<span>订单申请中</span>";
          } else {
            itemDemo += "<span>订单编号：</span>";
            if (item.orderNo) {
              itemDemo += "<em>" + item.orderNo + "</em>";
            } else {
              itemDemo += "<em>--</em>";
            }
          }
          var state = '';
          if (item.reportState === 'SoftCopyDelivered' || item.reportState === 'HardCopyDelivered' || item.reportState === 'Reworked' || item.reportState === 'Revised') {
            state = '已完成'
          } else {
            state = item.orderState
          }
          itemDemo += "</li>" +
            "<li>" +
            "<span>订单状态：</span>" +
            "<em>" + state + "</em>" +
            "</li>";
          if (item.CSName) {
            itemDemo += "<li class='orderList__item__header--half CSName'>" +
              "<span>订单处理人：</span>" +
              "<em>" + item.CSName + "</em>" +
              "</li>";
          }
          if (item.CSTelephone) {
            itemDemo += "<li class='orderList__item__header--half CSTelephone'>" +
              "<span>处理人电话：</span>" +
              "<em>" + item.CSTelephone + "</em>" +
              "</li>";
          }
          if (item.CSEmail) {
            itemDemo += "<li class='orderList__item__header--half CSEmail'>" +
              "<span>订单处邮件：</span>" +
              "<em>" + item.CSEmail + "</em>" +
              "</li>";
          }
          itemDemo += "<li class='orderList__item__header--full'>" +
            "<span>订单备注：</span>";
          if (item.remark) {
            itemDemo += "<em>" + item.remark + "</em>";
          } else {
            itemDemo += "<em>--</em>";
          }
          itemDemo += "</li>" +
            "</ul>" +
            "<div class='hide' id='handleToggle'>展开</div>" +
            "</header >" +
            "<section class='orderList__item__step'>";
          // 1.包裹已处理 2.受理中 3.测试中 4.已完成  5.取消
          // status1 未完成 status2 进行中 status3完成
          // NEW("已受理", "New"), CLOSE("已完成 ", "Close"), CANCEL("已取消", "Cancel"), PENDING("暂缓处理", "Pending"), TESTING("测试中", "Testing"), REPORTING("测试中", "Reporting")
          var step = 0;

          if (item.orderState === '包裹处理中' || item.orderState === '包裹暂缓处理') {
            step = 1;
          } else if (item.orderState === '受理中' || item.orderState === '暂缓处理' || item.orderState === '已受理') {
            step = 2;
          } else if (item.orderState === '测试中') {
            step = 3;
          } else if (item.orderState === '已完成') {
            step = 4;
          } else if (item.orderState === '已取消') {
            step = 5;
          }
          if (item.reportState === 'SoftCopyDelivered' || item.reportState === 'HardCopyDelivered' || item.reportState === 'Reworked' || item.reportState === 'Revised') {
            step = 4;
          }
          if (step === 1) {
            itemDemo += "<div class='step step1 status2'>" +
              "<i></i>" +
              "<span>拆分中</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step2 status1'>" +
              "<i></i>" +
              "<span>未受理</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step3 status1'>" +
              "<i></i>" +
              "<span>未测试</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step4 status1'>" +
              "<i></i>" +
              "<span>未完成</span>" +
              "</div>";
          } else if (step === 2) {
            itemDemo += "<div class='step step1 status3'>" +
              "<i></i>" +
              "<span>已拆分</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step2 status2'>" +
              "<i></i>" +
              "<span>受理中</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step3 status1'>" +
              "<i></i>" +
              "<span>未测试</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step4 status1'>" +
              "<i></i>" +
              "<span>未完成</span>" +
              "</div>";
          } else if (step === 3) {
            itemDemo += "<div class='step step1 status3'>" +
              "<i></i>" +
              "<span>已拆分</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step2 status3'>" +
              "<i></i>" +
              "<span>已受理</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step3 status2'>" +
              "<i></i>" +
              "<span>测试中</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step4 status1'>" +
              "<i></i>" +
              "<span>未完成</span>" +
              "</div>";
          } else if (step === 4) {
            itemDemo += "<div class='step step1 status3'>" +
              "<i></i>" +
              "<span>已拆分</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step2 status3'>" +
              "<i></i>" +
              "<span>已受理</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step3 status3'>" +
              "<i></i>" +
              "<span>已测试</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step4 status3'>" +
              "<i></i>" +
              "<span>已完成</span>" +
              "</div>";
          } else if (step === 5) {
            itemDemo += "<div class='step step1 status1'>" +
              "<i></i>" +
              "<span>未拆分</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step2 status1'>" +
              "<i></i>" +
              "<span>未受理</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step3 status1'>" +
              "<i></i>" +
              "<span>未测试</span>" +
              "</div>" +
              "<div class='stepLine'>" +
              "<em></em>" +
              "</div>" +
              "<div class='step step4 status1'>" +
              "<i></i>" +
              "<span>未完成</span>" +
              "</div>";
          }
          itemDemo += "</section>" +
            "<section class='orderList__item__option'>" +
            "<ul>" +
            "<li class='orderList__item__option--full'>" +
            "<span class='name'>样品名称：</span>";
          if (item.SampleName) {
            itemDemo += "<em>" + item.SampleName + "</em>";
          } else {
            itemDemo += "<em>--</em>";
          }
          itemDemo += "</li>" +
            "<li>" +
            "<span class='localtion'>所属实验室：</span>";
          if (item.labName) {
            itemDemo += "<em>" + item.labName + "</em>";
          } else {
            itemDemo += "<em>--</em>";
          }
          itemDemo += "</li>" +
            "<li>" +
            "<span class='date'>预期交付时间：</span>";
          if (item.ExpectDueDate) {
            itemDemo += "<em>" + item.ExpectDueDate.substr(0, 10) + "</em>";
          } else {
            itemDemo += "<em>--</em>";
          }
          itemDemo += "</li>" +
            "</ul>" +
            "</section>" +
            "</section>";
        })
        if (isFinal) $('.orderList__status').find("#orderList__status-icon").addClass('final');
      } else {
        itemDemo = ''
      }
      $('#itemBox').html(itemDemo);
    }
    // sidebar toggle
    var $_yours = $(".yours");
    $_yours.click(function () {
      var $_con = $(this).siblings(".your-cons");
      if ($_con.css("height") == "0px") {
        $_con.animate({
          "height": 486
        }).end().parents(".your").siblings(".your").children(".your-cons").animate({
          "height": 0
        });
        $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jianhao.png');
        $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(0).attr("src",
          "<%- locals.static %>/images/jiahao.png");
        $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(1).attr("src",
          "<%- locals.static %>/images/jiahao.png")
      } else {
        $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jiahao.png');
        $_con.animate({
          "height": 0
        });
      }
    })

    // order list toggle
    $("#itemBox").on('click', '#handleToggle', function () {
      if ($(this).hasClass('show')) {
        $(this).removeClass('show').addClass('hide')
        $(this).html('展开')
        $(this).closest('.orderList__item').find('.orderList__item__step, .orderList__item__option').hide();
      } else {
        $(this).removeClass('hide').addClass('show')
        $(this).html('收起')
        $(this).closest('.orderList__item').find('.orderList__item__step, .orderList__item__option').show();
      }
    })
  })
</script>

<% include footer.html %>