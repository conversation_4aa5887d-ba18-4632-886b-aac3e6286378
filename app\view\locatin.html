<% include header.html %>
<% include ./components/header.html %>

<div class="contact_v2">
  <div class="contact_main1">
    <div class="contact_tab">
      <ul>
        <li>
          <a href='/quote'>咨询报价</a>
        </li>
        <!-- <li>
          <a href='/contact'>建议反馈</a>
        </li> -->
        <!-- <li class="activity">
          <a href='/locatin'>服务网点</a>
        </li> -->
        <li>
          <a href='/hotline'>客服电话</a>
        </li>
      </ul>
    </div>
    <div class="contact_locatin">
      <img class="liucheng-step-img" src="../public/images/locatin.png" alt="" style="margin-right: 55px;">
      <div class="contact-center">
        <div class="red-line" style="margin-bottom: 15px;">
          <span>北京总部</span> <br>
          <span>通标标准技术服务有限公司</span>
        </div>
        <div style="padding-left: 15px;font-size: 14px;color: #333;margin-bottom: 30px;">
          地址: 北京市海淀区阜成路73号世纪裕惠大厦16层
          <br>
          邮编: 100142
        </div>
        <div class="red-line" style="margin-bottom: 10px;">各地联系地址：</div>
        <div style="padding-left: 15px;">
          <select class="select2" id="addrSelect" onchange="changeAddr(this)" style="width:320px;float:left;">
            <option value="sh">上海市</option>
            <option value="gd">广东省</option>
            <option value="gx">广西壮族自治区</option>
            <option value="ah">安徽省</option>
            <option value="fj">福建省</option>
            <option value="hb">河北省</option>
            <option value="hn">海南省</option>
            <option value="hn2">河南省</option>
            <option value="hb2">湖北省</option>
            <option value="hn3">湖南省</option>
            <option value="jl">吉林省</option>
            <option value="js">江苏省</option>
            <option value="jx">江西省</option>
            <option value="ln">辽宁省</option>
            <option value="sd">山东省</option>
            <option value="sx">陕西省</option>
            <option value="sc">四川省</option>
            <option value="tj">天津市</option>
            <option value="xj">新疆维吾尔自治区</option>
            <option value="yn">云南省</option>
            <option value="zj">浙江省</option>
            <option value="cq">重庆市</option>
            <option value="xg">香港</option>
          </select>
        </div>
      </div>
      <div class="contact-right" id="addrShow" style="flex: 1;">

      </div>
    </div>
  </div>
  <div class="contact_ads"></div>
</div>

<style>
  body {
    background-color: #f5f5f5
  }

  .myLocation {
    margin-bottom: 20px;
  }

  .searchleftBox {
    background-color: #FFF;
    width: 796px;
    padding: 0 30px;
  }

  .searchrightBox {
    background-color: #FFF
  }

  .selectBox {
    width: 100%
  }

  .select,
  .selectC {
    width: 240px
  }

  .yoursInfoCC {
    width: 372px;
  }

  .yoursInfoCC .selectC {
    width: 372px
  }

  ul.select2-results__options {
    max-height: 300px;
  }
</style>
<script src="<%- locals.static %>/js/addr.js"></script>
<script src="<%- locals.static %>/js/select2.full.min.js"></script>
<script>
  $('#addrSelect').trigger('change');
  $('.citys a').click(function () {

    var con = $(this).data('name');
    $('.country').val(con);
    $('.provice').css({ 'display': 'none' });
  })

  // $('.country').blur(function () {
  //     $('.provice').css({'display':'none'});
  // })

  $(document).click(function (e) {
    e = e || window.event;
    var o = e.target || e.srcElement;
    if ($(o).attr('class') != 'selectC country') {
      $('.provice').css({ 'display': 'none' });

    }
  })

  $('.country').focus(function () {
    $('.provice').css({ 'display': 'block' });

  })



  $('.country').bind('input propertychange', function () {
    var $_this = $(this);
    if ($(this).val() != '') {
      $('.citys a').css({ 'width': '100%', "display": 'none' });
      $('.citys a').each(function (index, value) {

        if ($(value).data('qp').indexOf($_this.val()) >= 0) {

          $(value).css({ "display": 'block' });
        } else if ($(value).data('name').indexOf($_this.val()) >= 0) {
          $(value).css({ "display": 'block' });

        } else if ($(value).data('jp').indexOf($_this.val()) >= 0) {
          $(value).css({ "display": 'block' });

        }

      })
    } else {
      $('.citys a').css({ 'width': '129px', 'display': 'inline-block' });
    }

  })

  var $select = $(".select");
  $('.filter-select,.select2').select2({ minimumResultsForSearch: -1 });

  $(".acceptIcon").click(function () {
    // console.log($(this).attr('class').split(' '));
    var classNames = $(this).attr('class').split(' ');
    if (classNames.indexOf("active") > -1) {
      $(this).removeClass('active');

    } else if (classNames.indexOf("active") < 0) {
      $(this).addClass('active');
      $('.submit').siblings('.error').css({ 'visibility': 'hidden' });
    }
  })

  $('.select2-selection--single').click(function () {
    $(this).css({ 'borderColor': '#eeeeee' }).parents('.selectC').siblings('.error').css({ 'visibility': 'hidden' });
  })

  $('#textarea').focus(function () {
    $(this).css({ 'borderColor': '#eeeeee' }).siblings('.error').css({ 'visibility': 'hidden' });
  })

  $('input.selectC').focus(function () {
    $(this).css({ 'borderColor': '#eeeeee' }).siblings('.error').css({ 'visibility': 'hidden' });
  })

  function changeAddr(obj) {
    var v = $(obj).val();
    var html = '';
    $.each(addrs, function (i, item) {
      if (item.code == v) {
        $.each(item.list, function (n, node) {
          html += '<div class="red-line" style="margin-bottom: 10px;">' + node.name + '</div><ul style="padding-left: 15px">';
          html += '<li>地址: ' + node.addr + '</li>'
          if (node.postcode) {
            html += '<li>邮编: ' + node.postcode + '</li>';
          }
          html += '</ul>'
        });

        $('#addrShow').html(html);
      }
    })
  }
</script>

<% include footer.html %>
