body,html{margin: 0;padding: 0;color: #333; font-family:'微软雅黑';}
input,select{outline: none;}
.banner{ height:425px; color: #fff;user-select: none;}
.banner .txt1{padding-top: 70px; text-align: center;font-size:42px;line-height: 42px; font-weight:bold;}
.banner .txt2{line-height: 30px; margin-top: 25px; display: flex;align-items: center;justify-content: center; font-size: 22px;font-weight:bold;}
.banner .txt2 img{margin-right: 10px; margin-left:25px;}
.banner .txt2 img:first-child{margin-left: 0;}
.banner .txt3{margin-top: 35px;line-height: 18px; display: flex; align-items:center;justify-content: center; font-weight: bold; font-size: 18px;}

.banner .txt3 span{ border-right:2px solid #ff6600; padding-right:15px; margin-right: 15px}
.banner .txt3 span:last-child{ margin: 0;padding: 0; border: 0}
.banner .txt4{text-align: center;letter-spacing:8px;font-size: 16px; margin-top: 35px;}
.banner .btn{ cursor: pointer; margin: auto; margin-top: 30px; text-align: center; border-radius: 6px; width:160px;height: 50px; line-height: 50px; font-size: 18px; background: #CA4300;}
.banner .btn:hover{background: #FF6600;}


.all_title{user-select: none; margin: auto; display: flex; flex-direction: column;align-items: center; padding-bottom: 30px;}
.all_title span{font-size:30px; margin: 40px 0 15px 0;}
.all_title b{ width: 120px; height: 3px; background: #E5E5E5; margin-bottom:20px;}
.all_title font{font-size: 14px;line-height: 24px; color: #999;}

.content1{user-select: none; position: relative; padding:0 30px ; box-sizing: border-box; width: 1226px; margin: auto; margin-bottom: 50px; background: #F5F5F5; }
.content1 .line1{ align-items: center; border-bottom: 1px dashed #CBCACA; height: 125px; padding:25px 0 ; display: flex;}
.content1 .line1:last-child{border:0}
.content1 .line1 img{ border-radius: 6px; width: 200px; height: 125px;}
.content1 .line1 .txt_in{ margin-left: 60px; width:610px ; margin-right: 140px;}
.content1 .line1 .btn{cursor: pointer; text-align: center; border-radius: 6px; background: #CA4300; width:130px; height: 44px; line-height: 44px; color: #fff; font-size:16px ;}
.content1 .line1 .btn:hover{background: #FF6600;}
.content1 .line1 .txt_in .txt1 b{display: flex;  align-items: flex-start; font-size: 28px; line-height: 28px; padding-right:20px;}
.content1 .line1 .txt_in .txt1 b span{padding-left: 5px; font-size: 14px;font-weight:normal;line-height: 14px;}
.content1 .line1 .txt_in .txt1{font: 18px; display: flex;align-items: center;}
.content1 .line1 .txt_in .txt2{ font-size:14px; color: #5A5A5A; padding-top: 15px; line-height: 24px;}
.content1 .line1 .txt_in .txt2 a{color: #CA4300;text-decoration: none;}
.content1 .line1 .txt_in .txt2 a:hover{text-decoration: underline;}
.content1 .more{text-decoration: none; color: #CA4300; font-size:14px ; position: absolute; right: 0; bottom: -40px;}
.content1 .more:hover{text-decoration: underline;}

.content2{position: relative; user-select: none; width: 1226px; margin: auto; margin-bottom: 50px; display: flex; justify-content: space-between; }
.content2 .list{ width:585px;}
.content2 img{ margin-left: 30px;}
.content2 .list .list_in{ display: flex; align-items: center; height:44px;border-bottom: 1px dashed #CBCACA;}
.content2 .list .list_in::before{margin-right: 5px; content: '▪';  color: #CA4300; font-size: 20px;}
.content2 .list .list_in a{font-size: 15px;color: #333;text-decoration: none;}
.content2 .list .list_in a:hover{text-decoration:underline; color: #CA4300;}
.content2 .list .list_in span{border-radius: 3px; padding: 0 3px; margin-left: 10px; background: #ff0000; font-size:10px;color: #fff; height:14px; font-weight: bold;}
.content2 .more{text-decoration: none; color: #CA4300; font-size:14px ; position: absolute; right: 0; bottom: -40px;}
.content2 .more:hover{text-decoration: underline;}

.content3{display: flex;flex-wrap: wrap; height:440px;user-select: none; position: relative; box-sizing: border-box; width: 1226px; margin: auto; margin-bottom: 50px; background: #F5F5F5; }
.content3 .content3_in{padding:25px 0 0 25px;  width: 613px; box-sizing: border-box; height: 220px;}
.content3 .content3_in .title{padding-bottom: 20px; display: flex; align-items: center; font-size: 18px; font-weight: bold;}
.content3 .content3_in .title img{ margin-right: 15px;}
.content3 .content3_in .txt{ display: flex; flex-wrap: wrap;   }
.content3 .content3_in .txt .txt_in{width: 290px;font-size: 15px;}
.content3 .content3_in .txt .txt_in a{ color: #333;line-height: 32px;text-decoration: none;}
.content3 .content3_in .txt .txt_in a:hover{text-decoration:underline; color: #CA4300;}
.content3 .xxx{position: absolute; height:0; border-bottom: 1px dashed #CBCACA;top:210px; left: 24px;right: 24px;}
.content3 .xxx span:nth-child(1){top:-4px;left: 0; position: absolute;  border-radius: 50%; width: 10px; height: 10px; background: #CBCACA; display: block;}
.content3 .xxx span:nth-child(2){top:-9px;left: 560px; position: absolute;  border-radius: 50%; width: 20px; height: 20px; background: #CBCACA; display: block;}
.content3 .xxx span:nth-child(3){top:-4px;right: 0; position: absolute;  border-radius: 50%; width: 10px; height: 10px; background: #CBCACA; display: block;}

.content3 .yyy{position: absolute; width:0; border-right: 1px dashed #CBCACA;top:25px; left:594px; bottom:25px }
.content3 .yyy span:nth-child(1){top:0;left: -4px; position: absolute;  border-radius: 50%; width: 10px; height: 10px; background: #CBCACA; display: block;}
.content3 .yyy span:nth-child(2){bottom:0;left: -4px; position: absolute;  border-radius: 50%; width: 10px; height: 10px; background: #CBCACA; display: block;}
.content3 .more{text-decoration: none; color: #CA4300; font-size:14px ; position: absolute; right: 0; bottom: -40px;}
.content3 .more:hover{text-decoration: underline;}

.content4{user-select: none; width: 1226px; height: 365px; display: flex; justify-content: space-between; margin: auto;}
.content4 .card{position: relative; width:365px ; height: 310px; background: #F5F5F5; border-radius: 6px;}
.content4 .card img{ border-radius: 6px 6px 0 0;}
.content4 .card .tips{width: 100%; position: absolute;font-size: 20px; font-weight: bold; text-align: center; top:34px;color: #fff;}
.content4 .card .txt{font-size: 15px; line-height: 32px; flex-direction: column; height: 130px; display: flex; align-items: center; justify-content: center;}
.content4 .card .btn{cursor: pointer; margin: auto; text-align: center; background: #CA4300;font-size: 16px; color: #fff; width: 130px; height: 44px; line-height: 44px; border-radius: 6px;}
.content4 .card .btn:hover{background: #FF6600;}

.content5{user-select: none; width: 1226px; height: 300px; margin: auto; display: flex; justify-content: space-between;}
.content5 .content5_in{cursor: pointer; width: 150px; display: flex; align-items: center; flex-direction: column;}
.content5 .content5_in .pic{ display: flex; justify-content: center; align-items: center; width: 150px; height: 150px;border-radius: 6px; background: #F0F0F0;}
.content5 .content5_in .pic img{ filter: grayscale(0.6); opacity: 0.6;}
.content5 .content5_in span{font-size: 14px; color: #878787;}
.content5 .content5_in span:nth-child(4){ margin-top: 5px;}
.content5 .content5_in b{font-size: 18px; color: #333; margin: 15px 0;}

.content5 .content5_in:hover .pic img{ filter: grayscale(0); opacity: 1;}
.content5 .content5_in:hover b{color: #CA4300;}
.content5 .content5_in:hover span{color: #333;}

.content6{user-select: none;  height: 350px; width: 1226px; margin: auto; display: flex; justify-content: space-between;}
.content6 .card{text-align: justify; width:250px; color: #5A5A5A; font-size: 14px; line-height: 24px;}
.content6 .card img{ width: 250px; height:123px; border-radius: 6px;}
.content6 .card .title {padding: 10px 0; display: flex; align-items: center;}
.content6 .card .title b{color: #CA4300; font-size:28px ; padding: 10px 0}
.content6 .card .title span{padding-left: 20px; font-size:18px; font-weight: bold;}

.content7{user-select: none;  background: #F5F5F5; padding-bottom: 50px;}
.content7 .action{display: flex;justify-content: center;}
.content7 .action input{margin-right: 15px; color: #333; font-size: 16px; padding: 0 10px; border-radius: 6px; height: 45px; border: 1px solid #D8D8D8;}
.content7 .action input:focus{border: 1px solid #CA4300;}
.content7 .action input:hover{border: 1px solid #CA4300;}
.content7 .action input:nth-child(1){width: 100px;}
.content7 .action input:nth-child(2){width: 120px;}
.content7 .action input:nth-child(3){width: 180px;}
.content7 .action select{width:140px; appearance:none;-moz-appearance:none;-webkit-appearance:none;background: #fff; margin-right: 15px; color: #333; font-size: 16px; padding: 0 10px; border-radius: 6px; height: 45px; border: 1px solid #D8D8D8;}
.content7 .action select:hover{border: 1px solid #CA4300;}




.content7 .action input:nth-child(5){width: 240px;}
.content7 .action  .btn{cursor: pointer; text-align: center; background: #CA4300;font-size: 16px; color: #fff; width: 130px; height: 45px; line-height: 44px; border-radius: 6px;}
.content7 .action  .btn:hover{background: #FF6600;}
.content7 .txt{ margin-top: 30px; text-align: center; font-size:14px; color: #878787;}
