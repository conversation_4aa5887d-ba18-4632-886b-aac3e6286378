.lab {
  background: #eee;
}
.lab * {
  box-sizing: border-box;
}
.lab .red {
  color: #ca4300;
}
.lab .head-box {
  height: 40px;
  background: #333333;
  padding: 5px 0;
  font-size: 12px;
  color: #C1C1C1;
  line-height: 30px;
}
.lab .head-box .page-title {
  display: inline-block;
  color: #C1C1C1;
}
.lab .user-content {
  float: right;
  display: flex;
}
.lab .user-content .user-item {
  margin-left: 27px;
}
.lab .user-content .user-item a {
  color: #C1C1C1;
}
.lab .head-img {
  height: 90px;
  background: #FFFFFF;
}
.lab .head-img .wrap {
  display: flex;
  align-items: center;
  position: relative;
  height: 100%;
}
.lab .head-img .logo {
  margin-right: 17px;
  vertical-align: bottom;
}
.lab .head-img .title {
  height: 43px;
  line-height: 43px;
  width: 230px;
  margin-right: 25px;
  font-size: 24px;
}
.lab .head-img .cma-cnas {
  position: relative;
  width: 300px;
}
.lab .head-img .cma-cnas .thum {
  height: 44px;
}
.lab .head-img .cma-cnas .cma-box {
  position: absolute;
  left: 0;
  top: 0;
  width: 50px;
  height: 44px;
  cursor: pointer;
  z-index: 3;
}
.lab .head-img .cma-cnas .cma-box .cma-img {
  position: absolute;
  left: -100px;
  bottom: -354px;
  display: none;
}
.lab .head-img .cma-cnas .cma-box:hover .cma-img {
  display: block;
}
.lab .head-img .cma-cnas .cnas-box {
  position: absolute;
  left: 113px;
  top: 0;
  width: 50px;
  height: 44px;
  cursor: pointer;
  z-index: 3;
}
.lab .head-img .cma-cnas .cnas-box .cnas-img {
  position: absolute;
  left: -100px;
  bottom: -354px;
  display: none;
}
.lab .head-img .cma-cnas .cnas-box:hover .cnas-img {
  display: block;
}
.lab .head-img .anchor {
  display: flex;
}
.lab .head-img .anchor img {
  width: 48px;
  height: 40px;
}
.lab .head-img .anchor ul {
  width: 225px;
  margin-left: 15px;
}
.lab .head-img .anchor ul li {
  width: 75px;
  height: 20px;
  line-height: 20px;
  float: left;
}
.lab .head-img .anchor ul a {
  font-size: 14px;
  color: #333;
}
.lab .head-img .anchor ul a:hover {
  color: #ca4300;
}
.lab .head-img .home_top_hotline {
  position: absolute;
  right: 0;
  top: 25px;
}
.lab .head-img .home_top_hotline i {
  width: 40px;
  height: 40px;
  background: url(../../images/promotion/lab/phone.png) no-repeat;
  position: absolute;
  left: -55px;
  top: 0;
}
.lab .head-img .home_top_hotline .home_top_hotline_phone span {
  font-size: 26px;
  font-weight: bold;
  color: #ca4300;
  line-height: 26px;
}
.lab .head-img .home_top_hotline .home_top_hotline_intro {
  font-size: 12px;
  color: #878787;
  text-align: right;
  line-height: 1;
  margin-top: 2px;
}
.lab .nav-box {
  background: #ECECEC;
}
.lab .nav-wrap {
  height: 50px;
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  color: #333333;
}
.lab .nav-wrap .nav-wrap-box {
  width: 1080px;
  overflow: hidden;
  height: 50px;
  position: relative;
}
.lab .nav-wrap .nav-wrap-box ul {
  width: 9999rem;
}
.lab .nav-wrap .nav-wrap-box ul li {
  float: left;
  padding-right: 35px;
  height: 50px;
  line-height: 50px;
}
.lab .nav-wrap .nav-wrap-box ul li:last-child {
  padding-right: 0;
}
.lab .nav-wrap .nav-wrap-box ul li a {
  color: #333;
  font-size: 16px;
}
.lab .nav-wrap .nav-wrap-box ul li a:hover {
  color: #ca4300;
  font-weight: bold;
}
.lab .nav-wrap .nav-wrap-box .btns span {
  position: absolute;
  display: block;
  width: 124px;
  height: 36px;
  z-index: 2;
  top: 8px;
  cursor: pointer;
}
.lab .nav-wrap .nav-wrap-box .btns span.left {
  background: url(../../images/promotion/lab/menu_left.png) no-repeat;
  left: 0;
}
.lab .nav-wrap .nav-wrap-box .btns span.right {
  right: 0;
  background: url(../../images/promotion/lab/menu_right.png) no-repeat;
}
.lab .nav-wrap .nav-btn {
  margin-top: 7px;
  width: 130px;
  height: 36px;
  line-height: 36px;
  background: #ca4300;
  font-size: 16px;
  color: #FFFFFF;
  text-align: center;
  z-index: 2;
  position: relative;
  cursor: pointer;
  border-radius: 3px;
}
.lab .nav-wrap .nav-btn:hover:before {
  width: 130px;
}
.lab .nav-wrap .nav-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 36px;
  background: #f49000;
  z-index: -1;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  border-radius: 3px;
}
.lab .banner-box {
  width: 100%;
  height: 500px;
  background: url("../../images/promotion/lab/banner.jpg") no-repeat center center;
}
.lab .banner-box .banner-wrap {
  position: relative;
  height: 100%;
}
.lab .banner-box .banner-wrap .kf5-btn {
  width: 180px;
  text-align: center;
  height: 50px;
  line-height: 50px;
  background: #000;
  box-shadow: 4px 6px 21px 0px rgba(0, 0, 0, 0.27);
  opacity: 0.6;
  border-radius: 3px;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  top: 325px;
  left: 0;
  position: absolute;
}
.lab .banner-box .banner-wrap .kf5-btn:hover {
  background: #ca4300;
  opacity: 1;
}
.lab .banner-box .oiq-plan {
  width: 524px;
  height: 320px;
  background: #FFFFFF;
  box-sizing: border-box;
  float: right;
  padding: 40px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.4);
  margin-top: 86px;
}
.lab .banner-box .oiq-plan h3 {
  height: 22px;
  line-height: 22px;
  font-size: 22px;
  font-weight: normal;
  color: #fff;
}
.lab .banner-box .oiq-plan h3 i {
  color: #FF8F1F;
}
.lab .banner-box .oiq-plan .oiq_msg {
  height: 40px;
  width: 100%;
  overflow: hidden;
  margin-top: 10px;
  position: relative;
}
.lab .banner-box .oiq-plan .oiq_msg ul {
  position: absolute;
  top: 0;
  left: 0;
}
.lab .banner-box .oiq-plan .oiq_msg ul li {
  height: 40px;
}
.lab .banner-box .oiq-plan .oiq_msg ul li .time {
  font-size: 12px;
  font-weight: 400;
  color: #fff;
  line-height: 20px;
  height: 20px;
}
.lab .banner-box .oiq-plan .oiq_msg ul li div * {
  font-size: 12px;
  padding-right: 3px;
  height: 20px;
  line-height: 20px;
}
.lab .banner-box .oiq-plan .oiq_msg ul li b {
  color: #fff;
}
.lab .banner-box .oiq-plan .oiq_msg ul li b.lastCategory {
  max-width: 123px;
  overflow: hidden;
  height: 14px;
}
.lab .banner-box .oiq-plan .oiq_msg ul li span {
  color: #fff;
}
.lab .banner-box .oiq-plan .oiq-search {
  margin-top: 20px;
}
.lab .banner-box .oiq-plan .oiq-search a {
  display: block;
  width: 424px;
  height: 45px;
  position: relative;
}
.lab .banner-box .oiq-plan .oiq-search a:hover input {
  border: 1px solid #ca4300;
  background: #fff;
}
.lab .banner-box .oiq-plan .oiq-search input {
  width: 424px;
  height: 45px;
  background: #F6F6F6;
  border-radius: 4px;
  font-size: 14px;
  color: #333333;
  outline: 0;
  border: 1px solid #fff;
  padding-left: 12px;
  cursor: pointer;
}
.lab .banner-box .oiq-plan .oiq-search i {
  width: 53px;
  height: 45px;
  border-radius: 4px;
  background: url('../../images/home/<USER>/oiq_search.png') no-repeat center center;
  display: block;
  position: absolute;
  top: 0;
  right: -10px;
  z-index: 2;
}
.lab .banner-box .oiq-plan .oiq-category {
  margin-top: 20px;
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  max-height: 40px;
  line-height: 20px;
}
.lab .banner-box .oiq-plan .oiq-category a,
.lab .banner-box .oiq-plan .oiq-category span {
  color: #ca4300;
  font-size: 14px;
  padding-right: 3px;
  height: 20px;
  line-height: 20px;
}
.lab .banner-box .oiq-plan .oiq_start {
  margin-top: 20px;
  width: 140px;
  text-align: center;
  height: 42px;
  line-height: 42px;
  background: #ca4300;
  font-size: 16px;
  color: #fff;
  display: block;
  position: relative;
}
.lab .banner-box .oiq-plan .oiq_start .oiq_start_text {
  color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  text-align: center;
  font-size: 14px;
}
.lab .banner-box .oiq-plan .oiq_start .oiq_start_layout {
  width: 0;
  height: 100%;
  background: #cc5500;
  position: absolute;
  top: 0;
  left: 0;
}
.lab .banner-box .oiq-plan .oiq_start:hover .oiq_start_layout {
  width: 100%;
  transition: 0.4s;
}
.lab .floor-title {
  font-size: 30px;
  font-weight: bold;
  color: #333333;
  text-align: center;
  padding: 0;
  line-height: 30px;
  width: 1026px;
  margin: auto;
  padding-top: 45px;
}
.lab .main-line {
  margin: 12px auto 24px;
  width: 60px;
  height: 3px;
  background: #ca4300;
}
.lab .floor-sub-title {
  font-size: 16px;
  color: #878787;
  opacity: 0.87;
  text-align: center;
  margin: 17px auto 40px;
  width: 1042px;
  line-height: 30px;
}
.lab .about {
  width: 1226px;
  margin: 0 auto;
}
.lab .about .content {
  display: flex;
  justify-content: space-between;
}
.lab .about .content .img,
.lab .about .content img,
.lab .about .content video {
  width: 572px;
  height: 325px;
}
.lab .about .content img {
  cursor: pointer;
}
.lab .about .textarea {
  width: 580px;
  margin-left: 73px;
}
.lab .about .textarea .kf5-btn {
  margin-top: 30px;
  width: 180px;
  text-align: center;
  height: 50px;
  line-height: 50px;
  background: #ca4300;
  border-radius: 3px;
  color: #fff;
  cursor: pointer;
}
.lab .about .textarea .kf5-btn:hover {
  background: #f49000;
}
.lab .service {
  width: 1226px;
  margin: 0 auto;
}
.lab .service .service_li > li {
  float: left;
  width: 284px;
  margin-right: 30px;
  background: #fff;
  margin-bottom: 25px;
  height: 370px;
  position: relative;
}
.lab .service .service_li > li:nth-child(4n) {
  margin-right: 0;
}
.lab .service .service_li > li .service_li_title {
  width: 284px;
  height: 130px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-size: cover;
}
.lab .service .service_li > li .service_li_title h2 {
  font-size: 24px;
  color: #fff;
  padding-bottom: 10px;
}
.lab .service .service_li > li .service_li_title span {
  background: #f49000;
  height: 1px;
  width: 45px;
}
.lab .service .service_li > li .service_li_list {
  margin: 20px 15px 0 15px;
  width: 250px;
  position: relative;
}
.lab .service .service_li > li .service_li_list .service_li_list_line {
  position: absolute;
  z-index: 1;
  width: 250px;
}
.lab .service .service_li > li .service_li_list .service_li_list_line div {
  border-bottom: 1px dashed #CACACA;
  height: 40px;
}
.lab .service .service_li > li .service_li_list ul {
  position: absolute;
  z-index: 2;
}
.lab .service .service_li > li .service_li_list li {
  float: left;
  line-height: 40px;
  height: 40px;
  max-width: 100%;
  overflow: hidden;
}
.lab .service .service_li > li .service_li_list a {
  padding: 0 10px;
  font-size: 15px;
  color: #333;
}
.lab .service .service_li > li .service_li_list a:hover {
  color: #ca4300;
}
.lab .service .service_li > li .service_li_list span {
  padding: 0 10px;
  font-size: 15px;
  color: #333;
}
.lab .service .service_li > li .service_li_btn {
  margin: 20px 0 20px 25px;
  line-height: 20px;
  position: absolute;
  bottom: 0;
}
.lab .service .service_li > li .service_li_btn .kf5-btn {
  display: flex;
  height: 20px;
  align-items: center;
}
.lab .service .service_li > li .service_li_btn span {
  color: #ca4300;
  font-size: 15px;
  padding-right: 7px;
  cursor: pointer;
}
.lab .service .service_li > li .service_li_btn span:hover {
  color: #f49000;
}
.lab .service .service_li > li .service_li_btn i {
  background: url(../../images/promotion/lab/service-arrow.png) no-repeat;
  display: inline-block;
  width: 14px;
  height: 12px;
}
.lab .service .service-btns {
  text-align: right;
}
.lab .service .service-btns a {
  color: #878787;
  font-size: 16px;
}
.lab .service .service-btns a:hover {
  color: #ca4300;
}
.lab .solution .solution-wrap {
  position: relative;
}
.lab .solution .solution-swiper {
  margin-top: 35px;
  position: relative;
}
.lab .solution .solutionCon {
  height: auto;
  overflow: hidden;
  padding-bottom: 0;
}
.lab .solution .solutionConLi {
  width: 593px;
  height: 162px;
  float: left;
  position: relative;
  margin-bottom: 26px;
  margin-right: 38px;
  list-style: none;
  background: #fff;
}
.lab .solution .solutionConLi:nth-child(2n) {
  margin-right: 0;
}
.lab .solution .solutionConLi .img {
  float: left;
  width: 237px;
  height: 162px;
  display: block;
  background: #ccc;
  cursor: pointer;
  background-size: cover;
}
.lab .solution .solutionConLi .cc {
  padding-left: 265px;
  text-align: left;
  padding-right: 20px;
}
.lab .solution .solutionConLi .cc a:hover {
  color: #ca4300;
}
.lab .solution .solutionConLi .cc p {
  font-size: 14px;
  width: 100%;
  line-height: 22px;
  overflow: hidden;
  color: #999;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  height: 44px;
  margin: 5px 0 10px 0;
}
.lab .solution .solutionConLi .solutionConLi-word1 {
  display: block;
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: 1.17;
  letter-spacing: normal;
  color: #333333;
  cursor: pointer;
  padding: 5px 0;
  margin-top: 20px;
  font-weight: bold;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 23px;
}
.lab .solution .solutionConLi .kf5-btn {
  width: 62px;
  text-align: center;
  height: 28px;
  line-height: 28px;
  display: block;
  border: 1px solid #ca4300;
  border-radius: 14px;
  color: #ca4300;
  font-size: 14px;
  cursor: pointer;
}
.lab .solution .solutionConLi .kf5-btn:hover {
  color: #fff;
  background: #ca4300;
}
.lab .solution .solutionConLi:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  -webkit-transform: translate3d(0, -2px, 0);
  transform: translate3d(0, -2px, 0);
  transition: 0.4s;
}
.lab .solution .swiper-button-next,
.lab .solution .swiper-button-prev {
  width: 34px;
  height: 76px;
  top: 140px;
  outline: none;
}
.lab .solution .swiper-button-prev {
  left: -52px;
  background: url("../../images/promotion/lab/solution-prev.png");
}
.lab .solution .swiper-button-next {
  right: -52px;
  background: url("../../images/promotion/lab/solution-next.png");
}
.lab .solution .swiper-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.lab .solution .swiper-pagination-bullet {
  width: 16px;
  height: 16px;
  background: #eee;
  border-radius: 50%;
  font-size: 12px;
  color: #878787;
  line-height: 16px;
  outline: none;
  opacity: 1;
  text-align: center;
}
.lab .solution .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: #ca4300;
  color: #fff;
}
.lab .solution .swiper-pagination-bullet:not(:last-child) {
  margin-right: 5px;
}
.lab .solution .solution-btns {
  text-align: right;
}
.lab .solution .solution-btns a {
  color: #878787;
  font-size: 16px;
}
.lab .solution .solution-btns a:hover {
  color: #ca4300;
}
.lab .area {
  width: 1226px;
  margin: 0 auto;
}
.lab .data {
  width: 1226px;
  margin: 0 auto;
}
.lab .certificate {
  width: 1226px;
  margin: 0 auto;
}
.lab .news {
  width: 1100px;
  margin: 0 auto;
}
.lab .news li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 54px;
  padding: 0 10px;
  border-bottom: 1px solid #E2E2E2;
}
.lab .news li:hover {
  background: #fff;
  border-bottom: 1px solid #fff;
}
.lab .news li .left {
  display: flex;
  align-items: center;
  justify-content: center;
}
.lab .news li .left i {
  display: block;
  width: 14px;
  height: 16px;
  background: url(../../images/promotion/lab/news-icon.png) no-repeat;
  margin-right: 20px;
}
.lab .news li .left a {
  width: 895px;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  color: #333;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.lab .news li .left a:hover {
  text-decoration: underline;
  color: #ca4300;
}
.lab .news li .right {
  display: flex;
  align-items: center;
  justify-content: center;
}
.lab .news li .right span {
  display: block;
  width: 50px;
  font-size: 14px;
  color: #A4A4A4;
  margin-left: 13px;
}
.lab .news li .right i {
  display: inline-block;
  width: 20px;
  height: 14px;
  background: url(../../images/promotion/lab/news-eye.png) no-repeat;
}
.lab .news li:hover .right i {
  display: inline-block;
  width: 20px;
  height: 14px;
  background: url(../../images/promotion/lab/news-eye-activity.png) no-repeat;
}
.lab .news .more {
  padding: 17px 0 0 42px;
}
.lab .news .more a {
  font-size: 16px;
  color: #ca4300;
}
.lab .news .more a:hover {
  color: #f49000;
}
.lab .news .more i {
  background: url(../../images/promotion/lab/service-arrow.png) no-repeat;
  display: inline-block;
  width: 14px;
  height: 12px;
}
.lab .case {
  margin-top: 63px;
}
.lab .case .news-wrap {
  position: relative;
}
.lab .case .news-swiper {
  margin-top: 49px;
  height: 463px;
  width: 1226px;
}
.lab .case .news-swiper .swiper-wrapper {
  height: 100%;
}
.lab .case .news-swiper .newsCon .card {
  margin-right: 35px;
  background: #fff;
  width: 385px;
  height: 420px;
  float: left;
}
.lab .case .news-swiper .newsCon .card:nth-child(3n) {
  margin-right: 0;
}
.lab .case .news-swiper .newsCon .card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  transition: 0.4s;
}
.lab .case .news-swiper .newsCon .card img {
  width: 385px;
  height: 250px;
}
.lab .case .news-swiper .newsCon .card .title {
  font-size: 18px;
  font-weight: 400;
  color: #333;
  padding: 20px 15px 15px 15px;
  width: 385px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.lab .case .news-swiper .newsCon .card .des {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  padding: 0 15px;
  width: 385px;
  font-size: 14px;
  color: #878787;
  line-height: 24px;
}
.lab .case .news-swiper .newsCon .card .tag {
  display: inline-block;
  margin-top: 15px;
  margin-left: 15px;
  height: 20px;
  font-size: 12px;
  color: #ca4300;
  line-height: 20px;
  background: rgba(202, 67, 0, 0.1);
  border-radius: 3px;
  padding: 0 5px;
}
.lab .case .swiper-button-next,
.lab .case .swiper-button-prev {
  width: 34px;
  height: 76px;
  top: 170px;
  outline: none;
}
.lab .case .swiper-button-prev {
  left: -52px;
  background: url("../../images/promotion/jiance-new/solution-prev.png");
}
.lab .case .swiper-button-next {
  right: -52px;
  background: url("../../images/promotion/jiance-new/solution-next.png");
}
.lab .case .swiper-pagination1 {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.lab .case .swiper-pagination-bullet {
  width: 16px;
  height: 16px;
  background: #eee;
  border-radius: 50%;
  font-size: 12px;
  color: #878787;
  line-height: 16px;
  outline: none;
  opacity: 1;
  text-align: center;
}
.lab .case .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: #ca4300;
  color: #fff;
}
.lab .case .swiper-pagination-bullet:not(:last-child) {
  margin-right: 5px;
}
.lab .case .more {
  margin-top: 40px;
  text-align: center;
}
.lab .case .more a {
  border: 1px solid #ca4300;
  display: inline-block;
  width: 220px;
  height: 50px;
  line-height: 50px;
  background: #F5F5F5;
  color: #ca4300;
  border-radius: 3px;
  font-size: 16px;
}
.lab .case .more a:hover {
  background: #ca4300;
  color: #fff;
}
.lab .contact_ads {
  margin-top: 39px;
  background: url(../../images/newPage/ads.jpg) no-repeat 50% 50% #e8e8e8;
  height: 100px;
}
.lab .Tab_list {
  display: flex;
  flex-direction: row;
  align-items: center;
  background: #FFFFFF;
  color: #000000;
  height: 60px;
}
.lab .Tab_list li {
  width: auto;
  margin: 3px;
  display: inline-block;
  padding: 20px 10px;
  cursor: pointer;
}
.lab .Tab_list .Tab_list_mid {
  display: block;
  white-space: nowrap;
  overflow: hidden;
}
.lab .Tab_list .Tab_list_mid .activeclass {
  border-bottom: 4px solid #f60;
}
.lab .Tab_list .tab_left {
  width: 20px;
  padding-bottom: 4px;
}
.lab .Tab_list .tab_right {
  width: 20px;
  padding-bottom: 4px;
}
