@import './../mixin.less';

.lab {
  background: #eee;

  * {
    box-sizing: border-box;
  }

  .red {
    color: @mainColor;
  }

  .head-box {
    height: 40px;
    background: #333333;
    padding: 5px 0;
    font-size: 12px;
    color: #C1C1C1;
    line-height: 30px;

    .page-title {
      display: inline-block;
      color: #C1C1C1;
    }
  }

  .user-content {
    float: right;
    display: flex;

    .user-item {
      margin-left: 27px;

      a {
        color: #C1C1C1;
      }
    }
  }

  .head-img {
    height: 90px;
    background: #FFFFFF;

    .wrap {
      display: flex;
      align-items: center;
      position: relative;
      height: 100%;
    }

    .logo {
      margin-right: 17px;
      vertical-align: bottom;
    }

    .title {
      height: 43px;
      line-height: 43px;
      width: 230px;
      margin-right: 25px;
      font-size: 24px;
    }

    .cma-cnas {
      position: relative;
      width: 300px;

      .thum {
        height: 44px;
      }

      .cma-box {
        position: absolute;
        left: 0;
        top: 0;
        width: 50px;
        height: 44px;
        cursor: pointer;
        z-index: 3;

        .cma-img {
          position: absolute;
          left: -100px;
          bottom: -354px;
          display: none;
        }

        &:hover .cma-img {
          display: block;
        }
      }

      .cnas-box {
        position: absolute;
        left: 113px;
        top: 0;
        width: 50px;
        height: 44px;
        cursor: pointer;
        z-index: 3;

        .cnas-img {
          position: absolute;
          left: -100px;
          bottom: -354px;
          display: none;
        }

        &:hover .cnas-img {
          display: block;
        }
      }
    }

    .anchor {
      display: flex;

      img {
        width: 48px;
        height: 40px;
      }

      ul {
        width: 225px;
        margin-left: 15px;

        li {
          width: 75px;
          height: 20px;
          line-height: 20px;
          float: left;
        }

        a {
          font-size: 14px;
          color: #333;

          &:hover {
            color: @mainColor;
          }
        }
      }
    }

    .home_top_hotline {
      position: absolute;
      right: 0;
      top: 25px;

      i {
        width: 40px;
        height: 40px;
        background: url(../../images/promotion/lab/phone.png) no-repeat;
        position: absolute;
        left: -55px;
        top: 0;
      }

      .home_top_hotline_phone {
        span {
          font-size: 26px;
          font-weight: bold;
          color: @mainColor;
          line-height: 26px;
        }
      }

      .home_top_hotline_intro {
        font-size: 12px;
        color: #878787;
        text-align: right;
        line-height: 1;
        margin-top: 2px;
      }
    }
  }

  .nav-box {
    background: #ECECEC;
  }

  .nav-wrap {
    height: 50px;
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    color: #333333;

    .nav-wrap-box {
      width: 1080px;
      overflow: hidden;
      height: 50px;
      position: relative;

      ul {
        width: 9999rem;

        li {
          float: left;
          padding-right: 35px;
          height: 50px;
          line-height: 50px;

          &:last-child {
            padding-right: 0;
          }

          a {
            color: #333;
            font-size: 16px;

            &:hover {
              color: @mainColor;
              font-weight: bold;
            }
          }
        }
      }

      .btns {
        span {
          position: absolute;
          display: block;
          width: 124px;
          height: 36px;
          z-index: 2;
          top: 8px;
          cursor: pointer;

          &.left {
            background: url(../../images/promotion/lab/menu_left.png) no-repeat;
            left: 0;
          }

          &.right {
            right: 0;
            background: url(../../images/promotion/lab/menu_right.png) no-repeat;
          }
        }
      }
    }

    .nav-btn {
      margin-top: 7px;
      width: 130px;
      height: 36px;
      line-height: 36px;
      background: @mainColor;
      font-size: 16px;
      color: #FFFFFF;
      text-align: center;
      z-index: 2;
      position: relative;
      cursor: pointer;
      border-radius: 3px;

      &:hover:before {
        width: 130px;
      }

      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 36px;
        background: @mainColorActive;
        z-index: -1;
        -webkit-transition: 0.4s;
        transition: 0.4s;
        border-radius: 3px;
      }
    }
  }

  .banner-box {
    width: 100%;
    height: 500px;
    background: url("../../images/promotion/lab/banner.jpg") no-repeat center center;

    .banner-wrap {
      position: relative;
      height: 100%;

      .kf5-btn {
        width: 180px;
        text-align: center;
        height: 50px;
        line-height: 50px;
        background: #000;
        box-shadow: 4px 6px 21px 0px rgba(0, 0, 0, 0.27);
        opacity: 0.6;
        border-radius: 3px;
        color: #fff;
        font-size: 16px;
        cursor: pointer;
        top: 325px;
        left: 0;
        position: absolute;

        &:hover {
          background: @mainColor;
          opacity: 1;
        }
      }
    }

    .oiq-plan {
      width: 524px;
      height: 320px;
      background: #FFFFFF;
      box-sizing: border-box;
      float: right;
      padding: 40px;
      border-radius: 8px;
      background: rgba(0, 0, 0, 0.4);
      margin-top: 86px;

      h3 {
        height: 22px;
        line-height: 22px;
        font-size: 22px;
        font-weight: normal;
        color: #fff;

        i {
          color: #FF8F1F;
        }
      }

      .oiq_msg {
        height: 40px;
        width: 100%;
        overflow: hidden;
        margin-top: 10px;
        position: relative;

        ul {
          position: absolute;
          top: 0;
          left: 0;

          li {
            height: 40px;

            .time {
              font-size: 12px;
              font-weight: 400;
              color: #fff;
              line-height: 20px;
              height: 20px;
            }

            div * {
              font-size: 12px;
              padding-right: 3px;
              height: 20px;
              line-height: 20px;
            }

            b {
              color: #fff;

              &.lastCategory {
                max-width: 123px;
                overflow: hidden;
                height: 14px;
              }
            }

            span {
              color: #fff;
            }
          }
        }
      }


      .oiq-search {
        margin-top: 20px;

        a {
          display: block;
          width: 424px;
          height: 45px;
          position: relative;

          &:hover {
            input {
              border: 1px solid @mainColor;
              background: #fff;
            }
          }
        }

        input {
          width: 424px;
          height: 45px;
          background: #F6F6F6;
          border-radius: 4px;
          font-size: 14px;
          color: #333333;
          outline: 0;
          border: 1px solid #fff;
          padding-left: 12px;
          cursor: pointer;
        }

        i {
          width: 53px;
          height: 45px;
          border-radius: 4px;
          background: url('./../../images/home/<USER>/oiq_search.png') no-repeat center center;
          display: block;
          position: absolute;
          top: 0;
          right: -10px;
          z-index: 2;
        }
      }

      .oiq-category {
        margin-top: 20px;
        width: 100%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        max-height: 40px;
        line-height: 20px;

        a,
        span {
          color: @mainColor;
          font-size: 14px;
          padding-right: 3px;
          height: 20px;
          line-height: 20px;
        }
      }

      .oiq_start {
        margin-top: 20px;
        width: 140px;
        text-align: center;
        height: 42px;
        line-height: 42px;
        background: @mainColor;
        font-size: 16px;
        color: #fff;
        display: block;
        position: relative;

        .oiq_start_text {
          color: #fff;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 2;
          width: 100%;
          text-align: center;
          font-size: 14px;
        }

        .oiq_start_layout {
          width: 0;
          height: 100%;
          background: #cc5500;
          position: absolute;
          top: 0;
          left: 0;
        }

        &:hover .oiq_start_layout {
          width: 100%;
          transition: 0.4s;
        }
      }
    }
  }

  .floor-title {
    font-size: 30px;
    font-weight: bold;
    color: #333333;
    text-align: center;
    padding: 0;
    line-height: 30px;
    width: 1026px;
    margin: auto;
    padding-top: 45px;
  }

  .main-line {
    margin: 12px auto 24px;
    width: 60px;
    height: 3px;
    background: #ca4300;
  }

  .floor-sub-title {
    font-size: 16px;
    color: #878787;
    opacity: 0.87;
    text-align: center;
    margin: 17px auto 40px;
    width: 1042px;
    line-height: 30px;
  }

  .about {
    width: 1226px;
    margin: 0 auto;

    .content {
      display: flex;
      justify-content: space-between;

      .img,
      img,
      video {
        width: 572px;
        height: 325px;
      }

      img {
        cursor: pointer;
      }
    }

    .textarea {
      width: 580px;
      margin-left: 73px;

      .kf5-btn {
        margin-top: 30px;
        width: 180px;
        text-align: center;
        height: 50px;
        line-height: 50px;
        background: @mainColor;
        border-radius: 3px;
        color: #fff;
        cursor: pointer;

        &:hover {
          background: @mainColorActive;
        }
      }
    }
  }

  .service {
    width: 1226px;
    margin: 0 auto;

    .service_li {

      &>li {
        float: left;
        width: 284px;
        margin-right: 30px;
        background: #fff;
        margin-bottom: 25px;
        height: 370px;
        position: relative;

        &:nth-child(4n) {
          margin-right: 0;
        }

        .service_li_title {
          width: 284px;
          height: 130px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background-size: cover;

          h2 {
            font-size: 24px;
            color: #fff;
            padding-bottom: 10px;
          }

          span {
            background: @mainColorActive;
            height: 1px;
            width: 45px;
          }
        }

        .service_li_list {
          margin: 20px 15px 0 15px;
          width: 250px;
          position: relative;

          .service_li_list_line {
            position: absolute;
            z-index: 1;
            width: 250px;

            div {
              border-bottom: 1px dashed #CACACA;
              height: 40px;
            }
          }

          ul {
            position: absolute;
            z-index: 2;
          }

          li {
            float: left;
            line-height: 40px;
            height: 40px;
            max-width: 100%;
            overflow: hidden;
          }

          a {
            padding: 0 10px;
            font-size: 15px;
            color: #333;

            &:hover {
              color: @mainColor;
            }
          }

          span {
            padding: 0 10px;
            font-size: 15px;
            color: #333;
          }
        }

        .service_li_btn {
          margin: 20px 0 20px 25px;
          line-height: 20px;
          position: absolute;
          bottom: 0;

          .kf5-btn {
            display: flex;
            height: 20px;
            align-items: center;
          }

          span {
            color: @mainColor;
            font-size: 15px;
            padding-right: 7px;
            cursor: pointer;

            &:hover {
              color: @mainColorActive;
            }
          }

          i {
            background: url(../../images/promotion/lab/service-arrow.png) no-repeat;
            display: inline-block;
            width: 14px;
            height: 12px;
          }
        }
      }
    }

    .service-btns {
      text-align: right;

      a {
        color: #878787;
        font-size: 16px;

        &:hover {
          color: @mainColor;
        }
      }
    }
  }

  .solution {
    .solution-wrap {
      position: relative;
    }

    .solution-swiper {
      margin-top: 35px;
      position: relative;
    }

    .solutionCon {
      height: auto;
      overflow: hidden;
      padding-bottom: 0;
    }

    .solution-list {}

    .solutionConLi {
      width: 593px;
      height: 162px;
      float: left;
      position: relative;
      margin-bottom: 26px;
      margin-right: 38px;
      list-style: none;
      background: #fff;

      &:nth-child(2n) {
        margin-right: 0;
      }

      .img {
        float: left;
        width: 237px;
        height: 162px;
        display: block;
        background: #ccc;
        cursor: pointer;
        background-size: cover;
      }

      .cc {
        padding-left: 265px;
        text-align: left;
        padding-right: 20px;

        a {
          &:hover {
            color: @mainColor;
          }
        }

        p {
          font-size: 14px;
          width: 100%;
          line-height: 22px;
          overflow: hidden;
          color: #999;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          height: 44px;
          margin: 5px 0 10px 0;
        }
      }

      .solutionConLi-word1 {
        display: block;
        font-size: 16px;
        font-weight: normal;
        font-style: normal;
        font-stretch: normal;
        line-height: 1.17;
        letter-spacing: normal;
        color: #333333;
        cursor: pointer;
        padding: 5px 0;
        margin-top: 20px;
        font-weight: bold;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        height: 23px;
      }

      .kf5-btn {
        width: 62px;
        text-align: center;
        height: 28px;
        line-height: 28px;
        display: block;
        border: 1px solid @mainColor;
        border-radius: 14px;
        color: @mainColor;
        font-size: 14px;
        cursor: pointer;

        &:hover {
          color: #fff;
          background: @mainColor;
        }
      }
    }

    .solutionConLi:hover {
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
      -webkit-transform: translate3d(0, -2px, 0);
      transform: translate3d(0, -2px, 0);
      transition: 0.4s;
    }

    .swiper-button-next,
    .swiper-button-prev {
      width: 34px;
      height: 76px;
      top: 140px;
      outline: none;
    }

    .swiper-button-prev {
      left: -52px;
      background: url("../../images/promotion/lab/solution-prev.png");
    }

    .swiper-button-next {
      right: -52px;
      background: url("../../images/promotion/lab/solution-next.png");
    }

    .swiper-pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
    }

    .swiper-pagination-bullet {
      width: 16px;
      height: 16px;
      background: #eee;
      border-radius: 50%;
      font-size: 12px;
      color: #878787;
      line-height: 16px;
      outline: none;
      opacity: 1;
      text-align: center;

      &.swiper-pagination-bullet-active {
        background: #ca4300;
        color: #fff;
      }

      &:not(:last-child) {
        margin-right: 5px;
      }
    }

    .solution-btns {
      text-align: right;

      a {
        color: #878787;
        font-size: 16px;

        &:hover {
          color: @mainColor;
        }
      }
    }
  }

  .area {
    width: 1226px;
    margin: 0 auto;

    .content {
      // background: #f00;
    }
  }

  .data {
    width: 1226px;
    margin: 0 auto;

    .content {
      // background: #f00;
    }
  }

  .certificate {
    width: 1226px;
    margin: 0 auto;

    .content {
      // background: #f00;
    }
  }

  .news {
    width: 1100px;
    margin: 0 auto;

    li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 54px;
      padding: 0 10px;
      border-bottom: 1px solid #E2E2E2;

      &:hover {
        background: #fff;
        border-bottom: 1px solid #fff;
      }

      .left {
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          display: block;
          width: 14px;
          height: 16px;
          background: url(../../images/promotion/lab/news-icon.png) no-repeat;
          margin-right: 20px;
        }

        a {
          width: 895px;
          height: 54px;
          line-height: 54px;
          font-size: 16px;
          color: #333;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          &:hover {
            text-decoration: underline;
            color: @mainColor;
          }
        }
      }

      .right {
        display: flex;
        align-items: center;
        justify-content: center;

        span {
          display: block;
          width: 50px;
          font-size: 14px;
          color: #A4A4A4;
          margin-left: 13px;
        }

        i {
          display: inline-block;
          width: 20px;
          height: 14px;
          background: url(../../images/promotion/lab/news-eye.png) no-repeat;
        }
      }

      &:hover {
        .right {
          i {
            display: inline-block;
            width: 20px;
            height: 14px;
            background: url(../../images/promotion/lab/news-eye-activity.png) no-repeat;
          }
        }
      }
    }

    .more {
      padding: 17px 0 0 42px;

      a {
        font-size: 16px;
        color: @mainColor;

        &:hover {
          color: @mainColorActive;
        }
      }

      i {
        background: url(../../images/promotion/lab/service-arrow.png) no-repeat;
        display: inline-block;
        width: 14px;
        height: 12px;
      }
    }
  }

  .case {
    margin-top: 63px;

    .news-wrap {
      position: relative;
    }

    .news-swiper {
      margin-top: 49px;
      height: 463px;
      width: 1226px;

      //overflow: hidden;
      .swiper-wrapper {
        height: 100%;
      }

      .newsCon {
        .card {
          margin-right: 35px;
          background: #fff;
          width: 385px;
          height: 420px;
          float: left;

          &:nth-child(3n) {
            margin-right: 0;
          }

          &:hover {
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
            //-webkit-transform: translate3d(0, -2px, 0);
            //transform: translate3d(0, -2px, 0);
            transition: 0.4s;
          }

          img {
            width: 385px;
            height: 250px;
            // background: #f00;
          }

          .title {
            font-size: 18px;
            font-weight: 400;
            color: #333;
            padding: 20px 15px 15px 15px;
            width: 385px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .des {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            padding: 0 15px;
            width: 385px;
            font-size: 14px;
            color: #878787;
            line-height: 24px;
          }

          .tag {
            display: inline-block;
            margin-top: 15px;
            margin-left: 15px;
            height: 20px;
            font-size: 12px;
            color: @mainColor;
            line-height: 20px;
            background: rgba(202, 67, 0, .1);
            border-radius: 3px;
            padding: 0 5px;
          }
        }
      }
    }

    .swiper-button-next,
    .swiper-button-prev {
      width: 34px;
      height: 76px;
      top: 170px;
      outline: none;
    }

    .swiper-button-prev {
      left: -52px;
      background: url("../../images/promotion/jiance-new/solution-prev.png");
    }

    .swiper-button-next {
      right: -52px;
      background: url("../../images/promotion/jiance-new/solution-next.png");
    }

    .swiper-pagination1 {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
    }

    .swiper-pagination-bullet {
      width: 16px;
      height: 16px;
      background: #eee;
      border-radius: 50%;
      font-size: 12px;
      color: #878787;
      line-height: 16px;
      outline: none;
      opacity: 1;
      text-align: center;

      &.swiper-pagination-bullet-active {
        background: #ca4300;
        color: #fff;
      }

      &:not(:last-child) {
        margin-right: 5px;
      }
    }

    .more {
      margin-top: 40px;
      text-align: center;

      a {
        border: 1px solid @mainColor;
        display: inline-block;
        width: 220px;
        height: 50px;
        line-height: 50px;
        background: #F5F5F5;
        color: @mainColor;
        border-radius: 3px;
        font-size: 16px;

        &:hover {
          background: @mainColor;
          color: #fff;
        }
      }
    }
  }

  .contact_ads {
    margin-top: 39px;
    background: url(../../images/newPage/ads.jpg) no-repeat 50% 50% #e8e8e8;
    height: 100px;
  }


  .Tab_list {
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #FFFFFF;
    color: #000000;
    height: 60px;

    li {
      width: auto;
      margin: 3px;
      display: inline-block;
      padding: 20px 10px;
      cursor: pointer;
    }

    .Tab_list_mid {
      display: block;
      white-space: nowrap;
      overflow: hidden;

      .activeclass {
        border-bottom: 4px solid #f60;
      }
    }

    .tab_left {
      width: 20px;
      padding-bottom: 4px;
    }

    .tab_right {
      width: 20px;
      padding-bottom: 4px;
    }
  }
}