@import './mixin.less';

.oiq-landing-page {
  background: #eeeeee;
  padding-bottom: 80px;
  position: relative;

  a {
    cursor: pointer;
  }

  .oiq-banner {
    max-width: 1920px;
    margin: auto;
    height: 350px;
    background: url(../images/oiq/landingPage/oiq-banner.jpg) no-repeat center center;
    position: relative;
  }

  .banner-content {
    margin: 61px 0 0 342px;
    width: 604px;
    display: inline-block;
    text-align: center;
  }

  .banner-title {
    font-weight: bold;
    font-size: 36px;
    color: #FFFFFF;
    text-shadow: 3px 3px 5px rgba(0, 0, 0, 0.28);
    display: inline-block;
  }

  .banner-detail {
    margin-top: 21px;
    text-align: center;
    color: #FFFFFF;
    font-size: 22px;
    margin-bottom: 27px;
  }

  .banner-btn {
    width: 170px;
    height: 36px;
    background: #FFFFFF;
    box-shadow: 5px 6px 18px 0px rgba(0, 0, 0, 0.3);
    border-radius: 25px;
    font-size: 21px;
    color: @mainColor;
    text-align: center;
    display: inline-block;
    line-height: 36px;
    padding: 7px 0;

    .banner-btn-icon {
      display: inline-block;
      width: 36px;
      height: 36px;
      background: url("../images/oiq/landingPage/banner-btn-icon.png");
      vertical-align: bottom;
    }

    &:hover {
      background: linear-gradient(190deg, #FE8A02, @mainColor);
      color: #FFFFFF;

      .banner-btn-icon {
        background: url("../images/oiq/landingPage/banner-btn-icon-active.png");
      }
    }
  }

  .big-data {
    box-sizing: border-box;
    width: 1226px;
    margin: auto;
    height: 190px;
    background-color: #fff;
    background-image: url(../images/oiq/landingPage/data-bg.png);
    position: relative;
    margin-top: -86px;
    border-radius: 8px;
    padding: 25px 43px 0 31px;
    box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.2);

    .data-title {
      font-size: 18px;
      color: #333333;
      line-height: 22px;
    }

    .data-icon {
      display: inline-block;
      width: 22px;
      height: 22px;
      background: url("../images/oiq/landingPage/data-icon.png");
      margin-right: 10px;
      vertical-align: text-bottom;
    }

    .data-scroll-list {
      height: 22px;
      list-style: none;
      float: right;
      overflow: hidden;
    }

    .data-scroll-list li {
      height: 22px;
      position: relative;
      line-height: 22px;
      text-align: right;
      font-size: 14px;
      color: #333333;
    }

    .data-list {
      display: flex;
      justify-content: space-between;
      padding: 0 86px 0 100px;
      margin-top: 31px;
    }

    .data-detail {
      font-size: 36px;
      line-height: 36px;
      font-family: Univers Condensed;
      font-weight: bold;
      color: @mainColor;
      //background: linear-gradient(235deg, #FE8A02 0%, @mainColor 100%);
      //-webkit-background-clip: text;
      //-webkit-text-fill-color: transparent;
      margin-bottom: 17px;
    }

    .detail-label {
      font-size: 16px;
      color: #333333;
      line-height: 16px;
    }
  }

  .big-btn-floor {
    width: 1226px;
    margin: auto;
    margin-top: 27px;
    display: flex;
    justify-content: space-between;

    .big-btn {
      width: 390px;
      height: 100px;
      //background: #000000;
      border-radius: 8px;
      cursor: pointer;
      line-height: 100px;
      color: #FFFFFF;
      box-sizing: border-box;
      padding-left: 90px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &.btn1 {
        background: url("../images/oiq/landingPage/big-btn1.png");
      }

      &.btn2 {
        background: url("../images/oiq/landingPage/big-btn2.png");
      }

      &.btn3 {
        background: url("../images/oiq/landingPage/big-btn3.png");
      }

      .title {
        font-size: 30px;
      }

      .detail {
        font-size: 16px;
        line-height: 20px;
        height: 40px;
        display: flex;
        padding-right: 50px;
      }

      .btn-icon {
        display: inline-block;
        width: 53px;
        height: 24px;
        background: #FAFAFA;
        box-shadow: 5px 6px 18px 0px rgba(0, 0, 0, 0.3);
        border-radius: 12px;
        text-align: center;
        box-sizing: border-box;
        padding: 4.5px 0;
        line-height: 1;
        margin-right: 80px;

        .big-btn-icon {
          display: inline-block;
          width: 17px;
          height: 15px;
          background: url("../images/oiq/landingPage/big-btn-icon.png");
        }

        &:hover {
          background: linear-gradient(235deg, #FE8A02, @mainColor);

          .big-btn-icon {
            background: url("../images/oiq/landingPage/big-btn-icon-active.png");
          }
        }
      }
    }
  }

  .video-floor {
    margin-top: 32px;

    .wrap {
      background: #fff;
      height: 456px;
      border-radius: 8px;
      display: flex;
      padding: 32px 0 0 26px;
      box-sizing: border-box;

      .video-left-content {
        margin-right: 55px;
      }

      .video-title {
        font-size: 18px;
        color: #333333;
        line-height: 21px;
        margin-bottom: 23px;

        img {
          width: 24px;
          height: 21px;
          vertical-align: text-bottom;
          margin-right: 10px;
        }
      }

      .video-content {
        width: 587px;
        height: 330px;

        img {
          width: 100%;
          height: 100%;
          cursor: pointer;
        }

        video {
          display: none;
        }
      }

      .video-right-content {
        padding-top: 45px;
        position: relative;
        flex: 1;

        .ques-title {
          font-size: 18px;
          font-weight: bold;
          color: #333333;
          line-height: 18px;
          margin-bottom: 19px;
        }

        .video-text-list {
          margin-bottom: 25px;
          font-size: 16px;
          color: #333333;
          line-height: 16px;

          li {
            display: flex;
            font-size: 16px;
            color: #333333;
            line-height: 16px;

            &:before {
              content: "";
              display: block;
              width: 6px;
              height: 6px;
              background: @mainColor;
              margin-right: 11px;
              margin-top: 5px;
            }

            &:not(:last-child) {
              margin-bottom: 14px;
            }
          }
        }

        .video-btn {
          position: absolute;
          bottom: 49px;
          left: 0;
          font-size: 16px;
          color: #FFFFFF;
          line-height: 44px;
          width: 161px;
          height: 44px;
          background: @mainColor;
          z-index: 2;
          text-align: center;
          cursor: pointer;

          &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 44px;
            background: #cc5500;
            z-index: -1;
            -webkit-transition: 0.2s;
            transition: 0.2s;
          }

          &:hover:before {
            width: 161px;
          }
        }

        .text-icon {
          position: absolute;
          top: 27px;
          right: 27px;
        }
      }
    }
  }

  .products-floor {
    width: 1226px;
    margin: auto;
    margin-top: 44px;
    overflow: hidden;
    .products-title {
      font-size: 30px;
      color: #333333;
      line-height: 1;
      margin-bottom: 33px;
      text-align: center;
    }
    .products-list {
      //display: flex;
      //justify-content: space-between;
      //flex-wrap: wrap;
      .products-item {
        width: 235px!important;
        height: 360px;
        background: #FFFFFF;
        text-align: center;
        box-sizing: border-box;
        padding-top: 25px;
        font-size: 16px;
        color: #333333;
        line-height: 1;
        margin-bottom: 18px;
        margin-right: 10.2px;
        &:hover {
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
          transition: 0.4s;
          transform: translate3d(0, -3px, 0);
          padding-top: 10px;
          .products-btn {
            display: inline-block;
          }
        }
        .tit {
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #333333;
          margin-bottom: 16px;
        }
        .title_img {
          width: 181px;
          height: 100px;
          /*display: block;*/
          /*margin: auto;*/
          /*margin-bottom: 16px;*/
        }
        .title_children {
          display: flex;
          flex-direction: column;
          padding: 0 24px;
          margin-top: 23px;
          .title_children_list {
            display: flex;
            height: 28px;
            align-items: center;
            .title_children_icon {
              margin-right: 3px;
              width: 19px;
              height: 19px;
              img {
                width: 19px;
                height: 19px;
              }
            }

            .title_children_content {
              font-size: 14px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #333333;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
        .products-btn {
          margin-top: 6px;
          display: none;
          width: 86px;
          height: 30px;
          border: 1px solid rgb(202 67 0);
          border-radius: 15px;
          box-sizing: border-box;
          color: #CA4300;
          font-size: 14px;
          font-weight: 400;
          &:hover {
            background: #ca4300;
            .products-text {
              color: #FFFFFF;
            }
            .products-btn-icon {
              background: url("../images/oiq/landingPage/category-btn-active.png");
            }
          }
          .products-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 28px;
            .products-text {
              margin-right: 5px;
            }
            .products-btn-icon {
              display: inline-block;
              width: 14px;
              height: 13px;
              background: url("../images/oiq/landingPage/category-btn.png");
            }
          }
        }
      }
    }
    .swiper-pagination1 {
      text-align: center;
      .swiper-pagination-bullet {
        width: 10px;
        height: 5px;
        background: #F98D16;
        border-radius: 3px;
        opacity: 1;
        &:not(:last-child) {
          margin-right: 5px;
        }
      }
      .swiper-pagination-bullet-active {
        width: 20px;
      }
    }
  }



  .category-floor {
    width: 1226px;
    margin: auto;
    margin-top: 44px;

    .category-title {
      font-size: 30px;
      color: #333333;
      line-height: 1;
      margin-bottom: 33px;
      text-align: center;
    }

    .category-list {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      .category-item {
        width: 144px;
        height: 144px;
        background: #FFFFFF;
        text-align: center;
        box-sizing: border-box;
        padding-top: 25px;
        font-size: 16px;
        color: #333333;
        line-height: 1;
        margin-bottom: 18px;

        img {
          width: 58px;
          height: 58px;
          display: block;
          margin: auto;
          margin-bottom: 16px;
        }

        .category-btn {
          margin-top: 6px;
          display: none;
          width: 37px;
          height: 22px;
          border: 1px solid #ca4300;
          border-radius: 11px;
          box-sizing: border-box;
          padding: 3.5px 0;
          text-align: center;

          .category-btn-icon {
            display: inline-block;
            width: 14px;
            height: 13px;
            background: url("../images/oiq/landingPage/category-btn.png");
          }

          &:hover {
            background: @mainColor;

            .category-btn-icon {
              background: url("../images/oiq/landingPage/category-btn-active.png");
            }
          }
        }

        &:hover {
          //background: linear-gradient(0deg, rgba(255, 223, 202, 0.5), rgba(255, 241, 230, 0.5));
          //box-shadow: 6px 6px 8px 0px rgba(0, 0, 0, 0.15);
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
          transition: .4s;
          transform: translate3d(0, -3px, 0);
          padding-top: 10px;

          .category-btn {
            display: inline-block;
          }
        }
      }
    }
  }

  .project-floor {
    width: 1226px;
    margin: auto;
    margin-top: 54px;

    .project-title {
      font-size: 30px;
      color: #333333;
      line-height: 1;
      margin-bottom: 33px;
      text-align: center;
    }

    .project-content {
      // height: 194px;
      background: #fff;
      padding: 28px 22px;
      box-sizing: border-box;
      display: flex;
      border-radius: 8px;

      .project-list {
        width: 197px;
        // height: 131px;
        box-sizing: border-box;
        border-right: 1px solid #eee;
        position: relative;

        .project-btn {
          position: absolute;
          left: 25px;
          bottom: 0;
          width: 135px;
          height: 28px;
          border: 1px solid @mainColor;
          border-radius: 15px;
          font-size: 16px;
          color: @mainColor;
          line-height: 28px;
          text-align: center;

          &:hover {
            background: @mainColor;
            color: #FFFFFF;
          }
        }

        &:last-child {
          border-right: 1px solid transparent;
        }

        &:first-child .project-item {
          padding-left: 7px;
        }

        .project-item {
          margin-bottom: 26.5px;
          padding-left: 25px;

          a {
            display: flex;
            font-size: 13px;
            color: #333333;
          }

          span {
            line-height: 26px;
          }

          img {
            width: 26px;
            height: 26px;
            margin-right: 9px;
          }

          .active-icon {
            display: none;
          }

          &:hover {
            .normal-icon {
              display: none;
            }

            .active-icon {
              display: block;
            }

            a {
              color: @mainColor;
              font-weight: bold;
            }
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .new-order-floor {
    width: 1226px;
    margin: auto;
    margin-top: 44px;
    position: relative;

    .order-title {
      font-size: 30px;
      color: #333333;
      line-height: 30px;
      margin-bottom: 33px;
      text-align: center;
      position: relative;

      .reload-list-btn {
        position: absolute;
        top: 0;
        right: 0;
        height: 16px;
        font-size: 16px;
        color: #878787;
        cursor: pointer;

        .reload-list-btn-icon {
          display: inline-block;
          width: 22px;
          height: 19px;
          background: url("../images/oiq/landingPage/reload-list.png");
          margin-right: 10px;
          vertical-align: text-bottom;
        }
      }
    }

    .order-list {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      .order-item {
        width: 600px;
        height: 160px;
        background: #FFFFFF;
        border: 1px solid #EEEEEE;
        border-radius: 8px;
        box-sizing: border-box;
        padding: 21px 19px 0 21px;
        margin-bottom: 30px;
        position: relative;

        &:hover {
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
          transition: .4s;
          transform: translate3d(0, -3px, 0);
        }

        .order-top-content {
          margin-bottom: 19px;
          display: flex;
        }

        .order-time {
          width: 75px;
          margin-right: 18px;
          font-size: 12px;
          font-family: Arial;
          color: #878787;
          line-height: 14px;
        }

        .top-title {
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          line-height: 1;
        }

        .category-content {
          margin-bottom: 6px;
          font-size: 14px;
          font-weight: 400;
          color: #333;
          line-height: 22px;
          display: flex;

          .item-label {
            width: 75px;
            height: 22px;
            background: #EDEDED;
            border-radius: 8px;
            margin-right: 18px;
            color: #878787;
            text-align: center;
            flex-shrink: 0;
          }

          .item-detail {
            white-space:nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            * {
              display: inline;
            }
          }
        }

        .order-btn {
          width: 135px;
          height: 30px;
          border: 1px solid @mainColor;
          border-radius: 15px;
          text-align: center;
          font-size: 14px;
          color: @mainColor;
          line-height: 28px;
          cursor: pointer;
          position: absolute;
          bottom: 17px;
          right: 19px;
        }

        &:hover .order-btn {
          background: @mainColor;
          color: #FFFFFF;
        }
      }
    }
  }

  .comment-floor {
    width: 1226px;
    margin: 14px auto 41px;
    height: 306px;
    background: url("../images/oiq/landingPage/comment-bg.png");
    box-sizing: border-box;
    padding: 116px 113px 0 78px;
    position: relative;

    .comment-title {
      font-size: 30px;
      color: #333333;
      line-height: 1;
      text-align: center;
      position: absolute;
      width: 100%;
      top: 46px;
      left: 0;
    }

    .comment-content {
      display: flex;

      .step-img {
        width: 44px;
        height: 29px;
        margin-right: 21px;
      }

      .avatar-img {
        width: 76px;
        height: 76px;
        margin-right: 27px;
      }

      .user-content {
        font-size: 16px;
        color: #333333;
        line-height: 16px;
        width: 197px;
        margin-right: 38px;

        .user-name {
          font-weight: bold;
        }

        .location-icon {
          width: 14px;
          height: 16px;
          margin: 0 7px 0 10px;
          vertical-align: text-top;
        }

        .location-name {
          font-size: 14px;
        }

        .company-name {
          margin-top: 12px;
          font-size: 14px;
          color: #878787;
          line-height: 24px;
        }
      }

      .comment-line {
        width: 1px;
        height: 84px;
        background: #D3D3D3;
        margin-right: 40px;
      }

      .comment-detail {
        font-size: 15px;
        color: #333333;
        line-height: 28px;
        width: 595px;

        .comment-icon {
          width: 24px;
          height: 15px;
          margin: 0 10px 0 8px;
        }
      }
    }

    .swiper-container, .swiper-wrapper {
      height: 190px;
    }

    .swiper-bottom {
      position: absolute;
      bottom: 0;
      left: 510px;
    }

    .swiper-pagination {
      position: relative;
      display: inline-block;
      top: -53px;
    }

    .swiper-pagination-bullet {
      width: 10px;
      height: 5px;
      background: #F98D16;
      border-radius: 3px;
      opacity: 1;

      &:not(:last-child) {
        margin-right: 5px;
      }

      &.swiper-pagination-bullet-active {
        width: 20px;
      }
    }

    .swiper-button-next, .swiper-button-prev {
      width: 80px;
      height: 306px;
      top: 0;
      margin-top: 0;
      background: none;

      &:focus {
        outline: none;
      }
    }

    .swiper-button-next {
      right: 0;
    }

    .swiper-button-prev {
      width: 78px;
      left: 0;
    }
  }

  .quick-btn-floor {
    height: 80px;
    background: #F0F0F0;
    padding-top: 16px;
    box-sizing: border-box;
    background: linear-gradient(0deg, #F1F1F1, #FFFFFF);
    border: 1px solid #D7D7D7;
    box-shadow: 0px -3px 16px 0px rgba(0, 0, 0, 0.2);
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 11;

    &.not-fixed {
      position: absolute;
    }

    .quick-btn {
      width: 170px;
      height: 50px;
      margin: auto;
      background: linear-gradient(190deg, #FE8A02, @mainColor);
      box-shadow: 5px 6px 18px 0px rgba(0, 0, 0, 0.3);
      border-radius: 25px;
      font-size: 21px;
      color: #FFFFFF;
      box-sizing: border-box;
      padding-left: 27px;
      line-height: 50px;
      position: relative;
      cursor: pointer;
      display: block;

      .quick-btn-icon {
        content: "";
        width: 36px;
        height: 36px;
        background: url("../images/oiq/landingPage/quick-btn-icon.png");
        position: absolute;
        top: 7px;
        right: 8px;
      }

      &:hover {
        background: @mainColor;

        .quick-btn-icon {
          background: url("../images/oiq/landingPage/quick-btn-icon-active.png");
        }
      }
    }
  }

  .oiq-loading-ads {
    position: fixed;
    left: 0;
    top: 38%;
    z-index: 11;
  }

  .plan {
    background: #fff;
    border-radius: 8px;
  }

  .plan-step {
    ul {
      display: flex;
      justify-content: space-between;
      width: 100%;
      height: 65px;
      background: url('./../images/oiq/landingPage/plan-step.png') no-repeat;
    }

    li {
      flex: 1;
      width: 110px;
      height: 65px;
      line-height: 65px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      padding-left: 140px;
    }
  }

  .plan-content {
    ul {
      display: flex;
      justify-content: space-between;
      width: 100%;
      height: 350px;
      background: url('./../images/oiq/landingPage/step-content.jpg') no-repeat center top;
    }

    li {
      width: 262px;
      height: 40px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: @mainColor;
      flex: 1;
      text-align: center;
      padding-top: 285px;
    }
  }

  .plan-button {
    display: flex;
    justify-content: space-around;
    align-content: center;

    div {
      cursor: pointer;
      width: 60px;
      height: 35px;
      background: url('./../images/oiq/landingPage/up.png') no-repeat;

      &.active {
        background: url('./../images/oiq/landingPage/down.png') no-repeat;
      }
    }
  }
}
