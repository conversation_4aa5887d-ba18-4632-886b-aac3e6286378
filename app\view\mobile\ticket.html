<!DOCTYPE html>
<html lang="zh_CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no,viewport-fit=cover">
    <title>咨询报价 - SGS Online</title>
    <link rel="stylesheet" href="/static/mobile/css/style.css?t=4">
    <% if(locals.env != 'local'){ %>
    <base
        href="<% if(locals.env != 'prod'){ %>http://uat.sgsmall.com.cn<% }else{ %>https://www.sgsmall.com.cn<% } %>">
    <% } %>
    <script src="/static/js/jquery.min.js"></script>
    <style>
        .kf5-mobile {
            display: none;
        }
    </style>
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?483ee3868ddd2d5ccd6d97a737305370";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>

<body style="background-color: #ededee">
    <header>
        <div class="logo"><a href="/mobile"><img src="/static/mobile/images/<EMAIL>" alt=""></a></div>
        <div class="rnav">
            <a href="javascrpit:void(0);" class="btn" id="hNavBtn" onclick="getNav(this)"></a>
            <div class="hnav" id="hNav">
                <ul>
                    <li><a onclick="record()" href="tel:4000-558-581"><i class="icon1"></i>客服热线</a></li>
                    <!-- <li><a href="javascrpit:void(0);" onclick="$('#kf5-support-btn').click();"><i class="icon2"></i>在线客服</a></li> -->
                    <li><a href="javascrpit:void(0);" class="handleIMtool"><i class="icon2"></i>在线客服</a></li>
                    <li><a href="javascrpit:void(0);"><i class="icon3"></i>咨询报价</a></li>
                </ul>
            </div>
        </div>
        <a onclick="record()" href="tel:4000-558-581" class="htel"></a>
    </header>
    <div class="wrapper">
        <!---->
        <div class="imain">
            <div class="form" id="Form">
                <div class="swrapper">
                    <div class="sws">
                        <div class="form-tit">
                            <h3>咨询内容</h3>
                            <p>请尽量详细描述您的需求，以便我们能更好地回复您的咨询</p>
                            <p style="text-align: right">带（<span class="imp">*</span>）为必填项</p>
                        </div>

                        <div class="form-item">
                            <span class="label">行业解决方案<span class="imp">*</span></span>
                            <select name="" id="trade">
                                <option value="">请选择</option>
                                <% if(tradeNavi && tradeNavi.length > 0){ %>
                                <% for (var item of tradeNavi){ %>
                                <option value="<%- item.id %>"><%- item.name %></option>
                                <% } %>
                                <% } %>
                                <option value="其他">其他</option>
                            </select>
                            <span class="arrfix"></span>
                            <p class="ttip">*必填项</p>
                        </div>

                        <div class="form-item">
                            <span class="label">服务类型<span class="imp">*</span></span>
                            <select name="" id="service">
                                <option value="">请选择</option>
                                <% if(serviceNavi && serviceNavi.length > 0){ %>
                                <% for (var item of serviceNavi){ %>
                                <option value="<%- item.id %>"><%- item.name %></option>
                                <% } %>
                                <% } %>
                                <option value="其他">其他</option>
                            </select>
                            <span class="arrfix"></span>
                            <p class="ttip">*必填项</p>
                        </div>

                        <div class="form-item">
                            <span class="label">咨询类型</span>
                            <select name="" id="type">
                                <option value="服务询价">服务询价</option>
                                <option value="业务咨询">业务咨询</option>
                                <option value="建议反馈">建议反馈</option>
                                <option value="其他">其他</option>
                            </select>
                            <span class="arrfix"></span>
                            <p class="ttip">*必填项</p>
                        </div>

                        <div class="form-item">
                            <span class="label">咨询内容<span class="imp">*</span></span>
                            <textarea id="textarea" cols="20" rows="5" placeholder="请详细描述您的需求"></textarea>
                            <p class="ttip">*必填项</p>
                        </div>

                        <div class="form-button">
                            <button type="button" onclick="next()">下一步</button>
                        </div>

                        <div class="steptip">
                            <i class="on"></i><i></i>
                        </div>
                    </div>

                    <div class="sws">
                        <div class="form-tit">
                            <h3>您的信息</h3>
                            <p>请提供您的联系方式，以便我们能更好地回复您的咨询</p>
                            <p style="text-align: right">带（<span class="imp">*</span>）为必填项</p>
                        </div>

                        <div class="form-item">
                            <span class="label">姓名<span class="imp">*</span></span>
                            <input id="customer" type="text" placeholder="请输入您的姓名">
                            <p class="ttip">*必填项</p>
                        </div>

                        <div class="form-item">
                            <span class="label">手机号码<span class="imp">*</span></span>
                            <input id="phone" type="tel" placeholder="请输入您的手机号码">
                            <p class="ttip">*必填项</p>
                        </div>

                        <div class="form-item">
                            <span class="label">邮箱<span class="imp">*</span></span>
                            <input id="email" type="email" placeholder="请输入您的电子邮箱">
                            <p class="ttip">*必填项</p>
                        </div>

                        <div class="form-item">
                            <span class="label">所在省份或地区<span class="imp">*</span></span>
                            <select name="" id="provice">
                                <option value="">请选择所在省份或地区</option>
                                <option value="安徽省">安徽省</option>
                                <option value="北京市">北京市</option>
                                <option value="重庆市">重庆市</option>
                                <option value="福建省">福建省</option>
                                <option value="甘肃省">甘肃省</option>
                                <option value="广东省">广东省</option>
                                <option value="广西壮族自治区">广西壮族自治区</option>
                                <option value="贵州省">贵州省</option>
                                <option value="海南省">海南省</option>
                                <option value="河北省">河北省</option>
                                <option value="黑龙江省">黑龙江省</option>
                                <option value="河南省">河南省</option>
                                <option value="湖北省">湖北省</option>
                                <option value="湖南省">湖南省</option>
                                <option value="江苏省">江苏省</option>
                                <option value="江西省">江西省</option>
                                <option value="吉林省">吉林省</option>
                                <option value="辽宁省">辽宁省</option>
                                <option value="内蒙古自治区">内蒙古自治区</option>
                                <option value="宁夏回族自治区">宁夏回族自治区</option>
                                <option value="青海省">青海省</option>
                                <option value="山东省">山东省</option>
                                <option value="上海市">上海市</option>
                                <option value="山西省">山西省</option>
                                <option value="陕西省">陕西省</option>
                                <option value="四川省">四川省</option>
                                <option value="天津市">天津市</option>
                                <option value="新疆维吾尔自治区">新疆维吾尔自治区</option>
                                <option value="西藏自治区">西藏自治区</option>
                                <option value="云南省">云南省</option>
                                <option value="浙江省">浙江省</option>
                                <option value="香港">香港</option>
                                <option value="澳门">澳门</option>
                                <option value="台湾">台湾</option>
                                <option value="国外">国外</option>
                            </select>
                            <span class="arrfix"></span>
                            <p class="ttip">*必填项</p>
                        </div>

                        <div class="form-item">
                            <span class="label">企业名称<span class="imp">*</span></span>
                            <input id="company" type="text" placeholder="请输入企业名称">
                            <p class="ttip">*必填项</p>
                        </div>

                        <div class="form-item" style="text-align: center;margin-top: 30px; ">
                            <label><input type="checkbox" name="" id="check">我接受</label><a
                                href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs"
                                style="color:#FE660B;text-decoration:none;" target="_blank">隐私政策</a>
                            <div style="text-align: center;width:100%;height:14px;position: relative;">
                                <p style="width: 100%" class="ttip" id="cvTip">*请同意隐私政策</p>
                            </div>
                        </div>

                        <div class="form-button">
                            <button style="margin-top: 0;" type="button" onclick="submit()">提交</button>
                            <p style="text-align: center;padding-top: 20px; "><span onclick="prev()">返回上一步</span></p>
                        </div>

                        <div class="steptip">
                            <i class="on"></i><i class="on"></i>
                        </div>
                    </div>
                </div>


            </div>
        </div>
        <footer>
            <a class="gopc" href="/setPlatform"><i></i>电脑版</a>
            <div class="finfo">
                © 2018 SGS SA <a target="_blank" href="http://beian.miit.gov.cn"
                    style="display: inline-block; margin: 0 0 0 15px;">京ICP备16004943号-4</a>
                <br>
                <a target="_blank" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010802024895"
                    style="display:inline-block; height:20px;line-height:20px;">
                    <img src="/static/images/gaicon.png" style="float: left;">
                    <span style=" display: inline-block; margin-left: 5px; ">京公网安备 11010802024895号</span>
                </a>
                <a target="_blank" href="https://beian.cac.gov.cn/#/index">
                    <span>网信算备110108210930801250019号</span>
                </a>
            </div>
        </footer>
    </div>
    <div class="fnav">
        <ul>
            <li><a href="javascrpit:void(0);" onclick="goTop()"><i class="icon1"></i>回到顶部</a></li>
            <!-- <li><a href="javascrpit:void(0);" onclick="$('#kf5-support-btn').click();"><i class="icon2"></i>在线客服</a></li> -->
            <li><a href="javascrpit:void(0);" class="handleIMtool"><i class="icon2"></i>在线客服</a></li>
            <li><a onclick="record()" href="tel:4000-558-581"><i class="icon3"></i>客服热线</a></li>
            <li><a href="javascrpit:void(0);"><i class="icon4"></i>咨询报价</a></li>
        </ul>
    </div>
    <div class="shadeBox"
        style="width: 100%;height: 100%;top:0;left:0;background: rgba(0,0,0,0.3);position: fixed;z-index: 9998;display: none">

    </div>
    <div class="tip_box"
        style="position: fixed;top: 0;left: 0;bottom: 0;right: 0;width: 300px;height: 220px;z-index: 9999;margin:auto;background: #ffffff;box-shadow: 1px 1px 50px rgba(0,0,0,.3);border-radius: 5px;display: none;padding-bottom: 20px;overflow: hidden;">
        <div class="tip_t"
            style="height: auto;line-height: 40px;color: #000;font-size: 20px;text-align: center;padding-top: 10px">提交成功
        </div>
        <div class="tip_info_makesure" style="height: 180px;border-radius: 0 0 5px 5px">
            <div class="tip_info"
                style="color: #000000;font-size: 14px;padding: 0 22px;line-height: 24px;margin-top: 18px;margin-bottom: 22px">
                您的咨询单已提交成功，请关注您的邮箱并保持电话畅通，我们会尽快与您取得联系。
            </div>
            <div class="tip_make_sure"
                style="width: 55px;height: 27px;background: #ff5702;color: white;line-height: 27px;text-align: center;font-size: 12px;margin: 0 auto;cursor: pointer;border-radius: 2px;"
                onclick="_ok()">确定</div>
        </div>
    </div>
    <script>
        $(function () {
            $('#check').on('change', function () {
                var v = $(this).prop('checked');
                if (v) {
                    $('#cvTip').hide();
                } else {
                    $('#cvTip').show();
                }
            })
        })

        function next() {
            $('#Form .sws:first select,#Form .sws:first input[type=text],#Form .sws:first textarea').each(function (i, item) {
                var $ErrTip = $(item).siblings('p.ttip');
                var v = $(item).val().trim();
                if (!v || v == '') {
                    $ErrTip.text('*必填项').show();
                    $(item).addClass('err');
                } else {
                    $ErrTip.hide();
                    $(item).removeClass('err');
                }
            }).on('focus', function () {
                $(this).siblings('p.ttip').hide();
                $(this).removeClass('err');
            });

            if ($('.sws:first p.ttip:visible').length == 0) {
                $('.sws:first').css({ marginLeft: '-100%' });
            }
        }

        function prev() {
            $('.sws:first').css({ marginLeft: '0' });
        }

        function submit() {
            $('#Form select,#Form input[type=text],#Form input[type=tel],#Form input[type=email],#Form textarea').each(function (i, item) {
                var $ErrTip = $(item).siblings('p.ttip');
                var v = $(item).val().trim();

                if ($ErrTip.length == 1) {
                    if (!v || v == '') {
                        $ErrTip.text('*必填项').show();
                        $(item).addClass('err');
                    } else {
                        $ErrTip.hide();
                        $(item).removeClass('err');
                    }
                }

            }).on('focus', function () {
                $(this).siblings('p.ttip').hide();
                $(this).removeClass('err');
            });

            var phone = $('#phone').val().trim();
            if (phone == '') {
                $('#phone').addClass('err').siblings('p.ttip').show().text('*必填项');
            } else if (!/^1\d{10}$/.test(phone)) {
                $('#phone').addClass('err').siblings('p.ttip').show().text('*请输入正确的手机号码');
            }

            var email = $('#email').val().trim();
            // var emailReg = /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
            var emailReg =   /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/;
            if (email == '') {
                $('#email').addClass('err').siblings('p.ttip').show().text('*必填项');
            } else if (!emailReg.test(email)) {
                $('#email').addClass('err').siblings('p.ttip').show().text('*请输入正确的电子邮箱');
            }

            var cv = $('#check').prop('checked');
            if (!cv) {
                $('#cvTip').show();
            } else {
                $('#cvTip').hide();
            }

            if ($('p.ttip:visible').length > 0) {
                // alert('请确认输入数据')
            } else {
                var params = {
                    type: $('#type').val(),
                    trade: $('#trade').val(),
                    tradeName: $('#trade>option:selected').text(),
                    service: $('#service').val(),
                    serviceName: $('#service>option:selected').text(),
                    content: $('#textarea').val(),
                    customer: $('#customer').val().trim(),
                    phone: $('#phone').val().trim(),
                    email: $('#email').val().trim(),
                    provice: $('#provice').val(),
                    company: $('#company').val() ? $('#company').val().trim() : '',
                    os_type: 'pc',
                    frontUrl: window.location.href,
                    _csrf: '<%- csrf %>',
                }

                $.post('/ticket/post', params, function (r) {
                    if (r.success) {
                        // alert('提交成功!');
                        // window.location.reload();
                        // var pw = $('html,body').height(), ph = $('html,body').height();
                        // $('.shadeBox').width(pw).height(ph).css('display', 'block');
                        // $('.tip_box').css('display', 'block');
                        window.location.href = '/mobile/success'
                    } else {
                        alert('提交失败，请重试或刷新。');
                    }
                })

            }
        }

        function _ok() {
            window.location.reload();
        }

    <% include commonjs.html %>
    </script>

    <!-- Start of KF5 supportbox script -->
    <script type="text/javascript">
            document.write('<script src="\/\/assets-cdn.kf5.com\/supportbox\/main.js?' + (new Date).getDay() + '" id="kf5-provide-supportBox" kf5-domain="sgs.kf5.com" charset="utf-8"><\/script>');
    </script>
    <!-- End of KF5 supportbox script -->
</body>

</html>
