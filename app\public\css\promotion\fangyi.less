@import './../mixin.less';
@grayColr: #878787;

.fangyi {
  button {
    border: none;
    outline: none;
    cursor: pointer;
  }

  a:hover {
    color: @mainColor  !important;
  }

  .banner {
    max-width: 1920px;
    margin: auto;
    height: 425px;
    background: url("../../images/promotion/fangyi/banner.jpg") no-repeat center center;
    position: relative;

    .wrap {
      position: relative;
    }

    .lange {
      position: absolute;
      left: 0;
      top: 20px;

      a {
        display: inline-block;
        width: 40px;
        height: 25px;
        line-height: 25px;
        border-radius: 2px;
        text-align: center;
        font-size: 12px;

        &.active {
          color: #fff;
          background: @mainColor;

          &:hover {
            color: #fff !important;
          }
        }
      }
    }

    p {
      text-align: right;
      padding-right: 70px;

      &:first-child {
        font-size: 48px;
        color: #333;
        font-weight: bold;
        line-height: 60px;
        padding-top: 75px;

        i {
          color: @mainColor;
        }
      }

      &:nth-child(2) {
        color: @grayColr;
        font-size: 18px;
        line-height: 30px;
        padding-top: 20px;
      }
    }

    label {
      display: block;
      padding-right: 70px;
      height: 50px;
      line-height: 50px;
      margin-top: 20px;

      span {
        font-size: 30px;
        color: @mainColor;
        font-weight: bold;
        background: url('./../../images/promotion/fangyi/telphone.png') no-repeat 0 center;
        padding-left: 40px;
        height: 50px;
        line-height: 50px;
        display: inline-block;
        float: right;
      }

      button {
        margin-left: 20px;
        width: 200px;
        height: 50px;
        line-height: 5px;
        text-align: center;
        color: #fff;
        font-size: 18px;
        background: @mainColor;
        float: right;

        &:hover {
          background: #cc5500;
          transition: 0.5s;
        }

        em {
          font-family: Arial;
          padding-left: 20px;
        }
      }
    }
  }

  .banner-bottom {
    height: 56px;
    position: relative;
    background: #2f2f2f;

    .link-text {
      color: #fff;
      cursor: pointer;
      display: inline-block;
      width: 260px;
      height: 56px;
      line-height: 56px;
      text-align: center;


      i {
        display: inline-block;
        height: 18px;
        border-left: 1px solid #191919;
        border-right: 1px solid #5c5c5c;
        float: left;
        margin-top: 19px;
      }

      &:hover {
        color: @mainColor;
        font-weight: bold;
      }

      &:last-child {
        width: 396px;
      }

      &:first-child {
        width: 296px;
      }
    }
  }

  .title {
    text-align: center;
    margin-bottom: 50px;
    padding-top: 60px;

    h2 {
      font-size: 30px;
      font-weight: normal;
      margin-bottom: 20px;
    }

    p {
      font-size: 14px;
      margin-bottom: 30px;
      margin: 0 0 10px 0;
      color: #999;
    }

    span {
      width: 120px;
      height: 3px;
      display: inline-block;
      background: #e5e5e5;
    }
  }

  .service-tab {
    li {
      float: left;
      width: 120px;
      height: 200px;
      text-align: center;
      float: left;
      margin-right: 38px;
      cursor: pointer;
      position: relative;
      position: relative;

      &:last-child {
        margin-right: 0;
      }

      &:hover,
      &.active {
        div {

          display: none;
        }

        img {
          border: 2px solid @mainColor;
        }

        span {
          color: @mainColor;
          font-weight: bold;
        }

        i {
          display: block;
        }
      }

      img {
        width: 116px;
        height: 116px;
        border: 2px solid #fff;
        border-radius: 50%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }

      div {
        width: 116px;
        height: 116px;
        border-radius: 50%;
        position: absolute;
        top: 2px;
        left: 2px;
        z-index: 2;
        background: rgba(0, 0, 0, .2);
        display: block;
      }

      span {
        display: inline-block;
        width: 120px;
        text-align: center;
        font-size: 18px;
        color: #333;
        padding-top: 140px;
      }

      i {
        display: none;
        width: 0;
        height: 0;
        position: absolute;
        overflow: hidden;
        font-size: 0;
        line-height: 0;
        border-width: 10px;
        border-style: solid dashed dashed dashed;
        border-color: transparent transparent #bfbfbf transparent;
        top: 180px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }

  .service-content {
    border-top: 1px solid #bfbfbf;
    background: #fafafa;
    padding: 20px 0 40px 0;

    .service-content-tab {
      height: 43px;
      border-bottom: 2px solid @mainColor;

      li {
        float: left;
        color: #333;
        font-size: 20px;
        text-align: center;
        position: relative;
        height: 43px;
        line-height: 43px;
        width: 150px;
        text-align: center;
        cursor: pointer;

        &:hover,
        &.active {
          color: #fff;
          background: @mainColor;

          i {
            display: none;
          }
        }

        i {
          position: absolute;
          display: block;
          width: 25px;
          height: 15px;
          background: url('./../../images/promotion/fangyi/hot.gif') no-repeat 0 0;
          top: 5px;
          right: 5px;
        }
      }
    }

    .service-content-con {
      .item {
        float: left;
        margin-right: 32px;
        padding-top: 40px;

        &:last-child {
          margin-right: 0;
        }

        &.item1 {
          width: 270px;

          img {
            width: 270px;
            height: 195px;
          }

          span {
            border-bottom: 0;
          }
        }

        &.item2 {
          width: 170px;
        }

        &.item3 {
          width: 340px;
        }

        &.item4 {
          width: 340px;
        }

        &.item5 {
          width: 700px;
        }

        &>span {
          color: #333;
          font-size: 18px;
          height: 40px;
          line-height: 40px;
          width: 100%;
          border-bottom: 1px solid #bfbfbf;
          display: block;
        }

        &>p {
          font-size: 16px;
          line-height: 28px;

          padding-top: 20px;
        }

        &>em {
          color: @mainColor;
          padding-top: 20px;
          display: block;
        }

        dl {
          margin-top: 15px;
          padding-top: 15px;
          border-top: 1px solid #bfbfbf;

          dd {
            color: #333;
            font-size: 16px;

            em {
              color: @mainColor;
              font-weight: bold;
            }
          }
        }

        ul {
          padding-top: 20px;

          li {
            // a {
            //   color: #333;
            //   font-size: 16px;
            //   line-height: 28px;
            //   padding-left: 10px;
            //   display: block;
            //   background: url('./../../images/promotion/fangyi/dot.png') no-repeat left 10px;
            // }
            color: #333;
            font-size: 16px;
            line-height: 28px;
            padding-left: 10px;
            display: block;
            background: url('./../../images/promotion/fangyi/dot.png') no-repeat left 10px;
          }
        }
      }
    }
  }

  .service-content-bottom {
    margin-top: 42px;
    border-top: 1px solid #bfbfbf;
    padding: 15px 0;
    text-align: center;

    p {
      line-height: 22px;
      font-size: 14px;
      color: #333;

      span {
        color: @mainColor;
      }
    }

    button {
      margin-top: 25px;
      width: 200px;
      height: 50px;
      line-height: 5px;
      text-align: center;
      color: #fff;
      font-size: 18px;
      background: @mainColor;

      &:hover {
        background: #cc5500;
        transition: 0.5s;
      }

      em {
        font-family: Arial;
        padding-left: 20px;
      }
    }

  }

  .advantage {
    padding-bottom: 50px;
    height: 170px;

    li {
      float: left;
      border-left: 1px solid #ddd;
      padding: 0 30px;
      height: 170px;
      width: 245px;

      &:last-child {
        border-right: 1px solid #ddd;
      }
    }

    h3 {
      color: #ca4300;
      font-size: 40px;
      height: 55px;
      line-height: 55px;

      em {
        font-family: Arail, Roboto, "Helvetica Neue";
      }
    }

    span {
      color: #333;
      font-size: 20px;
      display: block;
      padding: 5px 0 10px 0;
      font-weight: bold;
    }

    p {
      color: #878787;
      line-height: 24px;
      font-size: 16px;
    }
  }

  .serviceDetail {
    background: #fafafa;

    .serverConLi {
      float: left;
      width: 284px;
      height: 234px;
      margin-right: 30px;
      margin-bottom: 25px;
      float: left;
      position: relative;
      cursor: pointer;
      list-style: none;

      &:nth-child(4n) {
        margin-right: 0;
      }

      i {
        position: absolute;
        display: block;
        width: 82px;
        height: 34px;
        overflow: hidden;
        background: url('./../../images/buyicon.png') no-repeat 0 0;
        z-index: 2;
        top: 0;
        left: 0;
      }
    }

    .serverConLiWord1 {
      margin: 20px auto 0;
    }
  }

  .cooperationDemo {
    padding-bottom: 50px;

    li {
      position: relative;

      span {
        position: absolute;
        background: url('./../../images//promotion/fangyi/hot.gif') no-repeat 0 0;
        top: 2px;
        left: 373px;
        display: block;
        width: 25px;
        height: 15px;
      }
    }

    p {
      text-align: center;
      padding-top: 40px;

      a {
        border: 1px solid @mainColor;
        color: @mainColor;
        font-size: 16px;
        display: inline-block;
        width: 185px;
        height: 45px;
        line-height: 45px;
        text-align: center;

        &:hover {
          transform: 0.5s;
          background: #ca4300;
          color: #fff !important;
        }
      }
    }
  }

  .consult {
    background: #fafafa;
    padding-bottom: 100px;

    label {
      float: left;

      &:first-child {
        margin-left: 50px;
      }

      input {
        height: 45px;
        line-height: 45px;
        padding-left: 10px;
        border: 1px solid #ddd;
        background: #fff;
        margin-right: 20px;
        width: 179px;

        &:last-child {
          margin-right: 10px;
        }

        &.input-big {
          width: 330px;
        }

        &:focus {
          border: 1px solid #ca4300;
          outline: none;
        }

        &.focus {
          border: 1px solid #ca4300;
        }
      }

    }

    button {
      background: #ca4300;
      color: #fff;
      height: 45px;
      line-height: 45px;
      text-align: center;
      width: 130px;
      border: 0;
      cursor: pointer;
      margin-left: 20px;

      &:hover {
        background: #cc5500;
        transition: 0.5s;
      }
    }
  }

  .inspection {
    div {
      text-align: center;
      padding-bottom: 25px;
    }

    a {
      display: inline-block;
      text-align: center;

      &.text {
        color: @mainColor;

        &:hover {
          text-decoration: underline;
        }
      }

      &.button {
        color: #333;
        width: 137px;
        height: 41px;
        line-height: 41px;
        background: url('./../../images/promotion/fangyi/button.png') no-repeat 0 0;
        font-size: 14px;
      }
    }

    dl {
      width: 1226px;
      height: 179px;
      background: url('./../../images/promotion/fangyi/pic-1.jpg') no-repeat 0 0;
      width: 556px;
      padding: 40px 0 0 670px;
      margin-top: 15px;
    }

    dt {
      font-size: 16px;
      padding-bottom: 10px;
    }

    dd {
      font-size: 14px;
      width: 300px;
      float: left;
      background: url('../../images/promotion/fangyi/dot.png') no-repeat left 12px;
      height: 30px;
      line-height: 30px;
      padding-left: 15px;

      &:nth-child(odd) {
        width: 200px;
      }
    }
  }
}