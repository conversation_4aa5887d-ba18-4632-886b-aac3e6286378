.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
ul,
li {
  list-style: none;
}
button {
  border: 0;
  outline: 0;
  cursor: pointer;
}
img {
  border: 0;
}
.wrap {
  width: 1226px;
  margin: 0 auto;
}
.bg-white {
  background: #fff;
}
.bg-gray {
  background: #f8f8f8;
}
.bg-orange {
  background: #ca4300;
}
.jiance .banner {
  width: 100%;
  height: 481px;
  background: url("../../images/promotion/jiance/banner.png") no-repeat center center;
}
.jiance .banner .banner-btn {
  /*width: 167px;*/
  margin: 260px 0 0 530px;
}
.jiance .banner .kf5-btn {
  width: 200px;
  margin: 290px 0 0 240px;
  height: 46px;
  line-height: 46px;
  display: block;
  text-align: center;
  background: linear-gradient(0deg, #FE5003, #FE7F03);
  box-shadow: 6px 7px 9px 0px rgba(42, 78, 102, 0.6);
  position: relative;
  border-radius: 5px;
}
.jiance .banner .kf5-btn .kf5-btn-text {
  width: 100%;
  height: 46px;
  border-radius: 5px;
  color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  line-height: 46px;
}
.jiance .banner .kf5-btn .kf5-btn-layout {
  width: 0;
  height: 100%;
  background: #cc5500;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 5px;
}
.jiance .banner .kf5-btn:hover .kf5-btn-layout {
  width: 100%;
  transition: 0.4s;
}
.jiance .china-floor {
  padding: 50px 0;
}
.jiance .title-content p {
  text-align: center;
  padding: 0;
  font-size: 17px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #878787;
}
.jiance .title-content .title {
  font-size: 28px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
}
.jiance .title-content .sub-title {
  margin-bottom: 15px;
}
.jiance .title-content .main-line {
  margin: 8px auto 24px;
  width: 40px;
  height: 3px;
  background: #ca4300;
}
.jiance .china-card {
  display: flex;
  margin-top: 50px;
}
.jiance .china-card .card-item {
  width: 284px;
  height: 310px;
  background: #FFFFFF;
  box-shadow: 6px 10px 15px 0px rgba(0, 0, 0, 0.05);
  padding: 0 37px;
}
.jiance .china-card .card-item:not(:last-child) {
  margin-right: 30px;
}
.jiance .china-card .card-item:hover {
  box-shadow: 4px 7px 24px 0px rgba(0, 0, 0, 0.18);
  transition: 0.4s;
}
.jiance .china-card .card-item:hover .card-label {
  color: #ca4300;
}
.jiance .china-card .card-item:hover .card-detail {
  color: #333333;
}
.jiance .china-card .card-item .card-icon {
  text-align: center;
}
.jiance .china-card .card-item:nth-child(1) .card-icon {
  margin: 46px 0 35px;
  height: 58px;
}
.jiance .china-card .card-item:nth-child(2) .card-icon {
  margin: 40px 0 32px;
  height: 67px;
}
.jiance .china-card .card-item:nth-child(3) .card-icon {
  margin: 45px 0 40px;
  height: 57px;
}
.jiance .china-card .card-item:nth-child(4) .card-icon {
  margin: 50px 0 42px;
  height: 52px;
}
.jiance .china-card .card-item .line-icon {
  margin: 23px 0 26px;
  height: 1px;
  background: #D2D2D2;
  border-radius: 1px;
  position: relative;
}
.jiance .china-card .card-item .line-icon .circle-icon {
  position: absolute;
  width: 13px;
  height: 13px;
  background: #D2D2D2;
  border: 4px solid #FFFFFF;
  border-radius: 50%;
  top: -6px;
  right: 99px;
  box-sizing: border-box;
}
.jiance .china-card .card-item .card-label {
  font-size: 24px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
  text-align: center;
  line-height: 24px;
}
.jiance .china-card .card-item .card-detail {
  font-size: 18px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #878787;
  text-align: center;
  line-height: 18px;
}
.jiance .china-card .card-item .card-detail span {
  display: inline-block;
  margin-bottom: 13px;
}
.jiance .jianceyewu-floor {
  padding: 68px 0 72px;
}
.jiance .jianceyewu-floor .jianceyewu-card {
  display: flex;
  margin-top: 50px;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item {
  width: 284px;
  height: 310px;
  padding: 0 50px;
  box-sizing: border-box;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item:not(:last-child) {
  margin-right: 30px;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item:nth-child(1),
.jiance .jianceyewu-floor .jianceyewu-card .card-item:nth-child(3) {
  background: url("../../../public/images/promotion/jiance/card-bg-1.png");
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item:nth-child(1) .card-btn:before,
.jiance .jianceyewu-floor .jianceyewu-card .card-item:nth-child(3) .card-btn:before {
  background: #333;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item:nth-child(1) .card-btn:hover,
.jiance .jianceyewu-floor .jianceyewu-card .card-item:nth-child(3) .card-btn:hover {
  border: 1px solid #333;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item:nth-child(1) .card-btn:hover:before,
.jiance .jianceyewu-floor .jianceyewu-card .card-item:nth-child(3) .card-btn:hover:before {
  width: 124px;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item:nth-child(2),
.jiance .jianceyewu-floor .jianceyewu-card .card-item:nth-child(4) {
  background: url("../../../public/images/promotion/jiance/card-bg-2.png");
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item .card-title {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  line-height: 25px;
  margin-top: 57px;
  text-align: center;
  margin-bottom: 15px;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item .card-detail {
  font-size: 18px;
  color: #fff;
  line-height: 30px;
  text-align: center;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item .card-btn {
  margin: 25px auto 0;
  text-transform: uppercase;
  overflow: hidden;
  width: 122px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  border: 1px solid #fff;
  color: #fff;
  font-size: 16px;
  font-style: normal;
  position: relative;
  cursor: pointer;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item .card-btn > div {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item .card-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 80px;
  background: #ca4300;
  transition: 0.2s;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item .card-btn:hover {
  border: 1px solid #ca4300;
}
.jiance .jianceyewu-floor .jianceyewu-card .card-item .card-btn:hover:before {
  width: 124px;
}
.jiance .fuwu-floor {
  padding: 56px 0 45px;
}
.jiance .fuwu-floor .fuwu-list {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.jiance .fuwu-floor .fuwu-list .fuwu-item {
  box-sizing: border-box;
  padding: 0 20px;
  height: 40px;
  background: #F8F8F8;
  border: 1px solid #D2D2D2;
  border-radius: 20px;
  line-height: 40px;
  cursor: pointer;
  margin-top: 29px;
  margin-right: 15px;
}
.jiance .fuwu-floor .fuwu-list .fuwu-item:hover {
  background: #ffefe5;
  border-color: #ca4300;
}
.jiance .fuwu-floor .fuwu-list .fuwu-item.is-active {
  background: #ca4300;
  color: #fff;
  border-color: #ca4300;
}
.jiance .qa-floor {
  padding: 75px 0 57px;
}
.jiance .qa-floor * {
  box-sizing: border-box;
}
.jiance .qa-floor .qa-content {
  margin-top: 52px;
  height: 453px;
  background: url("../../../public/images/promotion/jiance/qa-bg.png");
}
.jiance .qa-floor .qa-title {
  width: 857px;
  height: 40px;
  background: url("../../../public/images/promotion/jiance/qa-title.png");
  font-size: 18px;
  font-weight: bold;
  line-height: 40px;
  color: #fff;
  padding-left: 16px;
}
.jiance .qa-floor .qa-detail {
  position: relative;
  width: 911px;
  padding-left: 40px;
  line-height: 40px;
  font-size: 18px;
  color: #333;
}
.jiance .qa-floor .qa-detail .green-text {
  position: absolute;
  left: 16px;
  font-weight: bold;
  color: #ca4300;
}
.jiance .qa-floor .qa-detail ul li:before {
  content: "";
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #ca4300;
  vertical-align: middle;
  margin: 0 5px;
  position: relative;
  top: -2px;
}
.jiance .xuqiu-floor {
  padding-bottom: 59px;
}
.jiance .xuqiu-floor .xuqiu-content {
  margin-top: 56px;
  display: flex;
}
.jiance .xuqiu-floor .xuqiu-item {
  font-size: 20px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
  line-height: 20px;
  position: relative;
  padding-left: 4px;
  box-sizing: border-box;
}
.jiance .xuqiu-floor .xuqiu-item:before {
  box-sizing: border-box;
  content: '';
  width: 4px;
  height: 20px;
  background: #EC6519;
  border: 2px solid #EC6519;
  position: absolute;
  left: 0;
  bottom: 0;
}
.jiance .xuqiu-floor .xuqiu-item:last-child:after {
  box-sizing: border-box;
  content: '';
  width: 4px;
  height: 20px;
  background: #EC6519;
  border: 2px solid #EC6519;
  position: absolute;
  right: 0;
  bottom: 0;
}
.jiance .xuqiu-floor .xuqiu-item > div {
  text-align: center;
}
.jiance .xuqiu-floor .xuqiu-item img {
  margin-bottom: 48px;
}
.jiance .xuqiu-floor .xuqiu-item:nth-child(1) {
  width: 228px;
}
.jiance .xuqiu-floor .xuqiu-item:nth-child(2) {
  width: 260px;
}
.jiance .xuqiu-floor .xuqiu-item:nth-child(3) {
  width: 251px;
}
.jiance .xuqiu-floor .xuqiu-item:nth-child(4) {
  width: 265px;
}
.jiance .xuqiu-floor .xuqiu-item:nth-child(5) {
  width: 215px;
  padding: 0 4px;
}
.jiance .shuju-floor {
  padding: 62px 0 0;
  background: url("../../../public/images/promotion/jiance/floor-bg.png") no-repeat center center;
  box-sizing: border-box;
  height: 341px;
}
.jiance .shuju-floor .shuju-list {
  display: flex;
  padding-left: 63px;
}
.jiance .shuju-floor .shuju-item {
  margin-top: 50px;
  padding: 12px 0;
}
.jiance .shuju-floor .shuju-item .shuju-detail {
  font-size: 33px;
  font-family: Univers Condensed;
  font-weight: bold;
  font-style: italic;
  color: #ca4300;
  line-height: 1;
}
.jiance .shuju-floor .shuju-item .shuju-detail .shuju-unit {
  font-size: 22px;
}
.jiance .shuju-floor .shuju-item .shuju-tips {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
  line-height: 16px;
  margin-top: 24px;
}
.jiance .shuju-floor .shuju-divider {
  margin-top: 50px;
  width: 17px;
  height: 100px;
}
.jiance .liucheng-floor {
  padding: 40px 0 68px;
}
.jiance .liucheng-floor .liucheng-content {
  background: url("../../../public/images/promotion/jiance/liucheng-bg.png") no-repeat center right;
  height: 340px;
  margin-top: 44px;
  position: relative;
}
.jiance .liucheng-floor .liucheng-step-img {
  position: absolute;
  left: 4px;
  top: 33px;
}
.jiance .liucheng-floor .liucheng-step-list {
  position: absolute;
  top: 33px;
  left: 66px;
}
.jiance .liucheng-floor .liucheng-step-item {
  font-size: 20px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 44px;
  margin-bottom: 26px;
}
.jiance .liucheng-floor .liucheng-step-item .liucheng-step-num {
  font-size: 24px;
  font-family: Univers 67 Condensed;
  font-weight: bold;
  margin-right: 21px;
}
.jiance .liucheng-floor .liucheng-left {
  width: 315px;
  height: 99px;
  top: 33px;
  position: absolute;
  left: 382px;
  background: url("../../../public/images/promotion/jiance/liucheng-left.png");
  padding: 25px 48px 26px 76px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  box-sizing: border-box;
}
.jiance .liucheng-floor .liucheng-left .liucheng-left-item {
  font-size: 17px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  font-style: italic;
  color: #ca4300;
  line-height: 17px;
  margin-bottom: 14px;
}
.jiance .liucheng-floor .liucheng-left .liucheng-left-item:before {
  content: '';
  display: inline-block;
  width: 5px;
  height: 5px;
  background: #ca4300;
  margin-right: 7px;
  position: relative;
  top: -4px;
}
.jiance .liucheng-floor .liucheng-right {
  width: 315px;
  height: 130px;
  top: 164px;
  position: absolute;
  left: 444px;
  background: url("../../../public/images/promotion/jiance/liucheng-right.png");
  padding: 27px 76px 25px 61px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.jiance .liucheng-floor .liucheng-right .liucheng-right-item {
  font-size: 17px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  font-style: italic;
  color: #333333;
}
.jiance .liucheng-floor .liucheng-right .liucheng-right-item:before {
  content: '';
  display: inline-block;
  width: 5px;
  height: 5px;
  background: #626262;
  margin-right: 6px;
  position: relative;
  top: -4px;
}
.jiance .zhengshu-floor {
  height: 485px;
  position: relative;
}
.jiance .zhengshu-floor .zhengshu-list {
  position: absolute;
  top: 48px;
  z-index: 1;
  margin: 54px auto 0;
  width: 1114px;
  display: flex;
  justify-content: space-between;
  left: 56px;
}
.jiance .zhengshu-floor .zhengshu-list p {
  text-align: center;
  margin-top: 19px;
}
.jiance .lianxi-floor {
  padding: 73px 0 83px;
}
.jiance .lianxi-floor .lianxi-list {
  margin-top: 50px;
  display: flex;
  justify-content: space-between;
}
.jiance .lianxi-floor .lianxi-item {
  width: 284px;
  height: 293px;
  border: 1px solid #DCDCDC;
  box-sizing: border-box;
  cursor: pointer;
  font-size: 22px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 1;
  display: block;
}
.jiance .lianxi-floor .lianxi-item:hover {
  background: #FFFFFF;
  border: 1px solid #ca4300;
  box-shadow: 9px 8px 18px 0px rgba(25, 28, 30, 0.13);
}
.jiance .lianxi-floor .lianxi-item * {
  text-align: center;
}
.jiance .lianxi-floor .lianxi-item .title {
  margin-bottom: 15px;
}
.jiance .lianxi-floor .lianxi-item .detail {
  color: #878787;
  font-size: 16px;
  margin-bottom: 10px;
}
.jiance .fuwu-detail-floor {
  padding: 46px 0 41px;
}
.jiance .fuwu-detail-floor .fuwu-detail-item {
  display: flex;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-left {
  width: 265px;
  height: 213px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-right: 27px;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-left .label {
  font-size: 18px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #343434;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-left img {
  width: 265px;
  height: 168px;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-right {
  flex: 1;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-top {
  display: flex;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-top .hot-fuwu-label {
  font-size: 18px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #343434;
  line-height: 18px;
  padding-bottom: 25px;
  width: 108px;
  border-bottom: 2px solid #ca4300;
  text-align: center;
  color: #ca4300;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-top .all-fuwu-label {
  flex: 1;
  height: 18px;
  padding-bottom: 25px;
  border-bottom: 2px solid #D3D3D3;
  text-align: right;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .all-fuwu-link {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #ca4300;
  line-height: 18px;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .all-fuwu-link:after {
  border: 6px solid transparent;
  border-left-color: #ca4300;
  display: inline-block;
  content: "";
  margin-left: 5px;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-list {
  display: flex;
  flex-wrap: wrap;
  padding-top: 40px;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item7 .fuwu-detail-list-item:nth-child(4n + 1) {
  width: 25%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item7 .fuwu-detail-list-item:nth-child(4n + 2) {
  width: 25%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item7 .fuwu-detail-list-item:nth-child(4n + 3) {
  width: 25%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item7 .fuwu-detail-list-item:nth-child(4n + 4) {
  width: 25%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item11 .fuwu-detail-list-item:nth-child(4n + 1) {
  width: 28%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item11 .fuwu-detail-list-item:nth-child(4n + 2) {
  width: 22%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item11 .fuwu-detail-list-item:nth-child(4n + 3) {
  width: 28%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item11 .fuwu-detail-list-item:nth-child(4n + 4) {
  width: 22%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item9 .fuwu-detail-list-item:nth-child(4n + 1) {
  width: 28%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item9 .fuwu-detail-list-item:nth-child(4n + 2) {
  width: 23%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item9 .fuwu-detail-list-item:nth-child(4n + 3) {
  width: 25%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item#fuwu-detail-item9 .fuwu-detail-list-item:nth-child(4n + 4) {
  width: 24%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-list-item {
  font-size: 17px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #343434;
  line-height: 17px;
  margin-bottom: 25px;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-list-item:hover {
  color: #ca4300;
  text-decoration: underline;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-list-item:nth-child(4n + 1) {
  width: 27%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-list-item:nth-child(4n + 2) {
  width: 23%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-list-item:nth-child(4n + 3) {
  width: 25%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-list-item:nth-child(4n + 4) {
  width: 25%;
}
.jiance .fuwu-detail-floor .fuwu-detail-item .fuwu-detail-list-item:before {
  content: "";
  width: 6px;
  height: 6px;
  background: #ca4300;
  display: inline-block;
  margin-right: 10px;
  position: relative;
  top: -3px;
}
.jiance .fuwu-detail-floor .fuwu-detail-item.is-hidden {
  display: none;
}
