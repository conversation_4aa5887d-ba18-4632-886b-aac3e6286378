<% include header.html %>
  <div style="background: #f8f8f8;">
    <div class="wrap">
      <div class="success">
        <div class="succes_infomation">
          <img src="<%- locals.static %>/images/success_mail.png" />
          <h2>您的咨询信息已收到，我们将尽快与您联系！</h2>
          <% if(type==='1' ){ %>
            <p>用户账号：<%= account %>
            </p>
            <% } %>
              <% if(type==='1' ){ %>
                <p>已为您注册SGS在线商城会员，可使用账号快捷登陆</p>
                <% } %>
                  <% if(type==='1' || type==='2' || type==='3' ){ %>
                    <p>到“<a href='<%- memberUrl %>/quote/list'>我的咨询</a>”查看咨询进度</p>
                    <% } %>
                      <% if(type==='4' ){ %>
                        <p>您可以通过所提交的邮箱，获取咨询进度反馈</p>
                        <p><a href='<%- memberUrl %>/login' class="succes_infomation_btn">注册用户</a></p>
                        <p>获得更全面的服务，在线追踪咨询进度</p>
                        <% } %>
                          <p class="countdown"><i id='countdown'>5</i>秒后自动跳转</p>
        </div>
        <div class="success_WXcode">
          <p>
            SGS商城小程序，扫一扫实时查看咨询进度
          </p>
          <div class="success_WXcode_item">
            <img src="<%- locals.static %>/images/mp-order-list.png" style="width: 120px; height: 120px;" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <script>
    $(function () {
      var time = 5
      var timer = null
      timer = setInterval(function () {
        if (time > 1) {
          time--
        } else {
          if (<%- type %> === 4) {
            window.location.href = '/'
          } else {
            window.location.href = '<%- memberUrl %>/quote/list'
          }
          clearInterval(timer)
        }
        $("#countdown").html(time)
      }, 1000);
    });
  </script>
  <% include footer.html %>