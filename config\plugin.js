'use strict';

// had enabled by egg
// exports.static = true;

/*
  view template
  ejs
*/

exports.ejs = {
  enable: true,
  package: 'egg-view-ejs',
};

/*
  database
  mysql
*/
exports.mysql = {
  enable: true,
  package: 'egg-mysql',
};

/*static files*/
exports.multipleStatic = {
  enable: true,
  package: 'egg-multiple-static',
};


/* 设置请求可跨域 */
exports.cors = {
  enable: true,
  package: 'egg-cors',
};

// 关闭watcher 
exports.watcher = false;

exports.sentry = {
  enable: process.env.npm_lifecycle_event === 'prod',
  package: 'egg-sentry',
};