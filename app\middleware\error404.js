module.exports = () => {
    return async function error404(ctx, next) {
        await next();
        if (ctx.status === 404 && !ctx.body) {
            if (ctx.acceptJSON) {
                ctx.body = {
                    error: 'Not Found'
                };
            } else {
                // ctx.body = '<h1>Page Not Found</h1>';
                ctx.redirect('/');
            }
        } else if (ctx.status === 412 && !ctx.body) {
            if (ctx.acceptJSON) {
                ctx.body = {
                    error: 'Not Found'
                };
            } else {
                ctx.redirect('/404');
                // const _info = require('../../config/info').siteInfo;
                // const tradeNavi = await ctx.service.catalog.getNaviTrade();
                // const serviceNavi = await ctx.service.catalog.getNaviService();

                // await ctx.render('nosource', {
                //     detail: {
                //         title: 'Not Found'
                //     },
                //     tradeNavi: tradeNavi.list,
                //     serviceNavi: serviceNavi.list,
                //     storeUrl: _info.storeUrl,
                // })
            }
        }
    }
};