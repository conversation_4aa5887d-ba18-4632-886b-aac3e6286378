
.clearfix:after{content:".";display:block;height:0;clear:both;visibility:hidden}
.clearfix{*+height:1%;}
ul, li {
  list-style: none;
}
img {
  border: 0;
}
.wrap {
  width: 1226px;
  margin: 0 auto;
}
.bg-white {
  background: #fff;
}
.bg-gray {
  background: #f9f9f9;
}
.banZuZhang * {
  margin: 0 auto;
  padding: 0;
}
.banZuZhang .banner {
  width: 100%;
  height: 425px;
  background: url("../../images/promotion/banZuZhang/banner.jpg") no-repeat center center;
}
.banZuZhang .title {
  text-align: center;
  margin-bottom: 50px;
  padding-top: 60px;
}
.banZuZhang .title h2 {
  font-size: 30px;
  font-weight: normal;
  margin-bottom: 20px;
}
.banZuZhang .title p {
  font-size: 14px;
  margin-bottom: 30px;
  margin: 0 0 10px 0;
  color: #999;
}
.banZuZhang .title span {
  width: 120px;
  height: 3px;
  display: inline-block;
  background: #e5e5e5;
}
.banZuZhang .list {
  margin-bottom: 50px;
}
.banZuZhang .list li {
  float: left;
  width: 284px;
  margin-right: 30px;
  margin-bottom: 60px;
  background: #fff;
}
.banZuZhang .list li:hover {
  box-shadow: 0 8px 16px rgba(0,0,0,0.2);
  transition: .4s;
  transform: translate3d(0, -3px, 0);
}
.banZuZhang .list li:nth-child(4n) {
  margin-right: 0;
}
.banZuZhang .img-box, .banZuZhang .list img {
  width: 284px;
  height: 220px;;
}
.banZuZhang .list img {
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
}
.banZuZhang .img-box {
  position: relative;
}
.banZuZhang .img-box em {
  display: inline-block;
  width: 83px;
  height: 28px;
  line-height: 28px;
  color: #fff;
  z-index: 2;
  position: absolute;
  left: 0;
  top: 0;
  background: url('../../images/promotion/banZuZhang/list/icon.png') no-repeat;
  font-style: normal;
  font-size: 14px;
  padding-left: 5px;
}
.banZuZhang .img-box a {
  position: absolute;
  bottom: 0;
  left: 0;
  display: inline-block;
  width: 274px;
  height: 27px;
  line-height: 27px;
  color: #fefefe;
  font-size: 12px;
  z-index: 2;
  background: rgba(0, 0, 0, .7);
  text-align: right;
  padding-right: 10px;
}
.banZuZhang .info-box {
  text-align: center;
  padding-bottom: 22px;
}
.banZuZhang .info-box h2 {
  font-size: 18px;
  margin-top: 24px;
}
.banZuZhang .info-box em {
  display: block;
  width: 72px;
  height: 3px;
  background: #e5e5e5;
  margin-top: 10px;
  margin-bottom: 10px;
}
.banZuZhang .info-box p {
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  color: #999;
}
.banZuZhang .info-box span, .banZuZhang .info-box a {
  display: inline-block;
  width: 120px;
  height: 27px;
  line-height: 27px;
  border: 1px solid #ca4300;
  text-align: center;
  margin-top: 20px;
  color: #ca4300;
  font-size: 16px;
  cursor: pointer;
}
.banZuZhang .info-box span:hover, .banZuZhang .info-box a:hover {
  color: #fff;
  background: #ca4300;
  transition: .4s;
}
.banZuZhang .list .more {
  background: #ca4300;
  width: 300px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  cursor: pointer;
}
.banZuZhang .list .more a {
  color: #fff;
}
.banZuZhang .list .more:hover {
  background: #cc5500;
  transition: .4s;
}
/* 课程价值 */
.banZuZhang .value {
  margin-bottom: 50px;
}
.banZuZhang .value li {
  float: left;
  width: 390px;
  height: 513px;
  margin-right: 28px;
}
.banZuZhang .value li:last-child {
  margin-right: 0;
}
.banZuZhang .value .company {
  background: url('../../images/promotion/banZuZhang/value/bg-1.png') no-repeat center center;
}
.banZuZhang .value .HR {
  background: url('../../images/promotion/banZuZhang/value/bg-2.png') no-repeat center center;
}
.banZuZhang .value .student {
  background: url('../../images/promotion/banZuZhang/value/bg-3.png') no-repeat center center;
}
.banZuZhang .value li {
  text-align: center;
  position: relative;
}
.banZuZhang .value h3 {
  font-size: 24px;
  color: #fff;
  padding-top: 50px;
}
.banZuZhang .value h2 {
  font-size: 36px;
  color: #fff;
  padding-top: 25px;
}
.banZuZhang .value .arrow-b {
  background: url('../../images/promotion/banZuZhang/value/arrow-b.png') no-repeat center center;
  width: 142px;
  height: 101px;
  position: absolute;
  z-index: 2;
  left: 110px;
  top: 180px;
}
.banZuZhang .value .up {
  background: url('../../images/promotion/banZuZhang/value/up.png') no-repeat center center;
  width: 95px;
  height: 59px;
  position: absolute;
  z-index: 3;
  left: 190px;
  top: 230px;
}
.banZuZhang .value .line-place {
  width: 230px;
  height: 1px;
  background: #ca4300;
  margin-top: 150px;
}
.banZuZhang .value .p-box {
  margin-top: 35px;
}
.banZuZhang .value p {
  color: #fff;
  font-size: 14px;
  line-height: 30px;
}
.banZuZhang .value .arrow-m {
  background: url('../../images/promotion/banZuZhang/value/arrow-m.png') no-repeat center center;
  width: 41px;
  height: 20px;
  position: absolute;
  z-index: 3;
  bottom: -7px;
  left: 175px;
  animation :myfirst 1s infinite linear;
  opacity: 0;
}
@keyframes myfirst {
  from {
    bottom: -7px;
    opacity: 0;
  }
  to {
    bottom: 9px;
    opacity: 1;
  }
}

/* 关键优势 */
.banZuZhang .key-advantage {
  background: url('../../images/promotion/banZuZhang/pic-1.jpg') no-repeat center center;
  height: 192px;
  margin-bottom: 80px;
}
/* 讲师团队 */
.banZuZhang .teacher {

}
.banZuZhang .teacher .content {
  width: 1226px;
  height: 550px;
  overflow: hidden;
  position: relative;
}
.banZuZhang .teacher .content .box {
  width: 2452px;
  height: 550px;
  position: absolute;
  left: 0;
  top: 0;
}
.banZuZhang .teacher .content .item {
  /* display: none; */
}
.banZuZhang .teacher .content li:hover .shade {
  /* display: block; */
}
.banZuZhang .teacher .content li {
  position: relative;
  width: 390px;
  height: 260px;
  float: left;
  margin-right: 28px;
  margin-bottom: 28px;
  overflow: hidden;
}
.banZuZhang .teacher .content li:nth-child(3n) {
  margin-right: 0;
}
.banZuZhang .teacher .content img {
  position: absolute;
  width: 390px;
  height: 260px;
  z-index: 1;
  top: 0;
  left: 0;
}
.banZuZhang .teacher .content .info {
  position: absolute;
  top: 30px;
  left: 220px;
  z-index: 3;
  height: 230px;
}
.banZuZhang .teacher .content .info span {
  color: #ca4300;
  display: block;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
}
.banZuZhang .teacher .content .info span em {
  display: inline-block;
  float: left;
  font-style: normal;
}
.banZuZhang .teacher .content .info span em.bold {
  font-weight: bold;
}
.banZuZhang .teacher .content .info span i {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-left: 10px;
  background: url('../../images/promotion/banZuZhang/teacher/arrow.png') no-repeat center center;
}
.banZuZhang .teacher .content .info p {
  margin: 20px 0;
  font-size: 14px;
  width: 150px;
}
.banZuZhang .teacher .content .info a {
  border: 1px solid #ca4300;
  color: #ca4300;
  display: inline-block;
  width: 115px;
  height: 35px;
  line-height: 35px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  position: absolute;
  bottom: 20px;
  left: 0;
  z-index: 3;
}
.banZuZhang .teacher .content .info a:hover {
  color: #fff;
  background: #ca4300;
  transition: .4s;
}
.banZuZhang .teacher .content .shade {
  /* display: none; */
  background: rgba(0, 0, 0, .6);
  width: 370px;
  height: 240px;
  position: absolute;
  color: #fff;
  padding: 10px;
  z-index: 4;
  top: 0;
  left: -390px;
  /* left: 0; */
  opacity: 0;
}
.banZuZhang .teacher .content .shade h2 {
  font-weight: normal;
  font-size: 18px;
  text-align: center;
}
.banZuZhang .teacher .content .shade dl {
  font-size: 12px;
  padding-top: 10px;
}
.banZuZhang .teacher .content .shade dt {
  color: #ca4300;
  font-size: 14px;
}
.banZuZhang .teacher .content .shade dd i {
  color: #ca4300;
  font-size: 16px;
}
.banZuZhang .teacher .content .shade dt, .banZuZhang .teacher .content .shade dd {
  line-height: 18px;
}

.banZuZhang .teacher .btn {
  text-align: center;
  margin-top: 45px;
}
.banZuZhang .teacher .btn span {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background: #bfbfbf;
  margin: 0 1px;
  border-radius:50%;
  cursor: pointer;
  color: #fff;
  font-size: 14px;
}
.banZuZhang .teacher .btn .active {
  background: #ca4300;
}
/* 培训的企业 */
.banZuZhang .company {
  text-align: center;
  position: relative;
  width: 1226px;
  /* height: 200px;  */
}
.banZuZhang .company span {
  display: inline-block;
  padding: 3px 15px;
  color: #fff;
  margin: 0 3px 5px 3px;
  font-size: 12px;
  /* position: absolute; */
}
.banZuZhang .company span.bgcolor0 {
  background: #ca4300;
}
.banZuZhang .company span.bgcolor1 {
  background: #fca765;
}
.banZuZhang .company span.bgcolor2 {
  background: #c7c7c7;
}
/* 服务流程 */
.banZuZhang .step {
  padding-bottom: 60px;
}
.banZuZhang .step .item {
  width: 218px;
  height: 218px;
  border-radius: 50%;
  background: #f9f9f9;
  text-align: center;
  float: left;
}
.banZuZhang .step .item:hover {
  background: #ca4300;
  color: #fff;
}
.banZuZhang .step .item:hover p, .banZuZhang .step .item:hover em {
  color: #fff;
}
.banZuZhang .step .item i {
  display: block;
  width: 55px;
  height: 47px;
  margin-top: 15px;
}
.banZuZhang .step .icon1 {
  background: url('../../images/promotion/banZuZhang/step/icon1-a.png') no-repeat center center;
}
.banZuZhang .step .acitve .icon1  {
  background: url('../../images/promotion/banZuZhang/step/icon1.png') no-repeat center center;
}
.banZuZhang .step .icon2 {
  background: url('../../images/promotion/banZuZhang/step/icon2-a.png') no-repeat center center;
}
.banZuZhang .step .acitve .icon2  {
  background: url('../../images/promotion/banZuZhang/step/icon2.png') no-repeat center center;
}
.banZuZhang .step .icon3 {
  background: url('../../images/promotion/banZuZhang/step/icon3-a.png') no-repeat center center;
}
.banZuZhang .step .acitve .icon3  {
  background: url('../../images/promotion/banZuZhang/step/icon3.png') no-repeat center center;
}
.banZuZhang .step .icon4 {
  background: url('../../images/promotion/banZuZhang/step/icon4-a.png') no-repeat center center;
}
.banZuZhang .step .acitve .icon4  {
  background: url('../../images/promotion/banZuZhang/step/icon4.png') no-repeat center center;
}
.banZuZhang .step .item em {
  font-style: normal;
  color: #000;
  font-size: 20px;
  margin-top: 20px;
  display: inline-block;
}
.banZuZhang .step .item p {
 font-size: 16px;
 color: #666;
 line-break: 24px;
 margin-top: 20px;
}
.banZuZhang .step .dot {
  height: 218px;
  line-height: 218px;
  margin: 0 20px;
  float: left;
}
.banZuZhang .step .dot span {
  background: #ca4300;
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  margin: 0 5px;
}
/* 案例集锦 */
.banZuZhang .case {
  padding-bottom: 100px;
}
.banZuZhang .case .tab {
  float: left;
  width: 220px;
}
.banZuZhang .case .tab li {
  width: 220px;
  height: 154px;
  background: #aeaeae;
  border-bottom: 3px solid #f9f9f9;
  color: #fff;
  font-size: 16px;
  text-align: center;
  line-height: 154px;
  cursor: pointer;
}
.banZuZhang .case .tab li.active {
  background: #ca4300;
  font-weight: normal;
}
.banZuZhang .case .tab li.active span {
  border-bottom: 2px solid #fff;
  padding-bottom: 5px;
}
.banZuZhang .case .card {
  float: left;
  border: 2px solid #ca4300;
  width: 942px;
  padding: 30px;
  height: 404px;
  overflow: hidden;
  position: relative;
}
.banZuZhang .case .card .box {
  height: 1212px;
  width: 942px;
  position: absolute;
  top: 0;
  left: 0;
}
.banZuZhang .case .card .item {
  height: 404px;
  padding-top: 30px;
  padding-bottom: 32px;
}
.banZuZhang .case .card .left {
  float: left;
  width: 250px;
  height: 410px;
  padding-right: 30px;
  margin-right: 30px;
  border-right: 1px solid #ddd;
  text-align: center;
}
.banZuZhang .case .card .left p {
  text-align: center;
  font-size: 18px;
  color: #000;
}
.banZuZhang .case .card .left img {
  margin: 30px 0;
  display: inline-block;
  width: 250px;
  height: 280px;
}
.banZuZhang .case .card .left a {
  display: inline-block;
  width: 125px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #ca4300;
  border: 1px solid #ca4300;
  font-size: 14px;
}
.banZuZhang .case .card .left a:hover {
  background: #ca4300;
  color: #fff;
}
.banZuZhang .case .card .right {
  float: left;
  width: 630px;
}
.banZuZhang .case .card .right dl {
  padding-bottom: 15px;
}
.banZuZhang .case .card .right dt {
  color: #000;
  font-size: 14px;
  padding-bottom: 5px;
}
.banZuZhang .case .card .right dd {
  color: #7f7f7f;
  font-size: 14px;
  line-height: 20px;
}
/* 客户评价 */
.banZuZhang .evaluate {
  /* height: 800px; */
}
.banZuZhang .evaluate .img {
  float: left;
  width: 550px;
  height: 550px;
  position: relative;
}
.banZuZhang .evaluate .img img {
  position: absolute;
  top: 220px;
  left: 300px;
  width: 1px;
  height: 1px;
  z-index: 2;
}
.banZuZhang .evaluate .round {
 border-radius: 50%;
 position: absolute;
 background: rgba(254, 161, 89, .9);
}
.banZuZhang .evaluate .round1 {
  width: 216px;
  height: 216px;
  left: -100px;
  bottom: -100px;
  z-index: 1;
  opacity: 0;
}
.banZuZhang .evaluate .round2 {
  width: 140px;
  height: 140px;
  right: 0;
  bottom: 0;
  z-index: 3;
  opacity: 0;
}
.banZuZhang .evaluate .round3 {
  width: 416px;
  height: 416px;
  top: 0;
  left: 0;
  z-index: 1;
  opacity: 0;
}
.banZuZhang .evaluate .info {
  float: right;
  width: 660px;
}
.banZuZhang .evaluate .info li {
  border: 1px solid #ddd;
  padding: 15px;
  margin-bottom: 30px;
}
.banZuZhang .evaluate .info .content {
  float: left;
  width: 430px;
}
.banZuZhang .evaluate .info .tit {
  color: #ca4300;
  font-size: 16px;
}
.banZuZhang .evaluate .info .tit span {
  padding-left: 10px;
}
.banZuZhang .evaluate .info .name {
  height: 24px;
  line-height: 24px;
  font-size: 14px;
  color: #666;
  padding-top: 5px;
}
.banZuZhang .evaluate .info .con {
  line-height: 24px;
  font-size: 14px;
  color: #666;
  text-indent: 2em;
}
.banZuZhang .evaluate .info a {
  float: right;
  display: block;
  width: 130px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #ca4300;
  border: 1px solid #ca4300;
  font-size: 14px;
  margin-top: 60px;
}
.banZuZhang .evaluate .info a:hover {
  background: #ca4300;
  color: #fff;
}
/* 培训现场 */
.banZuZhang .scene {
  padding-bottom: 50px;
}
.banZuZhang .scene .img {
  position: relative;
  height: 420px;
}
.banZuZhang .scene .img > div {
  position: absolute;
}
.banZuZhang .scene .img .left {
  height: 250px;
  width: 340px;
  top: 84px;
  left: 0;
  z-index: 2;
  position: absolute;
}
.banZuZhang .scene .img .content {
  width: 560px;
  height: 420px;
  top: 0;
  left: 331px;
  z-index: 3;
  box-shadow: 0 8px 8px rgba(0,0,0,0.6);
  transition: .4s;
  transform: translate3d(0, -3px, 0);
}
.banZuZhang .scene .img .right {
  height: 250px;
  width: 340px;
  top: 84px;
  right: 0;
  z-index: 2;
  position: absolute;
}
.banZuZhang .scene .img .left img, .banZuZhang .scene .img .right img {
  height: 250px;
  width: 340px;
}
.banZuZhang .scene .img .content img {
  width: 560px;
  height: 420px;
}
.banZuZhang .scene .img .opacity {
  display: block;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .6);
  position: absolute;
  z-index: 5;
}
.banZuZhang .scene .img i {
  width: 100px;
  height: 100px;
  display: block;
  position: absolute;
  z-index: 6;
  cursor: pointer;
}
.banZuZhang .scene .prev {
  background: url("../../images/promotion/banZuZhang/scene/prev.png") no-repeat 0 center;
  left: 200px;
  top: 159px;
}
.banZuZhang .scene .next {
  right: 200px;
  top: 159px;
  background: url("../../images/promotion/banZuZhang/scene/next.png") no-repeat 0 center;
}
.banZuZhang .scene .btn {
  margin-top: 25px;
  text-align: center;
}
.banZuZhang .scene .btn span {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin: 0 2px;
  background: #a6a6a6;
  display: inline-block;
  cursor: pointer;
}
.banZuZhang .scene .btn .active {
  background: #ca4300;
}
/* 更多课程推荐 */
.banZuZhang .news {}
.banZuZhang .news .item {
  float: left;
  width: 291px;
  margin-right: 20px;
}
.banZuZhang .news .item:last-child {
  margin-right: 0;
}
.banZuZhang .news .item dt {
  font-size: 20px;
  color: #000;
  border-bottom: 2px solid #ca4300;
  padding-bottom: 15px;
  margin-bottom: 20px;
}
.banZuZhang .news .item dd {
  border-bottom: 1px dashed #ddd;
  font-size: 14px;
  height: 36px;
  line-height: 36px;
}
.banZuZhang .news .item dd a {
  color: #666;
}
.banZuZhang .news .item dd a:hover {
  color: #ca4300;
}
.banZuZhang .news .item .more {
  text-align: right;
  padding: 10px 0 50px 0;
}
.banZuZhang .news .item .more a {
  color: #ca4300;
  font-size: 14px;
}
/* 留言 */
.banZuZhang .messageBox {
  /* padding: 25px 0; */
  height: 70px;
  line-height: 70px;
  color: #fff;
  font-size: 16px;
  background: #232323;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 9999;
}
.banZuZhang .messageBox label {
  margin-right: 15px;
}
.banZuZhang .messageBox em {
  color: #f00;
}
.banZuZhang .messageBox input {
  width: 135px;
  height: 35px;
  line-height: 35px;
  border: 2px solid #232323;
  outline: 0;
}
.banZuZhang .messageBox input.focus {
  border: 2px solid #cc5500;
  background: #ffe0cc;
}
.banZuZhang .messageBox input.input-b {
  width: 340px;
}
.banZuZhang .messageBox button {
  background: #ca4300;
  color: #fff;
  height: 35px;
  line-height: 35px;
  text-align: center;
  width: 160px;
  border: 0;
  cursor: pointer;
  margin-left: 20px;
}
.banZuZhang .consult {
  padding-bottom: 100px;
  height: 45px;
}
.banZuZhang .consult label {
  float: left;
}
.banZuZhang .consult input {
  height: 45px;
  line-height: 45px;
  padding-left: 10px;
  border: 1px solid #ddd;
  background: #fff;
  margin-right: 20px;
  width: 179px;
}
.banZuZhang .consult input:last-child {
  margin-right: 10px;
}
.banZuZhang .consult input.input-big {
  width: 250px;
}
.banZuZhang .consult input:focus {
  border: 1px solid #ca4300;
  outline: none;
}
.banZuZhang .consult input.focus {
  border: 1px solid #ca4300;
}
.banZuZhang .consult button {
  background: #ca4300;
  color: #fff;
  height: 45px;
  line-height: 45px;
  text-align: center;
  width: 130px;
  border: 0;
  cursor: pointer;
  margin-left: 20px;
}
.banZuZhang .consult p {
  text-align: center;
  color: #999;
  font-size: 16px;
  padding-top: 20px;
}

.pop {
  display: none;
}
.pop .layout {
  background: rgba(0, 0, 0, .7);
  width: 1000px;
  height: 500px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
}
.pop .popBox {
  width: 300px;
  height: 160px;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999999;
  background: #fff;
  border-radius: 5px;
  margin: auto;
}
.pop .popBox .tit {
  text-align: left;
  background: #f5f5f5;
  padding-left: 20px;
  font-size: 14px;
  border-bottom: 1px solid #ddd;
  height: 35px;
  line-height: 35px;
  margin: 0;
  border-radius: 3px 3px 0 0;
}
.pop .popBox .cont {
  text-indent: 2em;
  font-size: 14px;
  padding: 20px;
}
.pop .popBox .btn {
  text-align: center;
  margin-top: 5px;
}
.pop .popBox .btn span {
  width: 50px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  border-radius: 3px;
  background: #ca4300;
  color: #fff;
  font-size: 14px;
  display: inline-block;
  cursor: pointer;
}

.consult-pop {
  display: none;
}
.consult-pop .layout {
  background: rgba(0, 0, 0, .7);
  width: 1000px;
  height: 500px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
}
.consult-pop .popBox {
  width: 0;
  height: 0;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999999;
  background: #fff;
  border-radius: 5px;
  margin: auto;
  text-align: center;
  padding: 30px;
  opacity: 0;
}
.consult-pop .popBox b {
  font-size: 30px;
  border-bottom: 2px solid #aeaeae;
  display: inline;
  padding-bottom: 20px;
  font-weight: normal;
}
.consult-pop .popBox p {
  color: #666;
  padding: 20px 0 0 0;
  font-size: 14px;
  margin: 20px 0 0 0;
}
.consult-pop .popBox em {
  color: #ca4300;
  font-size: 12px;
  font-style: normal;
}
.consult-pop .popBox input {
  width: 185px;
  height: 45px;
  margin-top: 20px;
  margin-right: 20px;
  outline: none;
  border: 1px solid #666;
  color: #888;
  padding-left: 10px;
}
.consult-pop .popBox input#phone, .consult-pop .popBox input#city {
  margin-right: 0;
}
.consult-pop .popBox input:focus {
  border: 1px solid #ca4300;
}
.consult-pop .popBox input.focus {
  border: 1px solid #ca4300;
}
.consult-pop .popBox textarea {
  border: 1px solid #666;
  width: 405px;
  height: 110px;
  margin-top: 20px;
  padding: 5px;
}
.consult-pop .popBox .content {
  display: none;
}
.consult-pop .popBox button {
  background: #ca4300;
  color: #fff;
  height: 45px;
  line-height: 45px;
  text-align: center;
  width: 415px;
  border: 0;
  cursor: pointer;
  margin-top: 20px;
}
.consult-pop .popBox .close {
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  right: 10px;
  font-style: normal;
}
.down-load-pop {
  display: none;
}
.down-load-pop .layout {
  background: rgba(0, 0, 0, .7);
  width: 1000px;
  height: 500px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
}
.down-load-pop .popBox {
  width: 0;
  height: 0;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999999;
  background: #fff;
  border-radius: 5px;
  margin: auto;
  text-align: center;
  padding: 30px;
  opacity: 0;
}
.down-load-pop .popBox b {
  font-size: 30px;
  border-bottom: 2px solid #aeaeae;
  display: inline;
  padding-bottom: 20px;
  font-weight: normal;
}
.down-load-pop .popBox p {
  color: #666;
  padding: 20px 0 0 0;
  font-size: 14px;
  margin: 20px 0 0 0;
}
.down-load-pop .popBox em {
  color: #ca4300;
  font-size: 12px;
  font-style: normal;
}
.down-load-pop .popBox input:not([type="checkbox"]) {
  width: 348px;
  height: 48px;
  margin-top: 20px;
  outline: none;
  border: 1px solid #666;
  color: #888;
  padding-left: 10px;
  box-sizing: border-box;
}
.down-load-pop .popBox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  vertical-align: bottom;
}
.down-load-pop .popBox input#phone, .down-load-pop .popBox input#city {
  margin-right: 0;
}
.down-load-pop .popBox input:focus {
  border: 1px solid #ca4300;
}
.down-load-pop .popBox input.focus {
  border: 1px solid #ca4300;
}
.down-load-pop .popBox textarea {
  border: 1px solid #666;
  width: 405px;
  height: 110px;
  margin-top: 20px;
  padding: 5px;
}
.down-load-pop .popBox .content {
  display: none;
}
.down-load-pop .popBox button {
  background: #ca4300;
  color: #fff;
  height: 48px;
  line-height: 48px;
  text-align: center;
  width: 348px;
  border: 0;
  cursor: pointer;
  margin-top: 20px;
  font-size: 16px;
}
.down-load-pop .popBox .close {
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  right: 10px;
  font-style: normal;
}
