<% include header.html %>
  <% include ./components/header.html %>

    <div class="myLocation">
      <div class="myLocationBox">
        <div class="myLocation-word1"><a href="/">第三方检测机构</a></div>
        <div class="myLocation-icon1"></div>
        <div class="myLocation-word2">“<%= keyword %>”的搜索结果</div>
      </div>
    </div>

    <div class="SearchBox">
      <div class="searchleftBox" id='search' v-cloak>
        <div class="download_tab" id='download_tab'>
          <ul>
            <li v-for='(type, index) of types' :key='index' :class='{active: currType === type.id}'
              @click='handlerTab(type.id)'>
              {{ type.name }}
              <i></i>
            </li>
          </ul>
        </div>
        <div class="download_filter">
          <div class="download_filster_industry" style="display: flex;">
            <span class="download_filter_tit">行业分类：</span>
            <div class="download_filster_checkboxGroup" id='download_industry'>
              <label v-for='(item, index) of tradeNavi' :key='index' :class='{active: item.isCheck}'
                @click='handlerIndustry(item)'>
                <i></i>
                <span>{{ item.name }}</span>
              </label>
            </div>
          </div>
          <div class='download_filster_service' style="display: flex;">
            <span class="download_filter_tit">服务类型：</span>
            <div class="download_filster_checkboxGroup" id='download_service'>
              <label v-for='(item, index) of serviceNavi' :key='index' :class='{active: item.isCheck}'
                @click='handlerService(item)'>
                <i></i>
                <span>{{ item.name }}</span>
              </label>
            </div>
          </div>
          <div v-if="isActives.length">
            <div class="download_filter_result" style="display: flex;">
              <span class="download_filter_tit">您已选择：</span>
              <div class="download_filter_result--items">
                <div id='download_filter_result'>
                  <label v-for='(item, index) of isActives' :key='index'>
                    <span>{{ item.name }}</span>
                    <i @click='handleCloseItem(item)'>x</i></label>
                  <em id='reset' @click='handleClear'>清除</em>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="search_list clearfix">
          <ul v-if='searchList.length'>
            <li v-for='(item, index) of searchList' :key='index'>
              <h2>
                <a v-html='item.title' v-bind:href='item | url' target="_blank"></a>
                <!-- <em v-html='item.title' v-bind:href='item | url' @click="e => itemClick(e, item)"></em> -->
                <span :class="{ active: item.id.indexOf('T_') > -1 }">{{ item.id | type }}</span>
              </h2>
              <p v-html='item.content' v-if='item.content'></p>
              <div class="search_list_down" v-if='!item.content && item.url'>
                <a content="down" :href="item.url" download="" target="_blank">下载</a>
              </div>
            </li>
          </ul>
          <div v-else>
            <div class='search_empty_list' v-if="searchhint==1">
              【加载中......】
            </div>
            <div class='search_empty_list' v-else>
              【很抱歉，没有为您找到对应的结果，请扩大筛选范围】
            </div>
          </div>


        </div>
        <!-- <div class="pagination" style="margin: 20px 0;">
            <ul class="serverpages" id='pagination'>
                <li v-for='(item, index) of totalNum' :class="{serverpage: true, pageActive: currPage === item}"
                    @click='handlePage(item)'>{{item}}</span>
            </ul>
        </div> -->
        <div class="pagination" style="margin: 20px 0; width: auto;" v-if='totalNum'>
          <el-pagination @current-change="handleCurrentChange" :current-page.sync="currPage" :page-size="10"
            @size-change="handleSizeChange" layout="prev, pager, next, jumper" :total="totalNum">
          </el-pagination>
        </div>
      </div>
      <div class="searchrightBox">
        <% if(goodsData.totalNum> 0){ %>
          <div class="order-line-search-result">
            <div>
              <i class="icon"></i>
              <div class="img" style="background-image: url('<%- goodsData.img %>');">
                <!-- <img src="<%- goodsData.img %>" alt="" /> -->
              </div>
              <div class="num">
                “<span>
                  <%= keyword %>
                </span>”相关服务<%- goodsData.totalNum %>个
              </div>
              <div class="description"><i></i>快速下单 <i></i>组合优惠 <i></i>周期保障 <i></i>即时沟通</div>
              <div class="price">￥ <%= goodsData.price %>
              </div>
              <a href="<%- portalHost %>/order-online?keyword=<%= keyword %>" target="_blank">查看详情</a>
            </div>
          </div>
          <% } %>
            <div class="searchrightBox1">
              <div class="your">
                <div class="yours">
                  <span class="your-word">行业解决方案</span>
                  <span class="dragbut">
                    <img src="<%- locals.static %>/images/jianhao.png" alt="" class="dragbut-icon">
                  </span>
                </div>

                <div class="your-cons first">
                  <div class="yourcon">
                    <ul>
                      <% if(tradeNavi && tradeNavi.length> 0){ %>
                        <% for (var item of tradeNavi){ %>
                          <li class="yourconLi">
                            <a href="/industry/<%- item.alias %>/"><%- item.name %></a>
                          </li>
                          <% } %>
                            <% } %>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="your">
                <div class="yours">
                  <span class="your-word">我们的服务</span>
                  <span class="dragbut">
                    <img src="<%- locals.static %>/images/jiahao.png" alt="" class="dragbut-icon">
                  </span>
                </div>

                <div class="your-cons">
                  <div class="yourcon">
                    <ul>
                      <% if(serviceNavi && serviceNavi.length> 0){ %>
                        <% for (var item of serviceNavi){ %>
                          <li class="yourconLi">
                            <a href="/service/<%- item.alias %>/"><%- item.name %></a>
                          </li>
                          <% } %>
                            <% } %>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="your">
                <div class="yours">
                  <span class="your-word">热点服务</span>
                  <span class="dragbut">
                    <img src="<%- locals.static %>/images/jiahao.png" alt="" class="dragbut-icon">
                  </span>
                </div>

                <div class="your-cons">
                  <div class="yourcon">
                    <ul>
                      <% if(hot && hot.length> 0){ %>
                        <% for (var item of hot){ %>
                          <li class="yourconLi">
                            <a href="/sku/product/<%- item.id %>/"><%- item.name %></a>
                          </li>
                          <% } %>
                            <% } %>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
      </div>
    </div>

    <script>
      var newVue = new Vue({
        name: 'search',
        el: '#search',
        data: {
          types: [
            {
              name: '全部',
              id: 0
            },
            {
              name: '服务与解决方案',
              id: 1
            },
            {
              name: '资讯中心',
              id: 2
            },
            {
              name: '集团新闻',
              id: 3
            },
            {
              name: '资料下载',
              id: 4
            },
          ],
          currType: 0,
          form: {
            title: '<%= keyword %>',
            industry: '<%- industry || "" %>',
            service: '<%- service || "" %>',
            type: '<%- type || "" %>',
            pageNum: '<%- pageNum || 1 %>',
            pageRow: '<%- pageRow || 10 %>',
            _csrf: '<%- csrf %>'
          },
          tradeNavi: <%- JSON.stringify(tradeNavi) %>,
          serviceNavi: <%- JSON.stringify(serviceNavi) %>,
          goodsData: <%- JSON.stringify(goodsData) %>,
          searchList: [],
          totalNum: 0,
          currPage: 1,
          isActives: [],
          searchhint: 1  //1 加载中  2 >15s没数据
        },

        created: function () {
        },
        mounted: function () {
          if (this.form.title) this.getSeachList()
        },
        methods: {
          // itemClick(e, item) {
          //   e.preventDefault()
          //   var name = item.title.replace(/<font color='red'>/g,'').replace(/<\/font>/g,'')
          //   var url = this.$options.filters.url(item)
          //   var id = item.id.split('_')[1];
          //   ga('ec:addProduct', {
          //     'id': id,
          //     'name': name,
          //     'category': '',
          //     'brand': '',
          //     'variant': '',
          //     'position': 1
          //   });
          //   ga('ec:setAction', 'click', {list: 'search list'});

          //   // Send click with an event, then send user to product page.
          //   ga('send', 'event', 'UX', 'click', 'Results', {
          //     hitCallback: function() {
          //       document.location = url;
          //     }
          //   });

          // },
          getSeachList: function () {
            var that = this
            var loading = this.$loading({
              lock: true,
              text: '加载中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
              fullscreen: false
            });
            that.searchhint = 1
            // this.form.csrf = '<%- csrf %>'
            $.ajaxSettings.timeout = '15000';
            $.post('/search', this.form, function (result) {
              if (result.resultCode === '0') {
                if (result.data) {
                  that.searchList = result.data.data
                  var searchList = result.data.data
                  that.googleListPreview(searchList)
                  that.totalNum = result.data.totalNum //Math.ceil(result.data.totalNum / 10)
                  $('html,body').stop().animate({ scrollTop: 0 });
                  if (that.searchList.length == 0) {
                    that.searchhint = 2
                  }
                }
              } else {
                that.$message({
                  message: result.resultMsg,
                  type: 'error'
                });
                that.searchhint = 2
              }
              loading.close();
            }).error(function (xhr, status, info) {
              that.searchhint = 2
              loading.close();
            })
          },
          googleListPreview: function (list) {
            // for (var i = 0; i < list.length; i++) {
            //   var item = list[i]
            //   // console.log(item.title.replace(/<font color='red'>/g,'').replace(/<\/font>/g,''), item.id.split('_')[1])
            //   ga('ec:addImpression', {
            //     'id': item.id.split('_')[1],
            //     'name': item.title.replace(/<font color='red'>/g,'').replace(/<\/font>/g,''),
            //     'category': '',
            //     'brand': '',
            //     'variant': '',
            //     'list': 'search list',
            //     'position': i
            //   });
            //   ga('send', 'pageview');
            // }
          },
          handlerTab: function (id) {
            this.form.type = id ? id : ''
            this.currType = id
            this.currPage = 1
            this.form.pageNum = 1
            if (this.form.title) this.getSeachList()
          },
          handlerIndustry: function (item) {
            if (item.isCheck) {
              for (var i = 0; i < this.tradeNavi.length; i++) {
                this.tradeNavi[i].isCheck = false
              }
              this.form.industry = ''
            } else {
              for (var i = 0; i < this.tradeNavi.length; i++) {
                this.tradeNavi[i].isCheck = false
              }
              item.isCheck = true
              this.form.industry = item.name
            }
            // item.isCheck = !item.isCheck
            this.createActivesList()
          },
          handlerService: function (item) {
            if (item.isCheck) {
              for (var i = 0; i < this.serviceNavi.length; i++) {
                this.serviceNavi[i].isCheck = false
              }
              this.form.service = ''
            } else {
              for (var i = 0; i < this.serviceNavi.length; i++) {
                this.serviceNavi[i].isCheck = false
              }
              item.isCheck = true
              this.form.service = item.name
            }
            // item.isCheck = !item.isCheck
            this.createActivesList()
          },
          createActivesList: function () {
            this.isActives = []
            for (var i = 0; i < this.tradeNavi.length; i++) {
              if (this.tradeNavi[i].isCheck) {
                this.isActives.push(this.tradeNavi[i])
              }
            }
            for (var i = 0; i < this.serviceNavi.length; i++) {
              if (this.serviceNavi[i].isCheck) {
                this.isActives.push(this.serviceNavi[i])
              }
            }
            this.currPage = 1
            this.form.pageNum = 1
            if (this.form.title) this.getSeachList()
          },
          handleCloseItem: function (item) {
            item.isCheck = false
            for (var i = 0; i < this.tradeNavi.length; i++) {
              if (this.tradeNavi[i].id === item.id && this.tradeNavi[i].name === item.name) {
                this.form.industry = ''
              }
            }
            for (var i = 0; i < this.serviceNavi.length; i++) {
              if (this.serviceNavi[i].id === item.id && this.serviceNavi[i].name === item.name) {
                this.form.service = ''
              }
            }
            this.createActivesList()
          },
          handleClear: function () {
            this.isActives = []
            for (var i = 0; i < this.tradeNavi.length; i++) {
              this.tradeNavi[i].isCheck = false
            }
            for (var i = 0; i < this.serviceNavi.length; i++) {
              this.serviceNavi[i].isCheck = false
            }
            this.form.service = ''
            this.form.industry = ''
            this.createActivesList()
          },
          // handlePage(currPage) {
          //     this.currPage = currPage
          //     this.form.pageNum = currPage
          //     this.getSeachList()
          // },
          handleSizeChange: function (val) {
            this.form.pageNum = val
            if (this.form.title) this.getSeachList()
          },
          handleCurrentChange: function (val) {
            this.form.pageNum = val
            if (this.form.title) this.getSeachList()
          }
        },
        beforeDestroy: function () {
          if (this.timer) { //如果定时器还在运行 或者直接关闭，不用判断
            clearInterval(this.timer); //关闭
          }
        },
        filters: {
          type: function (val) {
            var tag_name = '',
              tagLabel = val.split('_')[0];
            switch (tagLabel) {
              case 'S':
                tag_name = '服务与解决方案'
                break;
              case 'C':
                tag_name = '案例'
                break;
              case 'N':
                tag_name = '新闻'
                break;
              case 'R':
                tag_name = '资源'
                break;
              case 'T':
                tag_name = '热门服务'
                break;
              default:
                tag_name = '未知类型'
                break;
            }
            return tag_name;
          },
          url: function (item) {
            var url = '',
              tagLabel = item.id.split('_')[0],
              id = item.id.split('_')[1];
            switch (tagLabel) {
              case 'S':
                if (item.ctype && item.calias) {
                  url = '/' + item.ctype + '/' + item.calias + '/' + id
                } else {
                  url = '/sku/' + item.alias + '/' + id;
                }
                break;
              case 'C':
                url = '/case/' + item.alias + '/detail-' + id + '.html'
                break;
              case 'N':
                url = '/news/' + item.alias + '/detail-' + id + '.html'
                break;
              case 'R':
                url = item.url
                break;
              case 'T':
                url = item.url
                break;
              default:
                url = 'javascript:void(0)'
                break;
            }
            return url;
          }
        }
      });
      $(function () {
        // side right
        var $_yours = $(".yours");
        $_yours.click(function () {
          var $_con = $(this).siblings(".your-cons");
          if ($_con.css("height") == "0px") {
            $_con.animate({ "height": 486 }).end().parents(".your").siblings(".your").children(".your-cons").animate({ "height": 0 });
            $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jianhao.png');
            $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(0).attr("src", "<%- locals.static %>/images/jiahao.png");
            $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(1).attr("src", "<%- locals.static %>/images/jiahao.png")
          } else {
            $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jiahao.png');
            $_con.animate({ "height": 0 });
          }
        })
      })
    </script>

    <% include footer.html %>