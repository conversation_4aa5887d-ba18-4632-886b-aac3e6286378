.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

ul,
li {
  list-style: none;
}

button {
  border: 0;
  outline: 0;
  cursor: pointer;
}

img {
  border: 0;
}

.wrap {
  width: 1226px;
  margin: 0 auto;
}

.bg-white {
  background: #fff;
}

.bg-gray {
  background: #f8f8f8;
}

.bg-orange {
  background: #ca4300;
}
.jiance {
  .banner {
    width: 100%;
    height: 481px;
    background: url("../../images/promotion/jiance/banner.png") no-repeat center center;
    .banner-btn {
      /*width: 167px;*/
      margin: 260px 0 0 530px;
    }
  }
  .banner .kf5-btn {
    width: 200px;
    margin: 290px 0 0 240px;
    height: 46px;
    line-height: 46px;
    display: block;
    text-align: center;
    background: linear-gradient(0deg, #FE5003, #FE7F03);
    box-shadow: 6px 7px 9px 0px rgba(42, 78, 102, 0.6);
    position: relative;
    border-radius: 5px;
    .kf5-btn-text {
      width: 100%;
      height: 46px;
      border-radius: 5px;
      color: #fff;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      text-align: center;
      font-size: 18px;
      font-weight: bold;
      line-height: 46px;
    }
    .kf5-btn-layout {
      width: 0;
      height: 100%;
      background: #cc5500;
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 5px;
    }
    &:hover .kf5-btn-layout{
      width: 100%;
      transition: 0.4s;
    }
  }
  .china-floor {
    padding: 50px 0;
  }
  .title-content {
    p {
      text-align: center;
      padding: 0;
      font-size: 17px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #878787;
    }
    .title {
      font-size: 28px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
    .sub-title {
      margin-bottom: 15px;
    }
    .main-line {
      margin: 8px auto 24px;
      width: 40px;
      height: 3px;
      background: #ca4300;
    }
  }
  .china-card {
    display: flex;
    margin-top: 50px;
  }
  .china-card .card-item {
    width: 284px;
    height: 310px;
    background: #FFFFFF;
    box-shadow: 6px 10px 15px 0px rgba(0, 0, 0, 0.05);
    padding: 0 37px;
    &:not(:last-child) {
      margin-right: 30px;
    }
    &:hover {
      box-shadow: 4px 7px 24px 0px rgba(0, 0, 0, 0.18);
      transition: .4s;
      //transform: translate3d(0, -3px, 0);
      .card-label {
        color: #ca4300;
      }
      .card-detail {
        color: #333333;
      }
    }
    .card-icon {
      text-align: center;
    }
    &:nth-child(1) .card-icon {
      margin: 46px 0 35px;
      height: 58px;
    }
    &:nth-child(2) .card-icon {
      margin: 40px 0 32px;
      height: 67px;
    }
    &:nth-child(3) .card-icon {
      margin: 45px 0 40px;
      height:57px;
    }
    &:nth-child(4) .card-icon {
      margin: 50px 0 42px;
      height:52px;
    }
    .line-icon {
      margin: 23px 0 26px;
      height: 1px;
      background: #D2D2D2;
      border-radius: 1px;
      position: relative;
      .circle-icon {
        position: absolute;
        width: 13px;
        height: 13px;
        background: #D2D2D2;
        border: 4px solid #FFFFFF;
        border-radius: 50%;
        top: -6px;
        right: 99px;
        box-sizing: border-box;
      }
    }
    .card-label {
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
      text-align: center;
      line-height: 24px;
    }
    .card-detail {
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #878787;
      text-align: center;
      line-height: 18px;
      span {
        display: inline-block;
        margin-bottom: 13px;
      }
    }
  }
  .jianceyewu-floor {
    padding: 68px 0 72px;
    .jianceyewu-card {
      display: flex;
      margin-top: 50px;
      .card-item {
        width: 284px;
        height: 310px;
        padding: 0 50px;
        box-sizing: border-box;
        &:not(:last-child) {
          margin-right: 30px;
        }
        &:nth-child(1), &:nth-child(3) {
          background: url("../../../public/images/promotion/jiance/card-bg-1.png");
          .card-btn:before {
            background: #333;
          }
          .card-btn:hover {
            border: 1px solid #333;
            &:before {
              width: 124px;
            }
          }
        }
        &:nth-child(2), &:nth-child(4) {
          background: url("../../../public/images/promotion/jiance/card-bg-2.png");
        }
        .card-title {
          font-size: 24px;
          font-weight: bold;
          color: #fff;
          line-height: 25px;
          margin-top: 57px;
          text-align: center;
          margin-bottom: 15px;
        }
        .card-detail {
          font-size: 18px;
          color: #fff;
          line-height: 30px;
          text-align: center;
        }
        .card-btn {
          margin: 25px auto 0;
          text-transform: uppercase;
          overflow: hidden;
          width: 122px;
          height: 38px;
          line-height: 38px;
          text-align: center;
          border: 1px solid #fff;
          color: #fff;
          font-size: 16px;
          font-style: normal;
          position: relative;
          cursor: pointer;
          & > div {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 2;
          }
          &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 80px;
            background: #ca4300;
            transition: 0.2s;
          }
          &:hover {
            border: 1px solid #ca4300;
            &:before {
              width: 124px;
            }
          }
        }
      }
    }
  }
  .fuwu-floor {
    padding: 56px 0 45px;
    .fuwu-list {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      .fuwu-item {
        box-sizing: border-box;
        padding: 0 20px;
        height: 40px;
        background: #F8F8F8;
        border: 1px solid #D2D2D2;
        border-radius: 20px;
        line-height: 40px;
        cursor: pointer;
        margin-top: 29px;
        margin-right: 15px;
        &:hover {
          background: #ffefe5;
          border-color: #ca4300;
        }
        &.is-active {
          background: #ca4300;
          color: #fff;
          border-color: #ca4300;
        }
      }
    }
  }
  .qa-floor {
    padding: 75px 0 57px;
    & * {
      box-sizing: border-box;
    }
    .qa-content {
      margin-top: 52px;
      height: 453px;
      background: url("../../../public/images/promotion/jiance/qa-bg.png");
    }
    .qa-title {
      width: 857px;
      height: 40px;
      background: url("../../../public/images/promotion/jiance/qa-title.png");
      font-size: 18px;
      font-weight: bold;
      line-height: 40px;
      color: #fff;
      padding-left: 16px;
    }
    .qa-detail {
      position: relative;
      width: 911px;
      padding-left: 40px;
      line-height: 40px;
      font-size: 18px;
      color: #333;
      .green-text {
        position: absolute;
        left: 16px;
        font-weight: bold;
        color: #ca4300;
      }
      ul li:before {
        content: "";
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #ca4300;
        vertical-align: middle;
        margin: 0 5px;
        position: relative;
        top: -2px;
      }
    }
  }
  .xuqiu-floor {
    padding-bottom: 59px;
    .xuqiu-content {
      margin-top: 56px;
      display: flex;
    }
    .xuqiu-item {
      font-size: 20px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
      line-height: 20px;
      position: relative;
      padding-left: 4px;
      box-sizing: border-box;
      &:before {
        box-sizing: border-box;
        content: '';
        width: 4px;
        height: 20px;
        background: #EC6519;
        border: 2px solid #EC6519;
        position: absolute;
        left: 0;
        bottom: 0;
      }
      &:last-child:after {
        box-sizing: border-box;
        content: '';
        width: 4px;
        height: 20px;
        background: #EC6519;
        border: 2px solid #EC6519;
        position: absolute;
        right: 0;
        bottom: 0;
      }
      & > div {
        text-align: center;
      }
      img {
        margin-bottom: 48px;
      }
    }
    .xuqiu-item:nth-child(1) {
      width: 228px;
    }
    .xuqiu-item:nth-child(2) {
      width: 260px;
    }
    .xuqiu-item:nth-child(3) {
      width: 251px;
    }
    .xuqiu-item:nth-child(4) {
      width: 265px;
    }
    .xuqiu-item:nth-child(5) {
      width: 215px;
      padding: 0 4px;
    }
  }
  .shuju-floor {
    padding: 62px 0 0;
    background: url("../../../public/images/promotion/jiance/floor-bg.png") no-repeat center center;
    box-sizing: border-box;
    height: 341px;
    .shuju-list {
      display: flex;
      padding-left: 63px;
    }
    .shuju-item {
      margin-top: 50px;
      padding: 12px 0;
      .shuju-detail {
        font-size: 33px;
        font-family: Univers Condensed;
        font-weight: bold;
        font-style: italic;
        color: #ca4300;
        line-height: 1;
        //background: linear-gradient(235deg, #FE8A02 0%, #ca4300 100%);
        //-webkit-background-clip: text;
        //-webkit-text-fill-color: transparent;
        .shuju-unit {
          font-size: 22px;
        }
      }
      .shuju-tips {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #333333;
        line-height: 16px;
        margin-top: 24px;
      }
    }
    .shuju-divider {
      margin-top: 50px;
      width: 17px;
      height: 100px;
    }
  }
  .liucheng-floor {
    padding: 40px 0 68px;
    .liucheng-content {
      background: url("../../../public/images/promotion/jiance/liucheng-bg.png") no-repeat center right;
      height: 340px;
      margin-top: 44px;
      position: relative;
    }
    .liucheng-step-img {
      position: absolute;
      left: 4px;
      top: 33px;
    }
    .liucheng-step-list {
      position: absolute;
      top: 33px;
      left: 66px;
    }
    .liucheng-step-item {
      font-size: 20px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 44px;
      margin-bottom: 26px;
      .liucheng-step-num {
        font-size: 24px;
        font-family: Univers 67 Condensed;
        font-weight: bold;
        margin-right: 21px;
      }
    }
    .liucheng-left {
      width: 315px;
      height: 99px;
      top: 33px;
      position: absolute;
      left: 382px;
      background: url("../../../public/images/promotion/jiance/liucheng-left.png");
      padding: 25px 48px 26px 76px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      box-sizing: border-box;
      .liucheng-left-item {
        font-size: 17px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-style: italic;
        color: #ca4300;
        line-height: 17px;
        margin-bottom: 14px;
        &:before {
          content: '';
          display: inline-block;
          width: 5px;
          height: 5px;
          background: #ca4300;
          margin-right: 7px;
          position: relative;
          top: -4px;
        }
      }
    }
    .liucheng-right {
      width: 315px;
      height: 130px;
      top: 164px;
      position: absolute;
      left: 444px;
      background: url("../../../public/images/promotion/jiance/liucheng-right.png");
      padding: 27px 76px 25px 61px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .liucheng-right-item {
        font-size: 17px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-style: italic;
        color: #333333;
        &:before {
          content: '';
          display: inline-block;
          width: 5px;
          height: 5px;
          background: #626262;
          margin-right: 6px;
          position: relative;
          top: -4px;
        }
      }
    }
  }
  .zhengshu-floor {
    height: 485px;
    position: relative;
    .zhengshu-list {
      position: absolute;
      top: 48px;
      z-index: 1;
      margin: 54px auto 0;
      width: 1114px;
      display: flex;
      justify-content: space-between;
      left: 56px;
      p {
        text-align: center;
        margin-top: 19px;
      }
    }
  }
  .lianxi-floor {
    padding: 73px 0 83px;
    .lianxi-list {
      margin-top: 50px;
      display: flex;
      justify-content: space-between;
    }
    .lianxi-item {
      width: 284px;
      height: 293px;
      border: 1px solid #DCDCDC;
      box-sizing: border-box;
      cursor: pointer;
      font-size: 22px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      line-height: 1;
      display: block;
      &:hover {
        background: #FFFFFF;
        border: 1px solid #ca4300;
        box-shadow: 9px 8px 18px 0px rgba(25, 28, 30, 0.13);
      }
      & * {
        text-align: center;
      }
      .title {
        margin-bottom: 15px;
      }
      .detail {
        color: #878787;
        font-size: 16px;
        margin-bottom: 10px;
      }
    }
  }
  .fuwu-detail-floor {
    padding: 46px 0 41px;
    .fuwu-detail-item {
      display: flex;
      .fuwu-detail-left {
        width: 265px;
        height: 213px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-right: 27px;
        .label {
          font-size: 18px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #343434;
        }
        img {
          width: 265px;
          height: 168px;
        }
      }
      .fuwu-detail-right {
        //display: flex;
        //flex-direction: column;
        flex: 1;
      }
      .fuwu-detail-top {
        display: flex;
        .hot-fuwu-label {
          font-size: 18px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #343434;
          line-height: 18px;
          padding-bottom: 25px;
          width: 108px;
          border-bottom: 2px solid #ca4300;
          text-align: center;
          color: #ca4300;
        }
        .all-fuwu-label {
          flex: 1;
          height: 18px;
          padding-bottom: 25px;
          border-bottom: 2px solid #D3D3D3;
          text-align: right;
        }
      }
      .all-fuwu-link {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ca4300;
        line-height: 18px;
        &:after {
          border: 6px solid transparent;
          border-left-color: #ca4300;
          display: inline-block;
          content: "";
          margin-left: 5px;
        }
      }
      .fuwu-detail-list {
        display: flex;
        flex-wrap: wrap;
        padding-top: 40px;
      }
      &#fuwu-detail-item7 .fuwu-detail-list-item{
        &:nth-child(4n + 1) {
          width: 25%;
        }
        &:nth-child(4n + 2) {
          width: 25%;
        }
        &:nth-child(4n + 3) {
          width: 25%;
        }
        &:nth-child(4n + 4) {
          width: 25%;
        }
      }
      &#fuwu-detail-item11 .fuwu-detail-list-item {
        &:nth-child(4n + 1) {
          width: 28%;
        }
        &:nth-child(4n + 2) {
          width: 22%;
        }
        &:nth-child(4n + 3) {
          width: 28%;
        }
        &:nth-child(4n + 4) {
          width: 22%;
        }
      }
      &#fuwu-detail-item9 .fuwu-detail-list-item {
        &:nth-child(4n + 1) {
          width: 28%;
        }
        &:nth-child(4n + 2) {
          width: 23%;
        }
        &:nth-child(4n + 3) {
          width: 25%;
        }
        &:nth-child(4n + 4) {
          width: 24%;
        }
      }
      .fuwu-detail-list-item {
        font-size: 17px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #343434;
        line-height: 17px;
        margin-bottom: 25px;
        &:hover {
          color: #ca4300;
          text-decoration: underline;
        }
        &:nth-child(4n + 1) {
          width: 27%;
        }
        &:nth-child(4n + 2) {
          width: 23%;
        }
        &:nth-child(4n + 3) {
          width: 25%;
        }
        &:nth-child(4n + 4) {
          width: 25%;
        }
        &:before {
          content: "";
          width: 6px;
          height: 6px;
          background: #ca4300;
          display: inline-block;
          margin-right: 10px;
          position: relative;
          top: -3px;
        }
      }
      &.is-hidden {
        display: none;
      }
    }
  }
}
