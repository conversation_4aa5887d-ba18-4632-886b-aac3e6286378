<% include ./../header.html %>
<div class="myLocation">
    <div class="myLocationBox">
        <div class="myLocation-word1"><a href="/">第三方检测机构</a></div>
        <div class="myLocation-icon1"></div>
        <div class="myLocation-word2"><a href="javascrpit:void(0);">业务咨询</a></div>
    </div>
</div>

<div class="SearchBox ticket">
    <div class="searchleftBox">
        <div class="messageCon">
            <div class="messageConT">CONTACT US</div>
            <div class="messageCon_switch">
                <a href='/quote'>中文</a>
                <a class="active" href='/ticket/en'>EN</a>
            </div>
        </div>
        <div class="tip">
            Fields marked with an asterisk (<span class="mustStar">*</span>) are mandatory.
        </div>
        <div class="selectBox">
            <div class="select">
                <div class="selectT">Sector<span class="mustStar">*</span></div>
                <div class="selectC">
                    <label class="form-label" style="margin-top:0px;height: 50px">
                        <select class="filter-select" id="trade" style="width: 260px;">
                            <option value="0">Select from the list</option>
                            <% if(tradeNavi && tradeNavi.length > 0){ %>
                            <% for (var item of tradeNavi){ %>
                            <option value="<%- item.id %>"><%- item.en_name || item.name %></option>
                            <% } %>
                            <% } %>
                            <option value="其他">Others</option>
                        </select>
                    </label>
                </div>
                <p class="error">*Required fields</p>
            </div>
            <div class="select">
                <div class="selectT">Service Type<span class="mustStar">*</span></div>
                <div class="selectC">
                    <label class="form-label" style="margin-top:0px;height: 50px">
                        <select class="filter-select" id="service" style="width: 260px;">
                            <option value="0">Select from the list</option>
                            <% if(serviceNavi && serviceNavi.length > 0){ %>
                            <% for (var item of serviceNavi){ %>
                            <option value="<%- item.id %>"><%- item.en_name || item.name %></option>
                            <% } %>
                            <% } %>
                            <option value="其他">Others</option>
                        </select>
                    </label>
                </div>
                <p class="error">*Required fields</p>
            </div>
            <div class="select">
                <div class="selectT">Inquiry Type</div>
                <div class="selectC">
                    <label class="form-label" style="margin-top:0px;height: 50px">
                        <select class="filter-select" id="type" style="width: 260px;">
                            <option value="服务询价">Request Quotation</option>
                            <option value="业务咨询">Request Information</option>
                            <option value="建议反馈">Provide Your feedback</option>
                            <option value="其他">Others</option>
                        </select>
                    </label>
                </div>
                <p class="error">*Required fields</p>
            </div>
        </div>

        <div class="content">
            <div class="selectT">How can we Help?<span class="mustStar">*</span></div>
            <textarea id="textarea" placeholder="Pls leave your request and your product name. "
                style="overflow-y: auto" maxlength='65535'></textarea>
            <p class="error">*Required fields</p>
        </div>

        <div class="yoursInfo">
            <div class="yoursT">Your Contact Information</div>
            <div class="yoursInfoC">
                <div class="yoursInfoCC">
                    <div class="selectT">Name<span class="mustStar">*</span></div>
                    <input type="text" class="selectC" placeholder="Pls input name" id="customer" maxlength='50'>
                    <p class="error">*Required fields</p>
                </div>
                <div class="yoursInfoCC">
                    <div class="selectT">Mobile Phone<span class="mustStar">*</span></div>
                    <input type="text" class="selectC" placeholder="Pls input Mobile Phone" id="phone">
                    <p class="error" id="phoneTip">*Required fields</p>
                </div>
                <div class="yoursInfoCC">
                    <div class="selectT">Email Address<span class="mustStar">*</span></div>
                    <input type="text" class="selectC" placeholder="Pls input Email Address" id="email" maxlength='50'>
                    <p class="error">*Required fields</p>
                </div>
                <div class="yoursInfoCC" style="position: relative">
                    <div class="selectT">Location of Factory/Supplier<span class="mustStar">*</span></div>
                    <input type="text" class="selectC country"
                        placeholder="Select from the list Location of Factory/Supplier" id="provice" readonly="">
                    <p class="error">*Required fields</p>
                    <div class="provice" style="position: absolute">
                        <p class="citys">
                            <a href="javascrpit:void(0);" data-jp="ah" data-qp="Anhui" data-value="123"
                                data-name="安徽省">Anhui</a>
                            <a href="javascrpit:void(0);" data-jp="bj" data-qp="Beijing" data-value="107"
                                data-name="北京市">Beijing</a>
                            <a href="javascrpit:void(0);" data-jp="cq" data-qp="Chongqing" data-value="110"
                                data-name="重庆市">Chongqing</a>
                            <a href="javascrpit:void(0);" data-jp="fj" data-qp="Fujian" data-value="126"
                                data-name="福建省">Fujian</a>
                            <a href="javascrpit:void(0);" data-jp="gs" data-qp="Gansu" data-value="117"
                                data-name="甘肃省">Gansu</a>
                            <a href="javascrpit:void(0);" data-jp="gd" data-qp="Guangdong" data-value="130"
                                data-name="广东省">Guangdong</a>
                            <a href="javascrpit:void(0);" data-jp="gx" data-qp="Guangxi" data-value="130"
                                data-name="广西壮族自治区">Guangxi</a>
                            <a href="javascrpit:void(0);" data-jp="gz" data-qp="Guizhou" data-value="134"
                                data-name="贵州省">Guizhou</a>
                            <a href="javascrpit:void(0);" data-jp="hn" data-qp="Hainan" data-value="132"
                                data-name="海南省">Hainan</a>
                            <a href="javascrpit:void(0);" data-jp="hb" data-qp="Hebei" data-value="121"
                                data-name="河北省">Hebei</a>
                            <a href="javascrpit:void(0);" data-jp="hlj" data-qp="Heilongjiang" data-value="111"
                                data-name="黑龙江省">Heilongjiang</a>
                            <a href="javascrpit:void(0);" data-jp="hn" data-qp="Henan" data-value="120"
                                data-name="河南省">Henan</a>
                            <a href="javascrpit:void(0);" data-jp="hb" data-qp="Hubei" data-value="128"
                                data-name="湖北省">Hubei</a>
                            <a href="javascrpit:void(0);" data-jp="hn" data-qp="Hunan" data-value="129"
                                data-name="湖南省">Hunan</a>
                            <a href="javascrpit:void(0);" data-jp="js" data-qp="Jiangsu" data-value="124"
                                data-name="江苏省">Jiangsu</a>
                            <a href="javascrpit:void(0);" data-jp="jx" data-qp="Jiangxi" data-value="127"
                                data-name="江西省">Jiangxi</a>
                            <a href="javascrpit:void(0);" data-jp="jl" data-qp="Jilin" data-value="112"
                                data-name="吉林省">Jilin</a>
                            <a href="javascrpit:void(0);" data-jp="jn" data-qp="Liaoning" data-value="113"
                                data-name="辽宁省">Liaoning</a>
                            <a href="javascrpit:void(0);" data-jp="nmg" data-qp="Inner Mongol" data-value="114"
                                data-name="内蒙古自治区">Inner Mongol</a>
                            <a href="javascrpit:void(0);" data-jp="nx" data-qp="Ningxia" data-value="116"
                                data-name="宁夏回族自治区">Ningxia</a>
                            <a href="javascrpit:void(0);" data-jp="qh" data-qp="Qinghai" data-value="137"
                                data-name="青海省">Qinghai</a>
                            <a href="javascrpit:void(0);" data-jp="sd" data-qp="Shandong" data-value="122"
                                data-name="山东省">Shandong</a>
                            <a href="javascrpit:void(0);" data-jp="sx" data-qp="Shanxi" data-value="118"
                                data-name="山西省">Shanxi</a>
                            <a href="javascrpit:void(0);" data-jp="sx" data-qp="Shaanxi" data-value="119"
                                data-name="陕西省">Shaanxi</a>
                            <a href="javascrpit:void(0);" data-jp="sh" data-qp="Shanghai" data-value="108"
                                data-name="上海市">Shanghai</a>
                            <a href="javascrpit:void(0);" data-jp="sc" data-qp="Sichuan" data-value="135"
                                data-name="四川省">Sichuan</a>
                            <a href="javascrpit:void(0);" data-jp="tj" data-qp="Tianjin" data-value="109"
                                data-name="天津市">Tianjin</a>
                            <a href="javascrpit:void(0);" data-jp="xj" data-qp="Tibet" data-value="115"
                                data-name="新疆维吾尔自治区">Tibet</a>
                            <a href="javascrpit:void(0);" data-jp="xz" data-qp="Sinkiang" data-value="136"
                                data-name="西藏自治区">Sinkiang</a>
                            <a href="javascrpit:void(0);" data-jp="yn" data-qp="Yunnan" data-value="133"
                                data-name="云南省">Yunnan</a>
                            <a href="javascrpit:void(0);" data-jp="zj" data-qp="Zhejiang" data-value="125"
                                data-name="浙江省">Zhejiang</a>
                                <a href="javascrpit:void(0);" data-jp="xg" data-qp="xianggang" data-value="1001" data-name="香港">Hong Kong</a>
                                <a href="javascrpit:void(0);" data-jp="am" data-qp="aomen" data-value="1002" data-name="澳门">Macau</a>
                                <a href="javascrpit:void(0);" data-jp="tw" data-qp="taiwan" data-value="1003" data-name="台湾">TaiWan</a>
                                <a href="javascrpit:void(0);" data-jp="gw" data-qp="guowai" data-value="1004" data-name="国外">Other area</a>
                        </p>
                    </div>
                </div>
                <div class="yoursInfoCC">
                    <div class="selectT">Company Name<span class="mustStar">*</span></div>
                    <input type="text" class="selectC" placeholder="Pls input company name" id="company" maxlength='255'>
                    <p class="error">*Required fields</p>
                </div>
            </div>
        </div>

        <div class="ticket_accept">
            <div class="accept">
                <div class="acceptIcon"></div>
                <div class="acceptWord">
                    I agree that SGS can use my data for the purposes of dealing with my request, <br />in accordance
                    with the <a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" class="privacy"
                        target="_blank">&nbsp;SGS Online Privacy Statement.
                    </a>
                </div>
            </div>
            <p class="error" style="text-align: center">*Please read the privacy policy and choose to agree</p>
            <button class="submit" data-agl-cvt="5">SEND MESSAGE</button>
        </div>
    </div>

    <div class="searchrightBox">
        <div class="searchrightBox1">
            <div class="your">
                <div class="yours">
                    <span class="your-word">行业解决方案</span>
                    <span class="dragbut">
                        <img src="<%- locals.static %>/images/jianhao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons first">
                    <div class="yourcon">
                        <ul>
                            <% if(tradeNavi && tradeNavi.length > 0){ %>
                            <% for (var item of tradeNavi){ %>
                            <li class="yourconLi">
                                <a href="/industry/<%- item.alias %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="your">
                <div class="yours">
                    <span class="your-word">我们的服务</span>
                    <span class="dragbut">
                        <img src="<%- locals.static %>/images/jiahao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons">
                    <div class="yourcon">
                        <ul>
                            <% if(serviceNavi && serviceNavi.length > 0){ %>
                            <% for (var item of serviceNavi){ %>
                            <li class="yourconLi">
                                <a href="/service/<%- item.alias %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="your">
                <div class="yours">
                    <span class="your-word">热点服务</span>
                    <span class="dragbut">
                        <img src="<%- locals.static %>/images/jiahao.png" alt="" class="dragbut-icon">
                    </span>
                </div>

                <div class="your-cons">
                    <div class="yourcon">
                        <ul>
                            <% if(hot && hot.length > 0){ %>
                            <% for (var item of hot){ %>
                            <li class="yourconLi">
                              <a href="/sku/<%- item.alias %>/<%- item.id %>/"><%- item.name %></a>
                            </li>
                            <% } %>
                            <% } %>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>


<div class="shadeBox" style="width: 100%;height: 100%;top:0;left:0;background: rgba(0,0,0,0.3);position: fixed;z-index: 9998; display: none;">

</div>
<div class="tip_box" style="position: fixed;top: 0;left: 0;bottom: 0;right: 0;width: 425px;height: 174px;z-index: 9999;margin: auto;background: #ffffff;box-shadow: 1px 1px 50px rgba(0,0,0,.3);border-radius: 5px; display: none;">
    <div class="tip_t" style="height: 38px;background: #f9f8f7;line-height: 38px;color: #000;font-size: 14px;padding: 0 22px;border-left: 1px solid #f9f8f7;border-right: 1px solid #f9f8f7;border-radius: 5px 5px 0 0">Success</div>
    <div class="tip_info_makesure" style="border: 1px solid #eeecea;height: 134px;border-radius: 0 0 5px 5px">
        <div class="tip_info" style="color: #000000;font-size: 14px;padding: 0 22px;line-height: 24px;margin-top: 18px;margin-bottom: 22px">
            Success!
        </div>
        <div class="tip_make_sure" style="width: 55px;height: 27px;background: #ff5702;color: white;line-height: 27px;text-align: center;font-size: 12px;margin: 0 auto;cursor: pointer;border-radius: 2px;" onclick="_ok()">确定</div>
    </div>

</div>

<script src="<%- locals.static %>/js/select2.full.min.js"></script>
<script>
    $(function () {
        var proviceName = '';
        $('.citys a').click(function () {
            var con = $(this).text();
            proviceName = $(this).data('name');
            $('.country').val(con);
            $('.provice').css({ 'display': 'none' });
        })

        $(document).click(function (e) {
            e = e || window.event;
            var o = e.target || e.srcElement;
            if ($(o).attr('class') != 'selectC country') {
                $('.provice').css({ 'display': 'none' });
            }
        })

        $('.country').focus(function () {
            $('.provice').css({ 'display': 'block' });
        })

        $('.country').bind('input propertychange', function () {
            var $_this = $(this);
            if ($(this).val() != '') {
                $('.citys a').css({ 'width': '100%', "display": 'none' });
                $('.citys a').each(function (index, value) {

                    if ($(value).data('qp').indexOf($_this.val()) >= 0) {

                        $(value).css({ "display": 'block' });
                    } else if ($(value).data('name').indexOf($_this.val()) >= 0) {
                        $(value).css({ "display": 'block' });

                    } else if ($(value).data('jp').indexOf($_this.val()) >= 0) {
                        $(value).css({ "display": 'block' });

                    }

                })
            } else {
                $('.citys a').css({ 'width': '129px', 'display': 'inline-block' });
            }

        })

        var $select = $(".select");
        $('.filter-select').select2({ minimumResultsForSearch: -1 });

        $(".acceptIcon").click(function () {
            // console.log($(this).attr('class').split(' '));
            var classNames = $(this).attr('class').split(' ');
            if (classNames.indexOf("active") > -1) {
                $(this).removeClass('active');

            } else if (classNames.indexOf("active") < 0) {
                $(this).addClass('active');
                $('.submit').siblings('.error').css({ 'visibility': 'hidden' });
            }
        })

        $('.select2-selection--single').click(function () {
            $(this).css({ 'borderColor': '#eeeeee' }).parents('.selectC').siblings('.error').css({ 'visibility': 'hidden' });
        })

        $('#textarea').focus(function () {
            $(this).css({ 'borderColor': '#eeeeee' }).siblings('.error').css({ 'visibility': 'hidden' });
        })

        $('input.selectC').focus(function () {
            $(this).css({ 'borderColor': '#eeeeee' }).siblings('.error').css({ 'visibility': 'hidden' });
        })

        $('.submit').click(function () {

            var $choice = $('.acceptIcon');
            if ($choice.attr('class').indexOf('active') < 0) {
                $choice.parents('.accept').siblings('.error').css({ 'visibility': 'visible' });
            }

            var $s = $('.select2-selection__rendered');
            $s.each(function (index, value) {
                if ($(value).text() == 'Select from the list') {
                    $(value).parents('.select2-selection--single').css({ 'borderColor': '#e50000' }).parents('.selectC').siblings('.error').css({ 'visibility': 'visible' });

                }
            })

            var textCon = $('#textarea').val();
            if (textCon == '') {
                $('#textarea').css({ 'borderColor': '#e50000' }).siblings('.error').css({ 'visibility': 'visible' });

            }

            var $input = $('input.selectC');
            $input.each(function (index, value) {
                if ($(value).val() == '') {
                    $(value).css({ 'borderColor': '#e50000' }).siblings('.error').css({ 'visibility': 'visible' });
                }
            });

            var phone = $('#phone').val().trim();
            if (phone == '') {
                $('#phone').css({ 'borderColor': '#e50000' }).siblings('.error').css({ 'visibility': 'visible' }).text('*Required fields');
            } else if (!isPhoneNum(phone)) {
                $('#phone').css({ 'borderColor': '#e50000' }).siblings('.error').css({ 'visibility': 'visible' }).text('*Please enter the correct mobile phone');
            }

            var email = $('#email').val().trim();
            var emailReg =   /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/;

            if (email == '') {
                $('#email').css({ 'borderColor': '#e50000' }).siblings('.error').css({ 'visibility': 'visible' }).text('*Required fields');
            } else if (!emailReg.test(email)) {
                $('#email').css({ 'borderColor': '#e50000' }).siblings('.error').css({ 'visibility': 'visible' }).text('*Please enter the correct email address');
            }

            var errLen = 0;
            $('p.error').each(function () {
                if ($(this).css('visibility') != 'hidden') {
                    errLen += 1;
                }
            })

            if (!errLen) {
                var params = {
                    type: $('#type').val(),
                    trade: $('#trade').val(),
                    tradeName: $('#trade>option:selected').text(),
                    service: $('#service').val(),
                    serviceName: $('#service>option:selected').text(),
                    content: $('#textarea').val(),
                    customer: $('#customer').val().trim(),
                    phone: $('#phone').val().trim(),
                    email: $('#email').val().trim(),
                    provice: proviceName,
                    company: $('#company').val().trim(),
                    os_type: 'pc',
                    frontUrl: window.location.href,
                    _csrf: '<%- csrf %>',
                }
                if (params.trade === '其他') {
                    params.trade = 0
                }
                if (params.service === '其他') {
                    params.service = 0
                }

                $('.submit').prop('disabled', true);
                $.post('/ticket/post', params, function (r) {
                    if (r.success) {
                        window.location = '/success';
                    } else {
                        $(".shadeBox").show()
                        $(".tip_box").show()
                        $('.submit').prop('disabled', false);
                    }
                })
            }
        })

        var $_yours = $(".yours");

        $_yours.click(function () {

            var $_con = $(this).siblings(".your-cons");

            // for(var i=0;i<$_drag.length;i++){
            //     if($_drag.eq(i).attr("src") == "./images/"){
            //
            //     }
            // }

            if ($_con.css("height") == "0px") {
                $_con.animate({ "height": 486 }).end().parents(".your").siblings(".your").children(".your-cons").animate({ "height": 0 });
                $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jianhao.png');
                $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(0).attr("src", "<%- locals.static %>/images/jiahao.png");
                $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(1).attr("src", "<%- locals.static %>/images/jiahao.png")
            } else {
                $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jiahao.png');
                $_con.animate({ "height": 0 });
            }
        })

    })

    function _ok() {
        window.location.reload();
    }
</script>

<% include ./../footer.html %>
