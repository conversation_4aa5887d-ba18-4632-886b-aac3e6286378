<% include ../common/head.html %>
  <% include ../components/header.html %>
    <link rel="stylesheet" href="<%- locals.static %>/css/promotion/jiance-new.css">
    <div class="jiance" id="change-position" v-cloak>
      <div class="head-box">
        <div class="wrap">
          <a class="page-title" href="/" target="_blank">SGS通标丨在线商城</a>
          <div class="user-content">
            <div class="user-item"><a href="/" target="_blank">首页</a></div>
            <% if (isLogin){ %>
              <div class="user-item"><a href="javascrpit:void(0);">
                  <%- userInfo.userNick || userInfo.userPhone || userInfo.userEmailCopy || userInfo.userEmail %>
                </a></div>
              <div class="user-item"><a href="javascrpit:void(0);" id='logout'>退出</a></div>
              <% }else{ %>
                <div class="user-item">
                  <a href="<%- memberUrl %>/login" target="_blank">登录</a>/
                  <a onclick="signup('<%- memberUrl %>')" style="cursor: pointer;">注册</a>
                </div>
                <% } %>
                  <div class="user-item"><a href="/information" target="_blank">资讯中心</a></div>
                  <div class="user-item"><a href="/baojia" target="_blank">业务咨询</a></div>
                  <div class="user-item"><a href="/overview/aboutSGS" target="_blank">关于SGS</a></div>
                  <div class="user-item" onclick="addFavorite()">收藏</div>
          </div>
        </div>
      </div>
      <div class="head-img">
        <div class="wrap">
          <% if (isTest){ %>
            <a href="/" target="_blank"><img class="logo logo1" src="../../public/images/promotion/jiance-new/logo.jpg"
                alt=""></a>
            <% }else{ %>
              <a href="/" target="_blank"><img class="logo logo2"
                  src="../../public/images/promotion/jiance-new/logo-1.png" alt=""></a>
              <% } %>
                <div class="title">
                  <div class="row1">专业第三方测试、检验和认证机构</div>
                  <div class="row2"><img src="../../public/images/promotion/jiance-new/suishi.jpg" alt="">瑞士上市代码 <i style="color: #183DBB;">SGSN</i>
                  </div>
                </div>
                <img class="head-line" src="../../public/images/promotion/jiance-new/head-line.png">
                <div class="detail-text">
                  <div class="row1">百万企业<span>信赖</span></div>
                  <div class="row2">深受消费者<span>认可</span></div>
                </div>
                <div class="cma-cnas">
                  <img src="../../public/images/promotion/jiance-new/CMA-CNAS.png" alt="">
                  <div class="cma-box">
                    <img class="cma-img" src="../../public/images/promotion/jiance-new/CMA.png" alt="">
                  </div>
                  <div class="cnas-box">
                    <img class="cnas-img" src="../../public/images/promotion/jiance-new/Cnas.png" alt="">
                  </div>
                </div>
                <div class="home_top_hotline">
                  <i></i>
                  <div class="home_top_hotline_phone clearfix">
                    <span class="hotlineText"><%- phoneNum %></span>
                  </div>
                  <div class="home_top_hotline_intro">
                    — 全国受理，最快当天出证 —
                  </div>
                </div>
        </div>
      </div>
      <div class="nav-box">
        <div class="wrap nav-wrap">
          <div class="nav-label"><img src="../../public/images/promotion/jiance-new/nav-label.png" alt="">您的行业</div>
          <ul class="nav-list">
            <% for(let [i, item] of navList.entries()){ %>
              <li class="nav-li">
                <div class="nav-li-title">
                  <%- item.name %>
                </div>
                <ul class="nav-sub-list">
                  <li class="nav-list-icon"></li>
                  <% for(let [j, subItem] of item.subList.entries()){ %>
                    <li class="nav-sub-li"><a href="<%- subItem.path %>" target="_blank">
                        <%- subItem.name %>
                      </a></li>
                    <% } %>
                      <li class="nav-sub-li-btn"><a href="javascrpit:void(0);" class="kf5-btn handleIMtool">立即咨询</a>
                      </li>
                </ul>
              </li>
              <% } %>
                <li class="nav-li">
                  <div class="nav-li-title"><a href="/" target="_blank">更多</a></div>
                </li>
          </ul>
          <div class="nav-btn kf5-btn handleIMtool">加急出证</div>
        </div>
      </div>
      <div class="banner-box">
        <div class="banner-box-form">
            <% include ./../components/inner_quote_form_testing.html %>
          <% include ./../pages/quote/modal.html %>
        </div>
        <div class="swiper-container banner-swiper">
          <div class="swiper-wrapper">
            <div class="swiper-slide swiper-slide1">
              <div class="swiper-slide-wrap">
                <div>
                  <a class="more-btn" href="/overview/aboutSGS" target="_blank">了解更多 ></a>
                </div>
                <div class='kf5-btn' id="handleIMtool">
                  <span class="btn-text">立即咨询</span>
                  <span class="btn-active"></span>
                </div>
              </div>
            </div>
            <div class="swiper-slide swiper-slide2"></div>
          </div>
        </div>
        <div class="swiper-bottom">
          <div class="swiper-pagination swiper-pagination-banner"></div>
        </div>
      </div>
      <div class="hot-service">
        <div class="wrap">
          <p class="floor-title">覆盖全行业的检测认证服务</p>
          <div class="main-line"></div>
          <p class="floor-sub-title">无论您身处何处，无论您从事什么行业，我们都能为您提供专业的检测认证服务。</p>
          <div class="hot-service-list">
            <% for(let [i, item] of hotServiceList.entries()){ %>
              <% if (i===0){ %>
                <div class="hot-service-item is-active">
                  <% }else{ %>
                    <div class="hot-service-item">
                      <% } %>
                        <div class="hot-service-title">
                          <img class="service-icon normal-icon"
                            src="../../public/images/promotion/jiance-new/hot-service-icon/<%- i+1 %>.png" alt="">
                          <img class="service-icon active-icon"
                            src="../../public/images/promotion/jiance-new/hot-service-icon/<%- i+1 %>-active.png"
                            alt="">
                          <div class="title-text">
                            <%- item.name %>
                          </div>
                          <img class="active-down-icon"
                            src="../../public/images/promotion/jiance-new/hot-service-icon/active.png" alt="">
                        </div>
                        <div class="hot-service-content"
                          style="background: url(../../public/images/promotion/jiance-new/hot-service/<%- i+1 %>.jpg) no-repeat 50% 50% #F8F8F8;">
                          <div class="hot-service-top">
                            <span>
                              <%- item.name %>热门服务
                            </span>
                            <a href="<%- item.morePath %>" target="_blank">查看全部服务</a>
                          </div>
                          <div class="sub-list-content">
                            <% for(let [j, item2] of item.subList.entries()){ %>
                              <ul class="sub-list">
                                <% for(let [k, item3] of item2.entries()){ %>
                                  <li><a href="<%- item3.path %>" target="_blank">
                                      <%- item3.name %>
                                    </a></li>
                                  <% } %>
                              </ul>
                              <% } %>
                          </div>
                          <div class="hot-service-btns">
                            <span class="btn1 kf5-btn handleIMtool">咨询检测</span>
                            <span class="btn2 kf5-btn handleIMtool">咨询认证</span>
                            <% if (item.needDiscountBtn){ %>
                              <!--<span class="btn3 kf5-btn">
                <span class="btn-text">领取首单优惠</span>
                <span class="btn-active"></span>
              </span>-->
                              <% } %>
                                <div class="btn4">
                                  <i></i>
                                  <div class="text">
                                    <div class="home_top_hotline_intro">全国业务，快速受理</div>
                                    <div class="hotlineText"><%- phoneNum %></div>
                                  </div>
                                </div>
                                <% if (i===1 ||　i===2 || i===3 || i===7){ %>
                                  <!-- <div class="btn5 kf5-btn">
                领取首单优惠
                </div> -->
                                  <% } %>
                          </div>
                        </div>
                    </div>
                    <% } %>
                      <div class="hot-service-item no-list">
                        <a class="hot-service-title" href="/" target="_blank">
                          <img class="service-icon normal-icon"
                            src="../../public/images/promotion/jiance-new/hot-service-icon/more.png" alt="">
                          <img class="service-icon active-icon"
                            src="../../public/images/promotion/jiance-new/hot-service-icon/more-acitve.png" alt="">
                          <span class="title-text">更多服务</span>
                        </a>
                      </div>
                </div>
          </div>
        </div>
        <div class="solution">
          <div class="wrap solution-wrap">
            <p class="floor-title">一站式检测认证解决方案</p>
            <div class="main-line"></div>
            <p class="floor-sub-title">SGS作为中国外贸企业青睐的第三方检测机构，凭借完善的资质能力、深厚的技术能力、优质的服务保障、权威的公信力和便捷的全国化服务网络，
              为全球各产业提供一站式检测整体技术解决方案。</p>
            <div class="swiper-container solution-swiper">
              <div class="swiper-wrapper">
                <% for(let [i, item] of solutionList.entries()){ %>
                  <div class="swiper-slide solutionCon">
                    <ul class="solution-list">
                      <% for(let [j, solutionItem] of item.entries()){ %>
                        <% if (j===0 || j===2){ %>
                          <li class="solutionConLi">
                            <% }else{ %>
                          <li class="solutionConLi serverRight0">
                            <% } %>
                              <a target="_blank" href="/sku/<%- solutionItem.alias %>/<%- solutionItem.id %>">
                                <% if (solutionItem.thumb_img){ %>
                                  <span class="img"
                                    style="background-image:url(<%- solutionItem.thumb_img.replace(/\/static/g,locals.static) %>)"></span>
                                  <% }else{ %>
                                    <span class="img" style="background-image:url()"></span>
                                    <% } %>
                                      <div class="cc">
                                        <span class="solutionConLi-word1">
                                          <%- solutionItem.name %>
                                        </span>
                                        <p>
                                          <%- solutionItem.description %>
                                        </p>
                                      </div>
                              </a>
                              <em class="btn kf5-btn handleIMtool">咨询</em>
                          </li>
                          <% } %>
                    </ul>
                  </div>
                  <% } %>
              </div>
            </div>
            <div class="swiper-bottom">
              <div class="swiper-pagination swiper-pagination-solution"></div>
            </div>
            <div class="swiper-button-prev"></div>
            <div class="swiper-button-next"></div>
            <div class="solution-btns">
              <a class="btn1" href="/baojia" target="_blank">
                <span class="btn-text">定制我的方案</span>
                <span class="btn-active"></span>
              </a>
            </div>
          </div>
        </div>

        <div class="data-content" id="shuju-floor">
          <div class="wrap data-wrap">
            <p class="floor-title">SGS在中国</p>
            <div class="main-line"></div>
            <p class="floor-sub-title">30年专业经验与服务大数据，用数据证实SGS的技术实力！</p>
            <div class="shuju-list" id="advantage">
              <div class="shuju-item" style="margin-right: 47px;">
                <div class="shuju-detail">
                  <em data-max='30'>0</em> <span class="shuju-unit">年</span>
                </div>
                <div class="shuju-tips">
                  检测认证服务经验
                </div>
              </div>
              <img src="../../public/images/promotion/jiance/shuju-divider.png" alt="" class="shuju-divider"
                style="margin-right: 75px;">
              <div class="shuju-item" style="margin-right: 87px;">
                <div class="shuju-detail" style="width: 77px;">
                  <em data-max='90'>0</em> <span class="shuju-unit">家</span>
                </div>
                <div class="shuju-tips">
                  分支机构
                </div>
              </div>
              <img src="../../public/images/promotion/jiance/shuju-divider.png" alt="" class="shuju-divider"
                style="margin-right: 59px;">
              <div class="shuju-item" style="margin-right: 40px;">
                <div class="shuju-detail">
                  <em data-max='200'>0</em> <span class="shuju-unit">间</span>
                </div>
                <div class="shuju-tips">
                  实验室遍布全国
                </div>
              </div>
              <img src="../../public/images/promotion/jiance/shuju-divider.png" alt="" class="shuju-divider"
                style="margin-right: 48px;">
              <div class="shuju-item" style="margin-right: 44px;">
                <div class="shuju-detail">
                  <em data-max='15000'>0</em> <span class="shuju-unit">+</span>
                </div>
                <div class="shuju-tips">
                  专业技术人员
                </div>
              </div>
              <img src="../../public/images/promotion/jiance/shuju-divider.png" alt="" class="shuju-divider"
                style="margin-right: 40px;">
              <div class="shuju-item">
                <div class="shuju-detail">
                  <em data-max='100'>0</em> <span class="shuju-unit">万</span>
                </div>
                <div class="shuju-tips">
                  客户见证SGS专业
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="panoramic">
          <div class="wrap panoramic-wrap">
            <div class="panoramic-text">在线参观 SGS实验室 360°全景浏览</div>
            <div class="panoramic-num-text">已有12,587,963人参观</div>
            <a class="panoramic-btn" href="https://720yun.com/t/07vkcbil58e" target="_blank">立即参观</a>
          </div>
        </div>

        <div class="discount">
          <div class="wrap discount-wrap">
            <div class="discount-btns">
              <div class="btn1">
                <div class="click-btn kf5-btn handleIMtool"></div>
                <img class="btn-icon" src="../../public/images/promotion/jiance-new/discoun-btn1.png" alt="">
                <span class="btn-text">咨询客服</span>
                <img class="position-icon" src="../../public/images/promotion/jiance-new/position.png" alt="">
                <div class="position-text">{{showCity}}</div>
                <div class="change-position">
                  <div class="change-btn" @click="showCascader">切换地点</div>
                  <div id="provice" v-if="showSelect">
                    <el-cascader ref="cascader" placeholder="请选择" clearable :options="provice" filterable :props="props"
                      v-model="proviceText" @keydown.enter.native="handleKeydown" @change="cityChange"></el-cascader>
                  </div>
                </div>
              </div>
              <div class="btn3">
                <i></i>
                <div class="text">
                  <div class="home_top_hotline_intro">全国业务，快速受理</div>
                  <div class="hotlineText"><%- phoneNum %></div>
                </div>
              </div>
              <div class="btn2 kf5-btn handleIMtool">
                <img src="../../public/images/promotion/jiance-new/discount-btn2.png" alt="">
                <span class="text1">加急通道</span>
                <span>最快当日出证</span>
              </div>
            </div>
          </div>
        </div>

        <div class="news">
          <div class="wrap news-wrap">
            <p class="floor-title">新闻资讯</p>
            <div class="main-line"></div>
            <div class="swiper-container news-swiper">
              <div class="swiper-wrapper">
                <% for(let [i, item] of newsList.entries()){ %>
                  <div class="swiper-slide newsCon">
                    <a class="left-content card1 card" href="<%- item[0].url %>" target="_blank">
                      <img src="../../public/images/promotion/jiance-new/news/<%- i+1 %>-1.png" alt="">
                      <div class="title">
                        <%- item[0].title %>
                      </div>
                    </a>
                    <div class="middle-content">
                      <a class="card2 card" href="<%- item[1].url %>" target="_blank">
                        <img src="../../public/images/promotion/jiance-new/news/<%- i+1 %>-2.png" alt="">
                        <div class="title">
                          <%- item[1].title %>
                        </div>
                      </a>
                      <a class="card4 card" href="<%- item[3].url %>" target="_blank">
                        <img src="../../public/images/promotion/jiance-new/news/<%- i+1 %>-4.png" alt="">
                        <div class="title">
                          <%- item[3].title %>
                        </div>
                      </a>
                    </div>
                    <div class="right-content">
                      <a class="card3 card" href="<%- item[2].url %>" target="_blank">
                        <img src="../../public/images/promotion/jiance-new/news/<%- i+1 %>-3.png" alt="">
                        <div class="title">
                          <%- item[2].title %>
                        </div>
                      </a>
                      <div class="news-btns kf5-btn handleIMtool">
                        <div class="btn1 news-btn">
                          <div><img src="../../public/images/promotion/jiance-new/news-btn1.png" alt=""></div>
                          <div>我要检测</div>
                        </div>
                        <div class="btn2 news-btn">
                          <div><img src="../../public/images/promotion/jiance-new/news-btn2.png" alt=""></div>
                          <div>我要认证</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <% } %>
              </div>
            </div>
            <div class="swiper-button-prev"></div>
            <div class="swiper-button-next"></div>
          </div>
        </div>
        <div class="contact_ads"></div>
        <div class="v-modal" tabindex="0" style="z-index: 2000;display: none;"></div>
      </div>
      <script src="<%- locals.static %>/js/swiper.min.js"></script>
      <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
      <script src="<%- locals.static %>/js/md5.js"></script>
      <script>
        var pid = '<%- mallPid %>',
          pcode = '<%- mallPcode %>'
        var newVue = new Vue({
          name: 'changePosition',
          el: '#change-position',
          mixins: [quoteMixins],
          data: function () {
            var validatorUserPhone = function (rule, value, callback) {
              var reg = /^1[2|3|4|5|6|7|8|9][0-9]\d{8}$/
              if (!value) {
                return callback(new Error('*请输入手机号'));
              } else if (!reg.test(value)) {
                return callback(new Error('*请输入正确的手机号码'));
              } else {
                return callback()
              }
            }
            return {
              provice: [],
              host: '<%- host %>',
              props: {
                value: 'orgName',
                label: 'orgName',
                children: 'citys'
              },
              proviceText: ['北京市', '北京市'],
              showSelect: false,
              showCity: '北京市',
              seconds: 59,
              timer: null,
              countdownTime: 5,
              countdownTimer: null,
              isLogin: <%- isLogin %>,
              disablePhone: <%- disablePhone %>,
              disableMail: <%- disableMail %>,
              pid: 'pid.mall',
              pcode: 'Z0zCnRE3IaY9Kzem',
              rules: {
                provice: [{ required: true, message: '*请选择所在城市' }],
                content: [{ required: true, message: '*请输入咨询内容' }],
                customer: [{ required: true, message: '*请输入您的称呼' }],
                phone: [{ validator: validatorUserPhone }],
              },
              approve: false,
              tab: 'phone',
              host: '<%- host %>',
              dialogVisibleReg: false,
              loading: false,
              form: {
                type: '业务咨询',
                trade: '',
                tradeName: '其他',
                tradeIndex: '',
                serviceIndex: '',
                service: '',
                serviceName: '其他',
                company: '',
                provice: '',
                content: '',
                customer: '<%- userInfo.userName %>',
                email: '<%- userInfo.userEmail %>',
                phone: '<%- userInfo.userPhone %>',
                imageCode: '',
                frontUrl: window.location.href,
                destCountry: '',
                verifyCode: '',
              },
              showModal: false,
              isSuccess: false,
              type: 0,
              contact_approve_show: true,
              pageInfo: {
                title: '验证手机号',
                sendModel: '已发送到手机号：+86',
                prepend: '手机验证码'
              },
              captchaAppId: '<%- captchaAppId %>',
            }
          },
          methods: {
            cityChange: function (data) {
              if (data && data.length) {
                this.showCity = data[1]
                this.showSelect = false
              }
            },
            showCascader: function (e) {
              e.stopPropagation()
              this.showSelect = !this.showSelect
            },
            qryCity: function () {
              var param = {}
              var timestamp = new Date().valueOf();
              var that = this
              $.ajax({
                type: 'POST',
                url: this.host + '/ticCenter/business/api.v1.center/org/qryCity',
                data: JSON.stringify(param),
                contentType: 'application/json',
                headers: {
                  pid: pid,
                  sign: this.sing(param, timestamp),
                  timestamp: timestamp,
                  frontUrl: window.location.href
                },
                success: function (res) {
                  if (res.resultCode === '0') {
                    for (var i = 0; i < res.data.length; i++) {
                      // res.data[i].citys = null
                      if (!res.data[i].citys || !res.data[i].citys.length) {
                        delete res.data[i].citys
                      }
                    }
                    const provice = res.data || []
                    const filterIds = ['970000000000', '980000000000', '990000000000', '1000000000000']
                    that.provice = provice.filter(function (v) {
                      return filterIds.indexOf(v.orgId) === -1
                    })
                  } else {
                    alert(res.resultMsg)
                  }
                },
                fail: function (data) {
                  alert(res.resultMsg)
                },
                complete: function (complete) {
                }
              })
            },
          }
        })
        $(function () {
          $('.jiance').on('click', '#handleIMtool', function () {
            var notLeadoo = false; // 不会触发leadoo的路由
            if (notLeadoo) {
              $('#zc__sdk__sys__btn').trigger('click')
            } else {
              if (IS_ZHICHI) {
                $('#zc__sdk__sys__btn').trigger('click')
              } else if (IS_CHATBOT) {
                document.querySelector("body > div.tic-chatbot-button").click()
              }
            }
          })

          // $('.kf5-btn').on("click", function () {
          //   $('#kf5-support-btn').trigger('click');
          // })
          $('.video-btn2').on("click", function () {
            $('.video-wrapper').css('display', 'block')
            $('.v-modal').css('display', 'block')
          })
          $('.video-close-btn').on("click", function () {
            $('.video-wrapper').css('display', 'none')
            $('.v-modal').css('display', 'none')
          })
          $('.hot-service-title').on("mouseenter", function (e) {
            var $parent = $(e.target).parents('.hot-service-item:not(.no-list)')
            if (!$parent.length || $parent.hasClass('is-active')) {
              return
            }
            $('.hot-service-item.is-active').removeClass('is-active')
            $parent.addClass('is-active')
          })
          var swiper = new Swiper('.solution-swiper', {
            navigation: {
              nextEl: '.solution-wrap .swiper-button-next',
              prevEl: '.solution-wrap .swiper-button-prev',
            },
            pagination: {
              el: '.swiper-pagination-solution',
              clickable: true,
              renderBullet: function (index, className) {
                return '<span class="' + className + '">' + (index + 1) + '</span>';
              },
            },
            speed: 1000,
            loop: true,
            fadeEffect: {
              crossFade: true,
            }
          });
          var newsSwiper = new Swiper('.news-swiper', {
            navigation: {
              nextEl: '.news-wrap .swiper-button-next',
              prevEl: '.news-wrap .swiper-button-prev',
            },
            speed: 1000,
            // loop: true,
            autoplay: {
              delay: 5000,
              disableOnInteraction: false,
            },
            // effect: 'fade',
            fadeEffect: {
              crossFade: true,
            }
          });
          var scontainer = $('.news-swiper')[0];
          scontainer.onmouseenter = function () {
            newsSwiper.autoplay.stop();
          };
          scontainer.onmouseleave = function () {
            newsSwiper.autoplay.start();
          };

          var bannerSwiper = new Swiper('.banner-swiper', {
            speed: 1000,
            loop: true,
            autoplay: {
              delay: 5000,
              disableOnInteraction: false,
            },
            effect: 'fade',
            fadeEffect: {
              crossFade: true,
            },
            pagination: {
              el: '.swiper-pagination-banner',
              clickable :true
            },
          });
          var bannerContainer = $('.banner-swiper')[0];
          bannerContainer.onmouseenter = function () {
            bannerSwiper.autoplay.stop();
          };
          bannerContainer.onmouseleave = function () {
            bannerSwiper.autoplay.start();
          };
          
          // advantage 关键优势区域数字动画
          var advantageFirst = true
          function advantageAnimate() {
            advantageFirst = false
            var box1 = $("#advantage em").eq(0);
            var min1 = 0;
            var max1 = box1.data('max');
            var box2 = $("#advantage em").eq(1);
            var min2 = 0;
            var max2 = box2.data('max');
            var box3 = $("#advantage em").eq(2);
            var min3 = 0;
            var max3 = box3.data('max');
            var box4 = $("#advantage em").eq(3);
            var min4 = 0;
            var max4 = box4.data('max');
            var box5 = $("#advantage em").eq(4);
            var min5 = 0;
            var max5 = box5.data('max');
            setInterval(function () {
              if (min1 < max1) {
                min1 = min1 + 3
                box1.html(min1)
              }
            }, 50)

            setInterval(function () {
              if (min2 < max2) {
                min2 = min2 + 10
                box2.html(min2)
              }
            }, 50)
            setInterval(function () {
              if (min3 < max3) {
                min3 = min3 + 10
                box3.html(min3)
              }
            }, 50)
            setInterval(function () {
              if (min4 < max4) {
                min4 = min4 + 1500
                box4.html(min4)
              }
            }, 50)
            setInterval(function () {
              if (min5 < max5) {
                min5 = min5 + 10
                box5.html(min5)
              }
            }, 50)
          }
          window.addEventListener('scroll', function () {
            const scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
            if ($('#shuju-floor').offset().top + 218 - $(window).height() < scrollTop) {
              if (advantageFirst) {
                advantageAnimate()
              }
            }
          })
          const scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
          if ($('#shuju-floor').offset().top + 218 - $(window).height() < scrollTop) {
            if (advantageFirst) {
              advantageAnimate()
            }
          }
        })
      </script>
      <% if(locals.env != 'prod'){ %>
      <script src="<%- locals.static %>/plugin/v2.test.js"></script>
      <% }else{ %>
      <script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js" type="text/javascript"></script>
      <% } %>
      <% include ../footer.html %>