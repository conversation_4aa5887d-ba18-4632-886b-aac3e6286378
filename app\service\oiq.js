'use strict';

const Service = require('egg').Service
const axios = require('axios');
const crypto = require('crypto');
const {
  env
} = require('../../config/info').siteInfo;

const ticMember = {
  pid: 'pid.member',
  pcode: 'CGz2RFHATB3XAsvg',
  appId: '99999999'
}

function getCookie(ctx, key) {
  if (!ctx.request.header.cookie) {
    return '';
  }
  const carr = ctx.request.header.cookie.split('; ');
  let sjson = {};
  for (let item of carr) {
    let iarr = item.split('=');
    sjson[iarr[0]] = iarr[1];
  }

  if (sjson) {
    return sjson[key];
  } else {
    return '';
  }
}


/*
  随机生成字符串
  length: 生成的字符串长度
*/
var randomStr = function (length) {
  var source = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  var result = '';
  for (var i = length; i > 0; --i)
    result += source[Math.floor(Math.random() * source.length)];
  return result;
};

function createHeader(type, params, header, that) {
  const ctx = that && that.ctx
  let frontUrl = '', si = ''
  if (ctx) {
    frontUrl = env[that.app.locals.env].url+ctx.request.url
    si = getCookie(ctx, 'sessionId') || '';
    if (!si) {
      ctx.cookies.set('sessionId', randomStr(32), {
        httpOnly: false
      })
    }
  }
  header = header || {}
  const pmd5 = crypto.createHash('md5').update((type.pid + type.pcode).toUpperCase()).digest('hex')
  const param = JSON.stringify(params) + pmd5.toUpperCase()
  const timestamp = new Date().getTime();
  const sign = crypto.createHash('md5').update(param + timestamp).digest('hex')
  const headers = {
    'Content-Type': 'application/json',
    pid: type.pid,
    pcode: type.pcode,
    timestamp,
    sign,
    appId: type.appId,
    appKey: type.pid,
    frontUrl: header.frontUrl || frontUrl,
    si: header.si || si,
    ia: header.ia || '',
  }
  for (var item in headers) {
    if (!headers[item]) delete headers[item]
  }
  return headers
}

class OiqService extends Service {
  async getOiqList(header) {
    const params = {
      pageNum: 1,
      pageRow: 8,
      filterTime: 3
    }
    let headers = createHeader(ticMember, params, header, this)
    let list = []
    try {
      let res = await axios({
        url: env[this.app.locals.env].ticMall + '/ticMember/business/api.v2.order/order/qryHeadList',
        method: 'post',
        data: params,
        headers: headers,
      })
      if (res.status === 200 && res.data && res.data.resultCode === '0') {
        list = res.data.data.items
        list.forEach(v => {
          const categoryPath = v.categoryPath.split('/')
          v.lastCategory = categoryPath[categoryPath.length-1]
          v.userName = `${v.userName}${v.userSex === 2 ? '女士' : v.userSex === 1 ? '先生' : ''}`
          // v.categoryLabel = v.categoryPath.replace(/\//g, ' >> ')
          // v.itemNameList = v.itemNameList.length > 45 ? v.itemNameList.slice(0, 45) + '...' : v.itemNameList
          v.stateDate = v.stateDate.slice(5, -3)
          v.otherLabel = categoryPath.slice(0, -2).join(' >> ') + ' >> '
        })
      }
    } catch (e) {
    }
    return list
  }
}

module.exports = OiqService;
