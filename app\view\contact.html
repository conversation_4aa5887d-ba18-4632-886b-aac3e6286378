<% include header.html %>
<% include ./components/header.html %>

<div class="contact_v2">
  <div class="contact_main">
    <div class="contact_tab">
      <ul>
        <li>
          <a href='/quote'>咨询报价</a>
        </li>
        <li class="activity">
          <a href='/contact'>建议反馈</a>
        </li>
        <!-- <li>
          <a href='/locatin'>服务网点</a>
        </li> -->
        <li>
          <a href='/hotline'>客服电话</a>
        </li>
      </ul>
    </div>
    <div class="contact_form" id="contact" v-loading="loading">
      <img src="../public/images/contact.png" alt="" class="contact-img">
      <el-form ref="ruleForm" :model="form" :rules="rules" label-width="88px" :inline="true" label-position="left" v-cloak>
        <div class="contact_form_content">
          <el-form-item label="" prop="customer" class="margin-right">
            <template slot="label">
              <span class="label-text">姓<span style="visibility: hidden">占位</span>名</span>
            </template>
            <el-input v-model="form.customer" placeholder="请输入您的姓名" maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="" prop="email">
            <template slot="label">
              <span class="label-text">邮<span style="visibility: hidden">占位</span>箱</span>
            </template>
            <el-input v-model="form.email" placeholder="请输入您的电子邮箱"></el-input>
          </el-form-item>
          <el-form-item label="手机号码" prop="phone" class="margin-right">
            <el-input v-model="form.phone" placeholder="请输入您的手机号码"></el-input>
          </el-form-item>
          <el-form-item label="" prop="verifyCode" class="code-item">
            <template slot="label">
              <span class="label-text">验&nbsp;&nbsp;证&nbsp;&nbsp;码</span>
            </template>
            <el-input v-model="form.verifyCode" placeholder="短信验证码">
              <div slot="append" @click="sendCaptcha" class="sendCaptcha">{{timer ? seconds + '后再次发送' : '发送验证码'}}</div>
            </el-input>
          </el-form-item>
          <el-form-item label="企业名称" prop="company" class="margin-right">
            <el-input v-model="form.company" placeholder="请输入企业名称" maxlength="255"></el-input>
          </el-form-item>
          <el-form-item label="所在地区" prop="provice">
            <!--<el-cascader ref="cascader" placeholder="请选择所在省份或地区" clearable :options="provice" filterable :props="props" v-model="form.provice" @keydown.enter.native="handleKeydown"></el-cascader>-->
            <el-select v-model="form.provice" placeholder="请选择所在省份或地区">
              <el-option v-for="(item, index) in provice" :key="item.orgName" :label="item.orgName" :value="item.orgName"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="建议反馈" prop="content">
            <el-input type="textarea" v-model="form.content" resize="none" placeholder="请填写您的建议反馈内容"></el-input>
          </el-form-item>
          <div style="padding-left: 88px;">
            <el-button class="contact-btn" @click="handleSubmit">提 交</el-button>
          </div>
        </div>
      </el-form>

    </div>
  </div>
  <div class="contact_ads"></div>
</div>
<script src="<%- locals.static %>/js/md5.js"></script>
<script>
  var pid = 'pid.mall',
    pcode = 'Z0zCnRE3IaY9Kzem'
  var validatorUserPhone = function(rule, value, callback) {
    const reg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/
    if (!value) {
      return callback(new Error('请输入手机'));
    } else if (!reg.test(value)) {
      return callback(new Error('请输入正确的手机号码'));
    } else {
      return callback()
    }
  }
  var newVue = new Vue({
    name: 'contact',
    el: '#contact',
    data: {
      host: '<%- host %>',
      form: {
        type: '建议反馈',
        trade: '其他',
        tradeName: '其他',
        service: '其他',
        serviceName: '其他',
        company: '',
        provice: '',
        content: '',
        customer: '<%- userInfo.userName %>',
        email: '<%- userInfo.userEmail %>',
        phone: '<%- userInfo.userPhone %>',
        frontUrl: window.location.href,
        verifyCode: ''
      },
      rules: {
        company: [{ required: true, message: '*必填字段' }],
        provice: [{ required: true, message: '*必填字段' }],
        content: [{ required: true, message: '*必填字段' }],
        customer: [{ required: true, message: '*必填字段' }],
        email: [{ required: true, message: '*必填字段' }, { trigger: 'blur', type: 'email', message: '请输入正确的邮箱' }],
        phone: [{ required: true, message: '*必填字段' }, { trigger: 'blur', validator: validatorUserPhone }],
        verifyCode: [{ required: true, message: '*必填字段' }]
      },
      timer: null,
      seconds: 59,
      provice: [],
      props: {
        value: 'orgName',
        label: 'orgName',
        children: 'citys'
      },
      loading: false
    },
    created: function () {
      this.qryCity()
    },
    mounted: function () {
    },
    methods: {
      sing: function (param, timestamp) {
        var pmd5 = md5((pid + pcode).toUpperCase());
        var str = JSON.stringify(param) + pmd5.toUpperCase();
        return md5(str + timestamp);
      },
      qryCity: function () {
        var param = { }
        var timestamp = new Date().valueOf();
        var that = this
        $.ajax({
          type: 'POST',
          url: this.host + '/ticCenter/business/api.v1.center/org/qryCity',
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: pid,
            sign: this.sing(param, timestamp),
             timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            if (res.resultCode === '0') {
              for(var i = 0; i < res.data.length; i++) {
                res.data[i].citys = null
                /*if (!res.data[i].citys || !res.data[i].citys.length) {
                  delete res.data[i].citys
                }*/
              }
              that.provice = res.data || []
            } else {
              alert(res.resultMsg)
            }
          },
          fail: function (data) {
            alert(res.resultMsg)
          },
          complete: function (complete) {
          }
        })
      },
      sendCaptcha: function() {
        if (this.timer) {
          return
        }
        if (!this.form.phone) {
          alert('请填写手机号码')
          return
        }
        const reg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/
        if (!reg.test(this.form.phone)) {
          alert('请填写正确的手机号码')
          return
        }
        var param = {
          projectName: 'TIC_TEMPLATE',
          verifyMode: 1,
          verifyNumber: this.form.phone,
          verifyType: 2
        }
        var timestamp = new Date().valueOf();
        var that = this
        $.ajax({
          type: 'POST',
          url: this.host + '/ticCenter/business/api.v1.center/verify/getCode',
          data: JSON.stringify(param),
          contentType: 'application/json',
          headers: {
            pid: pid,
            sign: this.sing(param, timestamp),
             timestamp: timestamp,
            frontUrl: window.location.href
          },
          success: function (res) {
            if (res.resultCode === '0') {
              that.timer = setInterval(function() {
                if (that.seconds > 1) {
                  that.seconds -= 1
                } else {
                  clearInterval(that.timer)
                  that.timer = null
                  that.seconds = 59
                }
              }, 1000)
            } else {
              alert(res.resultMsg || '提交失败，请重试或刷新。');
            }
          },
          fail: function (data) {
            alert(res.resultMsg)
          },
          complete: function (complete) {
          }
        })
      },
      handleSubmit: function() {
        const that = this
        that.loading = true
        this.$refs.ruleForm.validate(function(valid) {
          if (valid) {
            var param = that.form
            var timestamp = new Date().valueOf();
            $.ajax({
              type: 'POST',
              url: '/submitTicket',
              data: JSON.stringify(param),
              contentType: 'application/json',
              headers: {
                pid: pid,
                sign: that.sing(param, timestamp),
                 timestamp: timestamp,
            frontUrl: window.location.href
              },
              success: function (res) {
                that.loading = false
                if (res.success) {
                  window.location = '/success';
                } else {
                  alert(res.resultMsg || '提交失败，请重试或刷新。');
                }
              },
              fail: function (data) {
                that.loading = false
                alert(res.resultMsg)
              },
              complete: function (complete) {
              }
            })
          } else {
            that.loading = false
          }
        })
      }
    },
    filters: {}
  });
</script>
<% include footer.html %>

