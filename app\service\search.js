'use strict';

const Service = require('egg').Service;
const moment = require('moment');

function subString(str, n) {
  var r = /[^\x00-\xff]/g;
  var m;

  if (str.replace(r, '**').length > n) {
    m = Math.floor(n / 2);

    for (var i = m, l = str.length; i < l; i++) {
      if (str.substr(0, i).replace(r, '**').length >= n) {
        var newStr = str.substr(0, i) + '...';
        return newStr;
      }
    }
  }

  return str;
}

var unique = function (arr, key) {
  var r = [];
  for (var i = 0, l = arr.length; i < l; i++) {
    for (var j = i + 1; j < l; j++) {
      if (key) {
        if (arr[i][key] === arr[j][key]) {
          j = ++i;
        }
      } else {
        if (arr[i] === arr[j]) {
          j = ++i;
        }
      }
    }
    r.push(arr[i]);
  }
  return r;
};

class SearchService extends Service {
  async getResult(params) {
    const {
      app
    } = this;
    const page = params.page || 1;
    const limit = params.limit || 10;
    const keyword = params.q;

    const type = params.type;
    const pid = params.pid;

    if (!keyword || keyword == '') {
      return {
        success: false,
        msg: '缺少关键字'
      }
    }

    const klen = await app.mysql.count('analysis_keyword', {
      'keyword': keyword
    });
    if (klen == 0) {
      await app.mysql.insert('analysis_keyword', {
        'keyword': keyword
      });
    } else {
      // await app.mysql.query('UPDATE analysis_keyword SET num = num+1 WHERE keyword="' + keyword + '"');
    }

    let result = [];
    let catas = [];
    let types = [false, false, false, false];
    let cataTypes = [false, false];
    let serviceLenQuery = [],
      solutionLenQuery = [],
      caseLenQuery = [],
      newsLenQuery = [],
      resourceLenQuery = [];
    serviceLenQuery.concat(await app.mysql.query('SELECT id FROM sku WHERE name LIKE "%' + keyword + '%" AND is_delete=0 AND is_use=1 AND type IN (1,2)'));
    solutionLenQuery.concat(await app.mysql.query('SELECT id FROM sku WHERE name LIKE "%' + keyword + '%" AND is_delete=0 AND is_use=1 AND type IN (3,4)'));
    caseLenQuery.concat(await app.mysql.query('SELECT id FROM cases WHERE title LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1'));
    newsLenQuery.concat(await app.mysql.query('SELECT id FROM news WHERE title LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1'));
    resourceLenQuery.concat(await app.mysql.query('SELECT id FROM resource WHERE title LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1'));
    serviceLenQuery = serviceLenQuery.concat(await app.mysql.query('SELECT id FROM sku WHERE description LIKE "%' + keyword + '%" AND is_delete=0 AND is_use=1 AND type IN (1,2)'));
    solutionLenQuery = solutionLenQuery.concat(await app.mysql.query('SELECT id FROM sku WHERE description LIKE "%' + keyword + '%" AND is_delete=0 AND is_use=1 AND type IN (3,4)'));
    caseLenQuery = caseLenQuery.concat(await app.mysql.query('SELECT id FROM cases WHERE content LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1'));
    newsLenQuery = newsLenQuery.concat(await app.mysql.query('SELECT id FROM news WHERE content LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1'));
    resourceLenQuery = resourceLenQuery.concat(await app.mysql.query('SELECT id FROM resource WHERE path LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1'));

    let serviceResult = await app.mysql.query('SELECT s.id AS serviceId,s.name AS title,s.description,s.gmt_modify AS time,c.parent_id AS cid FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id LEFT JOIN catalog c ON c.id=cr.catalog_id WHERE s.name LIKE "%' + keyword + '%" AND s.is_delete=0 AND is_use=1 AND s.type IN (1,2)');
    let solutionResult = await app.mysql.query('SELECT s.id AS solutionId,s.name AS title,s.description,s.gmt_modify AS time,c.id AS cid,c.name AS cname FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id LEFT JOIN catalog c ON c.id=cr.catalog_id WHERE s.name LIKE "%' + keyword + '%" AND s.is_delete=0 AND is_use=1 AND s.type IN (3,4)');
    let caseResult = await app.mysql.query('SELECT id AS case_id,title,content AS description,gmt_publish_time AS time FROM cases WHERE title LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1');
    let newsResult = await app.mysql.query('SELECT id AS news_id,title,content AS description,gmt_publish_time AS time FROM news WHERE title LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1');
    let resourceResult = await app.mysql.query('SELECT id AS resource_id,title,path,gmt_publish_time AS time FROM resource WHERE title LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1');

    serviceResult = serviceResult.concat(await app.mysql.query('SELECT s.id AS serviceId,s.name AS title,s.description,s.gmt_modify AS time,c.parent_id AS cid FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id LEFT JOIN catalog c ON c.id=cr.catalog_id WHERE s.description LIKE "%' + keyword + '%" AND s.is_delete=0 AND is_use=1 AND s.type IN (1,2)'));
    solutionResult = solutionResult.concat(await app.mysql.query('SELECT s.id AS solutionId,s.name AS title,s.description,s.gmt_modify AS time,c.id AS cid,c.name AS cname FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id LEFT JOIN catalog c ON c.id=cr.catalog_id WHERE s.description LIKE "%' + keyword + '%" AND s.is_delete=0 AND is_use=1 AND s.type IN (3,4)'));
    caseResult = caseResult.concat(await app.mysql.query('SELECT id AS case_id,title,content AS description,gmt_publish_time AS time FROM cases WHERE content LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1'));
    newsResult = newsResult.concat(await app.mysql.query('SELECT id AS news_id,title,content AS description,gmt_publish_time AS time FROM news WHERE content LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1'));
    resourceResult = resourceResult.concat(await app.mysql.query('SELECT id AS resource_id,title,path,gmt_publish_time AS time FROM resource WHERE path LIKE "%' + keyword + '%" AND is_delete=0 AND is_publish=1'));

    if (typeof type == 'undefined' || type == 0) {
      for (let item of serviceResult) {
        item.time = moment(item.time).format('YYYY-MM-DD');

        if (item.cid && item.cid != 1 && item.cid != 2) {
          let cataInfo = await app.mysql.select('catalog', {
            columns: ['id', 'name', 'parent_id', 'alias'],
            where: {
              id: item.cid
            }
          });
          catas.push(cataInfo[0]);
          item.alias = cataInfo[0].alias;
          item.pid = cataInfo[0].parent_id;
        }

        let serviceRepeat = false;
        result.some(r => {
          if (item.serviceId == r.serviceId) {
            serviceRepeat = true;
          }
        })

        if (!serviceRepeat) {
          if (typeof pid == 'undefined' || pid == item.cid) {
            result.push(item);
          }

        }

      }

      for (let item of solutionResult) {
        item.time = moment(item.time).format('YYYY-MM-DD');

        const ssinfo = await app.mysql.get('catalog', {
          id: item.cid
        });

        if (ssinfo) {
          catas.push({
            id: item.cid,
            name: item.cname,
            parent_id: ssinfo.parent_id
          });
          item.alias = ssinfo.alias;
          item.pid = ssinfo.parent_id;
        }

        let solutionRepeat = false;
        result.some(r => {
          if (item.solutionId == r.solutionId) {
            solutionRepeat = true;
          }
        })

        if (!solutionRepeat) {
          if (typeof pid == 'undefined' || pid == item.cid) {
            result.push(item);
          }
        }
      }
    }

    if (typeof type == 'undefined' || type == 1) {
      for (let item of caseResult) {
        const ssinfo = await app.mysql.get('catalog_relation', {
          case_id: item.case_id
        });
        const cataInfo = await app.mysql.get('catalog', {
          id: ssinfo.catalog_id
        });
        item.catalog_name = cataInfo.name;
        item.catalogAlias = cataInfo.alias;
        item.pid = cataInfo.parent_id;
        item.time = moment(item.time).format('YYYY-MM-DD');
        item.description ? item.description = subString(item.description.replace(/<.*?>/g, ""), 200) : null;
        result.push(item);
      }
    }

    if (typeof type == 'undefined' || type == 2) {
      for (let item of newsResult) {
        item.time = moment(item.time).format('YYYY-MM-DD');
        item.description ? item.description = subString(item.description.replace(/<.*?>/g, ""), 200) : null;
        result.push(item);
      }
    }

    if (typeof type == 'undefined' || type == 3) {
      for (let item of resourceResult) {
        item.time = moment(item.time).format('YYYY-MM-DD');
        result.push(item);
      }
    }

    // }

    let total = result.length;
    let list = [];
    for (let [i, item] of result.entries()) {
      if (i >= (page - 1) * limit && i < page * limit) {
        list.push(item);
      }
    }

    catas = unique(catas, 'id');

    if (serviceLenQuery.length > 0 || solutionLenQuery.length > 0) {
      types[0] = true;
    }

    if (caseLenQuery.length > 0) {
      types[1] = true;
    }

    if (newsLenQuery.length > 0) {
      types[2] = true;
    }

    if (resourceLenQuery.length > 0) {
      types[3] = true;
    }

    catas.some(item => {
      if (item.parent_id == 1) {
        cataTypes[0] = true;
      } else if (item.parent_id == 2) {
        cataTypes[1] = true;
      }
    })

    return {
      list,
      total,
      catas,
      types,
      cataTypes,
      words: [{
        word: keyword
      }],
    }
  }

  async hotsku() {
    const {
      app
    } = this;

    const list = await app.mysql.query('SELECT s.id,s.name,s.alias FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id WHERE cr.catalog_id=6 AND s.is_delete=0 AND s.is_use=1 ORDER BY cr.sort_num,s.gmt_modify DESC');

    return {
      list
    };
  }
}

module.exports = SearchService;