.oiq-landing-page {
  background: #eeeeee;
  padding-bottom: 80px;
  position: relative;
}
.oiq-landing-page a {
  cursor: pointer;
}
.oiq-landing-page .oiq-banner {
  max-width: 1920px;
  margin: auto;
  height: 350px;
  background: url(../images/oiq/landingPage/oiq-banner.jpg) no-repeat center center;
  position: relative;
}
.oiq-landing-page .banner-content {
  margin: 61px 0 0 342px;
  width: 604px;
  display: inline-block;
  text-align: center;
}
.oiq-landing-page .banner-title {
  font-weight: bold;
  font-size: 36px;
  color: #FFFFFF;
  text-shadow: 3px 3px 5px rgba(0, 0, 0, 0.28);
  display: inline-block;
}
.oiq-landing-page .banner-detail {
  margin-top: 21px;
  text-align: center;
  color: #FFFFFF;
  font-size: 22px;
  margin-bottom: 27px;
}
.oiq-landing-page .banner-btn {
  width: 170px;
  height: 36px;
  background: #FFFFFF;
  box-shadow: 5px 6px 18px 0px rgba(0, 0, 0, 0.3);
  border-radius: 25px;
  font-size: 21px;
  color: #ca4300;
  text-align: center;
  display: inline-block;
  line-height: 36px;
  padding: 7px 0;
}
.oiq-landing-page .banner-btn .banner-btn-icon {
  display: inline-block;
  width: 36px;
  height: 36px;
  background: url("../images/oiq/landingPage/banner-btn-icon.png");
  vertical-align: bottom;
}
.oiq-landing-page .banner-btn:hover {
  background: linear-gradient(190deg, #FE8A02, #ca4300);
  color: #FFFFFF;
}
.oiq-landing-page .banner-btn:hover .banner-btn-icon {
  background: url("../images/oiq/landingPage/banner-btn-icon-active.png");
}
.oiq-landing-page .big-data {
  box-sizing: border-box;
  width: 1226px;
  margin: auto;
  height: 190px;
  background-color: #fff;
  background-image: url(../images/oiq/landingPage/data-bg.png);
  position: relative;
  margin-top: -86px;
  border-radius: 8px;
  padding: 25px 43px 0 31px;
  box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.2);
}
.oiq-landing-page .big-data .data-title {
  font-size: 18px;
  color: #333333;
  line-height: 22px;
}
.oiq-landing-page .big-data .data-icon {
  display: inline-block;
  width: 22px;
  height: 22px;
  background: url("../images/oiq/landingPage/data-icon.png");
  margin-right: 10px;
  vertical-align: text-bottom;
}
.oiq-landing-page .big-data .data-scroll-list {
  height: 22px;
  list-style: none;
  float: right;
  overflow: hidden;
}
.oiq-landing-page .big-data .data-scroll-list li {
  height: 22px;
  position: relative;
  line-height: 22px;
  text-align: right;
  font-size: 14px;
  color: #333333;
}
.oiq-landing-page .big-data .data-list {
  display: flex;
  justify-content: space-between;
  padding: 0 86px 0 100px;
  margin-top: 31px;
}
.oiq-landing-page .big-data .data-detail {
  font-size: 36px;
  line-height: 36px;
  font-family: Univers Condensed;
  font-weight: bold;
  color: #ca4300;
  margin-bottom: 17px;
}
.oiq-landing-page .big-data .detail-label {
  font-size: 16px;
  color: #333333;
  line-height: 16px;
}
.oiq-landing-page .big-btn-floor {
  width: 1226px;
  margin: auto;
  margin-top: 27px;
  display: flex;
  justify-content: space-between;
}
.oiq-landing-page .big-btn-floor .big-btn {
  width: 390px;
  height: 100px;
  border-radius: 8px;
  cursor: pointer;
  line-height: 100px;
  color: #FFFFFF;
  box-sizing: border-box;
  padding-left: 90px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.oiq-landing-page .big-btn-floor .big-btn.btn1 {
  background: url("../images/oiq/landingPage/big-btn1.png");
}
.oiq-landing-page .big-btn-floor .big-btn.btn2 {
  background: url("../images/oiq/landingPage/big-btn2.png");
}
.oiq-landing-page .big-btn-floor .big-btn.btn3 {
  background: url("../images/oiq/landingPage/big-btn3.png");
}
.oiq-landing-page .big-btn-floor .big-btn .title {
  font-size: 30px;
}
.oiq-landing-page .big-btn-floor .big-btn .detail {
  font-size: 16px;
  line-height: 20px;
  height: 40px;
  display: flex;
  padding-right: 50px;
}
.oiq-landing-page .big-btn-floor .big-btn .btn-icon {
  display: inline-block;
  width: 53px;
  height: 24px;
  background: #FAFAFA;
  box-shadow: 5px 6px 18px 0px rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  text-align: center;
  box-sizing: border-box;
  padding: 4.5px 0;
  line-height: 1;
  margin-right: 80px;
}
.oiq-landing-page .big-btn-floor .big-btn .btn-icon .big-btn-icon {
  display: inline-block;
  width: 17px;
  height: 15px;
  background: url("../images/oiq/landingPage/big-btn-icon.png");
}
.oiq-landing-page .big-btn-floor .big-btn .btn-icon:hover {
  background: linear-gradient(235deg, #FE8A02, #ca4300);
}
.oiq-landing-page .big-btn-floor .big-btn .btn-icon:hover .big-btn-icon {
  background: url("../images/oiq/landingPage/big-btn-icon-active.png");
}
.oiq-landing-page .video-floor {
  margin-top: 32px;
}
.oiq-landing-page .video-floor .wrap {
  background: #fff;
  height: 456px;
  border-radius: 8px;
  display: flex;
  padding: 32px 0 0 26px;
  box-sizing: border-box;
}
.oiq-landing-page .video-floor .wrap .video-left-content {
  margin-right: 55px;
}
.oiq-landing-page .video-floor .wrap .video-title {
  font-size: 18px;
  color: #333333;
  line-height: 21px;
  margin-bottom: 23px;
}
.oiq-landing-page .video-floor .wrap .video-title img {
  width: 24px;
  height: 21px;
  vertical-align: text-bottom;
  margin-right: 10px;
}
.oiq-landing-page .video-floor .wrap .video-content {
  width: 587px;
  height: 330px;
}
.oiq-landing-page .video-floor .wrap .video-content img {
  width: 100%;
  height: 100%;
  cursor: pointer;
}
.oiq-landing-page .video-floor .wrap .video-content video {
  display: none;
}
.oiq-landing-page .video-floor .wrap .video-right-content {
  padding-top: 45px;
  position: relative;
  flex: 1;
}
.oiq-landing-page .video-floor .wrap .video-right-content .ques-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  line-height: 18px;
  margin-bottom: 19px;
}
.oiq-landing-page .video-floor .wrap .video-right-content .video-text-list {
  margin-bottom: 25px;
  font-size: 16px;
  color: #333333;
  line-height: 16px;
}
.oiq-landing-page .video-floor .wrap .video-right-content .video-text-list li {
  display: flex;
  font-size: 16px;
  color: #333333;
  line-height: 16px;
}
.oiq-landing-page .video-floor .wrap .video-right-content .video-text-list li:before {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  background: #ca4300;
  margin-right: 11px;
  margin-top: 5px;
}
.oiq-landing-page .video-floor .wrap .video-right-content .video-text-list li:not(:last-child) {
  margin-bottom: 14px;
}
.oiq-landing-page .video-floor .wrap .video-right-content .video-btn {
  position: absolute;
  bottom: 49px;
  left: 0;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 44px;
  width: 161px;
  height: 44px;
  background: #ca4300;
  z-index: 2;
  text-align: center;
  cursor: pointer;
}
.oiq-landing-page .video-floor .wrap .video-right-content .video-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 44px;
  background: #cc5500;
  z-index: -1;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}
.oiq-landing-page .video-floor .wrap .video-right-content .video-btn:hover:before {
  width: 161px;
}
.oiq-landing-page .video-floor .wrap .video-right-content .text-icon {
  position: absolute;
  top: 27px;
  right: 27px;
}
.oiq-landing-page .products-floor {
  width: 1226px;
  margin: auto;
  margin-top: 44px;
  overflow: hidden;
}
.oiq-landing-page .products-floor .products-title {
  font-size: 30px;
  color: #333333;
  line-height: 1;
  margin-bottom: 33px;
  text-align: center;
}
.oiq-landing-page .products-floor .products-list .products-item {
  width: 235px!important;
  height: 360px;
  background: #FFFFFF;
  text-align: center;
  box-sizing: border-box;
  padding-top: 25px;
  font-size: 16px;
  color: #333333;
  line-height: 1;
  margin-bottom: 18px;
  margin-right: 10.2px;
}
.oiq-landing-page .products-floor .products-list .products-item:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transition: 0.4s;
  transform: translate3d(0, -3px, 0);
  padding-top: 10px;
}
.oiq-landing-page .products-floor .products-list .products-item:hover .products-btn {
  display: inline-block;
}
.oiq-landing-page .products-floor .products-list .products-item .tit {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16px;
}
.oiq-landing-page .products-floor .products-list .products-item .title_img {
  width: 181px;
  height: 100px;
  /*display: block;*/
  /*margin: auto;*/
  /*margin-bottom: 16px;*/
}
.oiq-landing-page .products-floor .products-list .products-item .title_children {
  display: flex;
  flex-direction: column;
  padding: 0 24px;
  margin-top: 23px;
}
.oiq-landing-page .products-floor .products-list .products-item .title_children .title_children_list {
  display: flex;
  height: 28px;
  align-items: center;
}
.oiq-landing-page .products-floor .products-list .products-item .title_children .title_children_list .title_children_icon {
  margin-right: 3px;
  width: 19px;
  height: 19px;
}
.oiq-landing-page .products-floor .products-list .products-item .title_children .title_children_list .title_children_icon img {
  width: 19px;
  height: 19px;
}
.oiq-landing-page .products-floor .products-list .products-item .title_children .title_children_list .title_children_content {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.oiq-landing-page .products-floor .products-list .products-item .products-btn {
  margin-top: 6px;
  display: none;
  width: 86px;
  height: 30px;
  border: 1px solid #ca4300;
  border-radius: 15px;
  box-sizing: border-box;
  color: #CA4300;
  font-size: 14px;
  font-weight: 400;
}
.oiq-landing-page .products-floor .products-list .products-item .products-btn:hover {
  background: #ca4300;
}
.oiq-landing-page .products-floor .products-list .products-item .products-btn:hover .products-text {
  color: #FFFFFF;
}
.oiq-landing-page .products-floor .products-list .products-item .products-btn:hover .products-btn-icon {
  background: url("../images/oiq/landingPage/category-btn-active.png");
}
.oiq-landing-page .products-floor .products-list .products-item .products-btn .products-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 28px;
}
.oiq-landing-page .products-floor .products-list .products-item .products-btn .products-icon .products-text {
  margin-right: 5px;
}
.oiq-landing-page .products-floor .products-list .products-item .products-btn .products-icon .products-btn-icon {
  display: inline-block;
  width: 14px;
  height: 13px;
  background: url("../images/oiq/landingPage/category-btn.png");
}
.oiq-landing-page .products-floor .swiper-pagination1 {
  text-align: center;
}
.oiq-landing-page .products-floor .swiper-pagination1 .swiper-pagination-bullet {
  width: 10px;
  height: 5px;
  background: #F98D16;
  border-radius: 3px;
  opacity: 1;
}
.oiq-landing-page .products-floor .swiper-pagination1 .swiper-pagination-bullet:not(:last-child) {
  margin-right: 5px;
}
.oiq-landing-page .products-floor .swiper-pagination1 .swiper-pagination-bullet-active {
  width: 20px;
}
.oiq-landing-page .category-floor {
  width: 1226px;
  margin: auto;
  margin-top: 44px;
}
.oiq-landing-page .category-floor .category-title {
  font-size: 30px;
  color: #333333;
  line-height: 1;
  margin-bottom: 33px;
  text-align: center;
}
.oiq-landing-page .category-floor .category-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.oiq-landing-page .category-floor .category-list .category-item {
  width: 144px;
  height: 144px;
  background: #FFFFFF;
  text-align: center;
  box-sizing: border-box;
  padding-top: 25px;
  font-size: 16px;
  color: #333333;
  line-height: 1;
  margin-bottom: 18px;
}
.oiq-landing-page .category-floor .category-list .category-item img {
  width: 58px;
  height: 58px;
  display: block;
  margin: auto;
  margin-bottom: 16px;
}
.oiq-landing-page .category-floor .category-list .category-item .category-btn {
  margin-top: 6px;
  display: none;
  width: 37px;
  height: 22px;
  border: 1px solid #ca4300;
  border-radius: 11px;
  box-sizing: border-box;
  padding: 3.5px 0;
  text-align: center;
}
.oiq-landing-page .category-floor .category-list .category-item .category-btn .category-btn-icon {
  display: inline-block;
  width: 14px;
  height: 13px;
  background: url("../images/oiq/landingPage/category-btn.png");
}
.oiq-landing-page .category-floor .category-list .category-item .category-btn:hover {
  background: #ca4300;
}
.oiq-landing-page .category-floor .category-list .category-item .category-btn:hover .category-btn-icon {
  background: url("../images/oiq/landingPage/category-btn-active.png");
}
.oiq-landing-page .category-floor .category-list .category-item:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transition: 0.4s;
  transform: translate3d(0, -3px, 0);
  padding-top: 10px;
}
.oiq-landing-page .category-floor .category-list .category-item:hover .category-btn {
  display: inline-block;
}
.oiq-landing-page .project-floor {
  width: 1226px;
  margin: auto;
  margin-top: 54px;
}
.oiq-landing-page .project-floor .project-title {
  font-size: 30px;
  color: #333333;
  line-height: 1;
  margin-bottom: 33px;
  text-align: center;
}
.oiq-landing-page .project-floor .project-content {
  background: #fff;
  padding: 28px 22px;
  box-sizing: border-box;
  display: flex;
  border-radius: 8px;
}
.oiq-landing-page .project-floor .project-content .project-list {
  width: 197px;
  box-sizing: border-box;
  border-right: 1px solid #eee;
  position: relative;
}
.oiq-landing-page .project-floor .project-content .project-list .project-btn {
  position: absolute;
  left: 25px;
  bottom: 0;
  width: 135px;
  height: 28px;
  border: 1px solid #ca4300;
  border-radius: 15px;
  font-size: 16px;
  color: #ca4300;
  line-height: 28px;
  text-align: center;
}
.oiq-landing-page .project-floor .project-content .project-list .project-btn:hover {
  background: #ca4300;
  color: #FFFFFF;
}
.oiq-landing-page .project-floor .project-content .project-list:last-child {
  border-right: 1px solid transparent;
}
.oiq-landing-page .project-floor .project-content .project-list:first-child .project-item {
  padding-left: 7px;
}
.oiq-landing-page .project-floor .project-content .project-list .project-item {
  margin-bottom: 26.5px;
  padding-left: 25px;
}
.oiq-landing-page .project-floor .project-content .project-list .project-item a {
  display: flex;
  font-size: 13px;
  color: #333333;
}
.oiq-landing-page .project-floor .project-content .project-list .project-item span {
  line-height: 26px;
}
.oiq-landing-page .project-floor .project-content .project-list .project-item img {
  width: 26px;
  height: 26px;
  margin-right: 9px;
}
.oiq-landing-page .project-floor .project-content .project-list .project-item .active-icon {
  display: none;
}
.oiq-landing-page .project-floor .project-content .project-list .project-item:hover .normal-icon {
  display: none;
}
.oiq-landing-page .project-floor .project-content .project-list .project-item:hover .active-icon {
  display: block;
}
.oiq-landing-page .project-floor .project-content .project-list .project-item:hover a {
  color: #ca4300;
  font-weight: bold;
}
.oiq-landing-page .project-floor .project-content .project-list .project-item:last-child {
  margin-bottom: 0;
}
.oiq-landing-page .new-order-floor {
  width: 1226px;
  margin: auto;
  margin-top: 44px;
  position: relative;
}
.oiq-landing-page .new-order-floor .order-title {
  font-size: 30px;
  color: #333333;
  line-height: 30px;
  margin-bottom: 33px;
  text-align: center;
  position: relative;
}
.oiq-landing-page .new-order-floor .order-title .reload-list-btn {
  position: absolute;
  top: 0;
  right: 0;
  height: 16px;
  font-size: 16px;
  color: #878787;
  cursor: pointer;
}
.oiq-landing-page .new-order-floor .order-title .reload-list-btn .reload-list-btn-icon {
  display: inline-block;
  width: 22px;
  height: 19px;
  background: url("../images/oiq/landingPage/reload-list.png");
  margin-right: 10px;
  vertical-align: text-bottom;
}
.oiq-landing-page .new-order-floor .order-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.oiq-landing-page .new-order-floor .order-list .order-item {
  width: 600px;
  height: 160px;
  background: #FFFFFF;
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  box-sizing: border-box;
  padding: 21px 19px 0 21px;
  margin-bottom: 30px;
  position: relative;
}
.oiq-landing-page .new-order-floor .order-list .order-item:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transition: 0.4s;
  transform: translate3d(0, -3px, 0);
}
.oiq-landing-page .new-order-floor .order-list .order-item .order-top-content {
  margin-bottom: 19px;
  display: flex;
}
.oiq-landing-page .new-order-floor .order-list .order-item .order-time {
  width: 75px;
  margin-right: 18px;
  font-size: 12px;
  font-family: Arial;
  color: #878787;
  line-height: 14px;
}
.oiq-landing-page .new-order-floor .order-list .order-item .top-title {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 1;
}
.oiq-landing-page .new-order-floor .order-list .order-item .category-content {
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 400;
  color: #333;
  line-height: 22px;
  display: flex;
}
.oiq-landing-page .new-order-floor .order-list .order-item .category-content .item-label {
  width: 75px;
  height: 22px;
  background: #EDEDED;
  border-radius: 8px;
  margin-right: 18px;
  color: #878787;
  text-align: center;
  flex-shrink: 0;
}
.oiq-landing-page .new-order-floor .order-list .order-item .category-content .item-detail {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.oiq-landing-page .new-order-floor .order-list .order-item .category-content .item-detail * {
  display: inline;
}
.oiq-landing-page .new-order-floor .order-list .order-item .order-btn {
  width: 135px;
  height: 30px;
  border: 1px solid #ca4300;
  border-radius: 15px;
  text-align: center;
  font-size: 14px;
  color: #ca4300;
  line-height: 28px;
  cursor: pointer;
  position: absolute;
  bottom: 17px;
  right: 19px;
}
.oiq-landing-page .new-order-floor .order-list .order-item:hover .order-btn {
  background: #ca4300;
  color: #FFFFFF;
}
.oiq-landing-page .comment-floor {
  width: 1226px;
  margin: 14px auto 41px;
  height: 306px;
  background: url("../images/oiq/landingPage/comment-bg.png");
  box-sizing: border-box;
  padding: 116px 113px 0 78px;
  position: relative;
}
.oiq-landing-page .comment-floor .comment-title {
  font-size: 30px;
  color: #333333;
  line-height: 1;
  text-align: center;
  position: absolute;
  width: 100%;
  top: 46px;
  left: 0;
}
.oiq-landing-page .comment-floor .comment-content {
  display: flex;
}
.oiq-landing-page .comment-floor .comment-content .step-img {
  width: 44px;
  height: 29px;
  margin-right: 21px;
}
.oiq-landing-page .comment-floor .comment-content .avatar-img {
  width: 76px;
  height: 76px;
  margin-right: 27px;
}
.oiq-landing-page .comment-floor .comment-content .user-content {
  font-size: 16px;
  color: #333333;
  line-height: 16px;
  width: 197px;
  margin-right: 38px;
}
.oiq-landing-page .comment-floor .comment-content .user-content .user-name {
  font-weight: bold;
}
.oiq-landing-page .comment-floor .comment-content .user-content .location-icon {
  width: 14px;
  height: 16px;
  margin: 0 7px 0 10px;
  vertical-align: text-top;
}
.oiq-landing-page .comment-floor .comment-content .user-content .location-name {
  font-size: 14px;
}
.oiq-landing-page .comment-floor .comment-content .user-content .company-name {
  margin-top: 12px;
  font-size: 14px;
  color: #878787;
  line-height: 24px;
}
.oiq-landing-page .comment-floor .comment-content .comment-line {
  width: 1px;
  height: 84px;
  background: #D3D3D3;
  margin-right: 40px;
}
.oiq-landing-page .comment-floor .comment-content .comment-detail {
  font-size: 15px;
  color: #333333;
  line-height: 28px;
  width: 595px;
}
.oiq-landing-page .comment-floor .comment-content .comment-detail .comment-icon {
  width: 24px;
  height: 15px;
  margin: 0 10px 0 8px;
}
.oiq-landing-page .comment-floor .swiper-container,
.oiq-landing-page .comment-floor .swiper-wrapper {
  height: 190px;
}
.oiq-landing-page .comment-floor .swiper-bottom {
  position: absolute;
  bottom: 0;
  left: 510px;
}
.oiq-landing-page .comment-floor .swiper-pagination {
  position: relative;
  display: inline-block;
  top: -53px;
}
.oiq-landing-page .comment-floor .swiper-pagination-bullet {
  width: 10px;
  height: 5px;
  background: #F98D16;
  border-radius: 3px;
  opacity: 1;
}
.oiq-landing-page .comment-floor .swiper-pagination-bullet:not(:last-child) {
  margin-right: 5px;
}
.oiq-landing-page .comment-floor .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 20px;
}
.oiq-landing-page .comment-floor .swiper-button-next,
.oiq-landing-page .comment-floor .swiper-button-prev {
  width: 80px;
  height: 306px;
  top: 0;
  margin-top: 0;
  background: none;
}
.oiq-landing-page .comment-floor .swiper-button-next:focus,
.oiq-landing-page .comment-floor .swiper-button-prev:focus {
  outline: none;
}
.oiq-landing-page .comment-floor .swiper-button-next {
  right: 0;
}
.oiq-landing-page .comment-floor .swiper-button-prev {
  width: 78px;
  left: 0;
}
.oiq-landing-page .quick-btn-floor {
  height: 80px;
  background: #F0F0F0;
  padding-top: 16px;
  box-sizing: border-box;
  background: linear-gradient(0deg, #F1F1F1, #FFFFFF);
  border: 1px solid #D7D7D7;
  box-shadow: 0px -3px 16px 0px rgba(0, 0, 0, 0.2);
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 11;
}
.oiq-landing-page .quick-btn-floor.not-fixed {
  position: absolute;
}
.oiq-landing-page .quick-btn-floor .quick-btn {
  width: 170px;
  height: 50px;
  margin: auto;
  background: linear-gradient(190deg, #FE8A02, #ca4300);
  box-shadow: 5px 6px 18px 0px rgba(0, 0, 0, 0.3);
  border-radius: 25px;
  font-size: 21px;
  color: #FFFFFF;
  box-sizing: border-box;
  padding-left: 27px;
  line-height: 50px;
  position: relative;
  cursor: pointer;
  display: block;
}
.oiq-landing-page .quick-btn-floor .quick-btn .quick-btn-icon {
  content: "";
  width: 36px;
  height: 36px;
  background: url("../images/oiq/landingPage/quick-btn-icon.png");
  position: absolute;
  top: 7px;
  right: 8px;
}
.oiq-landing-page .quick-btn-floor .quick-btn:hover {
  background: #ca4300;
}
.oiq-landing-page .quick-btn-floor .quick-btn:hover .quick-btn-icon {
  background: url("../images/oiq/landingPage/quick-btn-icon-active.png");
}
.oiq-landing-page .oiq-loading-ads {
  position: fixed;
  left: 0;
  top: 38%;
  z-index: 11;
}
.oiq-landing-page .plan {
  background: #fff;
  border-radius: 8px;
}
.oiq-landing-page .plan-step ul {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 65px;
  background: url('../images/oiq/landingPage/plan-step.png') no-repeat;
}
.oiq-landing-page .plan-step li {
  flex: 1;
  width: 110px;
  height: 65px;
  line-height: 65px;
  font-size: 18px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  padding-left: 140px;
}
.oiq-landing-page .plan-content ul {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 350px;
  background: url('../images/oiq/landingPage/step-content.jpg') no-repeat center top;
}
.oiq-landing-page .plan-content li {
  width: 262px;
  height: 40px;
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #ca4300;
  flex: 1;
  text-align: center;
  padding-top: 285px;
}
.oiq-landing-page .plan-button {
  display: flex;
  justify-content: space-around;
  align-content: center;
}
.oiq-landing-page .plan-button div {
  cursor: pointer;
  width: 60px;
  height: 35px;
  background: url('../images/oiq/landingPage/up.png') no-repeat;
}
.oiq-landing-page .plan-button div.active {
  background: url('../images/oiq/landingPage/down.png') no-repeat;
}
