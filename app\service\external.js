'use strict';

const Service = require('egg').Service;
const axios = require('axios');
const crypto = require('crypto');
const util = require('./../controller/util');
const env = require('../../config/info').siteInfo.env;
const envData = require('../../config/info').siteInfo.env;

axios.defaults.timeout = 1000 * 10; // 10s

function getCookie(ctx, key) {
  if (!ctx.request.header.cookie) {
    return '';
  }
  const carr = ctx.request.header.cookie.split('; ');
  let sjson = {};
  for (let item of carr) {
    let iarr = item.split('=');
    sjson[iarr[0]] = iarr[1];
  }

  if (sjson) {
    return sjson[key];
  } else {
    return '';
  }
}

const ticSend = {
  pid: 'pid.order',
  pcode: 'kPhG6so9xOHY3QsO'
}
const ticMall = {
  pid: 'pid.mall',
  pcode: 'Z0zCnRE3IaY9Kzem'
}
const ticMember = {
  pid: 'pid.member',
  pcode: 'CGz2RFHATB3XAsvg'
}
const ticSolr = {
  pid: 'pid.cms',
  pcode: 'HttKUdw7EUC2DFIM'
}

/*
  随机生成字符串
  length: 生成的字符串长度
*/
var randomStr = function (length) {
  var source = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  var result = '';
  for (var i = length; i > 0; --i)
    result += source[Math.floor(Math.random() * source.length)];
  return result;
};

function createHeader(env, type, params, header, that) {
  const ctx = that && that.ctx
  let frontUrl = '', si = ''
  if (ctx) {
    frontUrl = envData[env].url + ctx.request.url
    si = getCookie(ctx, 'sessionId') || '';
    if (!si) {
      ctx.cookies.set('sessionId', randomStr(32), {
        httpOnly: false
      })
    }
  }
  header = header || {}
  const pmd5 = crypto.createHash('md5').update((type.pid + type.pcode).toUpperCase()).digest('hex')
  const param = JSON.stringify(params) + pmd5.toUpperCase()
  const timestamp = new Date().getTime();
  const sign = crypto.createHash('md5').update(param + timestamp).digest('hex')
  const headers = {
    'Content-Type': 'application/json',
    pid: type.pid,
    pcode: type.pcode,
    timestamp,
    sign,
    version: env === 'gray' || env === 'prodGray' ? 'gray' : '', // 'gray'
    frontUrl: header.frontUrl || frontUrl,
    si: header.si || si,
    ia: header.ia || '',
    appid: '********',
    System: 'member'
  }
  // console.log(headers);
  for (var item in headers) {
    if (!headers[item]) delete headers[item]
  }
  return headers
}

class externalService extends Service {
  async sendSms(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    let result = await axios({
      url: host + '/ticSend/openapi/api.v1.send/smsSend/send',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticSend, params, headers, this),
    }).then(res => {
      ctx.logger.error('/ticSend/openapi/api.v1.send/smsSend/send', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticSend/openapi/api.v1.send/smsSend/send', err);
      return err
    });

    return {
      success: result && result.resultCode === '0' ? true : false,
      resultMsg: result && result.resultMsg
    };
  }

  async submitTicket(params, token, header) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    /* 添加来源 */
    if (params.webSource) {
      params.regSource = params.webSource
    } else {
      params.regSource = params.regSource ? params.regSource : 'mall'
    }
    params.regFrom = params.regFrom ? params.regFrom : params.frontUrl
    const headers = createHeader(app.locals.env, ticMall, params, header, this);
    return await axios({
      url: host + '/ticMall/business/api.v1.mall/ticket/createByVerify',
      method: 'post',
      data: JSON.stringify(params),
      headers: Object.assign(headers, {
        accessToken: token || '',
        appId: '********'
      }),
    }).then(res => {
      ctx.logger.error('/ticMall/business/api.v1.mall/ticket/createByVerify', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/ticket/createByVerify', err);
      return err
    });
  }

  async submitTicketByEmail(params, token, header) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    const headers = createHeader(app.locals.env, ticMall, params, header, this);
    return await axios({
      url: host + '/ticMall/business/api.v1.mall/ticket/create',
      method: 'post',
      data: JSON.stringify(params),
      headers: Object.assign(headers, {
        accessToken: token || '',
      }),
    }).then(res => {
      ctx.logger.error('/ticMall/business/api.v1.mall/ticket/create', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/ticket/create', err);
      return err
    });
  }

  async qryPackage(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMember/openapi/api.v1.packages/packages/qry',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMember, params, headers, this),
    }).then(res => {
      ctx.logger.error('/ticMember/openapi/api.v1.packages/packages/qry', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMember/openapi/api.v1.packages/packages/qry', err);
      return err
    });
  }

  async qryHotWord(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/solrServer/business/api.v1.solr/content/qryTitle',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error('/solrServer/business/api.v1.solr/content/qryTitle', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/solrServer/business/api.v1.solr/content/qryTitle', err);
      return err
    });
  }

  async clickHotWord(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/solrServer/business/api.v1.solr/content/click',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error('/solrServer/business/api.v1.solr/content/click', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/solrServer/business/api.v1.solr/content/click', err);
      return err
    });
  }

  // 获取新闻列表
  async qryNewsList(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/news/qryNews',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error('/ticMall/business/api.v1.mall/news/qryNews', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/news/qryNews', err);
      return err
    });
  }

  // 获取新闻详情
  async qryNewsDetail(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/news/qryDtl',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error('/ticMall/business/api.v1.mall/news/qryDtl', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/news/qryDtl', err);
      return err
    });
  }

  // 获取案例列表
  async qryCaseList(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/cases/qryCase',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error('/ticMall/business/api.v1.mall/cases/qryCase', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/cases/qryCase', err);
      return err
    });
  }

  // 获取案例详情
  async qryCaseDetail(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/cases/qryDtl',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error('/ticMall/business/api.v1.mall/cases/qryDtl', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/cases/qryDtl', err);
      return err
    });
  }

  // 获取课程列表
  async qryClassList(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/sku/qrySku',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error('/ticMall/business/api.v1.mall/sku/qrySku', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/sku/qrySku', err);
      return err
    });
  }

  // 资源下载数量增加
  async addDownloadNum(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/resource/download',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error('/ticMall/business/api.v1.mall/resource/download', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/resource/download', err);
      return err
    });
  }

  // 关键词点击数量增加
  async addKeywordNum(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/keyword/click',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error('/ticMall/business/api.v1.mall/keyword/click', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/keyword/click', err);
      return err
    });
  }

  // 页面来源统计
  async saveEntry(params, headers = {}) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/entry/save',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(host + '/ticMall/business/api.v1.mall/entry/save', res);
      return res.data
    }).catch(err => {
      ctx.logger.error(host + '/ticMall/business/api.v1.mall/entry/save', err);
      // xss错误拦截
      if (err.response && err.response.status === 500) {
        return {
          data: {
            data: [],
            totalNum: 0
          },
          resultCode: '0',
          resultMsg: 'Successful'
        }
      } else {
        return err
      }
    });
  }

  // 400客服电话点击
  async click400(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/record/save',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error('/ticMall/business/api.v1.mall/record/save', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/record/save', err);
      return err
    });
  }

  // 搜索接口
  async searchSolr(params, headers) {
    const {
      app,
      ctx
    } = this;

    return await axios({
      url: env[this.app.locals.env].solrUrl,
      env,
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticSolr, params, headers, this),
    }).then(res => {
      ctx.logger.error(env[this.app.locals.env].solrUrl, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(env[this.app.locals.env].solrUrl, err);
      // xss错误拦截
      if (err.response.status === 500) {
        return {
          data: {
            data: [],
            totalNum: 0
          },
          resultCode: '0',
          resultMsg: 'Successful'
        }
      } else {
        // sentry fix bug TIC-VIEW-1R
        return err
      }
    });
  }

  // 获取服务详情
  async qrySkuDetail(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/sku/qryDtl',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error('/ticMall/business/api.v1.mall/sku/qryDtl', res);
      return res.data
    }).catch(err => {
      ctx.logger.error('/ticMall/business/api.v1.mall/sku/qryDtl', err);
      return err
    });
  }

  // 登出接口
  async logout(params, token, header) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    const headers = createHeader(app.locals.env, ticMall, params, header, this);

    return await axios({
      url: host + '/ticSso/business/api.v1.sso/account/logout',
      method: 'post',
      data: JSON.stringify(params),
      headers: Object.assign(headers, {
        accessToken: token || '',
        appId: ********
      }),
    }).then(res => {
      ctx.logger.error('/ticSso/business/api.v1.sso/account/logout', res);
      return res.status == 200 ? true : false
    }).catch(err => {
      ctx.logger.error('/ticSso/business/api.v1.sso/account/logout', err);
      return err
    });
  }

  // get userinfo
  async getUserInfo(params, token, header) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    const headers = createHeader(app.locals.env, ticMall, params, header, this);

    return await axios({
      url: host + '/ticSso/business/api.v1.sso/account/getUserInfo',
      method: 'post',
      data: JSON.stringify(params),
      headers: Object.assign(headers, {
        accessToken: token || '',
        appId: ********
      }),
    }).then(res => {
      ctx.logger.error(`${host}/ticSso/business/api.v1.sso/account/getUserInfo`, res);
      if (res.data.resultCode === '0') {
        if (res.data.data.userEmail.length > 25) res.data.data.userEmailCopy = res.data.data.userEmail.substr(0, 25) + '...'
        return res.data.data
      } else {
        return null
      }
    }).catch(err => {
      ctx.logger.error(`${host}/ticSso/business/api.v1.sso/account/getUserInfo`, err);
      return err
    });
  }

  // 查询用户提交活动
  async qryActivity(params, token, header) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    const headers = createHeader(app.locals.env, ticMall, params, header, this);

    return await axios({
      url: host + '/ticMember/business/api.v2.user/promotion/qrySubmit',
      method: 'post',
      data: JSON.stringify(params),
      headers: Object.assign(headers, {
        accessToken: token || '',
        appId: ********
      }),
    }).then(res => {
      ctx.logger.error(`${host}/ticSso/business/api.v1.sso/account/getUserInfo`, res);
      if (res.data.resultCode === '0') {
        return res.data.data.items
      } else {
        return null
      }
    }).catch(err => {
      ctx.logger.error(`${host}/ticSso/business/api.v1.sso/account/getUserInfo`, err);
      return err
    });
  }

  //获取我的询价单数量
  async GetStateNum(params, token, header) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    const headers = createHeader(app.locals.env, ticMall, params, header, this);
    return await axios({
      url: host + '/ticMember/business/api.v2.order/order/getTotalNum',
      method: 'post',
      data: JSON.stringify(params),
      headers: Object.assign(headers, {
        accessToken: token || '',
        appId: ********
      }),
    }).then(res => {
      ctx.logger.error(`${host}/ticMember/business/api.v2.order/order/getTotalNum`, res);
      let num
      if (res.data.data && res.data.data.length > 0) {
        for (let i in res.data.data) {
          if (res.data.data[i].orderType === '200000') {
            num = res.data.data[i].num
          }
        }
      } else {
        num = 0
      }
      return num
    }).catch(err => {
      ctx.logger.error(`${host}/ticMember/business/api.v2.order/order/getTotalNum`, err);
      return err
    });
  }

  // 获取IM工具列表
  async getIMtool(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticCenter/business/api.v1.center/config/qry',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticCenter/business/api.v1.center/config/qry`, res);
      res.data.data.items.forEach(v => {
        v.groupName = v.paraCode.split("_")[0]
      })
      return res.data && res.data.data.items || []
    }).catch(err => {
      ctx.logger.error(`${host}/ticCenter/business/api.v1.center/config/qry`, err);
      return err
    });
  }

  // 获取导航菜单
  async getNavication(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/navi/qry',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/navi/qry`, res);
      return res.data && res.data.data.items || []
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/navi/qry`, err);
      return err
    });
  }

  // 推送SPM信息
  async sendSPM(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    return await axios.get(host + '/index.html?' + params)
      .then(function (res) {
        return res.data
      })
      .catch(function (e) {
        // fix bug TIC-VIEW-16
        return e
      });
  }

  // 获取短信验证码
  async getCodeQuote(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticCenter/business/api.v1.center/verify/check',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticCenter/business/api.v1.center/verify/check`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticCenter/business/api.v1.center/verify/check`, err);
      return err
    });
  }

  // 文件列表
  async getFileListApi(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/resource/qry',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/resource/qry`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/resource/qry`, err);
      return err
    });
  }

  // sku集合列表
  async getSkuCataApi(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/sku/qrySku',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/sku/qrySku`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/sku/qrySku`, err);
      return err
    });
  }

  // sku推荐列表
  async getSkuTagApi(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/sku/qryByTag',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/sku/qryByTag`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/sku/qryByTag`, err);
      return err
    });
  }

  // sku推荐列表
  async collection(params, token, header) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    const headers = createHeader(app.locals.env, ticMember, params, header, this)
    return await axios({
      url: host + '/ticMember/business/api.v2.user/like/stat',
      method: 'post',
      data: JSON.stringify(params),
      headers: Object.assign(headers, {
        accessToken: token || '',
      }),

    }).then(res => {
      ctx.logger.error(`${host}/ticMember/business/api.v2.user/like/stat`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMember/business/api.v2.user/like/stat`, err);
      return err
    });
  }

  // 登录成功回调
  async successCallback(params, token, header) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    const headers = createHeader(app.locals.env, ticMember, params, header, this)
    return await axios({
      url: host + '/ticMember/business/api.v2.user/like/set',
      method: 'post',
      data: JSON.stringify(params),
      headers: Object.assign(headers, {
        accessToken: token || '',
      }),

    }).then(res => {
      ctx.logger.error(`${host}/ticMember/business/api.v2.user/like/set`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMember/business/api.v2.user/like/set`, err);
      return err
    });
  }

  // 邮件分享
  async emailSharing(params, token, header) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    const headers = createHeader(app.locals.env, ticMember, params, header, this)
    return await axios({
      url: host + '/ticMall/business/api.v1.mall/resource/share',
      method: 'post',
      data: JSON.stringify(params),
      headers: Object.assign(headers, {
        accessToken: token || '',
      }),

    }).then(res => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/resource/share`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/resource/share`, err);
      return err
    });
  }

  // 校验token
  async checkToken(params, token, header) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    const headers = createHeader(app.locals.env, ticMember, params, header, this)
    return await axios({
      url: host + '/ticSso/business/api.v1.sso/account/checkToken',
      method: 'post',
      data: JSON.stringify(params),
      headers: Object.assign(headers, {
        accessToken: token || '',
      }),

    }).then(res => {
      ctx.logger.error(`${host}/ticSso/business/api.v1.sso/account/checkToken`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticSso/business/api.v1.sso/account/checkToken`, err);
      return err
    });
  }

  // 校验token
  async statByBusi(params, token, header) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);
    const headers = createHeader(app.locals.env, ticMall, params, header, this)
    var url = '/ticBigdata/business/api.v3.bigdata/page/statByBusi/' + params.id
    return await axios({
      url: host + url,
      method: 'post',
      data: JSON.stringify(params),
      headers: Object.assign(headers, {
        accessToken: token || '',
      }),

    }).then(res => {
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}+url`, err);
      return err
    });
  }

  // 获取case的相关推荐
  async casesQryRelate(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/cases/qryRelate',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/cases/qryRelate`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/cases/qryRelate`, err);
      return err
    });
  }

  // 获取核心供应商页面内容
  async getCoreSupplier(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/pageconfig/qryByCode',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/pageconfig/qryByCode`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/pageconfig/qryByCode`, err);
      return err
    });
  }

  // 滑动验证码验证
  async tencentCaptchaApi(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticCenter/business/api.v1.third/verify/slide',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticCenter/business/api.v1.third/verify/slide`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticCenter/business/api.v1.third/verify/slide`, err);
      return err
    });
  }

  // 搜索结果推荐lv3集合数据
  async goodsQryRecommend(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/goods/qryRecommend',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/goods/qryRecommend`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/goods/qryRecommend`, err);
      return err
    });
  }

  // 查询集合页信息 - 首页
  async pageQryHome(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticMall/business/api.v1.mall/pageconfig/qry',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/pageconfig/qry`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticMall/business/api.v1.mall/pageconfig/qry`, err);
      return err
    });
  }

  /* 
    https://yapi.sgsonline.com.cn/project/136/interface/api/4930
    查询首页在线下单滚动消息
  */
  async homeOnlineOrder(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticBigdata/business/api.v3.bigdata/order/newOrder',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticBigdata/business/api.v3.bigdata/order/newOrder`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticBigdata/business/api.v3.bigdata/order/newOrder`, err);
      return err
    });
  }

  /* 注册登录 */
  async loginReg(params, headers) {
    const {
      app,
      ctx
    } = this;
    const host = await util.getHost(app.locals.env, ctx);

    return await axios({
      url: host + '/ticSso/business/api.v1.sso/account/loginReg',
      method: 'post',
      data: JSON.stringify(params),
      headers: createHeader(app.locals.env, ticMall, params, headers, this),
    }).then(res => {
      ctx.logger.error(`${host}/ticSso/business/api.v1.sso/account/loginReg`, res);
      return res.data
    }).catch(err => {
      ctx.logger.error(`${host}/ticSso/business/api.v1.sso/account/loginReg`, err);
      return err
    });
  }
}

module.exports = externalService;
