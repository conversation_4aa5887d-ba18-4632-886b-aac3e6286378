html,
body,
.oiq,
.start {
    height: 100%;
    background: #d9d9d9;
}

.oiq {
    background: #d9d9d9;

    .start {
        min-height: 640px;
        background: url('./../images/oiq/start/bg.jpg') center 0 no-repeat;

        .wrap {
            width: 1240px;
            margin: 0 auto;
        }

        header {
            padding-top: 45px;
            line-height: 57px;
            height: 57px;

            .logo {
                float: left;
                display: flex;
                align-items: center;
                img {
                    float: left;
                }

                .text {
                    float: left;
                    line-height: 57px;

                    span {
                        display: block;
                        margin-left: 30px;

                        &:first-child {
                            width: 113px;
                            height: 27px;
                            font-size: 28px;
                            font-weight: 500;
                            color: #333333;
                            line-height: 26px;
                            padding-top: 7px;
                        }

                        &:last-child {
                            width: 112px;
                            height: 13px;
                            font-size: 14px;
                            font-weight: 400;
                            color: #878787;
                            line-height: 30px;
                        }
                    }
                }
            }

            .menu {
                float: right;
                height: 30px;
                line-height: 30px;

                a {
                    font-size: 14px;
                    color: #333333;
                    line-height: 30px;
                    height: 30px;

                    &:hover {
                        color: #ca4300;
                    }
                }

                em {
                    display: inline-block;
                    margin: 0 20px;
                    width: 1px;
                    height: 12px;
                    background: #000000;
                    opacity: 0.2;
                }
            }
        }

        .content {
            position: relative;

            .step {
                float: left;
                margin-top: 68px;

                h2 {
                    height: 45px;
                    font-size: 47px;
                    font-weight: 500;
                    color: #000000;
                    line-height: 30px;
                }

                ul {
                    margin: 40px 0;
                }

                li {
                    height: 30px;
                    line-height: 30px;
                    border-bottom: 1px dotted #A8A8A8;
                    width: 338px;
                    padding: 10px 0;
                    cursor: pointer;

                    i {
                        float: left;
                        display: block;
                        width: 30px;
                        height: 30px;
                        background-size: cover;

                        &.icon1 {
                            background-image: url('./../images/oiq/start/icon-1.png');
                        }

                        &.icon2 {
                            background-image: url('./../images/oiq/start/icon-2.png');
                        }

                        &.icon3 {
                            background-image: url('./../images/oiq/start/icon-3.png');
                        }
                    }

                    span {
                        float: left;
                        display: block;
                        width: 30px;
                        height: 30px;
                        width: 278px;
                        font-size: 19px;
                        color: #878787;
                        padding-left: 18px;
                    }

                    &:hover,
                    &.active {
                        span {
                            color: #000000;
                            font-weight: bold;
                        }

                        i {
                            &.icon1 {
                                background-image: url('./../images/oiq/start/icon-1-active.png');
                            }

                            &.icon2 {
                                background-image: url('./../images/oiq/start/icon-2-active.png');
                            }

                            &.icon3 {
                                background-image: url('./../images/oiq/start/icon-3-active.png');
                            }
                        }
                    }
                }

                a {
                    display: inline-block;
                    width: 246px;
                    text-align: center;
                    height: 67px;
                    background: linear-gradient(180deg, #FFB71D, #ca4300);
                    box-shadow: 3px 5px 8px 1px rgba(0, 0, 0, 0.14);
                    border-radius: 34px;
                    font-size: 24px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #FFFFFF;
                    line-height: 67px;

                    &:hover {
                        background: linear-gradient(180deg, #ca4300, #FFB71D);
                    }

                    &:active {
                        background: #ca4300;
                    }
                }
            }

            .img {
                width: 720px;
                height: 658px;
                top: -20px;
                right: 0;
                position: absolute;

                .img_box {
                    img {
                        width: 720px;
                        height: 658px;
                        top: 0;
                        right: 0;
                        position: absolute;
                        opacity: 0;

                        &:first-child {
                            right: -45px;
                        }

                        &:nth-child(2) {
                            right: -20px;
                        }

                        &:last-child {
                            right: -50px;
                        }
                    }
                }
            }
        }
    }
}
