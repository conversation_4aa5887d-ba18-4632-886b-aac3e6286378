html,
body,
.oiq,
.start {
  height: 100%;
  background: #d9d9d9;
}
.oiq {
  background: #d9d9d9;
}
.oiq .start {
  min-height: 640px;
  background: url('../images/oiq/start/bg.jpg') center 0 no-repeat;
}
.oiq .start .wrap {
  width: 1240px;
  margin: 0 auto;
}
.oiq .start header {
  padding-top: 45px;
  line-height: 57px;
  height: 57px;
}
.oiq .start header .logo {
  float: left;
  display: flex;
  align-items: center;
}
.oiq .start header .logo img {
  float: left;
}
.oiq .start header .logo .text {
  float: left;
  line-height: 57px;
}
.oiq .start header .logo .text span {
  display: block;
  margin-left: 30px;
}
.oiq .start header .logo .text span:first-child {
  width: 113px;
  height: 27px;
  font-size: 28px;
  font-weight: 500;
  color: #333333;
  line-height: 26px;
  padding-top: 7px;
}
.oiq .start header .logo .text span:last-child {
  width: 112px;
  height: 13px;
  font-size: 14px;
  font-weight: 400;
  color: #878787;
  line-height: 30px;
}
.oiq .start header .menu {
  float: right;
  height: 30px;
  line-height: 30px;
}
.oiq .start header .menu a {
  font-size: 14px;
  color: #333333;
  line-height: 30px;
  height: 30px;
}
.oiq .start header .menu a:hover {
  color: #ca4300;
}
.oiq .start header .menu em {
  display: inline-block;
  margin: 0 20px;
  width: 1px;
  height: 12px;
  background: #000000;
  opacity: 0.2;
}
.oiq .start .content {
  position: relative;
}
.oiq .start .content .step {
  float: left;
  margin-top: 68px;
}
.oiq .start .content .step h2 {
  height: 45px;
  font-size: 47px;
  font-weight: 500;
  color: #000000;
  line-height: 30px;
}
.oiq .start .content .step ul {
  margin: 40px 0;
}
.oiq .start .content .step li {
  height: 30px;
  line-height: 30px;
  border-bottom: 1px dotted #A8A8A8;
  width: 338px;
  padding: 10px 0;
  cursor: pointer;
}
.oiq .start .content .step li i {
  float: left;
  display: block;
  width: 30px;
  height: 30px;
  background-size: cover;
}
.oiq .start .content .step li i.icon1 {
  background-image: url('../images/oiq/start/icon-1.png');
}
.oiq .start .content .step li i.icon2 {
  background-image: url('../images/oiq/start/icon-2.png');
}
.oiq .start .content .step li i.icon3 {
  background-image: url('../images/oiq/start/icon-3.png');
}
.oiq .start .content .step li span {
  float: left;
  display: block;
  width: 30px;
  height: 30px;
  width: 278px;
  font-size: 19px;
  color: #878787;
  padding-left: 18px;
}
.oiq .start .content .step li:hover span,
.oiq .start .content .step li.active span {
  color: #000000;
  font-weight: bold;
}
.oiq .start .content .step li:hover i.icon1,
.oiq .start .content .step li.active i.icon1 {
  background-image: url('../images/oiq/start/icon-1-active.png');
}
.oiq .start .content .step li:hover i.icon2,
.oiq .start .content .step li.active i.icon2 {
  background-image: url('../images/oiq/start/icon-2-active.png');
}
.oiq .start .content .step li:hover i.icon3,
.oiq .start .content .step li.active i.icon3 {
  background-image: url('../images/oiq/start/icon-3-active.png');
}
.oiq .start .content .step a {
  display: inline-block;
  width: 246px;
  text-align: center;
  height: 67px;
  background: linear-gradient(180deg, #FFB71D, #ca4300);
  box-shadow: 3px 5px 8px 1px rgba(0, 0, 0, 0.14);
  border-radius: 34px;
  font-size: 24px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 67px;
}
.oiq .start .content .step a:hover {
  background: linear-gradient(180deg, #ca4300, #FFB71D);
}
.oiq .start .content .step a:active {
  background: #ca4300;
}
.oiq .start .content .img {
  width: 720px;
  height: 658px;
  top: -20px;
  right: 0;
  position: absolute;
}
.oiq .start .content .img .img_box img {
  width: 720px;
  height: 658px;
  top: 0;
  right: 0;
  position: absolute;
  opacity: 0;
}
.oiq .start .content .img .img_box img:first-child {
  right: -45px;
}
.oiq .start .content .img .img_box img:nth-child(2) {
  right: -20px;
}
.oiq .start .content .img .img_box img:last-child {
  right: -50px;
}
/*# sourceMappingURL=oiq.css.map */