'use strict';

const Service = require('egg').Service;
const moment = require('moment');

class SkuService extends Service {
  async getInfo(id) {
    const detail = await this.app.mysql.get('sku', {
      id: id
    });
    if (detail) {
      const content = await this.app.mysql.select('sku_content', {
        columns: ['id', 'title', 'content', 'hide_title', 'template_id', 'anchor_title', 'anchor_show'],
        where: {
          'sku_id': detail.id
        },
        orders: [
          ['id', '']
        ]
      });
      const qa = await this.app.mysql.select('sku_qa', {
        columns: ['question', 'answer', 'title', 'anchor_title', 'anchor_show'],
        where: {
          'sku_id': detail.id
        },
        orders: [
          ['id', '']
        ]
      });
      for (let item of content) {
        item.hide_title == 0 ? item.hide_title = false : item.hide_title = true;
        if (item.content) {
          item.content = item.content.replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
        }
      }
      for (let item of qa) {
        item.hide_title == 0 ? item.hide_title = false : item.hide_title = true;
        if (item.content) {
          item.content = item.content.replace(/\n/g, '').replace(/\.\.\/\.\.\/static/g, '/static').replace(/\.\.\/static/g, '/static');
        }
      }
      content.forEach(item => {
        if (item.template_id) {
          this.app.mysql.select('templates', {
            columns: ['id', 'name', 'title', 'content'],
            where: {
              'id': item.template_id
            }
          }).then(res => {
            if (res.length) {
              item.title = res[0].title
              item.content = res[0].content
            }
          })
        }
      })

      detail.content = content;
      detail.qa = qa;
      return {
        detail
      }
    } else {
      return {
        detail: ''
      };
    }
  }

  async tagAdpt(id, isRandom) {
    const tag_name = await this.app.mysql.select('tag_relation', {
      where: {
        sku_id: id
      }
    });
    if (tag_name.length == 0) {
      let list = [];
      return {
        list
      }
    } else {
      let arr = [];
      for (let item of tag_name) {
        arr.push(item.tag_name);
      }

      let list = await this.app.mysql.query('SELECT s.id,s.name,s.thumb_img,s.is_buy, s.sub_title, s.alias FROM tag_relation t LEFT JOIN sku s ON t.sku_id=s.id WHERE t.tag_name IN (' + this.app.mysql.escape(arr) + ') AND t.sku_id IS NOT NULL AND t.sku_id!=? AND s.is_use=1 AND s.is_delete=0 GROUP BY s.id', id);

      // if (list.length > 3) {
      let outList = [];

      for (var i = 0; i < list.length; i++) {
        outList[i] = i;
      }

      if (isRandom) {
        outList.sort(function () {
          return 0.5 - Math.random();
        });
      }

      let newList = [];
      for (var i = 0; i < list.length; i++) {
        newList.push(list[outList[i]]);
      }

      list = newList;
      return {
        list
      };

    }
  }

  async getSkuByCata(data) {
    const {
      app
    } = this;
    const catalog_id = data.id,
      name = data.name.trim() || '',
      page = data.page || 1,
      limit = data.limit || 10;

    let query = [];
    query.push('cr.catalog_id=' + catalog_id);
    query.push('s.is_delete=0');
    query.push('s.is_use=1');
    query.push('s.type IN (1,2)');
    if (name && name != '') {
      query.push('s.name LIKE "%' + name + '%"');
    }

    query = query.join(' AND ');
    console.log('SELECT s.id,s.name,s.sub_title,s.description,s.thumb_img,s.is_buy,s.alias FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id WHERE ' + query + ' ORDER BY cr.sort_num,s.is_use DESC,s.gmt_modify DESC LIMIT ' + (page - 1) * limit + ',' + limit);
    const list = await app.mysql.query('SELECT s.id,s.name,s.sub_title,s.description,s.thumb_img,s.is_buy,s.alias FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id WHERE ' + query + ' ORDER BY cr.sort_num,s.is_use DESC,s.gmt_modify DESC LIMIT ' + (page - 1) * limit + ',' + limit);
    let total = await app.mysql.query('SELECT s.id FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id WHERE ' + query);
    total = total.length;

    if (list.length > 0) {
      for (let item of list) {
        item.tags = await app.mysql.select('tag_relation', {
          columns: ['id', 'tag_name'],
          where: {
            sku_id: item.id
          },
        })
      }
    }

    return {
      list,
      total,
      page
    };
  }

  async getSkuByTrainCata(data) {
    const {
      app
    } = this;
    const catalog_id = data.id,
      name = data.name.trim() || '',
      page = data.page || 1,
      limit = data.limit || 10,
      area = data.area,
      time = data.time,
      days = data.days,
      is_buy = data.is_buy;

    let query = [];
    query.push('cr.catalog_id=' + catalog_id);
    query.push('s.is_delete=0');
    query.push('s.is_use=1');
    if (is_buy == 1) {
      query.push('s.is_buy=1');
    }
    query.push('s.type IN (1,2)');
    if (name && name != '') {
      query.push('s.name LIKE "%' + name + '%"');
    }

    query = query.join(' AND ');

    let list = await app.mysql.query('SELECT s.id,s.name,s.sub_title,s.description,s.thumb_img,s.class_info,s.is_buy FROM sku s LEFT JOIN catalog_relation cr ON cr.sku_id=s.id WHERE ' + query + ' ORDER BY cr.sort_num,s.is_use DESC,s.gmt_modify DESC');

    let newList = [],
      newList2 = [],
      newList3 = [],
      newList4 = [];
    for (let item of list) {
      let class_info = item.class_info;
      if (class_info) {
        item.class_info = JSON.parse(class_info);
        newList.push(item);
      }
    }

    if (time) {
      for (let item of newList) {
        let class_info = item.class_info;
        if (time && class_info.time.indexOf(time) != -1) {
          newList2.push(item);
        }
      }
    } else {
      newList2 = newList;
    }


    if (area) {
      for (let item of newList2) {
        let class_info = item.class_info;
        if (area && class_info.area.indexOf(area) != -1) {
          newList3.push(item);
        }
      }
    } else {
      newList3 = newList2;
    }

    if (days) {
      for (let item of newList3) {
        let class_info = item.class_info;
        if (days && class_info.days.replace(/\s/g, '') == days) {
          newList4.push(item);
        }
      }
    } else {
      newList4 = newList3;
    }


    if (!time && !area && !days) {
      newList4 = list;
    }

    let total = newList4.length;

    let outList = [];
    for (let [i, item] of newList4.entries()) {
      if (i >= (page - 1) * limit && i < page * limit) {
        outList.push(item);
      }
    }

    // if (outList.length > 0) {
    //     for (let item of outList) {
    //         item.tags = await app.mysql.select('tag_relation', {
    //             columns: ['id', 'tag_name'],
    //             where: { sku_id: item.id },
    //         })
    //     }
    // }

    list = outList;
    return {
      list,
      total,
      page
    };
  }

  async getRecommend(id) {
    // const list = await this.app.mysql.select('sku_recommend', {
    //     where: {
    //         source_id: id
    //     }
    // });
    // let list = await this.app.mysql.query(`SELECT * FROM sku_recommend WHERE source_id=${id} AND sku_id!= ${id}`)
    let list = await this.app.mysql.query(`select 
            a.*, b.sub_title, b.alias
            from sku_recommend a
            JOIN sku b
            ON a.sku_id = b.id
            WHERE a.source_id = ${id} AND a.sku_id != ${id} `)
    if (list.length) {
      list.forEach(item => {
        item.thumb_img = item.sku_image
        item.id = item.sku_id
        item.name = item.sku_name
        item.is_buy = 0
      })
    } else {
      list = []
    }
    return {
      list
    }
  }

  async skuCases(id) {
    let list = await this.app.mysql.query(`SELECT b.id, b.title, a.anchor_title, a.anchor_show, a.title as floor_title FROM sku_case a INNER JOIN cases b ON a.case_id = b.id WHERE sku_id = ${id}`)
    if (!list.length) {
      list = []
    }
    return {
      list
    }
  }

  async skuResource(id) {
    // let list = await this.app.mysql.select('sku_resource', {
    //     where: {
    //         sku_id: id
    //     }
    // });
    let list = await this.app.mysql.query(`SELECT a.*, b.is_public FROM sku_resource a left JOIN resource b ON a.resource_id=b.id WHERE sku_id=${id}`)
    if (!list.length) {
      list = []
    }
    return {
      list
    }
  }

  async skuFloorSort(id) {
    let list = await this.app.mysql.select('sku_floor_sort', {
      where: {
        sku_id: id
      }
    });
    if (!list.length) {
      list = []
    }
    return {
      list
    }
  }

  // 检测中心页面，查询特有sku数据
  async querySkuById({ ids }) {
    let list = []
    if (ids) {
      const query = ids.split(',').map(v => { return `id = ${v}` }).join(' or ')
      list = await this.app.mysql.query(`SELECT * from sku WHERE ${query}`)
    }

    return {
      list
    }
  }
}

module.exports = SkuService;