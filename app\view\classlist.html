<% include header.html %>
<% include ./components/header.html %>
<div class="banner">
    <div class="swiper-container">
        <div class="swiper-wrapper">

            <% for(item of banner){ %>
            <div class="swiper-slide bannerLi1"
                style="background-image:url(<%- item.img_path.replace(/\/static/g,locals.static) %>)">
                <% if(item.btn_url && !item.btn_value){ %><a href="<%- item.btn_url %>" target="blank"
                    style="display:block;width:100%;height:100%;"><% } %>
                    <div class="baa">
                        <div class="bannerTitle"><%- item.btn_text %></div>
                        <div class="bannerDetail"><%- item.btn_description %></div>
                        <% if(item.btn_value){ %>
                        <a class="bannerMore" href="<%- item.btn_url %>" target="_blank">
                            <%- item.btn_value %>
                            <div class="bannermoreBox">

                            </div>
                            <div class="bannermoreword"><%- item.btn_value %></div>
                        </a>
                        <% } %>
                    </div>
                    <% if(item.btn_url && !item.btn_value){ %></a><% } %>
            </div>
            <% } %>
        </div>
        <!-- Add Arrows -->
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
    </div>
</div>
<div class="location">
    <ul>
        <li class="locationLi">
            <a href="/">第三方检测机构</a></li>
        <li class="locationLi icon"></li>
        <li class="locationLi"><a href="./"><%- detail.name %></a></li>
        <li class="locationLi icon"></li>
        <li class="locationLi"><a href="javascrpit:void(0);">全部课程</a></li>
    </ul>
</div>
<div class="listBigBox1" id='classList' v-cloak>
    <div class="categorybox">
        <div class="categorybox-wrap">
            <ul class="categoryUl" id="CataUl">
                <li class="categoryLi">课程类别：</li>
                <li class="categoryLi" style="margin: 0;cursor: default;">
                    <ul style="padding-left: 0;width:1120px;">
                        <li
                            :class="{categoryLi: true, categoryLi1: true, clickActive: index === classCategoryIndex}"
                            v-for='(item, index) of classCategoryList'
                            :key='index'
                            @click='handleGetList(index, item.id, "catalogId")'
                        >
                            {{ item.name }}
                        </li>
                    </ul>
                </li>
            </ul>

            <ul class="categoryUl" id="CataUl1">
                <li class="categoryLi">开课地点：</li>
                <li class="categoryLi" style="margin: 0;cursor: default;">
                    <ul style="padding-left: 0;width:1120px;">
                        <li
                            :class="{categoryLi: true, categoryLi1: true, clickActive: index === classSiteIndex}"
                            v-for='(item, index) of class_info.classArea'
                            :key='index'
                            @click='handleGetList(index, item, "classArea")'
                        >
                            {{ item }}
                        </li>
                    </ul>
                </li>

            </ul>

            <ul class="categoryUl" id="CataUl2">
                <li class="categoryLi">开课时间：</li>
                <li
                    :class="{categoryLi: true, categoryLi1: true, clickActive: index === classTimeIndex}"
                    v-for='(item, index) of class_info.classTime'
                    :key='index'
                    @click='handleGetList(index, item, "classTime")'
                >
                    {{ item }}
                </li>
            </ul>

            <ul class="categoryUl" id="CataUl3">
                <li class="categoryLi">课程天数：</li>
                <li
                    :class="{categoryLi: true, categoryLi1: true, clickActive: index === classDaysIndex}"
                    v-for='(item, index) of class_info.classDays'
                    :key='index'
                    @click='handleGetList(index, item, "classDays")'
                >
                    {{ item }}
                </li>
            </ul>
        </div>
    </div>
    <div class="trwrap">
        <el-row>
            <el-col :span='6'>
                <el-checkbox true-label=1 false-label='' v-model="form.isBuy" @change='handleChangeIsBuy'>可在线下单</el-checkbox>
            </el-col>
            <el-col :span='18' style="text-align: right;">
                <el-form :inline="true" :model="form" class="demo-form-inline">
                    <el-form-item>
                        <el-input size="mini" v-model="form.name" placeholder="请输入课程标题..."></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="mini" type="primary" @click="handlerSearch" class='el-icon-search'></el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
    </div>
    <div class="listBigBox">
        <div class="listBox">
            <ul class="listUl" id="List2"  v-if='classList.length'>
                <li class="listLi" v-for='(item, index) of classList' :key="index">
                    <div class="list-img">
                        <img :src="item.thumbImg" :alt="item.name">
                        <span v-if='item.isBuy' class="buyicon"></span>
                    </div>
                    <div class="listwordbox">
                        <div class="listwordTS">
                            <a class="listwordT" :href="item | filterHref">{{ item.name }}</a>
                            <span v-if='item.isBuy' class="buytag">可在线下单</span>
                            <div class="listwordS">{{ item.subTitle }}</div>
                        </div>
                        <div class="listwordI">
                           {{ item.description }}
                        </div>
                    </div>
                </li>
            </ul>
            <!-- <p v-else>很抱歉，没有为您找到对应的结果，请扩大筛选范围</p> -->
            <div v-else>
                <p v-if="searchhint==1">加载中......</p>
                <p v-else>很抱歉，没有为您找到对应的结果，请扩大筛选范围</p>
            </div>
        </div>
        <div class="pagination" style="text-align: right; width: 1226px;">
            <el-pagination
                layout="prev, pager, next, jumper"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                :current-page.sync="form.pageNum"
                :page-size="form.pageRow"
                :total="totalNum">
            </el-pagination>
        </div>
    </div>
</div>
<style>
    .categoryUl {
        padding: 0;
        width: 100%;
    }

    .categorybox-wrap {
        padding: 20px 0;
        overflow: hidden;
        min-width: 1226px;
    }
</style>
<script src="<%- locals.static %>/js/swiper.min.js"></script>
<script>
    var swiper = new Swiper('.swiper-container', {
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        speed: 1000,
        loop: true,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        effect: 'fade',
        fadeEffect: {
            crossFade: true,
        }
    });

    var scontainer = $('.swiper-container')[0];
    scontainer.onmouseenter = function () {
        swiper.autoplay.stop();
    };
    scontainer.onmouseleave = function () {
        swiper.autoplay.start();
    };

    var newVue = new Vue({
        name: 'classList',
        el: '#classList',
        data: {
            classCategoryList: <%- JSON.stringify(children) %>,
            class_info: <%- JSON.stringify(class_info) %>,
            totalNum: 0,
            form: {
                pageRow: 10,
                pageNum: 1,
                type: 2,
                catalogId: '',
                name: '',
                classTime: '',
                classArea: '',
                classDays: '',
                isBuy: ''
            },
            classCategoryIndex: 0,
            classSiteIndex: 0,
            classTimeIndex: 0,
            classDaysIndex: 0,
            classList: [],
            searchhint:1
        },
        methods: {
            handleChangeIsBuy(val) {
                this.form.isBuy = val;
                this.getClassList();
            },
            handlerSearch() {
                this.form.pageNum = 1;
                this.getClassList();
            },
            handleCurrentChange: function (val) {
                this.form.pageNum = val;
                this.getClassList();
            },
            handleSizeChange: function (val) {
              this.form.pageNum = val;
                this.getClassList();
            },
            getClassList() {
                var that = this
                var loading = this.$loading({
                    lock: true,
                    text: '加载中...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                    fullscreen: false
                });
                that.searchhint=1
                axios.post('/class/list', this.form,{timeout:15000})
                    .then(function (res) {
                        if (res.status === 200 && res.data.resultCode === '0') {
                            var datas = res.data.data;
                            that.totalNum = datas.totalNum;
                            that.classList = datas.items;
                            if(datas.length==0){
                                that.searchhint=2
                            }
                        } else {
                            that.$message({
                                message: '获取数据异常，请稍后重试。',
                                type: 'warning'
                            });
                            that.searchhint=2
                        }
                        loading.close();
                    })
                    .catch(function (error) {
                        that.$message({
                            message: error,
                            type: 'warning'
                        });
                        loading.close();
                        that.searchhint=2
                    });
            },
            handleGetList: function (index, params, type) {
                if (type === 'catalogId') {
                    this.classCategoryIndex = index
                } else if (type === 'classArea') {
                    this.classSiteIndex = index
                } else if (type === 'classTime') {
                    this.classTimeIndex = index
                } else {
                    this.classDaysIndex = index
                }
                this.form.pageNum = 1
                this.form[type] = params

                this.getClassList()
            }
        },
        filters: {
            filterHref: function (val) {
                var href = '';
                if (<%- detail.parent_id %> === 1) {
                    href += '/sku/'
                } else {
                    href += '/sku/'
                }
                href += val.alias + '/' + val.id;
                return href;
            },
            thumbImg: function (val) {
                return val.replace(/\/static/g, '<%- locals.static %>');
            }
        },
        created: function () {
            this.classCategoryList.unshift({
                name: '全部课程',
                id: ''
            });
            this.class_info.classArea.unshift('全国')
            this.class_info.classTime.unshift('全部')
            this.class_info.classDays.unshift('全部')
        },
        mounted: function () {
            this.getClassList()
        }
    });
</script>
<% include footer.html %>
