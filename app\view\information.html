<% include header.html %>
<% include ./components/header.html %>
<div class="information" id='information'>
  <div class="SearchBox" style="padding-bottom: 55px;">
    <div class="searchleftBox">
      <div class="download_tab" id='download_tab'>
        <ul>
          <li class="<% if(!type){ %>active <% } %>">
            全部
            <i></i>
          </li>
          <% if(informationTypes && informationTypes.length> 0){ %>
            <% for (var item of informationTypes){ %>
              <li data-type='<%- item.id %>' class="<% if(item.id==type){ %>active <% } %>">
                <%- item.name %>
                  <i></i>
              </li>
            <% } %>
          <% } %>
        </ul>
      </div>
      <div class="download_filter">
        <div class="download_filter_action">
          <span>所有分类</span>
          <em>展开筛选</em>
        </div>
        <div class="download_filster_industry" style="display: none;">
          <span class="download_filter_tit">行业分类：</span>
          <div class="download_filster_checkboxGroup" id='download_industry'>
            <% if(tradeNavi && tradeNavi.length> 0){ %>
              <% for (var item of tradeNavi){ %>
                <label data-trade='<%- item.id %>'>
                  <i></i>
                  <span><%- item.name %></span>
                </label>
              <% } %>
            <% } %>
          </div>
        </div>
        <div class='download_filster_service' style="display: none;">
          <span class="download_filter_tit">服务类型：</span>
          <div class="download_filster_checkboxGroup" id='download_service'>
            <% if(serviceNavi && serviceNavi.length> 0){ %>
              <% for (var item of serviceNavi){ %>
                <label data-service='<%- item.id %>'>
                  <i></i>
                  <span><%- item.name %></span>
                </label>
                <% } %>
                  <% } %>
          </div>
        </div>
        <div class="download_filster_keyword">
          <span class="download_filter_tit">关键词：</span>
          <div>
            <input type="text" placeholder="请输入关键词" id='title' />
            <button id='download_filster_keyword-btn'>搜索</button>
          </div>
        </div>
        <!--                <div class="download_filster_date">-->
        <!--                    <span class="download_filter_tit">发布日期：</span>-->
        <!--                    <div>-->
        <!--                        <input type="text" class="dSearch-input" placeholder="请选择发布日期" name="time" id="time" readonly=""-->
        <!--                            value="<%- time %>" />-->
        <!--                    </div>-->
        <!--                </div>-->
        <div class="download_filter_result">
          <span class="download_filter_tit">您已选择：</span>
          <div class="download_filter_result--items">
            <div id='download_filter_result'>
              <em id='reset'>清除</em>
            </div>
          </div>
        </div>
      </div>
      <div class="download_list" id='download_list'>
        <% if(informationList && informationList.length> 0){ %>
          <ul>
            <% for (var item of informationList){ %>
              <li>
                <div class="download_list_content <% if(item.imgUrl){ %>hasImg<% } %>">
                  <h2>
                    <a target="_blank" href='/case/<%- item.alias %>/detail-<%- item.id %>.html'><%- item.title
                        %></a>
                    <% if(item.original){ %>
                      <span class="original">原创</span>
                      <% } %>
                  </h2>
                  <p>
                    <%- item.content %>
                  </p>
                  <label>
                    <span><%- item.publishTime %></span>
                    <em>
                      <% if(item.catalogName.split(',').length> 0){ %>
                        <% for (var service of item.catalogName.split(',')){ %>
                          <span><%- service %></span>
                          <% } %>
                            <% } %>
                    </em>
                    <span>阅读：<%- item.hits || 0 %></span>
                    <% if(item.catalogName.split(',').length> 5){ %>
                      <i>...</i>
                      <% } %>
                  </label>
                </div>
                <% if(item.imgUrl){ %>
                  <div class="img" style="background-image: url('<%- item.imgUrl %>');"></div>
                  <% } %>
              </li>
              <% } %>
          </ul>
          <% }else{ %>
            <h3>【很抱歉，没有为您找到对应的结果，请扩大筛选范围】</h3>
            <% } %>
      </div>
      <!-- <div class="pagination">
            <ul class="serverpages" id='pagination'>
                <% for(var i=1; i <= total; i++){ %>
                <li class="serverpage<% if(i==page){ %> pageActive<% } %>"><%- i %></li>
                <% } %>
            </ul>
        </div> -->
      <div class="pagination" v-cloak>
        <el-pagination @current-change="handleCurrentChange" :current-page.sync="currPage" :page-size="10"
          @size-change="handleSizeChange" layout="prev, pager, next, jumper" :total="totalNum">
        </el-pagination>
      </div>
    </div>
    <div class="searchrightBox bannerInfo" v-cloak>
      <% include ./components/sku_side_form.html %>
      <% include ./pages/quote/modal.html %>
    </div>
  </div>
</div>
<script src="../../public/js/information.js" type="text/javascript"></script>
<script src="<%- locals.static %>/js/laydate.js"></script>
<script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
<% if(locals.env != 'prod'){ %>
<script src="<%- locals.static %>/plugin/v2.test.js"></script>
<% }else{ %>
<script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js" type="text/javascript"></script>
<% } %>
<script>
  $(function () {
    var params = {
      cataId: '',
      cid: '',
      title: '',
      gmtPublishTime: '',
      pageRow: 10,
      pageNum: 1
    },
      param = {
        csrf: '<%- csrf %>',
        id: '<%- type %>',
        service: "",
        trade: "",
        title: "",
        page: 1,
        time: '',
        is_publish: 1
      },
      total = <%- total %> || 0,
      industryActives = [],
      serviceActives = [],
      searchVal = [];

    $('#title').focus(function () {
      $(this).addClass('active')
    });
    $('#title').blur(function () {
      if ($(this).val()) {
        $(this).addClass("active")
      } else {
        $(this).removeClass("active")
      }
    })
    // calendar controls
    laydate.render({
      elem: '#time',
      range: true,
      theme: '#ca4300',
      done: function (value, date, endDate) {
        console.log(value, date, endDate)
        var timeArr = value.split(' - ');
        console.log(timeArr)
        if (value) {
          $('#time').addClass("active")
        } else {
          $('#time').removeClass("active")
        }
        if (timeArr.length > 1) {
          params.gmtPublishTime = timeArr[0] + ' 00:00:00/' + timeArr[1] + ' 23:59:59';
        } else {
          params.gmtPublishTime = ''
        }
        params.pageNum = 1;
        getInformationList()
      }
    });
    // side right
    var $_yours = $(".yours");
    $_yours.click(function () {
      var $_con = $(this).siblings(".your-cons");
      if ($_con.css("height") == "0px") {
        $_con.animate({ "height": 486 }).end().parents(".your").siblings(".your").children(".your-cons").animate({ "height": 0 });
        $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jianhao.png');
        $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(0).attr("src", "<%- locals.static %>/images/jiahao.png");
        $(this).parents(".your").siblings('.your').find(".dragbut-icon").eq(1).attr("src", "<%- locals.static %>/images/jiahao.png")
      } else {
        $(this).find(".dragbut-icon").attr("src", '<%- locals.static %>/images/jiahao.png');
        $_con.animate({ "height": 0 });
      }
    })


    setTimeout(function () {
    // handler tab
    $("#download_tab").on('click', 'li', function () {
      if ($(this).data('type')) {
        window.location.href = '/information?type=' + $(this).data('type')
      } else {
        window.location.href = '/information'
      }
    })
    // handler more
    $('.download_filter_action em').on('click', function () {
      if ($(this).hasClass('active')) {
        $('.download_filster_industry').hide();
        $('.download_filster_service').hide();
      } else {
        $('.download_filster_industry').css({
          display: 'flex'
        });
        $('.download_filster_service').css({
          display: 'flex'
        });
      }
      $(this).toggleClass('active');
    });
    // handler industry
    $("#download_industry").on('click', 'label', function () {
      if ($(this).hasClass('active')) {
        $(this).removeClass('active')
      } else {
        $(this).addClass('active')
      }
      createSelectList()
      params.pageNum = 1;
    })
    // hanlabeler service
    $("#download_service").on('click', 'label', function () {
      if ($(this).hasClass('active')) {
        $(this).removeClass('active')
      } else {
        $(this).addClass('active')
      }
      createSelectList()
      params.pageNum = 1;
    })
    // handler button
    $("#download_filster_keyword-btn").on('click', function () {
      var val = $('#title').val();
      params.title = val;
      params.pageNum = 1;
      createSelectList()
    });
    // handler reset
    $("#reset").on("click", function () {
      $("#download_industry label").removeClass();
      $("#download_service label").removeClass();
      $("#title").val('');
      $('#title').removeClass('active');
      params.cataId = '';
      params.title = '';
      params.pageNum = 1;
      createSelectList()
    });
  } , 1000)
    // create select list
    function createSelectList() {
      industryActives = [];
      serviceActives = [];
      searchVal = [];
      var industryIds = [],
        serviceIds = [];
      $('#download_industry label').each(function () {
        if ($(this).hasClass('active')) {
          var tag = {
            name: $(this).find('span').text(),
            category: 'industry'
          }
          industryActives.push(tag)
          industryIds.push($(this).data('trade'));
        }
      })
      $('#download_service label').each(function () {
        if ($(this).hasClass('active')) {
          var tag = {
            name: $(this).find('span').text(),
            category: 'service'
          }
          serviceActives.push(tag)
          serviceIds.push($(this).data('service'));
        }
      })
      if ($("#title").val()) {
        searchVal.push({
          name: $("#title").val(),
          category: 'search'
        });
      }
      var actives = industryActives.concat(serviceActives, searchVal)
      if (actives.length) {
        console.log(actives)
        $(".download_filter_result").css({
          display: 'flex'
        })
        var dom = '',
          box = $("#download_filter_result");
        for (var i = 0; i < actives.length; i++) {
          dom += ' <label>' +
            '<span>' + htmlSpecialChars(actives[i].name) + '</span>' +
            '<i data-category="' + actives[i].category + '">x</i>' +
            '</label>';
        }
        box.find('label').each(function () {
          $(this).remove();
        })
        box.prepend(dom);
      } else {
        $(".download_filter_result").hide();
        $('#download_filter_result').find('label').each(function () {
          $(this).remove();
        })
      }
      // param.trade = industryIds.join(',')
      // param.service = serviceIds.join(',')
      // console.log(industryIds, serviceIds)
      params.cataId = industryIds.concat(serviceIds).join(',')
      getInformationList();
    }

    // close select list
    $('.download_filter_result').on('click', 'i', function () {
      var category = $(this).data('category');
      var text = $(this).closest('label').find('span').text();
      if (category === 'industry') {
        $("#download_industry label").each(function () {
          if ($(this).find('span').text() === text) {
            $(this).removeClass('active')
          }
        })
        createSelectList();
      } else if (category === 'service') {
        $("#download_service label").each(function () {
          if ($(this).find('span').text() === text) {
            $(this).removeClass('active')
          }
        })
        createSelectList();
      } else if (category === 'search') {
        $("#download_filter_result label").each(function (index, value) {
          if ($(this).find('span').text() === text) {
            $('#download_filter_result label').eq(index).remove()
            var len = $("#download_filter_result label").length
            $("#title").val("")
            if (len === 0) {
              $(".download_filter_result").css('display', 'none');
            }
          }
        })
      }
      // createSelectList();
    })

    // async get list
    function getInformationList(form) {
      var that = this
      params.cid = window.location.search.substr(window.location.search.indexOf('=') + 1, window.location.search.length)
      axios.post('/case/list', params)
        .then(function (res) {
          if (res.status === 200 && res.data.resultCode === '0') {
            createListDom(res.data.data.items)
            if (form !== 'pagination') {
              createPagination(Math.ceil(res.data.data.totalNum / params.pageRow))
              total = res.data.data.totalNum
              newVue.totalNum = res.data.data.totalNum
            } else {
              $('html,body').stop().animate({ scrollTop: 0 });
            }
          } else {
            alert('获取数据异常，请稍后重试。');
          }
        })
        .catch(function (error) {
          alert(error);
          console.log(error)
        });
    }

    function createListDom(datas) {
      var box = $('#download_list'),
        dom = '<ul>';
      if (datas.length) {
        for (var i = 0; i < datas.length; i++) {
          var hits = 0;
          if (datas[i].hits) {
            hits = datas[i].hits
          }
          dom +=
            '<li>';
          if (datas[i].imgUrl) {
            dom += '<div class="download_list_content hasImg"> ';
          } else {
            dom += '<div class="download_list_content"> ';
          }
          dom += '<h2> ' +
            '<a target="_blank" href="/case/' + datas[i].alias + '/detail-' + datas[i].id + '.html">' + datas[i].title + '</a> ';

          if (datas[i].original) {
            dom += '<span class="original">原创</span>'
          }
          ;
          // if (datas[i].catalogCode === 'jswz') {
          //   dom += '<span class="vip">VIP</span>'
          // };
          dom += '</h2> ' +
            '<p>' + datas[i].content + '</p> ' +
            '<label> ' +
            '<span>' + datas[i].publishTime + '</span> ';
          dom += '<em>';
          if (datas[i].catalogName) {
            for (var s = 0; s < datas[i].catalogName.split(',').length; s++) {
              dom += '<span>' + datas[i].catalogName.split(',')[s] + '</span> ';
            }
          }
          dom += '</em><span style="border: 0;">阅读：' + hits + '</span>';
          if (datas[i].catalogName.split(',').length > 5) {
            dom += '<i></i>';
          }
          dom += '</label></div>';
          if (datas[i].imgUrl) {
            dom += '<div class="img" style="background-image: url(' + datas[i].imgUrl + ');"></div>';
          }
          dom += '</li> ';
        }
        dom += '</ul>';
        box.html(dom)
      } else {
        box.html('<h3>【很抱歉，没有为您找到对应的结果，请扩大筛选范围】</h3>')
      }
    }

    function createPagination(num) {
      var box = $("#pagination"),
        dom = '';
      if (num) {
        for (var i = 0; i < num; i++) {
          if (i === 0) {
            dom += '<li class="serverpage pageActive">' + (Number(i) + 1) + '</li>';
          } else {
            dom += '<li class="serverpage">' + (Number(i) + 1) + '</li>';
          }
        }
        box.show();
        box.html(dom);
      } else {
        box.hide();
      }
    }

    var newVue = new Vue({
      name: 'information',
      el: '#information',
      mixins: [quoteMixins],
      data: {
        isLogin: <%- isLogin %>,
        totalNum: <%- total %>,
        currPage: 1,
        // 留言表单数据
        loading: false,
        form: {
          type: '业务咨询',
          trade: '',
          tradeName: '其他',
          tradeIndex: '',
          serviceIndex: '',
          service: '',
          serviceName: '其他',
          company: '',
          provice: '',
          content: '',
          frontTitle: '<%- detail.name %>',
          customer: '<%- userInfo.userName %>',
          email: '<%- userInfo.userEmail %>',
          phone: '<%- userInfo.userPhone %>',
          imageCode: '',
          frontUrl: window.location.href,
          destCountry: '',
          verifyCode: '',
          regMode: 'information',
          regSource: 'mall',
          regFrom: window.location.href,
          // busiCode: '<%- detail.id %>'
        },
        showModal: false,
        seconds: 59,
        timer: null,
        countdownTime: 5,
        countdownTimer: null,
        disablePhone: <%- disablePhone %>,
        disableMail: <%- disableMail %>,
        isSuccess: false,
        approve: false,
        tab: 'phone',
        provice: [],
        rules: {
          provice: [{ required: true, message: '*请选择所在城市' }],
          content: [{ required: true, message: '*请输入您的咨询内容' }],
          customer: [{ required: true, message: '*请输入您的称呼' }],
          // email: [{ validator: validatorEmail }],
          // phone: [{ validator: validatorUserPhone }],
        },
        dialogVisibleReg: false,
        contact_approve_show: true,
        loginSource: '',
        caseLoading: false,
        pageInfo: {
          title: '验证手机号',
          sendModel: '已发送到手机号：+86',
          prepend: '手机验证码'
        },
        captchaAppId: '<%- captchaAppId %>',
        pid: 'pid.mall',
        pcode: 'Z0zCnRE3IaY9Kzem',
        host: '<%- host %>',
      },
      mounted() {
        getInformationList()
      },
      methods: {
        handleCurrentChange: function (val) {
          params.pageNum = val
          getInformationList()
        },
        handleSizeChange: function (val) {
          params.pageNum = val
          getInformationList()
        },
          headerRegister: function () {
            this.showLoginModal = true
            this.closeBtn = true
            this.loginSource = 'header'
          },
          handleCloseLogin: function () {
            this.showLoginModal = false
          },
          successCallback: function (type) {
            var that = this
            that.showLoginModal = false
            that.caseLoading = true
            location.reload()
          },
          handleChange: function () { },
          handleJumpStep1: function () {
            window.open('<%- memberUrl %>/questionnaire/step')
          },
      }
    });
  })
</script>
<% include footer.html %>