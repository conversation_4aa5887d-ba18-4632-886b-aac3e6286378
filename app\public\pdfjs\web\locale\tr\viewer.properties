# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Önceki sayfa
previous_label=Önceki
next.title=Sonraki sayfa
next_label=Sonraki

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Sayfa
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=/ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} / {{pagesCount}})

zoom_out.title=Uzaklaştır
zoom_out_label=Uzaklaştır
zoom_in.title=Yaklaştır
zoom_in_label=Yaklaştır
zoom.title=Yakınlaştırma
presentation_mode.title=Sunum moduna geç
presentation_mode_label=Sunum modu
open_file.title=Dosya aç
open_file_label=Aç
print.title=Yazdır
print_label=Yazdır
download.title=İndir
download_label=İndir
bookmark.title=Geçerli görünüm (kopyala veya yeni pencerede aç)
bookmark_label=Geçerli görünüm

save.title=Kaydet
save_label=Kaydet
bookmark1.title=Geçerli sayfa (geçerli sayfanın adresini görüntüle)
bookmark1_label=Geçerli sayfa

open_in_app.title=Uygulamada aç
open_in_app_label=Uygulamada aç

# Secondary toolbar and context menu
tools.title=Araçlar
tools_label=Araçlar
first_page.title=İlk sayfaya git
first_page_label=İlk sayfaya git
last_page.title=Son sayfaya git
last_page_label=Son sayfaya git
page_rotate_cw.title=Saat yönünde döndür
page_rotate_cw_label=Saat yönünde döndür
page_rotate_ccw.title=Saat yönünün tersine döndür
page_rotate_ccw_label=Saat yönünün tersine döndür

cursor_text_select_tool.title=Metin seçme aracını etkinleştir
cursor_text_select_tool_label=Metin seçme aracı
cursor_hand_tool.title=El aracını etkinleştir
cursor_hand_tool_label=El aracı

scroll_page.title=Sayfa kaydırmayı kullan
scroll_page_label=Sayfa kaydırma
scroll_vertical.title=Dikey kaydırma kullan
scroll_vertical_label=Dikey kaydırma
scroll_horizontal.title=Yatay kaydırma kullan
scroll_horizontal_label=Yatay kaydırma
scroll_wrapped.title=Yan yana kaydırmayı kullan
scroll_wrapped_label=Yan yana kaydırma

spread_none.title=Yan yana sayfaları birleştirme
spread_none_label=Birleştirme
spread_odd.title=Yan yana sayfaları tek numaralı sayfalardan başlayarak birleştir
spread_odd_label=Tek numaralı
spread_even.title=Yan yana sayfaları çift numaralı sayfalardan başlayarak birleştir
spread_even_label=Çift numaralı

# Document properties dialog box
document_properties.title=Belge özellikleri…
document_properties_label=Belge özellikleri…
document_properties_file_name=Dosya adı:
document_properties_file_size=Dosya boyutu:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bayt)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bayt)
document_properties_title=Başlık:
document_properties_author=Yazar:
document_properties_subject=Konu:
document_properties_keywords=Anahtar kelimeler:
document_properties_creation_date=Oluturma tarihi:
document_properties_modification_date=Değiştirme tarihi:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}} {{time}}
document_properties_creator=Oluşturan:
document_properties_producer=PDF üreticisi:
document_properties_version=PDF sürümü:
document_properties_page_count=Sayfa sayısı:
document_properties_page_size=Sayfa boyutu:
document_properties_page_size_unit_inches=inç
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=dikey
document_properties_page_size_orientation_landscape=yatay
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Hızlı web görünümü:
document_properties_linearized_yes=Evet
document_properties_linearized_no=Hayır
document_properties_close=Kapat

print_progress_message=Belge yazdırılmaya hazırlanıyor…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent=%{{progress}}
print_progress_close=İptal

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Kenar çubuğunu aç/kapat
toggle_sidebar_notification2.title=Kenar çubuğunu aç/kapat (Belge ana hat/ekler/katmanlar içeriyor)
toggle_sidebar_label=Kenar çubuğunu aç/kapat
document_outline.title=Belge ana hatlarını göster (Tüm öğeleri genişletmek/daraltmak için çift tıklayın)
document_outline_label=Belge ana hatları
attachments.title=Ekleri göster
attachments_label=Ekler
layers.title=Katmanları göster (tüm katmanları varsayılan duruma sıfırlamak için çift tıklayın)
layers_label=Katmanlar
thumbs.title=Küçük resimleri göster
thumbs_label=Küçük resimler
current_outline_item.title=Mevcut ana hat öğesini bul
current_outline_item_label=Mevcut ana hat öğesi
findbar.title=Belgede bul
findbar_label=Bul

additional_layers=Ek katmanlar
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Sayfa {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Sayfa {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}}. sayfanın küçük hâli

# Find panel button title and messages
find_input.title=Bul
find_input.placeholder=Belgede bul…
find_previous.title=Önceki eşleşmeyi bul
find_previous_label=Önceki
find_next.title=Sonraki eşleşmeyi bul
find_next_label=Sonraki
find_highlight=Tümünü vurgula
find_match_case_label=Büyük-küçük harfe duyarlı
find_match_diacritics_label=Fonetik işaretleri bul
find_entire_word_label=Tam sözcükler
find_reached_top=Belgenin başına ulaşıldı, sonundan devam edildi
find_reached_bottom=Belgenin sonuna ulaşıldı, başından devam edildi
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{total}} eşleşmeden {{current}}. eşleşme
find_match_count[two]={{total}} eşleşmeden {{current}}. eşleşme
find_match_count[few]={{total}} eşleşmeden {{current}}. eşleşme
find_match_count[many]={{total}} eşleşmeden {{current}}. eşleşme
find_match_count[other]={{total}} eşleşmeden {{current}}. eşleşme
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]={{limit}} eşleşmeden fazla
find_match_count_limit[one]={{limit}} eşleşmeden fazla
find_match_count_limit[two]={{limit}} eşleşmeden fazla
find_match_count_limit[few]={{limit}} eşleşmeden fazla
find_match_count_limit[many]={{limit}} eşleşmeden fazla
find_match_count_limit[other]={{limit}} eşleşmeden fazla
find_not_found=Eşleşme bulunamadı

# Error panel labels
error_more_info=Daha fazla bilgi al
error_less_info=Daha az bilgi
error_close=Kapat
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js sürüm {{version}} (yapı: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=İleti: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Yığın: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Dosya: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Satır: {{line}}

# Predefined zoom values
page_scale_width=Sayfa genişliği
page_scale_fit=Sayfayı sığdır
page_scale_auto=Otomatik yakınlaştır
page_scale_actual=Gerçek boyut
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent=%{{scale}}

# Loading indicator messages
loading=Yükleniyor…

# Loading indicator messages
loading_error=PDF yüklenirken bir hata oluştu.
invalid_file_error=Geçersiz veya bozulmuş PDF dosyası.
missing_file_error=PDF dosyası eksik.
unexpected_response_error=Beklenmeyen sunucu yanıtı.

rendering_error=Sayfa yorumlanırken bir hata oluştu.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} işareti]
password_label=Bu PDF dosyasını açmak için parolasını yazın.
password_invalid=Geçersiz parola. Lütfen yeniden deneyin.
password_ok=Tamam
password_cancel=İptal

printing_not_supported=Uyarı: Yazdırma bu tarayıcı tarafından tam olarak desteklenmemektedir.
printing_not_ready=Uyarı: PDF tamamen yüklenmedi ve yazdırmaya hazır değil.
web_fonts_disabled=Web fontları devre dışı: Gömülü PDF fontları kullanılamıyor.

# Editor
editor_free_text2.title=Metin
editor_free_text2_label=Metin
editor_ink2.title=Çiz
editor_ink2_label=Çiz

free_text2_default_content=Yazmaya başlayın…

# Editor Parameters
editor_free_text_color=Renk
editor_free_text_size=Boyut
editor_ink_color=Renk
editor_ink_thickness=Kalınlık
editor_ink_opacity=Saydamlık

# Editor aria
editor_free_text2_aria_label=Metin düzenleyicisi
editor_ink2_aria_label=Çizim düzenleyicisi
editor_ink_canvas_aria_label=Kullanıcı tarafından oluşturulan resim
