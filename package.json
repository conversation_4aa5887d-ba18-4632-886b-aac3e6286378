{"name": "sgsmall", "code": "project_tic", "version": "24.11.26", "author": "Ken<PERSON>@sgs.com", "description": "sgsmall副站PC端管理系统", "private": true, "dependencies": {"axios": "^0.18.0", "egg": "^2.28.0", "egg-alinode": "^2.0.1", "egg-cors": "^2.2.3", "egg-multiple-static": "^0.2.1", "egg-mysql": "^3.0.0", "egg-scripts": "^2.5.0", "egg-sentry": "^1.0.0", "egg-view": "^2.1.0", "egg-view-ejs": "^2.0.0", "moment": "^2.22.1", "mysql": "^2.18.1", "request": "^2.86.0", "urlencode": "^1.1.0"}, "devDependencies": {"autod": "^3.0.1", "autod-egg": "^1.0.0", "egg-bin": "^4.3.5", "egg-ci": "^1.18.0", "egg-mock": "^3.14.0", "eslint": "^4.11.0", "eslint-config-egg": "^6.0.0", "webstorm-disable-index": "^1.2.0"}, "engines": {"node": ">=8.9.0"}, "scripts": {"prod": "set EGG_SERVER_ENV=prod && egg-scripts start --daemon --title=SGSmall --env=prod --port 9511", "uat": "egg-scripts start --daemon --title=SGSmall --env=UAT --port 9511", "dev": "egg-scripts start --daemon --title=SGSmall --env=dev --port 9511", "test": "egg-scripts start --daemon --title=SGSmall --env=test --port 9511", "prod_gray": "egg-scripts start --daemon --title=SGSmall --env=prodGray --port 9511", "gray": "egg-scripts start --title=SGSmall --env=gray --port 9511", "local": "egg-bin dev --port 9511 --env=local", "start": "egg-scripts start --daemon --title=SGSmall --port 9511 --ignore-stderr", "startBackup": "egg-scripts start --daemon --title=SGSmall --port 9511", "stop": "egg-scripts stop --title=SGSmall", "debug": "egg-bin debug --port 9511", "test-local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov", "autod": "autod"}, "ci": {"version": "8"}, "repository": {"type": "git", "url": ""}, "license": "MIT"}