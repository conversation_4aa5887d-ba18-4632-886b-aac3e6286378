<% include header.html %>
<div class="location detailLoca">
    <ul>
        <li class="locationLi"><a href="/">第三方检测机构</a></li>
        <li class="locationLi icon"></li>
        <% if(detail.catalog_name){ %><li class="locationLi"><a
                href="/<% if(detail.catalogParent == 1){ %>industry<% }else{ %>service<% } %>/<%- detail.catalogAlias %>/"><%- detail.catalog_name %></a>
        </li>
        <li class="locationLi icon"></li><% } %>
        <li class="locationLi"><%- detail.name %></li>
    </ul>
</div>
<div class="detailBox" style="overflow: hidden;position: relative;">
    <div class="bannerInfo" style="float:right;">

        <div style="min-height:216px">
            <div class="word1"><%- detail.name %></div>
            <div class="word2"><%- detail.sub_title %></div>
            <div class="word3" style="min-height: 0"><%- detail.description.replace(/\n/g,'<br>') %></div>

            <% if(detail.type == 2){ %>
            <div class="word-day">
                <!--<div class="word-day-icon"></div>-->
                <% if(class_infoTime && class_infoTime != ''){ %>
                <div class="word-day-word">开课时间：<% for(var item of class_infoTime){ %> <span
                        style="display:inline-block;white-space: nowrap"><%- item %></span> <% } %></div>
                <% } %>
                <% if(class_infoArea && class_infoArea != ''){ %>
                <div class="word-day-word">开课城市：<% for(var item of class_infoArea){ %> <span
                        style="display:inline-block;white-space: nowrap"><%- item %></span> <% } %></div>
                <% } %>
                <% if(class_infoDay && class_infoDay != ''){ %>
                <div class="word-day-word">课程天数： <span
                        style="display:inline-block;white-space: nowrap"><%- class_infoDay %>天</span></div>
                <% } %>
            </div>
            <% } %>
        </div>

        <% if(detail.is_el == 1 && detail.is_buy == 1 && (detail.el_url != 'https://' && detail.el_url != 'http://') && (detail.buy_url != 'https://' && detail.buy_url != 'http://')){ %>
        <div class="zixun-dinggou">
            <a href="/ticket" target="_blank" style="float:left;margin-bottom: 20px;width:150px;margin-right:40px;">
                <div class="zixun" style="width:148px">咨询报价</div>
            </a>
            <div class="dinggou" style="margin-bottom: 20px">
                <div class="deepen"></div>
                <div class="dinggouword"><a href="<%- detail.buy_url %>" style="color:#FFF" target="_blank">在线下单</a>
                </div>
            </div>
            <div class="dinggou">
                <div class="deepen"></div>
                <% if(isLogin){ %>
                <div class="dinggouword"><a href="javascrpit:void(0);" onclick="getEl('<%- detail.el_url %>')"
                        style="color:#FFF">立刻学习</a></div>
                <% }else{ %>
                <div class="dinggouword"><a href="<%- memberUrl %>/login" style="color:#FFF">立刻学习</a>
                </div>
                <% } %>

            </div>
        </div>
        <%  }else if(detail.is_el == 1 && detail.el_url != 'https://' && detail.el_url != 'http://'){ %>
        <div class="zixun-dinggou">
            <a href="/ticket" target="_blank">
                <div class="zixun">咨询报价</div>
            </a>
            <div class="dinggou">
                <div class="deepen"></div>
                <% if(isLogin){ %>
                <div class="dinggouword"><a href="javascrpit:void(0);" onclick="getEl('<%- detail.el_url %>')"
                        style="color:#FFF">立刻学习</a></div>
                <% }else{ %>
                <div class="dinggouword"><a href="<%- memberUrl %>/login" style="color:#FFF">立刻学习</a>
                </div>
                <% } %>

            </div>
        </div>
        <% }else if(detail.is_buy == 1 && detail.buy_url != 'https://' && detail.buy_url != 'http://'){ %>
        <div class="zixun-dinggou">
            <a href="/ticket" target="_blank">
                <div class="zixun">咨询报价</div>
            </a>
            <div class="dinggou">
                <div class="deepen"></div>
                <div class="dinggouword"><a href="<%- detail.buy_url %>" style="color:#FFF" target="_blank">在线下单</a>
                </div>
            </div>
        </div>
        <% }else{ %>
        <div class="word5">
            <a href="/ticket" style="color:#ca4300" target="_blank">
                <div class="word5-1"></div>
                <div class="word5-2">咨询报价</div>
            </a>
        </div>
        <% } %>
    </div>

    <div style="float: left">
        <% if(detail.banner_type == 0){ %>
        <div class="detailbannerBox">
            <div class="detailbanner">
                <img alt='<% if(detail.page_keywords.indexOf(",") > -1){ %><%- detail.page_keywords.substr(0, detail.page_keywords.indexOf(",")).trim() %><% }else{ %><%- detail.page_keywords.trim() %><% } %>'
                    src="<%- detail.cover_img.replace(/\/static/g,locals.static) %>" />
            </div>
        </div>
        <% }else{ %>
        <div class="detailbannerContent">
            <%- detail.banner_content.replace(/\/static/g, locals.static) %>
        </div>
        <% } %>

        <% for (var item of detail.content){ %>
        <div class="serverConBox">
            <% if(!item.hide_title){ %>
            <div class="serverTitleBox">
                <div class="serverCon" style="float:left;width:auto;font-weight: bold;"><%- item.title %></div>
            </div>
            <% } %>
            <div class="rtfc" style="clear:both;width:100%;height:auot;overflow:hidden;">
                <%- item.content.replace(/\/static/g,locals.static) %></div>
        </div>
        <% } %>

        <% if(detail.qa.length > 0){ %>
        <div class="serverConBox">
            <div class="serverTitleBox" style="margin-bottom:25px">
                <div class="serverCon" style="font-weight: bold;"><%- detail.qa[0].title || '常见问题' %></div>
            </div>

            <% for (var item of detail.qa){ %>
            <div class="questionBox" style="clear:both;overflow:hidden;">
                <div class="question-word1"><%- item.question %></div>
                <div class="question-word2 rtfc" style="font-size: 14px;line-height: 2">
                    <%- item.answer.replace(/\/static/g,locals.static) %></div>
                <div class="question-icon"></div>
            </div>
            <% } %>

        </div>
        <% } %>

        <% if(tagAdpt.length > 0){ %>
        <div class="recoment">
            <div class="square"></div>
            <div class="recoment-word1">为您推荐</div>
            <% if(tagAdpt.length > 3){ %><span onclick="_random()">
                <div class="recoment-word2">换一批</div>
                <div class="recoment-icon"></div>
            </span><% } %>
            <ul class="recomentUl" id="recList">
                <% for(var [i,item] of tagAdpt.entries()){ %>
                <% if(i<3){ %>
                <li class="recomentLi">
                    <a href="/sku/<%- item.id %>" target="_blank">
                        <div class="recomentLi-img">
                            <img src="<%- item.thumb_img.replace(/\/static/g,locals.static) %>" alt="">
                            <% if(item.is_buy == 1){ %><span class="buyicon"></span><% } %>
                        </div>
                        <div class="recomentLi-word"><%- item.name %></div>
                    </a>
                </li>
                <% } %>
                <% } %>
            </ul>
        </div>
        <% } %>

    </div>
</div>

<!--下载课表需要填写的信息窗口-->
<div class="down-load-pop">
  <div class="layout"></div>
  <div class='popBox'>
    <div class="title">
      <b>资源下载</b>
      <p>填写信息免费下载</p>
    </div>
    <div class="content">
      <i class="close">X</i>
      <input type="text" name='customer' id='customer2' placeholder="您的姓名" />
      <input type="text" name='phone' id='phone2' maxlength="11" placeholder="手机号码" />
      <input type="text" name='email' id='email2' placeholder="电子邮箱" />
      <div style="margin-top: 10px;">
        <label><input type="checkbox" name="" id="check">我接受</label><a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" style="color:#FE660B;text-decoration:none;" target="_blank">《隐私政策》</a>
      </div>
      <button id='submit2'>提交并下载</button>
    </div>
  </div>
</div>

<!--第二版下载课表需要填写的信息窗口-->
<div class="down-load-pop2">
  <div class="layout"></div>
  <div class='popBox'>
    <div class="title">
      <b>资源下载</b>
      <p>填写信息免费下载</p>
    </div>
    <div class="content">
      <i class="close">X</i>
      <div style="display: flex;flex-wrap: wrap;justify-content: space-between;">
        <input type="text" name='customer' id='customer3' placeholder="您的姓名" />
        <input type="text" name='phone' id='phone3' maxlength="11" placeholder="手机号码" />
        <input type="text" name='email' id='email3' placeholder="电子邮箱" />
        <input type="text" name='email' id='company3' placeholder="企业名称" />
        <textarea id="textarea3" placeholder="请简述您下载本资源最希望解决的问题" style="overflow-y: auto"></textarea>
      </div>
      <div style="text-align: left">
        <div style="font-size: 14px;margin-top:10px;">贵公司是否已与SGS有合作</div>
        <div style="font-size: 14px;margin-top:10px;display: flex">
          <input type="radio" name="isCoop" value="1">
          <span>是，IATF 16949认证</span>
        </div>
        <div style="font-size: 14px;margin-top:10px;display: flex">
          <input type="radio" name="isCoop" value="2">
          <span>否</span>
          <input type="text" name='coopName' id='coopName' placeholder="请输入您企业的IATF 16949认证机构" />
        </div>
      </div>
      <div style="margin-top: 20px;">
        <label><input type="checkbox" name="" id="check3">我接受</label><a href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs" style="color:#FE660B;text-decoration:none;" target="_blank">《隐私政策》</a>
      </div>
      <button id='submit3'>提交并下载</button>
    </div>
  </div>
</div>

<script>
    function _random() {
        $.get('/sku/randomSku', { id:<%- id %>}, function (r) {
            if (r.code == 0) {
                var list = r.data;
                var html = '';
                $.each(list, function (i, item) {
                    if (i < 3) {
                        html += '<li class="recomentLi"><a href="/sku/' + item.id + '" target="_blank"><div class="recomentLi-img"><img src="' + item.thumb_img.replace(/\/static/g, '<%- locals.static %>') + '" alt=""></div><div class="recomentLi-word">' + item.name + '</div></a></li>';
                    }
                });
                $('#recList').html(html);
            }
        })
    }

    function getEl(url) {
        var newWin = window.open();
        $.post('/getCode', { _csrf: '<%- csrf %>' }, function (res) {
            if (res.result_code == 0) {
                var token = res.data.token;
                var newurl = '<%- tokenUrl %>/interface/getSgsTokenIn?token=' + token + '&notifyUrl=' + url;
                newWin.location.href = newurl;
            }
        })
    }
    var pw = $('html,body').width(),
        ph = $('html,body').height();

    $('.down-load-pop .layout').css({
        width: pw,
        height: ph
    })
    $('.down-load-pop2 .layout').css({
        width: pw,
        height: ph
    })
    var downloadUrl,
        downloadType,
        cookieKey = location.pathname
    $('.download-btn').on('click', function(e) {
        var _target = $(e.target)
        downloadUrl = _target.attr('data-url')
        downloadType = _target.attr('data-type')
        var download = localStorage.getItem(cookieKey)
        if (!download) {
            $('.down-load-pop').show();
            $('.down-load-pop .popBox').animate({
                width: 349,
                height: 400,
                opacity: 1
            }, 200, function () {
                $('.down-load-pop .content').show();
            })
        } else if (JSON.parse(download).isSend) {
            // window.location.href = downloadUrl
            window.open(downloadUrl)
        }
    })
    $('.down-load-pop .close').on('click', function () {
        $('.down-load-pop .content').hide();
        $('.down-load-pop .popBox').animate({
            width: 0,
            height: 0,
            opacity: 0
        }, 200, function () {
            $('.down-load-pop').hide();
        })
    });
    $("#submit2").on("click", function () {
        var data = {
            type: downloadType,
            trade: '其他',
            tradeName: '其他',
            service: '其他',
            serviceName: '其他',
            content: '页面文件下载',
            customer: $('#customer2').val().trim(),
            phone: $('#phone2').val().trim(),
            email: $('#email2').val().trim(), // 不能为空，不能为非法邮箱
            provice: '其他地区',
            company: '系统定义',
            os_type: 'pc',
            _csrf: '<%- csrf %>'
        }
        if (!$('#customer2').val().trim()) {
            alert('请输入姓名！')
        } else if (!$('#phone2').val().trim()) {
            alert('请输入手机号码！')
        } else if (!/^1\d{10}$/.test($('#phone2').val().trim())) {
            alert('请输入正确的手机号码！')
        } else if (!$('#email2').val().trim()) {
            alert('请输入电子邮箱')
        } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email2').val().trim())) {
            alert('请输入正确电子邮箱')
        } else if (!$('#check').prop("checked")) {
            alert('请同意隐私政策！')
        } else {
            $.ajax({
                url: "/ticket/post",
                type: 'POST',
                data: data,
                success: function (res) {
                    if (res.success) {
                        $('.down-load-pop').hide();
                        localStorage.setItem(cookieKey, JSON.stringify({ isSend: true })); // 用于校验后期下载文件是否可以直接下载
                        // window.location.href = downloadUrl
                        window.open(downloadUrl)
                    } else {
                        alert(res.data)
                    }
                },
                fail: function (e) {
                }
            })
        }
    })

    // 第二版下载交互需求
    $('.download-btn2').on('click', function(e) {
        var _target = $(e.target)
        downloadUrl = _target.attr('data-url')
        downloadType = _target.attr('data-type')
        var download = localStorage.getItem(cookieKey)
        if (!download) {
            $('.down-load-pop2').show();
            $('.down-load-pop2 .popBox').animate({
                width: 422,
                height: 531,
                opacity: 1
            }, 200, function () {
                $('.down-load-pop2 .content').show();
            })
        } else if (JSON.parse(download).isSend) {
            // window.location.href = downloadUrl
            window.open(downloadUrl)
        }
    })
    $('.down-load-pop2 .close').on('click', function () {
        $('.down-load-pop2 .content').hide();
        $('.down-load-pop2 .popBox').animate({
            width: 0,
            height: 0,
            opacity: 0
        }, 200, function () {
            $('.down-load-pop2').hide();
        })
    });
    $("#submit3").on("click", function () {
        var company = $('#company3').val().trim()
        var content = $('#textarea3').val().trim()
        var isCoop = $('input[name="isCoop"]:checked').val()
        var data = {
            type: isCoop === '1' ? 'IATF 16949' : $('#coopName').val().trim(),
            trade: '其他',
            tradeName: '其他',
            service: '其他',
            serviceName: '其他',
            content: content,
            customer: $('#customer3').val().trim(),
            phone: $('#phone3').val().trim(),
            email: $('#email3').val().trim(), // 不能为空，不能为非法邮箱
            provice: '其他地区',
            company: company,
            os_type: 'pc',
            _csrf: '<%- csrf %>'
        }
        if (!$('#customer3').val().trim()) {
            alert('请输入姓名！')
        } else if (!$('#phone3').val().trim()) {
            alert('请输入手机号码！')
        } else if (!/^1\d{10}$/.test($('#phone3').val().trim())) {
            alert('请输入正确的手机号码！')
        } else if (!$('#email3').val().trim()) {
            alert('请输入电子邮箱')
        } else if (!  /^[a-zA-Z0-9_.-]+@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/.test($('#email3').val().trim())) {
            alert('请输入正确电子邮箱')
        } else if (!company) {
            alert('请输入企业名称')
        } else if (!content) {
            alert('请简述您下载本资源最希望解决的问题')
        } else if (!isCoop) {
            alert('请选择贵公司是否已与SGS有合作')
        } else if (!data.type) {
            alert('请输入您企业的IATF 16949认证机构')
        } else if (!$('#check3').prop("checked")) {
            alert('请同意隐私政策！')
        } else {
            $.ajax({
                url: "/ticket/post",
                type: 'POST',
                data: data,
                success: function (res) {
                    if (res.success) {
                        $('.down-load-pop2').hide();
                        localStorage.setItem(cookieKey, JSON.stringify({ isSend: true })); // 用于校验后期下载文件是否可以直接下载
                        // window.location.href = downloadUrl
                        window.open(downloadUrl)
                    } else {
                        alert(res.data)
                    }
                },
                fail: function (e) {
                }
            })
        }
    })

    // 2019年9月16日 set tm sup
    $(function () {
        var wordHtml = $('.word1').html();
        if (wordHtml === 'WELLTM性能验证服务') {
            $('.word1').html('WELL<sup style="font-size: 10px;">TM</sup>性能验证服务')
        }
    });
</script>
<% include footer.html %>
