$(function () {
    if($('.detailBox').length > 0){
        boxFix();
        boxMove();
    }
   

})
    $(window).resize(function () {
        if($('.detailBox').length > 0){
            boxFix();
        }
        
    })
    function boxFix() {
        var $_deban = $(".detailBox");

        if($_deban.length == 0){
            return;
        }

        var offset = $_deban.offset();

        var $_ban = $(".bannerInfo");
        $_ban.css('position','fixed');
        var sc = $(document);

        var left = offset.left + 825 + 59;
        var top = offset.top;
        $_ban.css("left",left).css("top",top);

        var fo = $('#helpInfo').offset().top,fh = $('#helpInfo').outerHeight(),bh = document.documentElement.clientHeight || document.body.clientHeight;
        var ph = $('body').height();
        var sh = $_ban.outerHeight();
        $(window).scroll(function () {
            if(sc.scrollTop()>=110){
                $_ban.addClass("ngsFix");
                // $_nav1.addClass("fix");
                /*
                if(sc.scrollTop()>=1700){
                    $_ban.addClass("ngsAbsolute").removeClass("ngsFix").css("left",left - offset.left);
                }else {
                    $_ban.removeClass("ngsAbsolute").addClass("ngsFix").css("left",left);
                }
                */
                
                if(sc.scrollTop() + 100 + sh >= fo - 100){
                    $_ban.css('top',bh - fh - $_ban.outerHeight() - 100);
                }else{
                    $_ban.css('top',100);
                }
            }else{
                $_ban.removeClass("ngsFix");
                $_ban.css("top",top - sc.scrollTop());
                // $_nav1.removeClass("fix");

            }
        })
    }

    function boxMove() {
        var $_nav = $(".nav");
        var sc = $(document);

        // var $_nav1 = $(".nav");
        $(window).scroll(function () {

            if(sc.scrollTop()>=110){

                $_nav.addClass("fix");
                // $_nav1.addClass("fix");
            }else{

                $_nav.removeClass("fix");
                // $_nav1.removeClass("fix");

            }
        })
    }