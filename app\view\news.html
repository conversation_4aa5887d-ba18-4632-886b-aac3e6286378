<% include header.html %>
<% include ./components/header.html %>
    <div class="myLocation">
        <div class="myLocationBox">
            <div class="myLocation-word1"><a href="/">第三方检测机构</a></div>
            <div class="myLocation-icon1 icon"></div>
            <div class="myLocation-word1"><a href="/news">新闻</a></div>
            <div class="myLocation-icon1 icon"></div>
            <div class="myLocation-word2"><%- detail.title %></div>
        </div>
    </div>
    <div class="newsBox clearfix" id="newsBox">
        <div class="leftBox" style="float: left;">
            <h1 class="news_title"><%- detail.title %></h1>
            <div class="subhead">
                <div class="subhead1"><%- detail.gmtCreate %></div>
                <div class="subhead2"><%- detail.name %></div>
                <div class="subhead2">作者： <%- detail.author %></div>
                <div class="subhead2">来源：SGS</div>
                <div class="subhead3">访问次数： <%- detail.visitsNum %></div>
            </div>
            <% if(detail.pageDescription){ %>
                <div class="summary">
                    【摘要】：<% if(detail.pageDescription.length> 60){ %><%- detail.pageDescription.slice(0, 60) %>...
                            <% }else{ %> <%- detail.pageDescription %>
                                    <% } %>
                </div>
                <% } %>
                    <div class="con1">
                        <!-- <%- detail.content.replace(/\/static/g,locals.static) %> -->
                        <%- detail.content %>
                    </div>
                    <div class="chapters">
                        <div class="lastchapter">上一篇：<% if(prevNews){ %><a
                                    href="/news/<%- prevNews.alias %>/detail-<%- prevNews.id %>.html"><%- prevNews.title
                                        %></a>
                                <% }else{ %>无<% } %>
                        </div>
                        <div class="nextchapter">下一篇：<% if(nextNews){ %><a
                                    href="/news/<%- prevNews.alias %>/detail-<%- nextNews.id %>.html"><%- nextNews.title
                                        %></a>
                                <% }else{ %>无<% } %>
                        </div>
                    </div>
                    <div class="bdsharebuttonbox" style="margin: 20px 0;">
                        <a href="#" class="bds_more" data-cmd="more"></a>
                        <a href="#" class="bds_qzone" data-cmd="qzone" title="分享到QQ空间"></a>
                        <a href="#" class="bds_tsina" data-cmd="tsina" title="分享到新浪微博"></a>
                        <a href="#" class="bds_tqq" data-cmd="tqq" title="分享到腾讯微博"></a>
                        <a href="#" class="bds_renren" data-cmd="renren" title="分享到人人网"></a>
                        <a href="#" class="bds_weixin" data-cmd="weixin" title="分享到微信"></a>
                    </div>
                    <script>window._bd_share_config = { "common": { "bdSnsKey": {}, "bdText": "", "bdMini": "2", "bdMiniList": false, "bdPic": "", "bdStyle": "0", "bdSize": "24" }, "share": {}, "image": { "viewList": ["qzone", "tsina", "tqq", "renren", "weixin"], "viewText": "分享到：", "viewSize": "16" }, "selectShare": { "bdContainerClass": null, "bdSelectMiniList": ["qzone", "tsina", "tqq", "renren", "weixin"] } }; with (document) 0[(getElementsByTagName('head')[0] || body).appendChild(createElement('script')).src = '<%- locals.static %>/api/js/share.js'];
                    </script>

                    <% if(relatedNews && relatedNews.length){ %>
                        <div class="relatedNews">
                            <p style="margin-bottom: 10px;">相关文章：</p>
                            <% for(var item of relatedNews){ %>
                                <div>
                                    <a style="color: #000;"
                                        href="/news/<%- item.alias %>/detail-<%- item.id %>.html"><%- item.title %></a>
                                </div>
                                <% } %>
                        </div>
                        <% } %>
                        <div class="rightBox">
                            <div class="rightBox1">
                                <div class="rela">相关内容：</div>
                                <% for(var item of otherNews){ %>
                                    <div class="rightContitleBox">
                                        <a class="rightContitle" href="/news/<%- item.alias %>/detail-<%- item.id %>.html"><%-
                                                item.title %></a>
                                        <div class="rightConSubtitle"><%- item.content %></div>
                                    </div>
                                    <% } %>
                            </div>
                        </div>

        </div>
        
        <div class="newsBox-form searchrightBox bannerInfo" v-cloak>
            <% include ./components/sku_side_form.html %>
            <% include ./pages/quote/modal.html %>
        </div>
    </div>
    </div>
    <script src="../../public/js/news.js" type="text/javascript"></script>
    <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
<% if(locals.env != 'prod'){ %>
<script src="<%- locals.static %>/plugin/v2.test.js"></script>
<% }else{ %>
<script src="https://cnstatic.sgsonline.com.cn/tic/plugin/v2/v2.js" type="text/javascript"></script>
<% } %>
    <script uac_busitype="<%- uac_busiType || '' %>" uac_busisubtype="<%- uac_busiSubType || '' %>"  uac_busicode="<%- uac_busiCode || '' %>"></script>
    <script>
        var newVue = new Vue({
            name: 'newsBox',
            el: '#newsBox',
            mixins: [quoteMixins],
            data: {
                isLogin: <%- isLogin %>,
                // 留言表单数据
                loading: false,
                form: {
                    type: '业务咨询',
                    trade: '',
                    tradeName: '其他',
                    tradeIndex: '',
                    serviceIndex: '',
                    service: '',
                    serviceName: '其他',
                    company: '',
                    provice: '',
                    content: '',
                    frontTitle: '<%- detail.name %>',
                    customer: '<%- userInfo.userName %>',
                    email: '<%- userInfo.userEmail %>',
                    phone: '<%- userInfo.userPhone %>',
                    imageCode: '',
                    frontUrl: window.location.href,
                    destCountry: '',
                    verifyCode: '',
                    regMode: 'news',
                    regSource: 'mall',
                    regFrom: window.location.href,
                    busiCode: '<%- detail.id %>'
                },
                showModal: false,
                seconds: 59,
                timer: null,
                countdownTime: 5,
                countdownTimer: null,
                disablePhone: <%- disablePhone %>,
                disableMail: <%- disableMail %>,
                isSuccess: false,
                approve: false,
                tab: 'phone',
                provice: [],
                rules: {
                    provice: [{ required: true, message: '*请选择所在城市' }],
                    content: [{ required: true, message: '*请输入您的咨询内容' }],
                    customer: [{ required: true, message: '*请输入您的称呼' }],
                },
                dialogVisibleReg: false,
                contact_approve_show: true,
                loginSource: '',
                caseLoading: false,
                pageInfo: {
                    title: '验证手机号',
                    sendModel: '已发送到手机号：+86',
                    prepend: '手机验证码'
                },
                captchaAppId: '<%- captchaAppId %>',
                pid: 'pid.mall',
                pcode: 'Z0zCnRE3IaY9Kzem',
                host: '<%- host %>',
            },
            methods: {
                headerRegister: function () {
                    this.showLoginModal = true
                    this.closeBtn = true
                    this.loginSource = 'header'
                },
                handleCloseLogin: function () {
                    this.showLoginModal = false
                },
                successCallback: function (type) {
                    var that = this
                    that.showLoginModal = false
                    that.caseLoading = true
                    location.reload()
                },
                handleChange: function () { },
                handleJumpStep1: function () {
                    window.open('<%- memberUrl %>/questionnaire/step')
                },
            }
        });

    </script>

    <% include footer.html %>